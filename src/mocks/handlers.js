import { http, HttpResponse } from 'msw';
import { masterUsers, masterFunctions, masterProcesses, masterApplicationLibrary, mockBIAs } from '../mockData';

export const handlers = [
  http.get('/api/users', () => {
    return HttpResponse.json(masterUsers);
  }),

  http.get('/api/functions', () => {
    return HttpResponse.json(masterFunctions);
  }),

  http.get('/api/processes/:functionId', ({ params }) => {
    const { functionId } = params;
    return HttpResponse.json(masterProcesses[functionId] || []);
  }),

  http.get('/api/applications', () => {
    return HttpResponse.json(masterApplicationLibrary);
  }),

  http.get('/api/bias', () => {
    return HttpResponse.json(mockBIAs);
  }),

  http.post('/api/bias', async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json({ id: 'bia-new', ...body });
  }),

  http.put('/api/bias/:id', async ({ params, request }) => {
    const body = await request.json();
    return HttpResponse.json({ id: params.id, ...body });
  }),

  http.post('/api/bias/:id/submit', async ({ params, request }) => {
    const body = await request.json();
    return HttpResponse.json({ id: params.id, status: 'Pending Approval', ...body });
  })
];