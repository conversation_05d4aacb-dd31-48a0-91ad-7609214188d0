import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container, Typography, Grid, Card, CardContent, Button, Box,
  Table, TableBody, TableCell, TableHead, TableRow, Chip, IconButton
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Visibility as ViewIcon } from '@mui/icons-material';
import { setBIAList } from '../store/biaSlice';

const Dashboard = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { bias: biaList } = useSelector(state => state.bia);
  const [stats, setStats] = useState({
    total: 0,
    draft: 0,
    pending: 0,
    approved: 0
  });

  useEffect(() => {
    // Mock data - in real app would fetch from API
    const mockBIAs = [
      {
        id: 'bia-001',
        name: 'Finance Department BIA',
        function: 'Finance',
        owner: '<PERSON>',
        status: 'Draft',
        lastModified: '2024-01-15',
        processes: 3
      },
      {
        id: 'bia-002', 
        name: 'IT Infrastructure BIA',
        function: 'Technology',
        owner: '<PERSON>',
        status: 'Pending Approval',
        lastModified: '2024-01-14',
        processes: 5,
        submittedAt: '2024-01-14'
      },
      {
        id: 'bia-003',
        name: 'HR Operations BIA', 
        function: 'Human Resources',
        owner: 'Carol Davis',
        status: 'Approved',
        lastModified: '2024-01-10',
        processes: 2,
        approvedAt: '2024-01-12'
      }
    ];

    dispatch(setBIAList(mockBIAs));

    // Calculate stats
    const newStats = {
      total: mockBIAs.length,
      draft: mockBIAs.filter(bia => bia.status === 'Draft').length,
      pending: mockBIAs.filter(bia => bia.status === 'Pending Approval').length,
      approved: mockBIAs.filter(bia => bia.status === 'Approved').length
    };
    setStats(newStats);
  }, [dispatch]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Draft': return 'default';
      case 'Pending Approval': return 'warning';
      case 'Approved': return 'success';
      default: return 'default';
    }
  };

  const handleEdit = (biaId) => {
    navigate(`/bia/${biaId}`);
  };

  const handleView = (biaId) => {
    window.open(`/bia/${biaId}/view`, '_blank');
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1">
          BIA Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/bia/new')}
        >
          Create New BIA
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total BIAs
              </Typography>
              <Typography variant="h4">
                {stats.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Draft
              </Typography>
              <Typography variant="h4" color="text.secondary">
                {stats.draft}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending Approval
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pending}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Approved
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.approved}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* BIA List */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            My BIAs
          </Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Function</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Processes</TableCell>
                <TableCell>Last Modified</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {biaList.map((bia) => (
                <TableRow key={bia.id}>
                  <TableCell>{bia.name}</TableCell>
                  <TableCell>{bia.function}</TableCell>
                  <TableCell>
                    <Chip 
                      label={bia.status} 
                      color={getStatusColor(bia.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{bia.processes}</TableCell>
                  <TableCell>{bia.lastModified}</TableCell>
                  <TableCell>
                    {bia.status === 'Draft' ? (
                      <IconButton 
                        size="small" 
                        onClick={() => handleEdit(bia.id)}
                        title="Edit"
                      >
                        <EditIcon />
                      </IconButton>
                    ) : (
                      <IconButton 
                        size="small" 
                        onClick={() => handleView(bia.id)}
                        title="View"
                      >
                        <ViewIcon />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </Container>
  );
};

export default Dashboard;