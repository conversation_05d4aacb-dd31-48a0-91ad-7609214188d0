import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export const fetchBIAs = createAsyncThunk('bia/fetchBIAs', async () => {
  const response = await fetch('/api/bias');
  return response.json();
});

export const saveBIA = createAsyncThunk('bia/saveBIA', async (biaData) => {
  const response = await fetch(`/api/bias/${biaData.id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(biaData)
  });
  return response.json();
});

export const submitBIA = createAsyncThunk('bia/submitBIA', async (submissionData) => {
  const response = await fetch(`/api/bias/${submissionData.id}/submit`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(submissionData)
  });
  return response.json();
});

const biaSlice = createSlice({
  name: 'bia',
  initialState: {
    bias: [],
    currentBIA: null,
    selectedProcess: null,
    status: 'idle'
  },
  reducers: {
    setBIAList: (state, action) => {
      state.bias = action.payload;
    },
    setCurrentBIA: (state, action) => {
      state.currentBIA = action.payload;
    },
    setSelectedProcess: (state, action) => {
      state.selectedProcess = action.payload;
    },
    updateBIAField: (state, action) => {
      const { field, value } = action.payload;
      if (state.currentBIA) {
        state.currentBIA[field] = value;
      }
    },
    updateProcessData: (state, action) => {
      const { processId, data } = action.payload;
      if (state.currentBIA) {
        if (!state.currentBIA.processData) {
          state.currentBIA.processData = {};
        }
        state.currentBIA.processData[processId] = {
          ...state.currentBIA.processData[processId],
          ...data
        };
      }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBIAs.fulfilled, (state, action) => {
        state.bias = action.payload;
        state.status = 'succeeded';
      });
  }
});

export const { setBIAList, setCurrentBIA, setSelectedProcess, updateBIAField, updateProcessData } = biaSlice.actions;
export default biaSlice.reducer;