{"ast": null, "code": "import { webSocketInterceptor } from './webSocketInterceptor.mjs';\nimport { onUnhandledRequest } from '../utils/request/onUnhandledRequest.mjs';\nimport { isHandlerKind } from '../utils/internal/isHandlerKind.mjs';\nfunction handleWebSocketEvent(options) {\n  webSocketInterceptor.on(\"connection\", async connection => {\n    const handlers = options.getHandlers().filter(isHandlerKind(\"EventHandler\"));\n    if (handlers.length > 0) {\n      options?.onMockedConnection(connection);\n      await Promise.all(handlers.map(handler => {\n        return handler.run(connection);\n      }));\n      return;\n    }\n    const request = new Request(connection.client.url, {\n      headers: {\n        upgrade: \"websocket\",\n        connection: \"upgrade\"\n      }\n    });\n    await onUnhandledRequest(request, options.getUnhandledRequestStrategy()).catch(error => {\n      const errorEvent = new Event(\"error\");\n      Object.defineProperty(errorEvent, \"cause\", {\n        enumerable: true,\n        configurable: false,\n        value: error\n      });\n      connection.client.socket.dispatchEvent(errorEvent);\n    });\n    options?.onPassthroughConnection(connection);\n    connection.server.connect();\n  });\n}\nexport { handleWebSocketEvent };", "map": {"version": 3, "names": ["webSocketInterceptor", "onUnhandledRequest", "isHandlerKind", "handleWebSocketEvent", "options", "on", "connection", "handlers", "getHandlers", "filter", "length", "onMockedConnection", "Promise", "all", "map", "handler", "run", "request", "Request", "client", "url", "headers", "upgrade", "getUnhandledRequestStrategy", "catch", "error", "errorEvent", "Event", "Object", "defineProperty", "enumerable", "configurable", "value", "socket", "dispatchEvent", "onPassthroughConnection", "server", "connect"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/ws/handleWebSocketEvent.ts"], "sourcesContent": ["import type { WebSocketConnectionData } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\nimport { RequestHandler } from '../handlers/RequestHandler'\nimport { WebSocketHandler } from '../handlers/WebSocketHandler'\nimport { webSocketInterceptor } from './webSocketInterceptor'\nimport {\n  onUnhandledRequest,\n  UnhandledRequestStrategy,\n} from '../utils/request/onUnhandledRequest'\nimport { isHandlerKind } from '../utils/internal/isHandlerKind'\n\ninterface HandleWebSocketEventOptions {\n  getUnhandledRequestStrategy: () => UnhandledRequestStrategy\n  getHandlers: () => Array<RequestHandler | WebSocketHandler>\n  onMockedConnection: (connection: WebSocketConnectionData) => void\n  onPassthroughConnection: (onnection: WebSocketConnectionData) => void\n}\n\nexport function handleWebSocketEvent(options: HandleWebSocketEventOptions) {\n  webSocketInterceptor.on('connection', async (connection) => {\n    const handlers = options.getHandlers().filter(isHandlerKind('EventHandler'))\n\n    // Ignore this connection if the user hasn't defined any handlers.\n    if (handlers.length > 0) {\n      options?.onMockedConnection(connection)\n\n      await Promise.all(\n        handlers.map((handler) => {\n          // Iterate over the handlers and forward the connection\n          // event to WebSocket event handlers. This is equivalent\n          // to dispatching that event onto multiple listeners.\n          return handler.run(connection)\n        }),\n      )\n\n      return\n    }\n\n    // Construct a request representing this WebSocket connection.\n    const request = new Request(connection.client.url, {\n      headers: {\n        upgrade: 'websocket',\n        connection: 'upgrade',\n      },\n    })\n    await onUnhandledRequest(\n      request,\n      options.getUnhandledRequestStrategy(),\n    ).catch((error) => {\n      const errorEvent = new Event('error')\n      Object.defineProperty(errorEvent, 'cause', {\n        enumerable: true,\n        configurable: false,\n        value: error,\n      })\n      connection.client.socket.dispatchEvent(errorEvent)\n    })\n\n    options?.onPassthroughConnection(connection)\n\n    // If none of the \"ws\" handlers matched,\n    // establish the WebSocket connection as-is.\n    connection.server.connect()\n  })\n}\n"], "mappings": "AAGA,SAASA,oBAAA,QAA4B;AACrC,SACEC,kBAAA,QAEK;AACP,SAASC,aAAA,QAAqB;AASvB,SAASC,qBAAqBC,OAAA,EAAsC;EACzEJ,oBAAA,CAAqBK,EAAA,CAAG,cAAc,MAAOC,UAAA,IAAe;IAC1D,MAAMC,QAAA,GAAWH,OAAA,CAAQI,WAAA,CAAY,EAAEC,MAAA,CAAOP,aAAA,CAAc,cAAc,CAAC;IAG3E,IAAIK,QAAA,CAASG,MAAA,GAAS,GAAG;MACvBN,OAAA,EAASO,kBAAA,CAAmBL,UAAU;MAEtC,MAAMM,OAAA,CAAQC,GAAA,CACZN,QAAA,CAASO,GAAA,CAAKC,OAAA,IAAY;QAIxB,OAAOA,OAAA,CAAQC,GAAA,CAAIV,UAAU;MAC/B,CAAC,CACH;MAEA;IACF;IAGA,MAAMW,OAAA,GAAU,IAAIC,OAAA,CAAQZ,UAAA,CAAWa,MAAA,CAAOC,GAAA,EAAK;MACjDC,OAAA,EAAS;QACPC,OAAA,EAAS;QACThB,UAAA,EAAY;MACd;IACF,CAAC;IACD,MAAML,kBAAA,CACJgB,OAAA,EACAb,OAAA,CAAQmB,2BAAA,CAA4B,CACtC,EAAEC,KAAA,CAAOC,KAAA,IAAU;MACjB,MAAMC,UAAA,GAAa,IAAIC,KAAA,CAAM,OAAO;MACpCC,MAAA,CAAOC,cAAA,CAAeH,UAAA,EAAY,SAAS;QACzCI,UAAA,EAAY;QACZC,YAAA,EAAc;QACdC,KAAA,EAAOP;MACT,CAAC;MACDnB,UAAA,CAAWa,MAAA,CAAOc,MAAA,CAAOC,aAAA,CAAcR,UAAU;IACnD,CAAC;IAEDtB,OAAA,EAAS+B,uBAAA,CAAwB7B,UAAU;IAI3CA,UAAA,CAAW8B,MAAA,CAAOC,OAAA,CAAQ;EAC5B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}