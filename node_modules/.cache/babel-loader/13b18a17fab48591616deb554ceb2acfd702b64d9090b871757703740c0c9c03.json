{"ast": null, "code": "// src/createDeferredExecutor.ts\nfunction createDeferredExecutor() {\n  const executor = (resolve, reject) => {\n    executor.state = \"pending\";\n    executor.resolve = data => {\n      if (executor.state !== \"pending\") {\n        return;\n      }\n      executor.result = data;\n      const onFulfilled = value => {\n        executor.state = \"fulfilled\";\n        return value;\n      };\n      return resolve(data instanceof Promise ? data : Promise.resolve(data).then(onFulfilled));\n    };\n    executor.reject = reason => {\n      if (executor.state !== \"pending\") {\n        return;\n      }\n      queueMicrotask(() => {\n        executor.state = \"rejected\";\n      });\n      return reject(executor.rejectionReason = reason);\n    };\n  };\n  return executor;\n}\n\n// src/DeferredPromise.ts\nvar DeferredPromise = class extends Promise {\n  #executor;\n  resolve;\n  reject;\n  constructor(executor = null) {\n    const deferredExecutor = createDeferredExecutor();\n    super((originalResolve, originalReject) => {\n      deferredExecutor(originalResolve, originalReject);\n      executor?.(deferredExecutor.resolve, deferredExecutor.reject);\n    });\n    this.#executor = deferredExecutor;\n    this.resolve = this.#executor.resolve;\n    this.reject = this.#executor.reject;\n  }\n  get state() {\n    return this.#executor.state;\n  }\n  get rejectionReason() {\n    return this.#executor.rejectionReason;\n  }\n  then(onFulfilled, onRejected) {\n    return this.#decorate(super.then(onFulfilled, onRejected));\n  }\n  catch(onRejected) {\n    return this.#decorate(super.catch(onRejected));\n  }\n  finally(onfinally) {\n    return this.#decorate(super.finally(onfinally));\n  }\n  #decorate(promise) {\n    return Object.defineProperties(promise, {\n      resolve: {\n        configurable: true,\n        value: this.resolve\n      },\n      reject: {\n        configurable: true,\n        value: this.reject\n      }\n    });\n  }\n};\nexport { DeferredPromise, createDeferredExecutor };", "map": {"version": 3, "names": ["createDeferredExecutor", "executor", "resolve", "reject", "state", "data", "result", "onFulfilled", "value", "Promise", "then", "reason", "queueMicrotask", "rejectionReason", "DeferredPromise", "constructor", "deferredExecutor", "originalResolve", "originalReject", "onRejected", "decorate", "catch", "finally", "onfinally", "#decorate", "promise", "Object", "defineProperties", "configurable"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@open-draft/deferred-promise/src/createDeferredExecutor.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@open-draft/deferred-promise/src/DeferredPromise.ts"], "sourcesContent": ["export type PromiseState = 'pending' | 'fulfilled' | 'rejected'\n\nexport type Executor<Value> = ConstructorParameters<typeof Promise<Value>>[0]\nexport type ResolveFunction<Value> = Parameters<Executor<Value>>[0]\nexport type RejectFunction<Reason> = Parameters<Executor<Reason>>[1]\n\nexport type DeferredPromiseExecutor<Input = never, Output = Input> = {\n  (resolve?: ResolveFunction<Input>, reject?: RejectFunction<any>): void\n\n  resolve: ResolveFunction<Input>\n  reject: RejectFunction<any>\n  result?: Output\n  state: PromiseState\n  rejectionReason?: unknown\n}\nexport function createDeferredExecutor<\n  Input = never,\n  Output = Input\n>(): DeferredPromiseExecutor<Input, Output> {\n  const executor = <DeferredPromiseExecutor<Input, Output>>((\n    resolve,\n    reject\n  ) => {\n    executor.state = 'pending'\n\n    executor.resolve = (data) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      executor.result = data as Output\n\n      const onFulfilled = <Value>(value: Value) => {\n        executor.state = 'fulfilled'\n        return value\n      }\n\n      return resolve(\n        data instanceof Promise ? data : Promise.resolve(data).then(onFulfilled)\n      )\n    }\n\n    executor.reject = (reason) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      queueMicrotask(() => {\n        executor.state = 'rejected'\n      })\n\n      return reject((executor.rejectionReason = reason))\n    }\n  })\n\n  return executor\n}\n", "import {\n  type Executor,\n  type RejectFunction,\n  type ResolveFunction,\n  type DeferredPromiseExecutor,\n  createDeferredExecutor,\n} from './createDeferredExecutor'\n\nexport class DeferredPromise<Input, Output = Input> extends Promise<Input> {\n  #executor: DeferredPromiseExecutor\n\n  public resolve: ResolveFunction<Output>\n  public reject: RejectFunction<Output>\n\n  constructor(executor: Executor<Input> | null = null) {\n    const deferredExecutor = createDeferredExecutor()\n    super((originalResolve, originalReject) => {\n      deferredExecutor(originalResolve, originalReject)\n      executor?.(deferredExecutor.resolve, deferredExecutor.reject)\n    })\n\n    this.#executor = deferredExecutor\n    this.resolve = this.#executor.resolve\n    this.reject = this.#executor.reject\n  }\n\n  public get state() {\n    return this.#executor.state\n  }\n\n  public get rejectionReason() {\n    return this.#executor.rejectionReason\n  }\n\n  public then<ThenResult = Input, CatchResult = never>(\n    onFulfilled?: (value: Input) => ThenResult | PromiseLike<ThenResult>,\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.then(onFulfilled, onRejected))\n  }\n\n  public catch<CatchResult = never>(\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.catch(onRejected))\n  }\n\n  public finally(onfinally?: () => void | Promise<any>) {\n    return this.#decorate(super.finally(onfinally))\n  }\n\n  #decorate<ChildInput>(\n    promise: Promise<ChildInput>\n  ): DeferredPromise<ChildInput, Output> {\n    return Object.defineProperties(promise, {\n      resolve: { configurable: true, value: this.resolve },\n      reject: { configurable: true, value: this.reject },\n    }) as DeferredPromise<ChildInput, Output>\n  }\n}\n"], "mappings": ";AAeO,SAASA,uBAAA,EAG4B;EAC1C,MAAMC,QAAA,GAAoDA,CACxDC,OAAA,EACAC,MAAA,KACG;IACHF,QAAA,CAASG,KAAA,GAAQ;IAEjBH,QAAA,CAASC,OAAA,GAAWG,IAAA,IAAS;MAC3B,IAAIJ,QAAA,CAASG,KAAA,KAAU,WAAW;QAChC;MACF;MAEAH,QAAA,CAASK,MAAA,GAASD,IAAA;MAElB,MAAME,WAAA,GAAsBC,KAAA,IAAiB;QAC3CP,QAAA,CAASG,KAAA,GAAQ;QACjB,OAAOI,KAAA;MACT;MAEA,OAAON,OAAA,CACLG,IAAA,YAAgBI,OAAA,GAAUJ,IAAA,GAAOI,OAAA,CAAQP,OAAA,CAAQG,IAAI,EAAEK,IAAA,CAAKH,WAAW,CACzE;IACF;IAEAN,QAAA,CAASE,MAAA,GAAUQ,MAAA,IAAW;MAC5B,IAAIV,QAAA,CAASG,KAAA,KAAU,WAAW;QAChC;MACF;MAEAQ,cAAA,CAAe,MAAM;QACnBX,QAAA,CAASG,KAAA,GAAQ;MACnB,CAAC;MAED,OAAOD,MAAA,CAAQF,QAAA,CAASY,eAAA,GAAkBF,MAAO;IACnD;EACF;EAEA,OAAOV,QAAA;AACT;;;AChDO,IAAMa,eAAA,GAAN,cAAqDL,OAAA,CAAe;EACzE,CAAAR,QAAA;EAEOC,OAAA;EACAC,MAAA;EAEPY,YAAYd,QAAA,GAAmC,MAAM;IACnD,MAAMe,gBAAA,GAAmBhB,sBAAA,CAAuB;IAChD,MAAM,CAACiB,eAAA,EAAiBC,cAAA,KAAmB;MACzCF,gBAAA,CAAiBC,eAAA,EAAiBC,cAAc;MAChDjB,QAAA,GAAWe,gBAAA,CAAiBd,OAAA,EAASc,gBAAA,CAAiBb,MAAM;IAC9D,CAAC;IAED,KAAK,CAAAF,QAAA,GAAYe,gBAAA;IACjB,KAAKd,OAAA,GAAU,KAAK,CAAAD,QAAA,CAAUC,OAAA;IAC9B,KAAKC,MAAA,GAAS,KAAK,CAAAF,QAAA,CAAUE,MAAA;EAC/B;EAEA,IAAWC,MAAA,EAAQ;IACjB,OAAO,KAAK,CAAAH,QAAA,CAAUG,KAAA;EACxB;EAEA,IAAWS,gBAAA,EAAkB;IAC3B,OAAO,KAAK,CAAAZ,QAAA,CAAUY,eAAA;EACxB;EAEOH,KACLH,WAAA,EACAY,UAAA,EACA;IACA,OAAO,KAAK,CAAAC,QAAA,CAAU,MAAMV,IAAA,CAAKH,WAAA,EAAaY,UAAU,CAAC;EAC3D;EAEOE,MACLF,UAAA,EACA;IACA,OAAO,KAAK,CAAAC,QAAA,CAAU,MAAMC,KAAA,CAAMF,UAAU,CAAC;EAC/C;EAEOG,QAAQC,SAAA,EAAuC;IACpD,OAAO,KAAK,CAAAH,QAAA,CAAU,MAAME,OAAA,CAAQC,SAAS,CAAC;EAChD;EAEA,CAAAH,QAAAI,CACEC,OAAA,EACqC;IACrC,OAAOC,MAAA,CAAOC,gBAAA,CAAiBF,OAAA,EAAS;MACtCvB,OAAA,EAAS;QAAE0B,YAAA,EAAc;QAAMpB,KAAA,EAAO,KAAKN;MAAQ;MACnDC,MAAA,EAAQ;QAAEyB,YAAA,EAAc;QAAMpB,KAAA,EAAO,KAAKL;MAAO;IACnD,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}