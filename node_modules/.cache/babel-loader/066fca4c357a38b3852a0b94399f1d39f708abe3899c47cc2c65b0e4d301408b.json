{"ast": null, "code": "import { naturalCompare } from '../jsutils/naturalCompare.mjs';\nimport { Kind } from '../language/kinds.mjs';\n/**\n * Sort ValueNode.\n *\n * This function returns a sorted copy of the given ValueNode.\n *\n * @internal\n */\n\nexport function sortValueNode(valueNode) {\n  switch (valueNode.kind) {\n    case Kind.OBJECT:\n      return {\n        ...valueNode,\n        fields: sortFields(valueNode.fields)\n      };\n    case Kind.LIST:\n      return {\n        ...valueNode,\n        values: valueNode.values.map(sortValueNode)\n      };\n    case Kind.INT:\n    case Kind.FLOAT:\n    case Kind.STRING:\n    case Kind.BOOLEAN:\n    case Kind.NULL:\n    case Kind.ENUM:\n    case Kind.VARIABLE:\n      return valueNode;\n  }\n}\nfunction sortFields(fields) {\n  return fields.map(fieldNode => ({\n    ...fieldNode,\n    value: sortValueNode(fieldNode.value)\n  })).sort((fieldA, fieldB) => naturalCompare(fieldA.name.value, fieldB.name.value));\n}", "map": {"version": 3, "names": ["naturalCompare", "Kind", "sortValueNode", "valueNode", "kind", "OBJECT", "fields", "sortFields", "LIST", "values", "map", "INT", "FLOAT", "STRING", "BOOLEAN", "NULL", "ENUM", "VARIABLE", "fieldNode", "value", "sort", "fieldA", "fieldB", "name"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/utilities/sortValueNode.mjs"], "sourcesContent": ["import { naturalCompare } from '../jsutils/naturalCompare.mjs';\nimport { Kind } from '../language/kinds.mjs';\n/**\n * Sort ValueNode.\n *\n * This function returns a sorted copy of the given ValueNode.\n *\n * @internal\n */\n\nexport function sortValueNode(valueNode) {\n  switch (valueNode.kind) {\n    case Kind.OBJECT:\n      return { ...valueNode, fields: sortFields(valueNode.fields) };\n\n    case Kind.LIST:\n      return { ...valueNode, values: valueNode.values.map(sortValueNode) };\n\n    case Kind.INT:\n    case Kind.FLOAT:\n    case Kind.STRING:\n    case Kind.BOOLEAN:\n    case Kind.NULL:\n    case Kind.ENUM:\n    case Kind.VARIABLE:\n      return valueNode;\n  }\n}\n\nfunction sortFields(fields) {\n  return fields\n    .map((fieldNode) => ({\n      ...fieldNode,\n      value: sortValueNode(fieldNode.value),\n    }))\n    .sort((fieldA, fieldB) =>\n      naturalCompare(fieldA.name.value, fieldB.name.value),\n    );\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,IAAI,QAAQ,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,aAAaA,CAACC,SAAS,EAAE;EACvC,QAAQA,SAAS,CAACC,IAAI;IACpB,KAAKH,IAAI,CAACI,MAAM;MACd,OAAO;QAAE,GAAGF,SAAS;QAAEG,MAAM,EAAEC,UAAU,CAACJ,SAAS,CAACG,MAAM;MAAE,CAAC;IAE/D,KAAKL,IAAI,CAACO,IAAI;MACZ,OAAO;QAAE,GAAGL,SAAS;QAAEM,MAAM,EAAEN,SAAS,CAACM,MAAM,CAACC,GAAG,CAACR,aAAa;MAAE,CAAC;IAEtE,KAAKD,IAAI,CAACU,GAAG;IACb,KAAKV,IAAI,CAACW,KAAK;IACf,KAAKX,IAAI,CAACY,MAAM;IAChB,KAAKZ,IAAI,CAACa,OAAO;IACjB,KAAKb,IAAI,CAACc,IAAI;IACd,KAAKd,IAAI,CAACe,IAAI;IACd,KAAKf,IAAI,CAACgB,QAAQ;MAChB,OAAOd,SAAS;EACpB;AACF;AAEA,SAASI,UAAUA,CAACD,MAAM,EAAE;EAC1B,OAAOA,MAAM,CACVI,GAAG,CAAEQ,SAAS,KAAM;IACnB,GAAGA,SAAS;IACZC,KAAK,EAAEjB,aAAa,CAACgB,SAAS,CAACC,KAAK;EACtC,CAAC,CAAC,CAAC,CACFC,IAAI,CAAC,CAACC,MAAM,EAAEC,MAAM,KACnBtB,cAAc,CAACqB,MAAM,CAACE,IAAI,CAACJ,KAAK,EAAEG,MAAM,CAACC,IAAI,CAACJ,KAAK,CACrD,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}