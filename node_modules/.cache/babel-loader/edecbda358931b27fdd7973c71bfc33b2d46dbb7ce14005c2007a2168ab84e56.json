{"ast": null, "code": "function jsonParse(value) {\n  try {\n    return JSON.parse(value);\n  } catch {\n    return void 0;\n  }\n}\nexport { jsonParse };", "map": {"version": 3, "names": ["jsonParse", "value", "JSON", "parse"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/internal/jsonParse.ts"], "sourcesContent": ["/**\n * Parses a given value into a JSON.\n * Does not throw an exception on an invalid JSON string.\n */\nexport function jsonParse<ValueType extends Record<string, any>>(\n  value: any,\n): ValueType | undefined {\n  try {\n    return JSON.parse(value)\n  } catch {\n    return undefined\n  }\n}\n"], "mappings": "AAIO,SAASA,UACdC,KAAA,EACuB;EACvB,IAAI;IACF,OAAOC,IAAA,CAAKC,KAAA,CAAMF,KAAK;EACzB,QAAQ;IACN,OAAO;EACT;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}