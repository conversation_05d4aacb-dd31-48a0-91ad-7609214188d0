{"ast": null, "code": "import { Kind } from './kinds.mjs';\nexport function isDefinitionNode(node) {\n  return isExecutableDefinitionNode(node) || isTypeSystemDefinitionNode(node) || isTypeSystemExtensionNode(node);\n}\nexport function isExecutableDefinitionNode(node) {\n  return node.kind === Kind.OPERATION_DEFINITION || node.kind === Kind.FRAGMENT_DEFINITION;\n}\nexport function isSelectionNode(node) {\n  return node.kind === Kind.FIELD || node.kind === Kind.FRAGMENT_SPREAD || node.kind === Kind.INLINE_FRAGMENT;\n}\nexport function isValueNode(node) {\n  return node.kind === Kind.VARIABLE || node.kind === Kind.INT || node.kind === Kind.FLOAT || node.kind === Kind.STRING || node.kind === Kind.BOOLEAN || node.kind === Kind.NULL || node.kind === Kind.ENUM || node.kind === Kind.LIST || node.kind === Kind.OBJECT;\n}\nexport function isConstValueNode(node) {\n  return isValueNode(node) && (node.kind === Kind.LIST ? node.values.some(isConstValueNode) : node.kind === Kind.OBJECT ? node.fields.some(field => isConstValueNode(field.value)) : node.kind !== Kind.VARIABLE);\n}\nexport function isTypeNode(node) {\n  return node.kind === Kind.NAMED_TYPE || node.kind === Kind.LIST_TYPE || node.kind === Kind.NON_NULL_TYPE;\n}\nexport function isTypeSystemDefinitionNode(node) {\n  return node.kind === Kind.SCHEMA_DEFINITION || isTypeDefinitionNode(node) || node.kind === Kind.DIRECTIVE_DEFINITION;\n}\nexport function isTypeDefinitionNode(node) {\n  return node.kind === Kind.SCALAR_TYPE_DEFINITION || node.kind === Kind.OBJECT_TYPE_DEFINITION || node.kind === Kind.INTERFACE_TYPE_DEFINITION || node.kind === Kind.UNION_TYPE_DEFINITION || node.kind === Kind.ENUM_TYPE_DEFINITION || node.kind === Kind.INPUT_OBJECT_TYPE_DEFINITION;\n}\nexport function isTypeSystemExtensionNode(node) {\n  return node.kind === Kind.SCHEMA_EXTENSION || isTypeExtensionNode(node);\n}\nexport function isTypeExtensionNode(node) {\n  return node.kind === Kind.SCALAR_TYPE_EXTENSION || node.kind === Kind.OBJECT_TYPE_EXTENSION || node.kind === Kind.INTERFACE_TYPE_EXTENSION || node.kind === Kind.UNION_TYPE_EXTENSION || node.kind === Kind.ENUM_TYPE_EXTENSION || node.kind === Kind.INPUT_OBJECT_TYPE_EXTENSION;\n}", "map": {"version": 3, "names": ["Kind", "isDefinitionNode", "node", "isExecutableDefinitionNode", "isTypeSystemDefinitionNode", "isTypeSystemExtensionNode", "kind", "OPERATION_DEFINITION", "FRAGMENT_DEFINITION", "isSelectionNode", "FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT", "isValueNode", "VARIABLE", "INT", "FLOAT", "STRING", "BOOLEAN", "NULL", "ENUM", "LIST", "OBJECT", "isConstValueNode", "values", "some", "fields", "field", "value", "isTypeNode", "NAMED_TYPE", "LIST_TYPE", "NON_NULL_TYPE", "SCHEMA_DEFINITION", "isTypeDefinitionNode", "DIRECTIVE_DEFINITION", "SCALAR_TYPE_DEFINITION", "OBJECT_TYPE_DEFINITION", "INTERFACE_TYPE_DEFINITION", "UNION_TYPE_DEFINITION", "ENUM_TYPE_DEFINITION", "INPUT_OBJECT_TYPE_DEFINITION", "SCHEMA_EXTENSION", "isTypeExtensionNode", "SCALAR_TYPE_EXTENSION", "OBJECT_TYPE_EXTENSION", "INTERFACE_TYPE_EXTENSION", "UNION_TYPE_EXTENSION", "ENUM_TYPE_EXTENSION", "INPUT_OBJECT_TYPE_EXTENSION"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/language/predicates.mjs"], "sourcesContent": ["import { Kind } from './kinds.mjs';\nexport function isDefinitionNode(node) {\n  return (\n    isExecutableDefinitionNode(node) ||\n    isTypeSystemDefinitionNode(node) ||\n    isTypeSystemExtensionNode(node)\n  );\n}\nexport function isExecutableDefinitionNode(node) {\n  return (\n    node.kind === Kind.OPERATION_DEFINITION ||\n    node.kind === Kind.FRAGMENT_DEFINITION\n  );\n}\nexport function isSelectionNode(node) {\n  return (\n    node.kind === Kind.FIELD ||\n    node.kind === Kind.FRAGMENT_SPREAD ||\n    node.kind === Kind.INLINE_FRAGMENT\n  );\n}\nexport function isValueNode(node) {\n  return (\n    node.kind === Kind.VARIABLE ||\n    node.kind === Kind.INT ||\n    node.kind === Kind.FLOAT ||\n    node.kind === Kind.STRING ||\n    node.kind === Kind.BOOLEAN ||\n    node.kind === Kind.NULL ||\n    node.kind === Kind.ENUM ||\n    node.kind === Kind.LIST ||\n    node.kind === Kind.OBJECT\n  );\n}\nexport function isConstValueNode(node) {\n  return (\n    isValueNode(node) &&\n    (node.kind === Kind.LIST\n      ? node.values.some(isConstValueNode)\n      : node.kind === Kind.OBJECT\n      ? node.fields.some((field) => isConstValueNode(field.value))\n      : node.kind !== Kind.VARIABLE)\n  );\n}\nexport function isTypeNode(node) {\n  return (\n    node.kind === Kind.NAMED_TYPE ||\n    node.kind === Kind.LIST_TYPE ||\n    node.kind === Kind.NON_NULL_TYPE\n  );\n}\nexport function isTypeSystemDefinitionNode(node) {\n  return (\n    node.kind === Kind.SCHEMA_DEFINITION ||\n    isTypeDefinitionNode(node) ||\n    node.kind === Kind.DIRECTIVE_DEFINITION\n  );\n}\nexport function isTypeDefinitionNode(node) {\n  return (\n    node.kind === Kind.SCALAR_TYPE_DEFINITION ||\n    node.kind === Kind.OBJECT_TYPE_DEFINITION ||\n    node.kind === Kind.INTERFACE_TYPE_DEFINITION ||\n    node.kind === Kind.UNION_TYPE_DEFINITION ||\n    node.kind === Kind.ENUM_TYPE_DEFINITION ||\n    node.kind === Kind.INPUT_OBJECT_TYPE_DEFINITION\n  );\n}\nexport function isTypeSystemExtensionNode(node) {\n  return node.kind === Kind.SCHEMA_EXTENSION || isTypeExtensionNode(node);\n}\nexport function isTypeExtensionNode(node) {\n  return (\n    node.kind === Kind.SCALAR_TYPE_EXTENSION ||\n    node.kind === Kind.OBJECT_TYPE_EXTENSION ||\n    node.kind === Kind.INTERFACE_TYPE_EXTENSION ||\n    node.kind === Kind.UNION_TYPE_EXTENSION ||\n    node.kind === Kind.ENUM_TYPE_EXTENSION ||\n    node.kind === Kind.INPUT_OBJECT_TYPE_EXTENSION\n  );\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,aAAa;AAClC,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EACrC,OACEC,0BAA0B,CAACD,IAAI,CAAC,IAChCE,0BAA0B,CAACF,IAAI,CAAC,IAChCG,yBAAyB,CAACH,IAAI,CAAC;AAEnC;AACA,OAAO,SAASC,0BAA0BA,CAACD,IAAI,EAAE;EAC/C,OACEA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACO,oBAAoB,IACvCL,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACQ,mBAAmB;AAE1C;AACA,OAAO,SAASC,eAAeA,CAACP,IAAI,EAAE;EACpC,OACEA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACU,KAAK,IACxBR,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACW,eAAe,IAClCT,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACY,eAAe;AAEtC;AACA,OAAO,SAASC,WAAWA,CAACX,IAAI,EAAE;EAChC,OACEA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACc,QAAQ,IAC3BZ,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACe,GAAG,IACtBb,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACgB,KAAK,IACxBd,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACiB,MAAM,IACzBf,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACkB,OAAO,IAC1BhB,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACmB,IAAI,IACvBjB,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACoB,IAAI,IACvBlB,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACqB,IAAI,IACvBnB,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACsB,MAAM;AAE7B;AACA,OAAO,SAASC,gBAAgBA,CAACrB,IAAI,EAAE;EACrC,OACEW,WAAW,CAACX,IAAI,CAAC,KAChBA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACqB,IAAI,GACpBnB,IAAI,CAACsB,MAAM,CAACC,IAAI,CAACF,gBAAgB,CAAC,GAClCrB,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACsB,MAAM,GACzBpB,IAAI,CAACwB,MAAM,CAACD,IAAI,CAAEE,KAAK,IAAKJ,gBAAgB,CAACI,KAAK,CAACC,KAAK,CAAC,CAAC,GAC1D1B,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACc,QAAQ,CAAC;AAEpC;AACA,OAAO,SAASe,UAAUA,CAAC3B,IAAI,EAAE;EAC/B,OACEA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAAC8B,UAAU,IAC7B5B,IAAI,CAACI,IAAI,KAAKN,IAAI,CAAC+B,SAAS,IAC5B7B,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACgC,aAAa;AAEpC;AACA,OAAO,SAAS5B,0BAA0BA,CAACF,IAAI,EAAE;EAC/C,OACEA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACiC,iBAAiB,IACpCC,oBAAoB,CAAChC,IAAI,CAAC,IAC1BA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACmC,oBAAoB;AAE3C;AACA,OAAO,SAASD,oBAAoBA,CAAChC,IAAI,EAAE;EACzC,OACEA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACoC,sBAAsB,IACzClC,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACqC,sBAAsB,IACzCnC,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACsC,yBAAyB,IAC5CpC,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACuC,qBAAqB,IACxCrC,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACwC,oBAAoB,IACvCtC,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACyC,4BAA4B;AAEnD;AACA,OAAO,SAASpC,yBAAyBA,CAACH,IAAI,EAAE;EAC9C,OAAOA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAAC0C,gBAAgB,IAAIC,mBAAmB,CAACzC,IAAI,CAAC;AACzE;AACA,OAAO,SAASyC,mBAAmBA,CAACzC,IAAI,EAAE;EACxC,OACEA,IAAI,CAACI,IAAI,KAAKN,IAAI,CAAC4C,qBAAqB,IACxC1C,IAAI,CAACI,IAAI,KAAKN,IAAI,CAAC6C,qBAAqB,IACxC3C,IAAI,CAACI,IAAI,KAAKN,IAAI,CAAC8C,wBAAwB,IAC3C5C,IAAI,CAACI,IAAI,KAAKN,IAAI,CAAC+C,oBAAoB,IACvC7C,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACgD,mBAAmB,IACtC9C,IAAI,CAACI,IAAI,KAAKN,IAAI,CAACiD,2BAA2B;AAElD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}