{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/Dependencies.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Box, Typography, Button, Table, TableBody, TableCell, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, RadioGroup, FormControlLabel, Radio, TextField, IconButton, Alert, Chip } from '@mui/material';\nimport { Add as AddIcon, Delete as DeleteIcon, Warning } from '@mui/icons-material';\nimport { updateProcessData } from '../../../store/biaSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dependencies = ({\n  processId\n}) => {\n  _s();\n  var _currentBIA$processDa;\n  const dispatch = useDispatch();\n  const {\n    currentBIA\n  } = useSelector(state => state.bia);\n  const processData = (currentBIA === null || currentBIA === void 0 ? void 0 : (_currentBIA$processDa = currentBIA.processData) === null || _currentBIA$processDa === void 0 ? void 0 : _currentBIA$processDa[processId]) || {};\n  const [dependencies, setDependencies] = useState(processData.dependencies || []);\n  const [modalOpen, setModalOpen] = useState(false);\n  const [newDependency, setNewDependency] = useState({\n    direction: 'upstream',\n    type: 'internal',\n    processId: '',\n    externalName: '',\n    description: ''\n  });\n  const [allProcesses, setAllProcesses] = useState([]);\n  const [dependencyConflicts, setDependencyConflicts] = useState(0);\n\n  // Fetch all processes for internal dependency selection\n  useEffect(() => {\n    const fetchAllProcesses = async () => {\n      try {\n        const functions = await fetch('/api/functions').then(r => r.json());\n        let processes = [];\n        for (let func of functions) {\n          const funcProcesses = await fetch(`/api/processes/${func.id}`).then(r => r.json());\n          processes = [...processes, ...funcProcesses.map(p => ({\n            ...p,\n            functionName: func.name\n          }))];\n        }\n        setAllProcesses(processes);\n      } catch (error) {\n        console.error('Error fetching processes:', error);\n      }\n    };\n    fetchAllProcesses();\n  }, []);\n\n  // Calculate dependency conflicts\n  useEffect(() => {\n    let conflicts = 0;\n    const currentProcessRTO = processData.finalRTO;\n    if (currentProcessRTO) {\n      dependencies.forEach(dep => {\n        if (dep.type === 'internal' && dep.approvedRTO) {\n          const currentRTOHours = parseRTOToHours(currentProcessRTO);\n          const depRTOHours = parseRTOToHours(dep.approvedRTO);\n\n          // Conflict if upstream dependency has longer RTO than current process\n          if (dep.direction === 'upstream' && depRTOHours > currentRTOHours) {\n            conflicts++;\n          }\n        }\n      });\n    }\n    setDependencyConflicts(conflicts);\n\n    // Update process data with conflict count\n    dispatch(updateProcessData({\n      processId,\n      data: {\n        dependencies,\n        dependencyConflicts: conflicts\n      }\n    }));\n  }, [dependencies, processData.finalRTO, dispatch, processId]);\n  const parseRTOToHours = rto => {\n    if (!rto) return 0;\n    const num = parseInt(rto);\n    if (rto.includes('Hour')) return num;\n    if (rto.includes('Week')) return num * 168; // 7 * 24\n    return num * 24; // Assume days\n  };\n  const handleAddDependency = async () => {\n    let dependencyToAdd = {\n      ...newDependency,\n      id: Date.now()\n    };\n    if (newDependency.type === 'internal' && newDependency.processId) {\n      // Fetch the approved RTO for internal dependencies\n      const selectedProcess = allProcesses.find(p => p.id === newDependency.processId);\n      dependencyToAdd.processName = selectedProcess === null || selectedProcess === void 0 ? void 0 : selectedProcess.name;\n      dependencyToAdd.approvedRTO = selectedProcess === null || selectedProcess === void 0 ? void 0 : selectedProcess.approvedRTO;\n      dependencyToAdd.functionName = selectedProcess === null || selectedProcess === void 0 ? void 0 : selectedProcess.functionName;\n    }\n    const updatedDependencies = [...dependencies, dependencyToAdd];\n    setDependencies(updatedDependencies);\n    setModalOpen(false);\n    setNewDependency({\n      direction: 'upstream',\n      type: 'internal',\n      processId: '',\n      externalName: '',\n      description: ''\n    });\n  };\n  const handleDeleteDependency = id => {\n    const updatedDependencies = dependencies.filter(dep => dep.id !== id);\n    setDependencies(updatedDependencies);\n  };\n  const hasConflict = dependency => {\n    if (dependency.type !== 'internal' || !dependency.approvedRTO || !processData.finalRTO) {\n      return false;\n    }\n    const currentRTOHours = parseRTOToHours(processData.finalRTO);\n    const depRTOHours = parseRTOToHours(dependency.approvedRTO);\n    return dependency.direction === 'upstream' && depRTOHours > currentRTOHours;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [\"Dependencies (\", dependencies.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 22\n        }, this),\n        onClick: () => setModalOpen(true),\n        children: \"Add Dependency\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), dependencyConflicts > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"warning\",\n      sx: {\n        mb: 2\n      },\n      icon: /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 56\n      }, this),\n      children: [dependencyConflicts, \" dependency conflict(s) detected. Upstream dependencies have longer RTOs than this process.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      children: [/*#__PURE__*/_jsxDEV(TableHead, {\n        children: /*#__PURE__*/_jsxDEV(TableRow, {\n          children: [/*#__PURE__*/_jsxDEV(TableCell, {\n            children: \"Direction\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: \"Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: \"RTO\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n        children: [dependencies.map(dependency => /*#__PURE__*/_jsxDEV(TableRow, {\n          children: [/*#__PURE__*/_jsxDEV(TableCell, {\n            children: /*#__PURE__*/_jsxDEV(Chip, {\n              label: dependency.direction,\n              color: dependency.direction === 'upstream' ? 'primary' : 'secondary',\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: dependency.type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: [dependency.type === 'internal' ? dependency.processName : dependency.externalName, dependency.functionName && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              display: \"block\",\n              color: \"text.secondary\",\n              children: dependency.functionName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: dependency.approvedRTO || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: hasConflict(dependency) ? /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"Conflict\",\n              color: \"error\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 75\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"OK\",\n              color: \"success\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleDeleteDependency(dependency.id),\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)]\n        }, dependency.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this)), dependencies.length === 0 && /*#__PURE__*/_jsxDEV(TableRow, {\n          children: /*#__PURE__*/_jsxDEV(TableCell, {\n            colSpan: 6,\n            align: \"center\",\n            children: \"No dependencies added yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: modalOpen,\n      onClose: () => setModalOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Add Dependency\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          children: /*#__PURE__*/_jsxDEV(RadioGroup, {\n            value: newDependency.direction,\n            onChange: e => setNewDependency(prev => ({\n              ...prev,\n              direction: e.target.value\n            })),\n            row: true,\n            children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"upstream\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 59\n              }, this),\n              label: \"Upstream (This process depends on)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"downstream\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 61\n              }, this),\n              label: \"Downstream (Depends on this process)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          children: /*#__PURE__*/_jsxDEV(RadioGroup, {\n            value: newDependency.type,\n            onChange: e => setNewDependency(prev => ({\n              ...prev,\n              type: e.target.value\n            })),\n            row: true,\n            children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"internal\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 59\n              }, this),\n              label: \"Internal Process\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"external\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 59\n              }, this),\n              label: \"External System/Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), newDependency.type === 'internal' ? /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Select Process\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: newDependency.processId,\n            onChange: e => setNewDependency(prev => ({\n              ...prev,\n              processId: e.target.value\n            })),\n            children: allProcesses.filter(p => p.id !== processId) // Don't show current process\n            .map(process => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: process.id,\n              children: [process.name, \" (\", process.functionName, \") - RTO: \", process.approvedRTO]\n            }, process.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"External System/Service Name\",\n          value: newDependency.externalName,\n          onChange: e => setNewDependency(prev => ({\n            ...prev,\n            externalName: e.target.value\n          })),\n          margin: \"normal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          multiline: true,\n          rows: 3,\n          label: \"Description\",\n          value: newDependency.description,\n          onChange: e => setNewDependency(prev => ({\n            ...prev,\n            description: e.target.value\n          })),\n          margin: \"normal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setModalOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleAddDependency,\n          variant: \"contained\",\n          disabled: newDependency.type === 'internal' && !newDependency.processId || newDependency.type === 'external' && !newDependency.externalName,\n          children: \"Add\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s(Dependencies, \"YU2z8w/AZkVfEXWW4G9nHzgSyec=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = Dependencies;\nexport default Dependencies;\nvar _c;\n$RefreshReg$(_c, \"Dependencies\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "Box", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableHead", "TableRow", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "RadioGroup", "FormControlLabel", "Radio", "TextField", "IconButton", "<PERSON><PERSON>", "Chip", "Add", "AddIcon", "Delete", "DeleteIcon", "Warning", "updateProcessData", "jsxDEV", "_jsxDEV", "Dependencies", "processId", "_s", "_currentBIA$processDa", "dispatch", "currentBIA", "state", "bia", "processData", "dependencies", "setDependencies", "modalOpen", "setModalOpen", "newDependency", "setNewDependency", "direction", "type", "externalName", "description", "allProcesses", "setAllProcesses", "dependencyConflicts", "setDependencyConflicts", "fetchAllProcesses", "functions", "fetch", "then", "r", "json", "processes", "func", "funcProcesses", "id", "map", "p", "functionName", "name", "error", "console", "conflicts", "currentProcessRTO", "finalRTO", "for<PERSON>ach", "dep", "approvedRTO", "currentRTOHours", "parseRTOToHours", "depRTOHours", "data", "rto", "num", "parseInt", "includes", "handleAddDependency", "dependencyToAdd", "Date", "now", "selectedProcess", "find", "processName", "updatedDependencies", "handleDeleteDependency", "filter", "hasConflict", "dependency", "children", "display", "justifyContent", "alignItems", "mb", "variant", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "severity", "sx", "icon", "label", "color", "size", "colSpan", "align", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "margin", "value", "onChange", "e", "prev", "target", "row", "control", "process", "multiline", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/Dependencies.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport {\n  Box, Typography, Button, Table, TableBody, TableCell, TableHead, TableRow,\n  Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel,\n  Select, MenuItem, RadioGroup, FormControlLabel, Radio, TextField,\n  IconButton, Alert, Chip\n} from '@mui/material';\nimport { Add as AddIcon, Delete as DeleteIcon, Warning } from '@mui/icons-material';\nimport { updateProcessData } from '../../../store/biaSlice';\n\nconst Dependencies = ({ processId }) => {\n  const dispatch = useDispatch();\n  const { currentBIA } = useSelector(state => state.bia);\n  const processData = currentBIA?.processData?.[processId] || {};\n  \n  const [dependencies, setDependencies] = useState(processData.dependencies || []);\n  const [modalOpen, setModalOpen] = useState(false);\n  const [newDependency, setNewDependency] = useState({\n    direction: 'upstream',\n    type: 'internal',\n    processId: '',\n    externalName: '',\n    description: ''\n  });\n  const [allProcesses, setAllProcesses] = useState([]);\n  const [dependencyConflicts, setDependencyConflicts] = useState(0);\n\n  // Fetch all processes for internal dependency selection\n  useEffect(() => {\n    const fetchAllProcesses = async () => {\n      try {\n        const functions = await fetch('/api/functions').then(r => r.json());\n        let processes = [];\n        \n        for (let func of functions) {\n          const funcProcesses = await fetch(`/api/processes/${func.id}`).then(r => r.json());\n          processes = [...processes, ...funcProcesses.map(p => ({ ...p, functionName: func.name }))];\n        }\n        \n        setAllProcesses(processes);\n      } catch (error) {\n        console.error('Error fetching processes:', error);\n      }\n    };\n\n    fetchAllProcesses();\n  }, []);\n\n  // Calculate dependency conflicts\n  useEffect(() => {\n    let conflicts = 0;\n    const currentProcessRTO = processData.finalRTO;\n    \n    if (currentProcessRTO) {\n      dependencies.forEach(dep => {\n        if (dep.type === 'internal' && dep.approvedRTO) {\n          const currentRTOHours = parseRTOToHours(currentProcessRTO);\n          const depRTOHours = parseRTOToHours(dep.approvedRTO);\n          \n          // Conflict if upstream dependency has longer RTO than current process\n          if (dep.direction === 'upstream' && depRTOHours > currentRTOHours) {\n            conflicts++;\n          }\n        }\n      });\n    }\n    \n    setDependencyConflicts(conflicts);\n    \n    // Update process data with conflict count\n    dispatch(updateProcessData({ \n      processId, \n      data: { \n        dependencies, \n        dependencyConflicts: conflicts \n      } \n    }));\n  }, [dependencies, processData.finalRTO, dispatch, processId]);\n\n  const parseRTOToHours = (rto) => {\n    if (!rto) return 0;\n    const num = parseInt(rto);\n    if (rto.includes('Hour')) return num;\n    if (rto.includes('Week')) return num * 168; // 7 * 24\n    return num * 24; // Assume days\n  };\n\n  const handleAddDependency = async () => {\n    let dependencyToAdd = { ...newDependency, id: Date.now() };\n    \n    if (newDependency.type === 'internal' && newDependency.processId) {\n      // Fetch the approved RTO for internal dependencies\n      const selectedProcess = allProcesses.find(p => p.id === newDependency.processId);\n      dependencyToAdd.processName = selectedProcess?.name;\n      dependencyToAdd.approvedRTO = selectedProcess?.approvedRTO;\n      dependencyToAdd.functionName = selectedProcess?.functionName;\n    }\n    \n    const updatedDependencies = [...dependencies, dependencyToAdd];\n    setDependencies(updatedDependencies);\n    \n    setModalOpen(false);\n    setNewDependency({\n      direction: 'upstream',\n      type: 'internal', \n      processId: '',\n      externalName: '',\n      description: ''\n    });\n  };\n\n  const handleDeleteDependency = (id) => {\n    const updatedDependencies = dependencies.filter(dep => dep.id !== id);\n    setDependencies(updatedDependencies);\n  };\n\n  const hasConflict = (dependency) => {\n    if (dependency.type !== 'internal' || !dependency.approvedRTO || !processData.finalRTO) {\n      return false;\n    }\n    \n    const currentRTOHours = parseRTOToHours(processData.finalRTO);\n    const depRTOHours = parseRTOToHours(dependency.approvedRTO);\n    \n    return dependency.direction === 'upstream' && depRTOHours > currentRTOHours;\n  };\n\n  return (\n    <Box>\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n        <Typography variant=\"h6\">\n          Dependencies ({dependencies.length})\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={() => setModalOpen(true)}\n        >\n          Add Dependency\n        </Button>\n      </Box>\n\n      {dependencyConflicts > 0 && (\n        <Alert severity=\"warning\" sx={{ mb: 2 }} icon={<Warning />}>\n          {dependencyConflicts} dependency conflict(s) detected. Upstream dependencies have longer RTOs than this process.\n        </Alert>\n      )}\n\n      <Table>\n        <TableHead>\n          <TableRow>\n            <TableCell>Direction</TableCell>\n            <TableCell>Type</TableCell>\n            <TableCell>Name</TableCell>\n            <TableCell>RTO</TableCell>\n            <TableCell>Status</TableCell>\n            <TableCell>Actions</TableCell>\n          </TableRow>\n        </TableHead>\n        <TableBody>\n          {dependencies.map((dependency) => (\n            <TableRow key={dependency.id}>\n              <TableCell>\n                <Chip \n                  label={dependency.direction} \n                  color={dependency.direction === 'upstream' ? 'primary' : 'secondary'}\n                  size=\"small\"\n                />\n              </TableCell>\n              <TableCell>{dependency.type}</TableCell>\n              <TableCell>\n                {dependency.type === 'internal' ? dependency.processName : dependency.externalName}\n                {dependency.functionName && (\n                  <Typography variant=\"caption\" display=\"block\" color=\"text.secondary\">\n                    {dependency.functionName}\n                  </Typography>\n                )}\n              </TableCell>\n              <TableCell>{dependency.approvedRTO || 'N/A'}</TableCell>\n              <TableCell>\n                {hasConflict(dependency) ? (\n                  <Chip label=\"Conflict\" color=\"error\" size=\"small\" icon={<Warning />} />\n                ) : (\n                  <Chip label=\"OK\" color=\"success\" size=\"small\" />\n                )}\n              </TableCell>\n              <TableCell>\n                <IconButton \n                  size=\"small\" \n                  onClick={() => handleDeleteDependency(dependency.id)}\n                >\n                  <DeleteIcon />\n                </IconButton>\n              </TableCell>\n            </TableRow>\n          ))}\n          {dependencies.length === 0 && (\n            <TableRow>\n              <TableCell colSpan={6} align=\"center\">\n                No dependencies added yet\n              </TableCell>\n            </TableRow>\n          )}\n        </TableBody>\n      </Table>\n\n      {/* Add Dependency Modal */}\n      <Dialog open={modalOpen} onClose={() => setModalOpen(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>Add Dependency</DialogTitle>\n        <DialogContent>\n          <FormControl fullWidth margin=\"normal\">\n            <RadioGroup\n              value={newDependency.direction}\n              onChange={(e) => setNewDependency(prev => ({ ...prev, direction: e.target.value }))}\n              row\n            >\n              <FormControlLabel value=\"upstream\" control={<Radio />} label=\"Upstream (This process depends on)\" />\n              <FormControlLabel value=\"downstream\" control={<Radio />} label=\"Downstream (Depends on this process)\" />\n            </RadioGroup>\n          </FormControl>\n\n          <FormControl fullWidth margin=\"normal\">\n            <RadioGroup\n              value={newDependency.type}\n              onChange={(e) => setNewDependency(prev => ({ ...prev, type: e.target.value }))}\n              row\n            >\n              <FormControlLabel value=\"internal\" control={<Radio />} label=\"Internal Process\" />\n              <FormControlLabel value=\"external\" control={<Radio />} label=\"External System/Service\" />\n            </RadioGroup>\n          </FormControl>\n\n          {newDependency.type === 'internal' ? (\n            <FormControl fullWidth margin=\"normal\">\n              <InputLabel>Select Process</InputLabel>\n              <Select\n                value={newDependency.processId}\n                onChange={(e) => setNewDependency(prev => ({ ...prev, processId: e.target.value }))}\n              >\n                {allProcesses\n                  .filter(p => p.id !== processId) // Don't show current process\n                  .map(process => (\n                  <MenuItem key={process.id} value={process.id}>\n                    {process.name} ({process.functionName}) - RTO: {process.approvedRTO}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          ) : (\n            <TextField\n              fullWidth\n              label=\"External System/Service Name\"\n              value={newDependency.externalName}\n              onChange={(e) => setNewDependency(prev => ({ ...prev, externalName: e.target.value }))}\n              margin=\"normal\"\n            />\n          )}\n\n          <TextField\n            fullWidth\n            multiline\n            rows={3}\n            label=\"Description\"\n            value={newDependency.description}\n            onChange={(e) => setNewDependency(prev => ({ ...prev, description: e.target.value }))}\n            margin=\"normal\"\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setModalOpen(false)}>Cancel</Button>\n          <Button \n            onClick={handleAddDependency}\n            variant=\"contained\"\n            disabled={\n              (newDependency.type === 'internal' && !newDependency.processId) ||\n              (newDependency.type === 'external' && !newDependency.externalName)\n            }\n          >\n            Add\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Dependencies;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EACzEC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAC1EC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,SAAS,EAChEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,QAClB,eAAe;AACtB,SAASC,GAAG,IAAIC,OAAO,EAAEC,MAAM,IAAIC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AACnF,SAASC,iBAAiB,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACtC,MAAMC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsC;EAAW,CAAC,GAAGrC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EACtD,MAAMC,WAAW,GAAG,CAAAH,UAAU,aAAVA,UAAU,wBAAAF,qBAAA,GAAVE,UAAU,CAAEG,WAAW,cAAAL,qBAAA,uBAAvBA,qBAAA,CAA0BF,SAAS,CAAC,KAAI,CAAC,CAAC;EAE9D,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC2C,WAAW,CAACC,YAAY,IAAI,EAAE,CAAC;EAChF,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC;IACjDkD,SAAS,EAAE,UAAU;IACrBC,IAAI,EAAE,UAAU;IAChBf,SAAS,EAAE,EAAE;IACbgB,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC;;EAEjE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMyD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF,MAAMC,SAAS,GAAG,MAAMC,KAAK,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;QACnE,IAAIC,SAAS,GAAG,EAAE;QAElB,KAAK,IAAIC,IAAI,IAAIN,SAAS,EAAE;UAC1B,MAAMO,aAAa,GAAG,MAAMN,KAAK,CAAC,kBAAkBK,IAAI,CAACE,EAAE,EAAE,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;UAClFC,SAAS,GAAG,CAAC,GAAGA,SAAS,EAAE,GAAGE,aAAa,CAACE,GAAG,CAACC,CAAC,KAAK;YAAE,GAAGA,CAAC;YAAEC,YAAY,EAAEL,IAAI,CAACM;UAAK,CAAC,CAAC,CAAC,CAAC;QAC5F;QAEAhB,eAAe,CAACS,SAAS,CAAC;MAC5B,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAEDd,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzD,SAAS,CAAC,MAAM;IACd,IAAIyE,SAAS,GAAG,CAAC;IACjB,MAAMC,iBAAiB,GAAGhC,WAAW,CAACiC,QAAQ;IAE9C,IAAID,iBAAiB,EAAE;MACrB/B,YAAY,CAACiC,OAAO,CAACC,GAAG,IAAI;QAC1B,IAAIA,GAAG,CAAC3B,IAAI,KAAK,UAAU,IAAI2B,GAAG,CAACC,WAAW,EAAE;UAC9C,MAAMC,eAAe,GAAGC,eAAe,CAACN,iBAAiB,CAAC;UAC1D,MAAMO,WAAW,GAAGD,eAAe,CAACH,GAAG,CAACC,WAAW,CAAC;;UAEpD;UACA,IAAID,GAAG,CAAC5B,SAAS,KAAK,UAAU,IAAIgC,WAAW,GAAGF,eAAe,EAAE;YACjEN,SAAS,EAAE;UACb;QACF;MACF,CAAC,CAAC;IACJ;IAEAjB,sBAAsB,CAACiB,SAAS,CAAC;;IAEjC;IACAnC,QAAQ,CAACP,iBAAiB,CAAC;MACzBI,SAAS;MACT+C,IAAI,EAAE;QACJvC,YAAY;QACZY,mBAAmB,EAAEkB;MACvB;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAC9B,YAAY,EAAED,WAAW,CAACiC,QAAQ,EAAErC,QAAQ,EAAEH,SAAS,CAAC,CAAC;EAE7D,MAAM6C,eAAe,GAAIG,GAAG,IAAK;IAC/B,IAAI,CAACA,GAAG,EAAE,OAAO,CAAC;IAClB,MAAMC,GAAG,GAAGC,QAAQ,CAACF,GAAG,CAAC;IACzB,IAAIA,GAAG,CAACG,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAOF,GAAG;IACpC,IAAID,GAAG,CAACG,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAOF,GAAG,GAAG,GAAG,CAAC,CAAC;IAC5C,OAAOA,GAAG,GAAG,EAAE,CAAC,CAAC;EACnB,CAAC;EAED,MAAMG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIC,eAAe,GAAG;MAAE,GAAGzC,aAAa;MAAEmB,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC;IAAE,CAAC;IAE1D,IAAI3C,aAAa,CAACG,IAAI,KAAK,UAAU,IAAIH,aAAa,CAACZ,SAAS,EAAE;MAChE;MACA,MAAMwD,eAAe,GAAGtC,YAAY,CAACuC,IAAI,CAACxB,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKnB,aAAa,CAACZ,SAAS,CAAC;MAChFqD,eAAe,CAACK,WAAW,GAAGF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAErB,IAAI;MACnDkB,eAAe,CAACV,WAAW,GAAGa,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEb,WAAW;MAC1DU,eAAe,CAACnB,YAAY,GAAGsB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEtB,YAAY;IAC9D;IAEA,MAAMyB,mBAAmB,GAAG,CAAC,GAAGnD,YAAY,EAAE6C,eAAe,CAAC;IAC9D5C,eAAe,CAACkD,mBAAmB,CAAC;IAEpChD,YAAY,CAAC,KAAK,CAAC;IACnBE,gBAAgB,CAAC;MACfC,SAAS,EAAE,UAAU;MACrBC,IAAI,EAAE,UAAU;MAChBf,SAAS,EAAE,EAAE;MACbgB,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAM2C,sBAAsB,GAAI7B,EAAE,IAAK;IACrC,MAAM4B,mBAAmB,GAAGnD,YAAY,CAACqD,MAAM,CAACnB,GAAG,IAAIA,GAAG,CAACX,EAAE,KAAKA,EAAE,CAAC;IACrEtB,eAAe,CAACkD,mBAAmB,CAAC;EACtC,CAAC;EAED,MAAMG,WAAW,GAAIC,UAAU,IAAK;IAClC,IAAIA,UAAU,CAAChD,IAAI,KAAK,UAAU,IAAI,CAACgD,UAAU,CAACpB,WAAW,IAAI,CAACpC,WAAW,CAACiC,QAAQ,EAAE;MACtF,OAAO,KAAK;IACd;IAEA,MAAMI,eAAe,GAAGC,eAAe,CAACtC,WAAW,CAACiC,QAAQ,CAAC;IAC7D,MAAMM,WAAW,GAAGD,eAAe,CAACkB,UAAU,CAACpB,WAAW,CAAC;IAE3D,OAAOoB,UAAU,CAACjD,SAAS,KAAK,UAAU,IAAIgC,WAAW,GAAGF,eAAe;EAC7E,CAAC;EAED,oBACE9C,OAAA,CAAC9B,GAAG;IAAAgG,QAAA,gBACFlE,OAAA,CAAC9B,GAAG;MAACiG,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAJ,QAAA,gBAC3ElE,OAAA,CAAC7B,UAAU;QAACoG,OAAO,EAAC,IAAI;QAAAL,QAAA,GAAC,gBACT,EAACxD,YAAY,CAAC8D,MAAM,EAAC,GACrC;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5E,OAAA,CAAC5B,MAAM;QACLmG,OAAO,EAAC,WAAW;QACnBM,SAAS,eAAE7E,OAAA,CAACN,OAAO;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBE,OAAO,EAAEA,CAAA,KAAMjE,YAAY,CAAC,IAAI,CAAE;QAAAqD,QAAA,EACnC;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELtD,mBAAmB,GAAG,CAAC,iBACtBtB,OAAA,CAACT,KAAK;MAACwF,QAAQ,EAAC,SAAS;MAACC,EAAE,EAAE;QAAEV,EAAE,EAAE;MAAE,CAAE;MAACW,IAAI,eAAEjF,OAAA,CAACH,OAAO;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAV,QAAA,GACxD5C,mBAAmB,EAAC,6FACvB;IAAA;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,eAED5E,OAAA,CAAC3B,KAAK;MAAA6F,QAAA,gBACJlE,OAAA,CAACxB,SAAS;QAAA0F,QAAA,eACRlE,OAAA,CAACvB,QAAQ;UAAAyF,QAAA,gBACPlE,OAAA,CAACzB,SAAS;YAAA2F,QAAA,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAChC5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC3B5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC3B5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,EAAC;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC1B5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,EAAC;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC7B5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,EAAC;UAAO;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACZ5E,OAAA,CAAC1B,SAAS;QAAA4F,QAAA,GACPxD,YAAY,CAACwB,GAAG,CAAE+B,UAAU,iBAC3BjE,OAAA,CAACvB,QAAQ;UAAAyF,QAAA,gBACPlE,OAAA,CAACzB,SAAS;YAAA2F,QAAA,eACRlE,OAAA,CAACR,IAAI;cACH0F,KAAK,EAAEjB,UAAU,CAACjD,SAAU;cAC5BmE,KAAK,EAAElB,UAAU,CAACjD,SAAS,KAAK,UAAU,GAAG,SAAS,GAAG,WAAY;cACrEoE,IAAI,EAAC;YAAO;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZ5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,EAAED,UAAU,CAAChD;UAAI;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxC5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,GACPD,UAAU,CAAChD,IAAI,KAAK,UAAU,GAAGgD,UAAU,CAACL,WAAW,GAAGK,UAAU,CAAC/C,YAAY,EACjF+C,UAAU,CAAC7B,YAAY,iBACtBpC,OAAA,CAAC7B,UAAU;cAACoG,OAAO,EAAC,SAAS;cAACJ,OAAO,EAAC,OAAO;cAACgB,KAAK,EAAC,gBAAgB;cAAAjB,QAAA,EACjED,UAAU,CAAC7B;YAAY;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACZ5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,EAAED,UAAU,CAACpB,WAAW,IAAI;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxD5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,EACPF,WAAW,CAACC,UAAU,CAAC,gBACtBjE,OAAA,CAACR,IAAI;cAAC0F,KAAK,EAAC,UAAU;cAACC,KAAK,EAAC,OAAO;cAACC,IAAI,EAAC,OAAO;cAACH,IAAI,eAAEjF,OAAA,CAACH,OAAO;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEvE5E,OAAA,CAACR,IAAI;cAAC0F,KAAK,EAAC,IAAI;cAACC,KAAK,EAAC,SAAS;cAACC,IAAI,EAAC;YAAO;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAChD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACZ5E,OAAA,CAACzB,SAAS;YAAA2F,QAAA,eACRlE,OAAA,CAACV,UAAU;cACT8F,IAAI,EAAC,OAAO;cACZN,OAAO,EAAEA,CAAA,KAAMhB,sBAAsB,CAACG,UAAU,CAAChC,EAAE,CAAE;cAAAiC,QAAA,eAErDlE,OAAA,CAACJ,UAAU;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAhCCX,UAAU,CAAChC,EAAE;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiClB,CACX,CAAC,EACDlE,YAAY,CAAC8D,MAAM,KAAK,CAAC,iBACxBxE,OAAA,CAACvB,QAAQ;UAAAyF,QAAA,eACPlE,OAAA,CAACzB,SAAS;YAAC8G,OAAO,EAAE,CAAE;YAACC,KAAK,EAAC,QAAQ;YAAApB,QAAA,EAAC;UAEtC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGR5E,OAAA,CAACtB,MAAM;MAAC6G,IAAI,EAAE3E,SAAU;MAAC4E,OAAO,EAAEA,CAAA,KAAM3E,YAAY,CAAC,KAAK,CAAE;MAAC4E,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAxB,QAAA,gBAClFlE,OAAA,CAACrB,WAAW;QAAAuF,QAAA,EAAC;MAAc;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzC5E,OAAA,CAACpB,aAAa;QAAAsF,QAAA,gBACZlE,OAAA,CAAClB,WAAW;UAAC4G,SAAS;UAACC,MAAM,EAAC,QAAQ;UAAAzB,QAAA,eACpClE,OAAA,CAACd,UAAU;YACT0G,KAAK,EAAE9E,aAAa,CAACE,SAAU;YAC/B6E,QAAQ,EAAGC,CAAC,IAAK/E,gBAAgB,CAACgF,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE/E,SAAS,EAAE8E,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YACpFK,GAAG;YAAA/B,QAAA,gBAEHlE,OAAA,CAACb,gBAAgB;cAACyG,KAAK,EAAC,UAAU;cAACM,OAAO,eAAElG,OAAA,CAACZ,KAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACM,KAAK,EAAC;YAAoC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpG5E,OAAA,CAACb,gBAAgB;cAACyG,KAAK,EAAC,YAAY;cAACM,OAAO,eAAElG,OAAA,CAACZ,KAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACM,KAAK,EAAC;YAAsC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEd5E,OAAA,CAAClB,WAAW;UAAC4G,SAAS;UAACC,MAAM,EAAC,QAAQ;UAAAzB,QAAA,eACpClE,OAAA,CAACd,UAAU;YACT0G,KAAK,EAAE9E,aAAa,CAACG,IAAK;YAC1B4E,QAAQ,EAAGC,CAAC,IAAK/E,gBAAgB,CAACgF,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE9E,IAAI,EAAE6E,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAC/EK,GAAG;YAAA/B,QAAA,gBAEHlE,OAAA,CAACb,gBAAgB;cAACyG,KAAK,EAAC,UAAU;cAACM,OAAO,eAAElG,OAAA,CAACZ,KAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACM,KAAK,EAAC;YAAkB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClF5E,OAAA,CAACb,gBAAgB;cAACyG,KAAK,EAAC,UAAU;cAACM,OAAO,eAAElG,OAAA,CAACZ,KAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACM,KAAK,EAAC;YAAyB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAEb9D,aAAa,CAACG,IAAI,KAAK,UAAU,gBAChCjB,OAAA,CAAClB,WAAW;UAAC4G,SAAS;UAACC,MAAM,EAAC,QAAQ;UAAAzB,QAAA,gBACpClE,OAAA,CAACjB,UAAU;YAAAmF,QAAA,EAAC;UAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvC5E,OAAA,CAAChB,MAAM;YACL4G,KAAK,EAAE9E,aAAa,CAACZ,SAAU;YAC/B2F,QAAQ,EAAGC,CAAC,IAAK/E,gBAAgB,CAACgF,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE7F,SAAS,EAAE4F,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAAA1B,QAAA,EAEnF9C,YAAY,CACV2C,MAAM,CAAC5B,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAK/B,SAAS,CAAC,CAAC;YAAA,CAChCgC,GAAG,CAACiE,OAAO,iBACZnG,OAAA,CAACf,QAAQ;cAAkB2G,KAAK,EAAEO,OAAO,CAAClE,EAAG;cAAAiC,QAAA,GAC1CiC,OAAO,CAAC9D,IAAI,EAAC,IAAE,EAAC8D,OAAO,CAAC/D,YAAY,EAAC,WAAS,EAAC+D,OAAO,CAACtD,WAAW;YAAA,GADtDsD,OAAO,CAAClE,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEd5E,OAAA,CAACX,SAAS;UACRqG,SAAS;UACTR,KAAK,EAAC,8BAA8B;UACpCU,KAAK,EAAE9E,aAAa,CAACI,YAAa;UAClC2E,QAAQ,EAAGC,CAAC,IAAK/E,gBAAgB,CAACgF,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE7E,YAAY,EAAE4E,CAAC,CAACE,MAAM,CAACJ;UAAM,CAAC,CAAC,CAAE;UACvFD,MAAM,EAAC;QAAQ;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF,eAED5E,OAAA,CAACX,SAAS;UACRqG,SAAS;UACTU,SAAS;UACTC,IAAI,EAAE,CAAE;UACRnB,KAAK,EAAC,aAAa;UACnBU,KAAK,EAAE9E,aAAa,CAACK,WAAY;UACjC0E,QAAQ,EAAGC,CAAC,IAAK/E,gBAAgB,CAACgF,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE5E,WAAW,EAAE2E,CAAC,CAACE,MAAM,CAACJ;UAAM,CAAC,CAAC,CAAE;UACtFD,MAAM,EAAC;QAAQ;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB5E,OAAA,CAACnB,aAAa;QAAAqF,QAAA,gBACZlE,OAAA,CAAC5B,MAAM;UAAC0G,OAAO,EAAEA,CAAA,KAAMjE,YAAY,CAAC,KAAK,CAAE;UAAAqD,QAAA,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC3D5E,OAAA,CAAC5B,MAAM;UACL0G,OAAO,EAAExB,mBAAoB;UAC7BiB,OAAO,EAAC,WAAW;UACnB+B,QAAQ,EACLxF,aAAa,CAACG,IAAI,KAAK,UAAU,IAAI,CAACH,aAAa,CAACZ,SAAS,IAC7DY,aAAa,CAACG,IAAI,KAAK,UAAU,IAAI,CAACH,aAAa,CAACI,YACtD;UAAAgD,QAAA,EACF;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACzE,EAAA,CAlRIF,YAAY;EAAA,QACCjC,WAAW,EACLC,WAAW;AAAA;AAAAsI,EAAA,GAF9BtG,YAAY;AAoRlB,eAAeA,YAAY;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}