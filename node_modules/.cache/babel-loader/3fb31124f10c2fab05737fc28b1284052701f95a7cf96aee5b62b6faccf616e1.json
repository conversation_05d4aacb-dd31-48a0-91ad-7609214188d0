{"ast": null, "code": "// src/utils/bufferUtils.ts\nvar encoder = new TextEncoder();\nfunction encodeBuffer(text) {\n  return encoder.encode(text);\n}\nfunction decodeBuffer(buffer, encoding) {\n  const decoder = new TextDecoder(encoding);\n  return decoder.decode(buffer);\n}\nfunction toArrayBuffer(array) {\n  return array.buffer.slice(array.byteOffset, array.byteOffset + array.byteLength);\n}\nexport { encodeBuffer, decodeBuffer, toArrayBuffer };", "map": {"version": 3, "names": ["encoder", "TextEncoder", "encodeBuffer", "text", "encode", "decodeBuffer", "buffer", "encoding", "decoder", "TextDecoder", "decode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "slice", "byteOffset", "byteLength"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/utils/bufferUtils.ts"], "sourcesContent": ["const encoder = new TextEncoder()\n\nexport function encodeBuffer(text: string): Uint8Array {\n  return encoder.encode(text)\n}\n\nexport function decodeBuffer(buffer: <PERSON>rrayBuffer, encoding?: string): string {\n  const decoder = new TextDecoder(encoding)\n  return decoder.decode(buffer)\n}\n\n/**\n * Create an `ArrayBuffer` from the given `Uint8Array`.\n * Takes the byte offset into account to produce the right buffer\n * in the case when the buffer is bigger than the data view.\n */\nexport function toArrayBuffer(array: Uint8Array): ArrayBuffer {\n  return array.buffer.slice(\n    array.byteOffset,\n    array.byteOffset + array.byteLength\n  )\n}\n"], "mappings": ";AAAA,IAAMA,OAAA,GAAU,IAAIC,WAAA,CAAY;AAEzB,SAASC,aAAaC,IAAA,EAA0B;EACrD,OAAOH,OAAA,CAAQI,MAAA,CAAOD,IAAI;AAC5B;AAEO,SAASE,aAAaC,MAAA,EAAqBC,QAAA,EAA2B;EAC3E,MAAMC,OAAA,GAAU,IAAIC,WAAA,CAAYF,QAAQ;EACxC,OAAOC,OAAA,CAAQE,MAAA,CAAOJ,MAAM;AAC9B;AAOO,SAASK,cAAcC,KAAA,EAAgC;EAC5D,OAAOA,KAAA,CAAMN,MAAA,CAAOO,KAAA,CAClBD,KAAA,CAAME,UAAA,EACNF,KAAA,CAAME,UAAA,GAAaF,KAAA,CAAMG,UAC3B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}