{"ast": null, "code": "class WebSocketMemoryClientStore {\n  store;\n  constructor() {\n    this.store = /* @__PURE__ */new Map();\n  }\n  async add(client) {\n    this.store.set(client.id, {\n      id: client.id,\n      url: client.url.href\n    });\n  }\n  getAll() {\n    return Promise.resolve(Array.from(this.store.values()));\n  }\n  async deleteMany(clientIds) {\n    for (const clientId of clientIds) {\n      this.store.delete(clientId);\n    }\n  }\n}\nexport { WebSocketMemoryClientStore };", "map": {"version": 3, "names": ["WebSocketMemoryClientStore", "store", "constructor", "Map", "add", "client", "set", "id", "url", "href", "getAll", "Promise", "resolve", "Array", "from", "values", "deleteMany", "clientIds", "clientId", "delete"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/ws/WebSocketMemoryClientStore.ts"], "sourcesContent": ["import { WebSocketClientConnectionProtocol } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\nimport {\n  SerializedWebSocketClient,\n  WebSocketClientStore,\n} from './WebSocketClientStore'\n\nexport class WebSocketMemoryClientStore implements WebSocketClientStore {\n  private store: Map<string, SerializedWebSocketClient>\n\n  constructor() {\n    this.store = new Map()\n  }\n\n  public async add(client: WebSocketClientConnectionProtocol): Promise<void> {\n    this.store.set(client.id, { id: client.id, url: client.url.href })\n  }\n\n  public getAll(): Promise<Array<SerializedWebSocketClient>> {\n    return Promise.resolve(Array.from(this.store.values()))\n  }\n\n  public async deleteMany(clientIds: Array<string>): Promise<void> {\n    for (const clientId of clientIds) {\n      this.store.delete(clientId)\n    }\n  }\n}\n"], "mappings": "AAMO,MAAMA,0BAAA,CAA2D;EAC9DC,KAAA;EAERC,YAAA,EAAc;IACZ,KAAKD,KAAA,GAAQ,mBAAIE,GAAA,CAAI;EACvB;EAEA,MAAaC,IAAIC,MAAA,EAA0D;IACzE,KAAKJ,KAAA,CAAMK,GAAA,CAAID,MAAA,CAAOE,EAAA,EAAI;MAAEA,EAAA,EAAIF,MAAA,CAAOE,EAAA;MAAIC,GAAA,EAAKH,MAAA,CAAOG,GAAA,CAAIC;IAAK,CAAC;EACnE;EAEOC,OAAA,EAAoD;IACzD,OAAOC,OAAA,CAAQC,OAAA,CAAQC,KAAA,CAAMC,IAAA,CAAK,KAAKb,KAAA,CAAMc,MAAA,CAAO,CAAC,CAAC;EACxD;EAEA,MAAaC,WAAWC,SAAA,EAAyC;IAC/D,WAAWC,QAAA,IAAYD,SAAA,EAAW;MAChC,KAAKhB,KAAA,CAAMkB,MAAA,CAAOD,QAAQ;IAC5B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}