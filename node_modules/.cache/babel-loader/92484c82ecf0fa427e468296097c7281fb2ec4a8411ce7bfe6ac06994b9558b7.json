{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Lone Schema definition\n *\n * A GraphQL document is only valid if it contains only one schema definition.\n */\nexport function LoneSchemaDefinitionRule(context) {\n  var _ref, _ref2, _oldSchema$astNode;\n  const oldSchema = context.getSchema();\n  const alreadyDefined = (_ref = (_ref2 = (_oldSchema$astNode = oldSchema === null || oldSchema === void 0 ? void 0 : oldSchema.astNode) !== null && _oldSchema$astNode !== void 0 ? _oldSchema$astNode : oldSchema === null || oldSchema === void 0 ? void 0 : oldSchema.getQueryType()) !== null && _ref2 !== void 0 ? _ref2 : oldSchema === null || oldSchema === void 0 ? void 0 : oldSchema.getMutationType()) !== null && _ref !== void 0 ? _ref : oldSchema === null || oldSchema === void 0 ? void 0 : oldSchema.getSubscriptionType();\n  let schemaDefinitionsCount = 0;\n  return {\n    SchemaDefinition(node) {\n      if (alreadyDefined) {\n        context.reportError(new GraphQLError('Cannot define a new schema within a schema extension.', {\n          nodes: node\n        }));\n        return;\n      }\n      if (schemaDefinitionsCount > 0) {\n        context.reportError(new GraphQLError('Must provide only one schema definition.', {\n          nodes: node\n        }));\n      }\n      ++schemaDefinitionsCount;\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "LoneSchemaDefinitionRule", "context", "_ref", "_ref2", "_oldSchema$astNode", "oldSchema", "getSchema", "alreadyDefined", "astNode", "getQueryType", "getMutationType", "getSubscriptionType", "schemaDefinitionsCount", "SchemaDefinition", "node", "reportError", "nodes"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/LoneSchemaDefinitionRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Lone Schema definition\n *\n * A GraphQL document is only valid if it contains only one schema definition.\n */\nexport function LoneSchemaDefinitionRule(context) {\n  var _ref, _ref2, _oldSchema$astNode;\n\n  const oldSchema = context.getSchema();\n  const alreadyDefined =\n    (_ref =\n      (_ref2 =\n        (_oldSchema$astNode =\n          oldSchema === null || oldSchema === void 0\n            ? void 0\n            : oldSchema.astNode) !== null && _oldSchema$astNode !== void 0\n          ? _oldSchema$astNode\n          : oldSchema === null || oldSchema === void 0\n          ? void 0\n          : oldSchema.getQueryType()) !== null && _ref2 !== void 0\n        ? _ref2\n        : oldSchema === null || oldSchema === void 0\n        ? void 0\n        : oldSchema.getMutationType()) !== null && _ref !== void 0\n      ? _ref\n      : oldSchema === null || oldSchema === void 0\n      ? void 0\n      : oldSchema.getSubscriptionType();\n  let schemaDefinitionsCount = 0;\n  return {\n    SchemaDefinition(node) {\n      if (alreadyDefined) {\n        context.reportError(\n          new GraphQLError(\n            'Cannot define a new schema within a schema extension.',\n            {\n              nodes: node,\n            },\n          ),\n        );\n        return;\n      }\n\n      if (schemaDefinitionsCount > 0) {\n        context.reportError(\n          new GraphQLError('Must provide only one schema definition.', {\n            nodes: node,\n          }),\n        );\n      }\n\n      ++schemaDefinitionsCount;\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,OAAO,EAAE;EAChD,IAAIC,IAAI,EAAEC,KAAK,EAAEC,kBAAkB;EAEnC,MAAMC,SAAS,GAAGJ,OAAO,CAACK,SAAS,CAAC,CAAC;EACrC,MAAMC,cAAc,GAClB,CAACL,IAAI,GACH,CAACC,KAAK,GACJ,CAACC,kBAAkB,GACjBC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GACtC,KAAK,CAAC,GACNA,SAAS,CAACG,OAAO,MAAM,IAAI,IAAIJ,kBAAkB,KAAK,KAAK,CAAC,GAC9DA,kBAAkB,GAClBC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAC1C,KAAK,CAAC,GACNA,SAAS,CAACI,YAAY,CAAC,CAAC,MAAM,IAAI,IAAIN,KAAK,KAAK,KAAK,CAAC,GACxDA,KAAK,GACLE,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAC1C,KAAK,CAAC,GACNA,SAAS,CAACK,eAAe,CAAC,CAAC,MAAM,IAAI,IAAIR,IAAI,KAAK,KAAK,CAAC,GAC1DA,IAAI,GACJG,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAC1C,KAAK,CAAC,GACNA,SAAS,CAACM,mBAAmB,CAAC,CAAC;EACrC,IAAIC,sBAAsB,GAAG,CAAC;EAC9B,OAAO;IACLC,gBAAgBA,CAACC,IAAI,EAAE;MACrB,IAAIP,cAAc,EAAE;QAClBN,OAAO,CAACc,WAAW,CACjB,IAAIhB,YAAY,CACd,uDAAuD,EACvD;UACEiB,KAAK,EAAEF;QACT,CACF,CACF,CAAC;QACD;MACF;MAEA,IAAIF,sBAAsB,GAAG,CAAC,EAAE;QAC9BX,OAAO,CAACc,WAAW,CACjB,IAAIhB,YAAY,CAAC,0CAA0C,EAAE;UAC3DiB,KAAK,EAAEF;QACT,CAAC,CACH,CAAC;MACH;MAEA,EAAEF,sBAAsB;IAC1B;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}