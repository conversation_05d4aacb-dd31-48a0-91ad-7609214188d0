{"ast": null, "code": "import { GraphQLError } from '../../../error/GraphQLError.mjs';\nimport { getNamedType } from '../../../type/definition.mjs';\nimport { isIntrospectionType } from '../../../type/introspection.mjs';\n\n/**\n * Prohibit introspection queries\n *\n * A GraphQL document is only valid if all fields selected are not fields that\n * return an introspection type.\n *\n * Note: This rule is optional and is not part of the Validation section of the\n * GraphQL Specification. This rule effectively disables introspection, which\n * does not reflect best practices and should only be done if absolutely necessary.\n */\nexport function NoSchemaIntrospectionCustomRule(context) {\n  return {\n    Field(node) {\n      const type = getNamedType(context.getType());\n      if (type && isIntrospectionType(type)) {\n        context.reportError(new GraphQLError(`GraphQL introspection has been disabled, but the requested query contained the field \"${node.name.value}\".`, {\n          nodes: node\n        }));\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "getNamedType", "isIntrospectionType", "NoSchemaIntrospectionCustomRule", "context", "Field", "node", "type", "getType", "reportError", "name", "value", "nodes"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/custom/NoSchemaIntrospectionCustomRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../../error/GraphQLError.mjs';\nimport { getNamedType } from '../../../type/definition.mjs';\nimport { isIntrospectionType } from '../../../type/introspection.mjs';\n\n/**\n * Prohibit introspection queries\n *\n * A GraphQL document is only valid if all fields selected are not fields that\n * return an introspection type.\n *\n * Note: This rule is optional and is not part of the Validation section of the\n * GraphQL Specification. This rule effectively disables introspection, which\n * does not reflect best practices and should only be done if absolutely necessary.\n */\nexport function NoSchemaIntrospectionCustomRule(context) {\n  return {\n    Field(node) {\n      const type = getNamedType(context.getType());\n\n      if (type && isIntrospectionType(type)) {\n        context.reportError(\n          new GraphQLError(\n            `GraphQL introspection has been disabled, but the requested query contained the field \"${node.name.value}\".`,\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,mBAAmB,QAAQ,iCAAiC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,+BAA+BA,CAACC,OAAO,EAAE;EACvD,OAAO;IACLC,KAAKA,CAACC,IAAI,EAAE;MACV,MAAMC,IAAI,GAAGN,YAAY,CAACG,OAAO,CAACI,OAAO,CAAC,CAAC,CAAC;MAE5C,IAAID,IAAI,IAAIL,mBAAmB,CAACK,IAAI,CAAC,EAAE;QACrCH,OAAO,CAACK,WAAW,CACjB,IAAIT,YAAY,CACd,yFAAyFM,IAAI,CAACI,IAAI,CAACC,KAAK,IAAI,EAC5G;UACEC,KAAK,EAAEN;QACT,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}