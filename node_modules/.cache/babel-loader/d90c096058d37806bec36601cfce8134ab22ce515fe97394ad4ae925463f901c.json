{"ast": null, "code": "// Note: This file is autogenerated using \"resources/gen-version.js\" script and\n// automatically updated by \"npm version\" command.\n\n/**\n * A string containing the version of the GraphQL.js library\n */\nexport const version = '16.11.0';\n/**\n * An object containing the components of the GraphQL.js version string\n */\n\nexport const versionInfo = Object.freeze({\n  major: 16,\n  minor: 11,\n  patch: 0,\n  preReleaseTag: null\n});", "map": {"version": 3, "names": ["version", "versionInfo", "Object", "freeze", "major", "minor", "patch", "preReleaseTag"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/version.mjs"], "sourcesContent": ["// Note: This file is autogenerated using \"resources/gen-version.js\" script and\n// automatically updated by \"npm version\" command.\n\n/**\n * A string containing the version of the GraphQL.js library\n */\nexport const version = '16.11.0';\n/**\n * An object containing the components of the GraphQL.js version string\n */\n\nexport const versionInfo = Object.freeze({\n  major: 16,\n  minor: 11,\n  patch: 0,\n  preReleaseTag: null,\n});\n"], "mappings": "AAAA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMA,OAAO,GAAG,SAAS;AAChC;AACA;AACA;;AAEA,OAAO,MAAMC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC;EACvCC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE,CAAC;EACRC,aAAa,EAAE;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}