{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/ProcessAnalysis.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Box, Typography, FormControl, InputLabel, Select, MenuItem, Grid, Paper, Tabs, Tab, Alert } from '@mui/material';\nimport { setSelectedProcess, updateProcessData } from '../../store/biaSlice';\nimport ImpactAssessment from './analysis/ImpactAssessment';\nimport Dependencies from './analysis/Dependencies';\nimport Resources from './analysis/Resources';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProcessAnalysis = () => {\n  _s();\n  var _currentBIA$processes;\n  const dispatch = useDispatch();\n  const {\n    currentBIA,\n    selectedProcess\n  } = useSelector(state => state.bia);\n  const [activeTab, setActiveTab] = useState(0);\n  const handleProcessChange = processId => {\n    dispatch(setSelectedProcess(processId));\n  };\n  const getProcessName = processId => {\n    // Mock lookup - in real app would use selector\n    const processes = {\n      'proc-f-01': 'Accounts Payable',\n      'proc-f-02': 'Payroll Processing',\n      'proc-t-01': 'Oracle Financials DB',\n      'proc-t-02': 'Core Network Services',\n      'proc-h-01': 'Employee Onboarding'\n    };\n    return processes[processId] || processId;\n  };\n  if (!(currentBIA !== null && currentBIA !== void 0 && (_currentBIA$processes = currentBIA.processes) !== null && _currentBIA$processes !== void 0 && _currentBIA$processes.length)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        children: \"Process Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Please select processes in the Setup & Scope section first.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: \"Process Analysis\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      sx: {\n        minWidth: 300,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        children: \"Select Process to Analyze\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        value: selectedProcess || '',\n        onChange: e => handleProcessChange(e.target.value),\n        children: currentBIA.processes.map(processId => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: processId,\n          children: getProcessName(processId)\n        }, processId, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), selectedProcess && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: (e, newValue) => setActiveTab(newValue),\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Impact Assessment & RTO/RPO\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Dependencies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Resources\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3\n        },\n        children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(ImpactAssessment, {\n          processId: selectedProcess\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 33\n        }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Dependencies, {\n          processId: selectedProcess\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 33\n        }, this), activeTab === 2 && /*#__PURE__*/_jsxDEV(Resources, {\n          processId: selectedProcess\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_s(ProcessAnalysis, \"c1grhAMy4jA0l4NqAyH2xmH+gG0=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = ProcessAnalysis;\nexport default ProcessAnalysis;\nvar _c;\n$RefreshReg$(_c, \"ProcessAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "Box", "Typography", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "Paper", "Tabs", "Tab", "<PERSON><PERSON>", "setSelectedProcess", "updateProcessData", "ImpactAssessment", "Dependencies", "Resources", "jsxDEV", "_jsxDEV", "ProcessAnalysis", "_s", "_currentBIA$processes", "dispatch", "currentBIA", "selectedProcess", "state", "bia", "activeTab", "setActiveTab", "handleProcessChange", "processId", "getProcessName", "processes", "length", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "sx", "min<PERSON><PERSON><PERSON>", "mb", "value", "onChange", "e", "target", "map", "mt", "newValue", "borderBottom", "borderColor", "label", "p", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/ProcessAnalysis.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport {\n  Box, Typography, FormControl, InputLabel, Select, MenuItem,\n  Grid, Paper, Tabs, Tab, Alert\n} from '@mui/material';\nimport { setSelectedProcess, updateProcessData } from '../../store/biaSlice';\nimport ImpactAssessment from './analysis/ImpactAssessment';\nimport Dependencies from './analysis/Dependencies';\nimport Resources from './analysis/Resources';\n\nconst ProcessAnalysis = () => {\n  const dispatch = useDispatch();\n  const { currentBIA, selectedProcess } = useSelector(state => state.bia);\n  const [activeTab, setActiveTab] = useState(0);\n\n  const handleProcessChange = (processId) => {\n    dispatch(setSelectedProcess(processId));\n  };\n\n  const getProcessName = (processId) => {\n    // Mock lookup - in real app would use selector\n    const processes = {\n      'proc-f-01': 'Accounts Payable',\n      'proc-f-02': 'Payroll Processing',\n      'proc-t-01': 'Oracle Financials DB',\n      'proc-t-02': 'Core Network Services',\n      'proc-h-01': 'Employee Onboarding'\n    };\n    return processes[processId] || processId;\n  };\n\n  if (!currentBIA?.processes?.length) {\n    return (\n      <Box>\n        <Typography variant=\"h5\" gutterBottom>\n          Process Analysis\n        </Typography>\n        <Alert severity=\"info\">\n          Please select processes in the Setup & Scope section first.\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Typography variant=\"h5\" gutterBottom>\n        Process Analysis\n      </Typography>\n\n      {/* Process Selection */}\n      <FormControl sx={{ minWidth: 300, mb: 3 }}>\n        <InputLabel>Select Process to Analyze</InputLabel>\n        <Select\n          value={selectedProcess || ''}\n          onChange={(e) => handleProcessChange(e.target.value)}\n        >\n          {currentBIA.processes.map(processId => (\n            <MenuItem key={processId} value={processId}>\n              {getProcessName(processId)}\n            </MenuItem>\n          ))}\n        </Select>\n      </FormControl>\n\n      {selectedProcess && (\n        <Paper sx={{ mt: 2 }}>\n          <Tabs \n            value={activeTab} \n            onChange={(e, newValue) => setActiveTab(newValue)}\n            sx={{ borderBottom: 1, borderColor: 'divider' }}\n          >\n            <Tab label=\"Impact Assessment & RTO/RPO\" />\n            <Tab label=\"Dependencies\" />\n            <Tab label=\"Resources\" />\n          </Tabs>\n\n          <Box sx={{ p: 3 }}>\n            {activeTab === 0 && <ImpactAssessment processId={selectedProcess} />}\n            {activeTab === 1 && <Dependencies processId={selectedProcess} />}\n            {activeTab === 2 && <Resources processId={selectedProcess} />}\n          </Box>\n        </Paper>\n      )}\n    </Box>\n  );\n};\n\nexport default ProcessAnalysis;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAC1DC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QACxB,eAAe;AACtB,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC5E,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,SAAS,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC5B,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwB,UAAU;IAAEC;EAAgB,CAAC,GAAGxB,WAAW,CAACyB,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EACvE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EAE7C,MAAMgC,mBAAmB,GAAIC,SAAS,IAAK;IACzCR,QAAQ,CAACV,kBAAkB,CAACkB,SAAS,CAAC,CAAC;EACzC,CAAC;EAED,MAAMC,cAAc,GAAID,SAAS,IAAK;IACpC;IACA,MAAME,SAAS,GAAG;MAChB,WAAW,EAAE,kBAAkB;MAC/B,WAAW,EAAE,oBAAoB;MACjC,WAAW,EAAE,sBAAsB;MACnC,WAAW,EAAE,uBAAuB;MACpC,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,SAAS,CAACF,SAAS,CAAC,IAAIA,SAAS;EAC1C,CAAC;EAED,IAAI,EAACP,UAAU,aAAVA,UAAU,gBAAAF,qBAAA,GAAVE,UAAU,CAAES,SAAS,cAAAX,qBAAA,eAArBA,qBAAA,CAAuBY,MAAM,GAAE;IAClC,oBACEf,OAAA,CAACjB,GAAG;MAAAiC,QAAA,gBACFhB,OAAA,CAAChB,UAAU;QAACiC,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtB,OAAA,CAACP,KAAK;QAAC8B,QAAQ,EAAC,MAAM;QAAAP,QAAA,EAAC;MAEvB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACEtB,OAAA,CAACjB,GAAG;IAAAiC,QAAA,gBACFhB,OAAA,CAAChB,UAAU;MAACiC,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbtB,OAAA,CAACf,WAAW;MAACuC,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAV,QAAA,gBACxChB,OAAA,CAACd,UAAU;QAAA8B,QAAA,EAAC;MAAyB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClDtB,OAAA,CAACb,MAAM;QACLwC,KAAK,EAAErB,eAAe,IAAI,EAAG;QAC7BsB,QAAQ,EAAGC,CAAC,IAAKlB,mBAAmB,CAACkB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAAAX,QAAA,EAEpDX,UAAU,CAACS,SAAS,CAACiB,GAAG,CAACnB,SAAS,iBACjCZ,OAAA,CAACZ,QAAQ;UAAiBuC,KAAK,EAAEf,SAAU;UAAAI,QAAA,EACxCH,cAAc,CAACD,SAAS;QAAC,GADbA,SAAS;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEd,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEbhB,eAAe,iBACdN,OAAA,CAACV,KAAK;MAACkC,EAAE,EAAE;QAAEQ,EAAE,EAAE;MAAE,CAAE;MAAAhB,QAAA,gBACnBhB,OAAA,CAACT,IAAI;QACHoC,KAAK,EAAElB,SAAU;QACjBmB,QAAQ,EAAEA,CAACC,CAAC,EAAEI,QAAQ,KAAKvB,YAAY,CAACuB,QAAQ,CAAE;QAClDT,EAAE,EAAE;UAAEU,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAAnB,QAAA,gBAEhDhB,OAAA,CAACR,GAAG;UAAC4C,KAAK,EAAC;QAA6B;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CtB,OAAA,CAACR,GAAG;UAAC4C,KAAK,EAAC;QAAc;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BtB,OAAA,CAACR,GAAG;UAAC4C,KAAK,EAAC;QAAW;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eAEPtB,OAAA,CAACjB,GAAG;QAACyC,EAAE,EAAE;UAAEa,CAAC,EAAE;QAAE,CAAE;QAAArB,QAAA,GACfP,SAAS,KAAK,CAAC,iBAAIT,OAAA,CAACJ,gBAAgB;UAACgB,SAAS,EAAEN;QAAgB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACnEb,SAAS,KAAK,CAAC,iBAAIT,OAAA,CAACH,YAAY;UAACe,SAAS,EAAEN;QAAgB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/Db,SAAS,KAAK,CAAC,iBAAIT,OAAA,CAACF,SAAS;UAACc,SAAS,EAAEN;QAAgB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpB,EAAA,CA5EID,eAAe;EAAA,QACFpB,WAAW,EACYC,WAAW;AAAA;AAAAwD,EAAA,GAF/CrC,eAAe;AA8ErB,eAAeA,eAAe;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}