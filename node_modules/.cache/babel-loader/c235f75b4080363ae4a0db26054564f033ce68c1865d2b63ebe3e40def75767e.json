{"ast": null, "code": "'use client';\n\n// @inheritedComponent IconButton\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"edge\", \"size\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system/colorManipulator';\nimport capitalize from '../utils/capitalize';\nimport SwitchBase from '../internal/SwitchBase';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport switchClasses, { getSwitchUtilityClass } from './switchClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n}), ({\n  theme\n}) => ({\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  variants: [...Object.entries(theme.palette).filter(([, value]) => value.main && value.light) // check all the used fields in the style below\n  .map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      [`&.${switchClasses.checked}`]: {\n        color: (theme.vars || theme).palette[color].main,\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n          '@media (hover: none)': {\n            backgroundColor: 'transparent'\n          }\n        },\n        [`&.${switchClasses.disabled}`]: {\n          color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.55)}`\n        }\n      },\n      [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n        backgroundColor: (theme.vars || theme).palette[color].main\n      }\n    }\n  }))]\n}));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track',\n  overridesResolver: (props, styles) => styles.track\n})(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n}));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb',\n  overridesResolver: (props, styles) => styles.thumb\n})(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n}));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n      className,\n      color = 'primary',\n      edge = false,\n      size = 'medium',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    edge,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const icon = /*#__PURE__*/_jsx(SwitchThumb, {\n    className: classes.thumb,\n    ownerState: ownerState\n  });\n  return /*#__PURE__*/_jsxs(SwitchRoot, {\n    className: clsx(classes.root, className),\n    sx: sx,\n    ownerState: ownerState,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, _extends({\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState\n    }, other, {\n      classes: _extends({}, classes, {\n        root: classes.switchBase\n      })\n    })), /*#__PURE__*/_jsx(SwitchTrack, {\n      className: classes.track,\n      ownerState: ownerState\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "refType", "composeClasses", "alpha", "darken", "lighten", "capitalize", "SwitchBase", "styled", "useDefaultProps", "switchClasses", "getSwitchUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "edge", "size", "color", "checked", "disabled", "slots", "root", "switchBase", "thumb", "track", "input", "composedClasses", "SwitchRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "width", "height", "overflow", "padding", "boxSizing", "position", "flexShrink", "zIndex", "verticalAlign", "colorAdjust", "variants", "style", "marginLeft", "marginRight", "transform", "SwitchSwitchBase", "theme", "top", "left", "vars", "palette", "Switch", "defaultColor", "mode", "common", "white", "grey", "transition", "transitions", "create", "duration", "shortest", "defaultDisabledColor", "opacity", "switchTrackDisabled", "backgroundColor", "action", "activeChannel", "hoverOpacity", "active", "Object", "entries", "filter", "value", "main", "light", "map", "mainChannel", "SwitchTrack", "borderRadius", "onBackground", "black", "switchTrack", "SwitchThumb", "boxShadow", "shadows", "forwardRef", "inProps", "ref", "className", "sx", "other", "icon", "children", "type", "checkedIcon", "process", "env", "NODE_ENV", "propTypes", "bool", "node", "object", "string", "oneOfType", "oneOf", "defaultChecked", "disable<PERSON><PERSON><PERSON>", "id", "inputProps", "inputRef", "onChange", "func", "required", "arrayOf", "any"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mui/material/Switch/Switch.js"], "sourcesContent": ["'use client';\n\n// @inheritedComponent IconButton\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"edge\", \"size\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system/colorManipulator';\nimport capitalize from '../utils/capitalize';\nimport SwitchBase from '../internal/SwitchBase';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport switchClasses, { getSwitchUtilityClass } from './switchClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n}), ({\n  theme\n}) => ({\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  variants: [...Object.entries(theme.palette).filter(([, value]) => value.main && value.light) // check all the used fields in the style below\n  .map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      [`&.${switchClasses.checked}`]: {\n        color: (theme.vars || theme).palette[color].main,\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n          '@media (hover: none)': {\n            backgroundColor: 'transparent'\n          }\n        },\n        [`&.${switchClasses.disabled}`]: {\n          color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.55)}`\n        }\n      },\n      [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n        backgroundColor: (theme.vars || theme).palette[color].main\n      }\n    }\n  }))]\n}));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track',\n  overridesResolver: (props, styles) => styles.track\n})(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n}));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb',\n  overridesResolver: (props, styles) => styles.thumb\n})(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n}));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n      className,\n      color = 'primary',\n      edge = false,\n      size = 'medium',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    edge,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const icon = /*#__PURE__*/_jsx(SwitchThumb, {\n    className: classes.thumb,\n    ownerState: ownerState\n  });\n  return /*#__PURE__*/_jsxs(SwitchRoot, {\n    className: clsx(classes.root, className),\n    sx: sx,\n    ownerState: ownerState,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, _extends({\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState\n    }, other, {\n      classes: _extends({}, classes, {\n        root: classes.switchBase\n      })\n    })), /*#__PURE__*/_jsx(SwitchTrack, {\n      className: classes.track,\n      ownerState: ownerState\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,EAAEC,MAAM,EAAEC,OAAO,QAAQ,8BAA8B;AACrE,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,aAAa,IAAIC,qBAAqB,QAAQ,iBAAiB;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC,IAAI;IACJC,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,IAAI,IAAI,OAAOb,UAAU,CAACa,IAAI,CAAC,EAAE,EAAE,OAAOb,UAAU,CAACc,IAAI,CAAC,EAAE,CAAC;IAC5EM,UAAU,EAAE,CAAC,YAAY,EAAE,QAAQpB,UAAU,CAACe,KAAK,CAAC,EAAE,EAAEC,OAAO,IAAI,SAAS,EAAEC,QAAQ,IAAI,UAAU,CAAC;IACrGI,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,MAAMC,eAAe,GAAG5B,cAAc,CAACsB,KAAK,EAAEb,qBAAqB,EAAEO,OAAO,CAAC;EAC7E,OAAOtB,QAAQ,CAAC,CAAC,CAAC,EAAEsB,OAAO,EAAEY,eAAe,CAAC;AAC/C,CAAC;AACD,MAAMC,UAAU,GAAGvB,MAAM,CAAC,MAAM,EAAE;EAChCwB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACX,IAAI,EAAER,UAAU,CAACE,IAAI,IAAIiB,MAAM,CAAC,OAAO9B,UAAU,CAACW,UAAU,CAACE,IAAI,CAAC,EAAE,CAAC,EAAEiB,MAAM,CAAC,OAAO9B,UAAU,CAACW,UAAU,CAACG,IAAI,CAAC,EAAE,CAAC,CAAC;EACrI;AACF,CAAC,CAAC,CAAC;EACDiB,OAAO,EAAE,aAAa;EACtBC,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAClBC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACnBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,CAAC;EACT;EACAC,aAAa,EAAE,QAAQ;EACvB;EACA,cAAc,EAAE;IACdC,WAAW,EAAE;EACf,CAAC;EACDC,QAAQ,EAAE,CAAC;IACTb,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACD8B,KAAK,EAAE;MACLC,UAAU,EAAE,CAAC;IACf;EACF,CAAC,EAAE;IACDf,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACD8B,KAAK,EAAE;MACLE,WAAW,EAAE,CAAC;IAChB;EACF,CAAC,EAAE;IACDhB,KAAK,EAAE;MACLf,IAAI,EAAE;IACR,CAAC;IACD6B,KAAK,EAAE;MACLX,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVE,OAAO,EAAE,CAAC;MACV,CAAC,MAAM/B,aAAa,CAACiB,KAAK,EAAE,GAAG;QAC7BW,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;MACV,CAAC;MACD,CAAC,MAAM7B,aAAa,CAACgB,UAAU,EAAE,GAAG;QAClCe,OAAO,EAAE,CAAC;QACV,CAAC,KAAK/B,aAAa,CAACY,OAAO,EAAE,GAAG;UAC9B8B,SAAS,EAAE;QACb;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,gBAAgB,GAAG7C,MAAM,CAACD,UAAU,EAAE;EAC1CyB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,YAAY;EAClBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACV,UAAU,EAAE;MACzB,CAAC,MAAMhB,aAAa,CAACmB,KAAK,EAAE,GAAGO,MAAM,CAACP;IACxC,CAAC,EAAEZ,UAAU,CAACI,KAAK,KAAK,SAAS,IAAIe,MAAM,CAAC,QAAQ9B,UAAU,CAACW,UAAU,CAACI,KAAK,CAAC,EAAE,CAAC,CAAC;EACtF;AACF,CAAC,CAAC,CAAC,CAAC;EACFiC;AACF,CAAC,MAAM;EACLX,QAAQ,EAAE,UAAU;EACpBY,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPX,MAAM,EAAE,CAAC;EACT;EACAxB,KAAK,EAAEiC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACC,YAAY,GAAG,GAAGN,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAK,GAAGT,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE;EACvJC,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;IAC1DC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC;EACF,CAAC,KAAK3D,aAAa,CAACY,OAAO,EAAE,GAAG;IAC9B8B,SAAS,EAAE;EACb,CAAC;EACD,CAAC,KAAK1C,aAAa,CAACa,QAAQ,EAAE,GAAG;IAC/BF,KAAK,EAAEiC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACW,oBAAoB,GAAG,GAAGhB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,GAAGV,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC;EAC5J,CAAC;EACD,CAAC,KAAKtD,aAAa,CAACY,OAAO,OAAOZ,aAAa,CAACkB,KAAK,EAAE,GAAG;IACxD2C,OAAO,EAAE;EACX,CAAC;EACD,CAAC,KAAK7D,aAAa,CAACa,QAAQ,OAAOb,aAAa,CAACkB,KAAK,EAAE,GAAG;IACzD2C,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACC,mBAAmB,GAAG,GAAGlB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG;EAC/G,CAAC;EACD,CAAC,MAAMnD,aAAa,CAACmB,KAAK,EAAE,GAAG;IAC7B2B,IAAI,EAAE,OAAO;IACblB,KAAK,EAAE;EACT;AACF,CAAC,CAAC,EAAE,CAAC;EACHgB;AACF,CAAC,MAAM;EACL,SAAS,EAAE;IACTmB,eAAe,EAAEnB,KAAK,CAACG,IAAI,GAAG,QAAQH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACgB,MAAM,CAACC,aAAa,MAAMrB,KAAK,CAACG,IAAI,CAACC,OAAO,CAACgB,MAAM,CAACE,YAAY,GAAG,GAAGzE,KAAK,CAACmD,KAAK,CAACI,OAAO,CAACgB,MAAM,CAACG,MAAM,EAAEvB,KAAK,CAACI,OAAO,CAACgB,MAAM,CAACE,YAAY,CAAC;IACpM;IACA,sBAAsB,EAAE;MACtBH,eAAe,EAAE;IACnB;EACF,CAAC;EACDzB,QAAQ,EAAE,CAAC,GAAG8B,MAAM,CAACC,OAAO,CAACzB,KAAK,CAACI,OAAO,CAAC,CAACsB,MAAM,CAAC,CAAC,GAAGC,KAAK,CAAC,KAAKA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACE,KAAK,CAAC,CAAC;EAAA,CAC5FC,GAAG,CAAC,CAAC,CAAC/D,KAAK,CAAC,MAAM;IACjBc,KAAK,EAAE;MACLd;IACF,CAAC;IACD4B,KAAK,EAAE;MACL,CAAC,KAAKvC,aAAa,CAACY,OAAO,EAAE,GAAG;QAC9BD,KAAK,EAAE,CAACiC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACrC,KAAK,CAAC,CAAC6D,IAAI;QAChD,SAAS,EAAE;UACTT,eAAe,EAAEnB,KAAK,CAACG,IAAI,GAAG,QAAQH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACrC,KAAK,CAAC,CAACgE,WAAW,MAAM/B,KAAK,CAACG,IAAI,CAACC,OAAO,CAACgB,MAAM,CAACE,YAAY,GAAG,GAAGzE,KAAK,CAACmD,KAAK,CAACI,OAAO,CAACrC,KAAK,CAAC,CAAC6D,IAAI,EAAE5B,KAAK,CAACI,OAAO,CAACgB,MAAM,CAACE,YAAY,CAAC;UAChM,sBAAsB,EAAE;YACtBH,eAAe,EAAE;UACnB;QACF,CAAC;QACD,CAAC,KAAK/D,aAAa,CAACa,QAAQ,EAAE,GAAG;UAC/BF,KAAK,EAAEiC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAAC,GAAGtC,KAAK,eAAe,CAAC,GAAG,GAAGiC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGxD,OAAO,CAACiD,KAAK,CAACI,OAAO,CAACrC,KAAK,CAAC,CAAC6D,IAAI,EAAE,IAAI,CAAC,GAAG9E,MAAM,CAACkD,KAAK,CAACI,OAAO,CAACrC,KAAK,CAAC,CAAC6D,IAAI,EAAE,IAAI,CAAC;QACjM;MACF,CAAC;MACD,CAAC,KAAKxE,aAAa,CAACY,OAAO,OAAOZ,aAAa,CAACkB,KAAK,EAAE,GAAG;QACxD6C,eAAe,EAAE,CAACnB,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACrC,KAAK,CAAC,CAAC6D;MACxD;IACF;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH,MAAMI,WAAW,GAAG9E,MAAM,CAAC,MAAM,EAAE;EACjCwB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAAC,CAAC;EACF0B;AACF,CAAC,MAAM;EACLf,MAAM,EAAE,MAAM;EACdD,KAAK,EAAE,MAAM;EACbiD,YAAY,EAAE,EAAE,GAAG,CAAC;EACpB1C,MAAM,EAAE,CAAC,CAAC;EACVoB,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,SAAS,EAAE,kBAAkB,CAAC,EAAE;IACpEC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC;EACFI,eAAe,EAAEnB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACI,MAAM,CAAC0B,YAAY,GAAG,GAAGlC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAAC2B,KAAK,GAAGnC,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAK,EAAE;EACpKQ,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACmB,WAAW,GAAG,GAAGpC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG;AACvG,CAAC,CAAC,CAAC;AACH,MAAM8B,WAAW,GAAGnF,MAAM,CAAC,MAAM,EAAE;EACjCwB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC/C,CAAC,CAAC,CAAC,CAAC;EACF2B;AACF,CAAC,MAAM;EACLsC,SAAS,EAAE,CAACtC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEuC,OAAO,CAAC,CAAC,CAAC;EAC3CpB,eAAe,EAAE,cAAc;EAC/BnC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVgD,YAAY,EAAE;AAChB,CAAC,CAAC,CAAC;AACH,MAAM5B,MAAM,GAAG,aAAa7D,KAAK,CAACgG,UAAU,CAAC,SAASnC,MAAMA,CAACoC,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAM7D,KAAK,GAAG1B,eAAe,CAAC;IAC5B0B,KAAK,EAAE4D,OAAO;IACd/D,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFiE,SAAS;MACT5E,KAAK,GAAG,SAAS;MACjBF,IAAI,GAAG,KAAK;MACZC,IAAI,GAAG,QAAQ;MACf8E;IACF,CAAC,GAAG/D,KAAK;IACTgE,KAAK,GAAGxG,6BAA6B,CAACwC,KAAK,EAAEtC,SAAS,CAAC;EACzD,MAAMoB,UAAU,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAEuC,KAAK,EAAE;IACrCd,KAAK;IACLF,IAAI;IACJC;EACF,CAAC,CAAC;EACF,MAAMF,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMmF,IAAI,GAAG,aAAavF,IAAI,CAAC8E,WAAW,EAAE;IAC1CM,SAAS,EAAE/E,OAAO,CAACS,KAAK;IACxBV,UAAU,EAAEA;EACd,CAAC,CAAC;EACF,OAAO,aAAaF,KAAK,CAACgB,UAAU,EAAE;IACpCkE,SAAS,EAAEjG,IAAI,CAACkB,OAAO,CAACO,IAAI,EAAEwE,SAAS,CAAC;IACxCC,EAAE,EAAEA,EAAE;IACNjF,UAAU,EAAEA,UAAU;IACtBoF,QAAQ,EAAE,CAAC,aAAaxF,IAAI,CAACwC,gBAAgB,EAAEzD,QAAQ,CAAC;MACtD0G,IAAI,EAAE,UAAU;MAChBF,IAAI,EAAEA,IAAI;MACVG,WAAW,EAAEH,IAAI;MACjBJ,GAAG,EAAEA,GAAG;MACR/E,UAAU,EAAEA;IACd,CAAC,EAAEkF,KAAK,EAAE;MACRjF,OAAO,EAAEtB,QAAQ,CAAC,CAAC,CAAC,EAAEsB,OAAO,EAAE;QAC7BO,IAAI,EAAEP,OAAO,CAACQ;MAChB,CAAC;IACH,CAAC,CAAC,CAAC,EAAE,aAAab,IAAI,CAACyE,WAAW,EAAE;MAClCW,SAAS,EAAE/E,OAAO,CAACU,KAAK;MACxBX,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFuF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/C,MAAM,CAACgD,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACErF,OAAO,EAAEvB,SAAS,CAAC6G,IAAI;EACvB;AACF;AACA;EACEL,WAAW,EAAExG,SAAS,CAAC8G,IAAI;EAC3B;AACF;AACA;EACE3F,OAAO,EAAEnB,SAAS,CAAC+G,MAAM;EACzB;AACF;AACA;EACEb,SAAS,EAAElG,SAAS,CAACgH,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACE1F,KAAK,EAAEtB,SAAS,CAAC,sCAAsCiH,SAAS,CAAC,CAACjH,SAAS,CAACkH,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAElH,SAAS,CAACgH,MAAM,CAAC,CAAC;EACjL;AACF;AACA;EACEG,cAAc,EAAEnH,SAAS,CAAC6G,IAAI;EAC9B;AACF;AACA;EACErF,QAAQ,EAAExB,SAAS,CAAC6G,IAAI;EACxB;AACF;AACA;AACA;EACEO,aAAa,EAAEpH,SAAS,CAAC6G,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;EACEzF,IAAI,EAAEpB,SAAS,CAACkH,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC9C;AACF;AACA;EACEb,IAAI,EAAErG,SAAS,CAAC8G,IAAI;EACpB;AACF;AACA;EACEO,EAAE,EAAErH,SAAS,CAACgH,MAAM;EACpB;AACF;AACA;EACEM,UAAU,EAAEtH,SAAS,CAAC+G,MAAM;EAC5B;AACF;AACA;EACEQ,QAAQ,EAAErH,OAAO;EACjB;AACF;AACA;AACA;AACA;AACA;AACA;EACEsH,QAAQ,EAAExH,SAAS,CAACyH,IAAI;EACxB;AACF;AACA;AACA;EACEC,QAAQ,EAAE1H,SAAS,CAAC6G,IAAI;EACxB;AACF;AACA;AACA;AACA;EACExF,IAAI,EAAErB,SAAS,CAAC,sCAAsCiH,SAAS,CAAC,CAACjH,SAAS,CAACkH,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAElH,SAAS,CAACgH,MAAM,CAAC,CAAC;EACzH;AACF;AACA;EACEb,EAAE,EAAEnG,SAAS,CAACiH,SAAS,CAAC,CAACjH,SAAS,CAAC2H,OAAO,CAAC3H,SAAS,CAACiH,SAAS,CAAC,CAACjH,SAAS,CAACyH,IAAI,EAAEzH,SAAS,CAAC+G,MAAM,EAAE/G,SAAS,CAAC6G,IAAI,CAAC,CAAC,CAAC,EAAE7G,SAAS,CAACyH,IAAI,EAAEzH,SAAS,CAAC+G,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACE7B,KAAK,EAAElF,SAAS,CAAC4H;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAehE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}