{"ast": null, "code": "import { stringToHeaders } from \"headers-polyfill\";\nfunction parseContentHeaders(headersString) {\n  const headers = stringToHeaders(headersString);\n  const contentType = headers.get(\"content-type\") || \"text/plain\";\n  const disposition = headers.get(\"content-disposition\");\n  if (!disposition) {\n    throw new Error('\"Content-Disposition\" header is required.');\n  }\n  const directives = disposition.split(\";\").reduce((acc, chunk) => {\n    const [name2, ...rest] = chunk.trim().split(\"=\");\n    acc[name2] = rest.join(\"=\");\n    return acc;\n  }, {});\n  const name = directives.name?.slice(1, -1);\n  const filename = directives.filename?.slice(1, -1);\n  return {\n    name,\n    filename,\n    contentType\n  };\n}\nfunction parseMultipartData(data, headers) {\n  const contentType = headers?.get(\"content-type\");\n  if (!contentType) {\n    return void 0;\n  }\n  const [, ...directives] = contentType.split(/; */);\n  const boundary = directives.filter(d => d.startsWith(\"boundary=\")).map(s => s.replace(/^boundary=/, \"\"))[0];\n  if (!boundary) {\n    return void 0;\n  }\n  const boundaryRegExp = new RegExp(`--+${boundary}`);\n  const fields = data.split(boundaryRegExp).filter(chunk => chunk.startsWith(\"\\r\\n\") && chunk.endsWith(\"\\r\\n\")).map(chunk => chunk.trimStart().replace(/\\r\\n$/, \"\"));\n  if (!fields.length) {\n    return void 0;\n  }\n  const parsedBody = {};\n  try {\n    for (const field of fields) {\n      const [contentHeaders, ...rest] = field.split(\"\\r\\n\\r\\n\");\n      const contentBody = rest.join(\"\\r\\n\\r\\n\");\n      const {\n        contentType: contentType2,\n        filename,\n        name\n      } = parseContentHeaders(contentHeaders);\n      const value = filename === void 0 ? contentBody : new File([contentBody], filename, {\n        type: contentType2\n      });\n      const parsedValue = parsedBody[name];\n      if (parsedValue === void 0) {\n        parsedBody[name] = value;\n      } else if (Array.isArray(parsedValue)) {\n        parsedBody[name] = [...parsedValue, value];\n      } else {\n        parsedBody[name] = [parsedValue, value];\n      }\n    }\n    return parsedBody;\n  } catch {\n    return void 0;\n  }\n}\nexport { parseMultipartData };", "map": {"version": 3, "names": ["stringToHeaders", "parseContentHeaders", "headersString", "headers", "contentType", "get", "disposition", "Error", "directives", "split", "reduce", "acc", "chunk", "name2", "rest", "trim", "join", "name", "slice", "filename", "parseMultipartData", "data", "boundary", "filter", "d", "startsWith", "map", "s", "replace", "boundaryRegExp", "RegExp", "fields", "endsWith", "trimStart", "length", "parsedBody", "field", "contentHeaders", "contentBody", "contentType2", "value", "File", "type", "parsedValue", "Array", "isArray"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/internal/parseMultipartData.ts"], "sourcesContent": ["import { stringToHeaders } from 'headers-polyfill'\nimport { DefaultRequestMultipartBody } from '../../handlers/RequestHandler'\n\ninterface ParsedContentHeaders {\n  name: string\n  filename?: string\n  contentType: string\n}\n\ninterface ContentDispositionDirective {\n  [key: string]: string | undefined\n  name: string\n  filename?: string\n  'form-data': string\n}\n\nfunction parseContentHeaders(headersString: string): ParsedContentHeaders {\n  const headers = stringToHeaders(headersString)\n  const contentType = headers.get('content-type') || 'text/plain'\n  const disposition = headers.get('content-disposition')\n\n  if (!disposition) {\n    throw new Error('\"Content-Disposition\" header is required.')\n  }\n\n  const directives = disposition.split(';').reduce((acc, chunk) => {\n    const [name, ...rest] = chunk.trim().split('=')\n    acc[name] = rest.join('=')\n    return acc\n  }, {} as ContentDispositionDirective)\n\n  const name = directives.name?.slice(1, -1)\n  const filename = directives.filename?.slice(1, -1)\n\n  return {\n    name,\n    filename,\n    contentType,\n  }\n}\n\n/**\n * Parses a given string as a multipart/form-data.\n * Does not throw an exception on an invalid multipart string.\n */\nexport function parseMultipartData<T extends DefaultRequestMultipartBody>(\n  data: string,\n  headers?: Headers,\n): T | undefined {\n  const contentType = headers?.get('content-type')\n\n  if (!contentType) {\n    return undefined\n  }\n\n  const [, ...directives] = contentType.split(/; */)\n  const boundary = directives\n    .filter((d) => d.startsWith('boundary='))\n    .map((s) => s.replace(/^boundary=/, ''))[0]\n\n  if (!boundary) {\n    return undefined\n  }\n\n  const boundaryRegExp = new RegExp(`--+${boundary}`)\n  const fields = data\n    .split(boundaryRegExp)\n    .filter((chunk) => chunk.startsWith('\\r\\n') && chunk.endsWith('\\r\\n'))\n    .map((chunk) => chunk.trimStart().replace(/\\r\\n$/, ''))\n\n  if (!fields.length) {\n    return undefined\n  }\n\n  const parsedBody: DefaultRequestMultipartBody = {}\n\n  try {\n    for (const field of fields) {\n      const [contentHeaders, ...rest] = field.split('\\r\\n\\r\\n')\n      const contentBody = rest.join('\\r\\n\\r\\n')\n      const { contentType, filename, name } =\n        parseContentHeaders(contentHeaders)\n\n      const value =\n        filename === undefined\n          ? contentBody\n          : new File([contentBody], filename, { type: contentType })\n\n      const parsedValue = parsedBody[name]\n\n      if (parsedValue === undefined) {\n        parsedBody[name] = value\n      } else if (Array.isArray(parsedValue)) {\n        parsedBody[name] = [...parsedValue, value]\n      } else {\n        parsedBody[name] = [parsedValue, value]\n      }\n    }\n\n    return parsedBody as T\n  } catch {\n    return undefined\n  }\n}\n"], "mappings": "AAAA,SAASA,eAAA,QAAuB;AAgBhC,SAASC,oBAAoBC,aAAA,EAA6C;EACxE,MAAMC,OAAA,GAAUH,eAAA,CAAgBE,aAAa;EAC7C,MAAME,WAAA,GAAcD,OAAA,CAAQE,GAAA,CAAI,cAAc,KAAK;EACnD,MAAMC,WAAA,GAAcH,OAAA,CAAQE,GAAA,CAAI,qBAAqB;EAErD,IAAI,CAACC,WAAA,EAAa;IAChB,MAAM,IAAIC,KAAA,CAAM,2CAA2C;EAC7D;EAEA,MAAMC,UAAA,GAAaF,WAAA,CAAYG,KAAA,CAAM,GAAG,EAAEC,MAAA,CAAO,CAACC,GAAA,EAAKC,KAAA,KAAU;IAC/D,MAAM,CAACC,KAAA,EAAM,GAAGC,IAAI,IAAIF,KAAA,CAAMG,IAAA,CAAK,EAAEN,KAAA,CAAM,GAAG;IAC9CE,GAAA,CAAIE,KAAI,IAAIC,IAAA,CAAKE,IAAA,CAAK,GAAG;IACzB,OAAOL,GAAA;EACT,GAAG,CAAC,CAAgC;EAEpC,MAAMM,IAAA,GAAOT,UAAA,CAAWS,IAAA,EAAMC,KAAA,CAAM,GAAG,EAAE;EACzC,MAAMC,QAAA,GAAWX,UAAA,CAAWW,QAAA,EAAUD,KAAA,CAAM,GAAG,EAAE;EAEjD,OAAO;IACLD,IAAA;IACAE,QAAA;IACAf;EACF;AACF;AAMO,SAASgB,mBACdC,IAAA,EACAlB,OAAA,EACe;EACf,MAAMC,WAAA,GAAcD,OAAA,EAASE,GAAA,CAAI,cAAc;EAE/C,IAAI,CAACD,WAAA,EAAa;IAChB,OAAO;EACT;EAEA,MAAM,GAAG,GAAGI,UAAU,IAAIJ,WAAA,CAAYK,KAAA,CAAM,KAAK;EACjD,MAAMa,QAAA,GAAWd,UAAA,CACde,MAAA,CAAQC,CAAA,IAAMA,CAAA,CAAEC,UAAA,CAAW,WAAW,CAAC,EACvCC,GAAA,CAAKC,CAAA,IAAMA,CAAA,CAAEC,OAAA,CAAQ,cAAc,EAAE,CAAC,EAAE,CAAC;EAE5C,IAAI,CAACN,QAAA,EAAU;IACb,OAAO;EACT;EAEA,MAAMO,cAAA,GAAiB,IAAIC,MAAA,CAAO,MAAMR,QAAQ,EAAE;EAClD,MAAMS,MAAA,GAASV,IAAA,CACZZ,KAAA,CAAMoB,cAAc,EACpBN,MAAA,CAAQX,KAAA,IAAUA,KAAA,CAAMa,UAAA,CAAW,MAAM,KAAKb,KAAA,CAAMoB,QAAA,CAAS,MAAM,CAAC,EACpEN,GAAA,CAAKd,KAAA,IAAUA,KAAA,CAAMqB,SAAA,CAAU,EAAEL,OAAA,CAAQ,SAAS,EAAE,CAAC;EAExD,IAAI,CAACG,MAAA,CAAOG,MAAA,EAAQ;IAClB,OAAO;EACT;EAEA,MAAMC,UAAA,GAA0C,CAAC;EAEjD,IAAI;IACF,WAAWC,KAAA,IAASL,MAAA,EAAQ;MAC1B,MAAM,CAACM,cAAA,EAAgB,GAAGvB,IAAI,IAAIsB,KAAA,CAAM3B,KAAA,CAAM,UAAU;MACxD,MAAM6B,WAAA,GAAcxB,IAAA,CAAKE,IAAA,CAAK,UAAU;MACxC,MAAM;QAAEZ,WAAA,EAAAmC,YAAA;QAAapB,QAAA;QAAUF;MAAK,IAClChB,mBAAA,CAAoBoC,cAAc;MAEpC,MAAMG,KAAA,GACJrB,QAAA,KAAa,SACTmB,WAAA,GACA,IAAIG,IAAA,CAAK,CAACH,WAAW,GAAGnB,QAAA,EAAU;QAAEuB,IAAA,EAAMH;MAAY,CAAC;MAE7D,MAAMI,WAAA,GAAcR,UAAA,CAAWlB,IAAI;MAEnC,IAAI0B,WAAA,KAAgB,QAAW;QAC7BR,UAAA,CAAWlB,IAAI,IAAIuB,KAAA;MACrB,WAAWI,KAAA,CAAMC,OAAA,CAAQF,WAAW,GAAG;QACrCR,UAAA,CAAWlB,IAAI,IAAI,CAAC,GAAG0B,WAAA,EAAaH,KAAK;MAC3C,OAAO;QACLL,UAAA,CAAWlB,IAAI,IAAI,CAAC0B,WAAA,EAAaH,KAAK;MACxC;IACF;IAEA,OAAOL,UAAA;EACT,QAAQ;IACN,OAAO;EACT;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}