{"ast": null, "code": "/**\n * Produce the GraphQL query recommended for a full schema introspection.\n * Accepts optional IntrospectionOptions.\n */\nexport function getIntrospectionQuery(options) {\n  const optionsWithDefault = {\n    descriptions: true,\n    specifiedByUrl: false,\n    directiveIsRepeatable: false,\n    schemaDescription: false,\n    inputValueDeprecation: false,\n    oneOf: false,\n    ...options\n  };\n  const descriptions = optionsWithDefault.descriptions ? 'description' : '';\n  const specifiedByUrl = optionsWithDefault.specifiedByUrl ? 'specifiedByURL' : '';\n  const directiveIsRepeatable = optionsWithDefault.directiveIsRepeatable ? 'isRepeatable' : '';\n  const schemaDescription = optionsWithDefault.schemaDescription ? descriptions : '';\n  function inputDeprecation(str) {\n    return optionsWithDefault.inputValueDeprecation ? str : '';\n  }\n  const oneOf = optionsWithDefault.oneOf ? 'isOneOf' : '';\n  return `\n    query IntrospectionQuery {\n      __schema {\n        ${schemaDescription}\n        queryType { name kind }\n        mutationType { name kind }\n        subscriptionType { name kind }\n        types {\n          ...FullType\n        }\n        directives {\n          name\n          ${descriptions}\n          ${directiveIsRepeatable}\n          locations\n          args${inputDeprecation('(includeDeprecated: true)')} {\n            ...InputValue\n          }\n        }\n      }\n    }\n\n    fragment FullType on __Type {\n      kind\n      name\n      ${descriptions}\n      ${specifiedByUrl}\n      ${oneOf}\n      fields(includeDeprecated: true) {\n        name\n        ${descriptions}\n        args${inputDeprecation('(includeDeprecated: true)')} {\n          ...InputValue\n        }\n        type {\n          ...TypeRef\n        }\n        isDeprecated\n        deprecationReason\n      }\n      inputFields${inputDeprecation('(includeDeprecated: true)')} {\n        ...InputValue\n      }\n      interfaces {\n        ...TypeRef\n      }\n      enumValues(includeDeprecated: true) {\n        name\n        ${descriptions}\n        isDeprecated\n        deprecationReason\n      }\n      possibleTypes {\n        ...TypeRef\n      }\n    }\n\n    fragment InputValue on __InputValue {\n      name\n      ${descriptions}\n      type { ...TypeRef }\n      defaultValue\n      ${inputDeprecation('isDeprecated')}\n      ${inputDeprecation('deprecationReason')}\n    }\n\n    fragment TypeRef on __Type {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n                ofType {\n                  kind\n                  name\n                  ofType {\n                    kind\n                    name\n                    ofType {\n                      kind\n                      name\n                      ofType {\n                        kind\n                        name\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  `;\n}", "map": {"version": 3, "names": ["getIntrospectionQuery", "options", "optionsWithDefault", "descriptions", "specifiedByUrl", "directiveIsRepeatable", "schemaDescription", "inputValueDeprecation", "oneOf", "inputDeprecation", "str"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/utilities/getIntrospectionQuery.mjs"], "sourcesContent": ["/**\n * Produce the GraphQL query recommended for a full schema introspection.\n * Accepts optional IntrospectionOptions.\n */\nexport function getIntrospectionQuery(options) {\n  const optionsWithDefault = {\n    descriptions: true,\n    specifiedByUrl: false,\n    directiveIsRepeatable: false,\n    schemaDescription: false,\n    inputValueDeprecation: false,\n    oneOf: false,\n    ...options,\n  };\n  const descriptions = optionsWithDefault.descriptions ? 'description' : '';\n  const specifiedByUrl = optionsWithDefault.specifiedByUrl\n    ? 'specifiedByURL'\n    : '';\n  const directiveIsRepeatable = optionsWithDefault.directiveIsRepeatable\n    ? 'isRepeatable'\n    : '';\n  const schemaDescription = optionsWithDefault.schemaDescription\n    ? descriptions\n    : '';\n\n  function inputDeprecation(str) {\n    return optionsWithDefault.inputValueDeprecation ? str : '';\n  }\n\n  const oneOf = optionsWithDefault.oneOf ? 'isOneOf' : '';\n  return `\n    query IntrospectionQuery {\n      __schema {\n        ${schemaDescription}\n        queryType { name kind }\n        mutationType { name kind }\n        subscriptionType { name kind }\n        types {\n          ...FullType\n        }\n        directives {\n          name\n          ${descriptions}\n          ${directiveIsRepeatable}\n          locations\n          args${inputDeprecation('(includeDeprecated: true)')} {\n            ...InputValue\n          }\n        }\n      }\n    }\n\n    fragment FullType on __Type {\n      kind\n      name\n      ${descriptions}\n      ${specifiedByUrl}\n      ${oneOf}\n      fields(includeDeprecated: true) {\n        name\n        ${descriptions}\n        args${inputDeprecation('(includeDeprecated: true)')} {\n          ...InputValue\n        }\n        type {\n          ...TypeRef\n        }\n        isDeprecated\n        deprecationReason\n      }\n      inputFields${inputDeprecation('(includeDeprecated: true)')} {\n        ...InputValue\n      }\n      interfaces {\n        ...TypeRef\n      }\n      enumValues(includeDeprecated: true) {\n        name\n        ${descriptions}\n        isDeprecated\n        deprecationReason\n      }\n      possibleTypes {\n        ...TypeRef\n      }\n    }\n\n    fragment InputValue on __InputValue {\n      name\n      ${descriptions}\n      type { ...TypeRef }\n      defaultValue\n      ${inputDeprecation('isDeprecated')}\n      ${inputDeprecation('deprecationReason')}\n    }\n\n    fragment TypeRef on __Type {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n                ofType {\n                  kind\n                  name\n                  ofType {\n                    kind\n                    name\n                    ofType {\n                      kind\n                      name\n                      ofType {\n                        kind\n                        name\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  `;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,qBAAqBA,CAACC,OAAO,EAAE;EAC7C,MAAMC,kBAAkB,GAAG;IACzBC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,KAAK;IACrBC,qBAAqB,EAAE,KAAK;IAC5BC,iBAAiB,EAAE,KAAK;IACxBC,qBAAqB,EAAE,KAAK;IAC5BC,KAAK,EAAE,KAAK;IACZ,GAAGP;EACL,CAAC;EACD,MAAME,YAAY,GAAGD,kBAAkB,CAACC,YAAY,GAAG,aAAa,GAAG,EAAE;EACzE,MAAMC,cAAc,GAAGF,kBAAkB,CAACE,cAAc,GACpD,gBAAgB,GAChB,EAAE;EACN,MAAMC,qBAAqB,GAAGH,kBAAkB,CAACG,qBAAqB,GAClE,cAAc,GACd,EAAE;EACN,MAAMC,iBAAiB,GAAGJ,kBAAkB,CAACI,iBAAiB,GAC1DH,YAAY,GACZ,EAAE;EAEN,SAASM,gBAAgBA,CAACC,GAAG,EAAE;IAC7B,OAAOR,kBAAkB,CAACK,qBAAqB,GAAGG,GAAG,GAAG,EAAE;EAC5D;EAEA,MAAMF,KAAK,GAAGN,kBAAkB,CAACM,KAAK,GAAG,SAAS,GAAG,EAAE;EACvD,OAAO;AACT;AACA;AACA,UAAUF,iBAAiB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYH,YAAY;AACxB,YAAYE,qBAAqB;AACjC;AACA,gBAAgBI,gBAAgB,CAAC,2BAA2B,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQN,YAAY;AACpB,QAAQC,cAAc;AACtB,QAAQI,KAAK;AACb;AACA;AACA,UAAUL,YAAY;AACtB,cAAcM,gBAAgB,CAAC,2BAA2B,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBA,gBAAgB,CAAC,2BAA2B,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUN,YAAY;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQA,YAAY;AACpB;AACA;AACA,QAAQM,gBAAgB,CAAC,cAAc,CAAC;AACxC,QAAQA,gBAAgB,CAAC,mBAAmB,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}