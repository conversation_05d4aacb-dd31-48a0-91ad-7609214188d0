{"ast": null, "code": "import { truncateMessage } from './truncateMessage.mjs';\nasync function getPublicData(data) {\n  if (data instanceof Blob) {\n    const text = await data.text();\n    return `Blob(${truncateMessage(text)})`;\n  }\n  if (typeof data === \"object\" && \"byteLength\" in data) {\n    const text = new TextDecoder().decode(data);\n    return `ArrayBuffer(${truncateMessage(text)})`;\n  }\n  return truncateMessage(data);\n}\nexport { getPublicData };", "map": {"version": 3, "names": ["truncateMessage", "getPublicData", "data", "Blob", "text", "TextDecoder", "decode"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/ws/utils/getPublicData.ts"], "sourcesContent": ["import { WebSocketData } from '@mswjs/interceptors/WebSocket'\nimport { truncateMessage } from './truncateMessage'\n\nexport async function getPublicData(data: WebSocketData): Promise<string> {\n  if (data instanceof Blob) {\n    const text = await data.text()\n    return `Blob(${truncateMessage(text)})`\n  }\n\n  // Handle all ArrayBuffer-like objects.\n  if (typeof data === 'object' && 'byteLength' in data) {\n    const text = new TextDecoder().decode(data as ArrayBuffer)\n    return `ArrayBuffer(${truncateMessage(text)})`\n  }\n\n  return truncateMessage(data)\n}\n"], "mappings": "AACA,SAASA,eAAA,QAAuB;AAEhC,eAAsBC,cAAcC,IAAA,EAAsC;EACxE,IAAIA,IAAA,YAAgBC,IAAA,EAAM;IACxB,MAAMC,IAAA,GAAO,MAAMF,IAAA,CAAKE,IAAA,CAAK;IAC7B,OAAO,QAAQJ,eAAA,CAAgBI,IAAI,CAAC;EACtC;EAGA,IAAI,OAAOF,IAAA,KAAS,YAAY,gBAAgBA,IAAA,EAAM;IACpD,MAAME,IAAA,GAAO,IAAIC,WAAA,CAAY,EAAEC,MAAA,CAAOJ,IAAmB;IACzD,OAAO,eAAeF,eAAA,CAAgBI,IAAI,CAAC;EAC7C;EAEA,OAAOJ,eAAA,CAAgBE,IAAI;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}