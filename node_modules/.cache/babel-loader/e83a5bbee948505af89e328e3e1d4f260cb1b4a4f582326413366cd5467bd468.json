{"ast": null, "code": "import { groupBy } from '../../jsutils/groupBy.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique argument definition names\n *\n * A GraphQL Object or Interface type is only valid if all its fields have uniquely named arguments.\n * A GraphQL Directive is only valid if all its arguments are uniquely named.\n */\nexport function UniqueArgumentDefinitionNamesRule(context) {\n  return {\n    DirectiveDefinition(directiveNode) {\n      var _directiveNode$argume;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const argumentNodes = (_directiveNode$argume = directiveNode.arguments) !== null && _directiveNode$argume !== void 0 ? _directiveNode$argume : [];\n      return checkArgUniqueness(`@${directiveNode.name.value}`, argumentNodes);\n    },\n    InterfaceTypeDefinition: checkArgUniquenessPerField,\n    InterfaceTypeExtension: checkArgUniquenessPerField,\n    ObjectTypeDefinition: checkArgUniquenessPerField,\n    ObjectTypeExtension: checkArgUniquenessPerField\n  };\n  function checkArgUniquenessPerField(typeNode) {\n    var _typeNode$fields;\n    const typeName = typeNode.name.value; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n\n    const fieldNodes = (_typeNode$fields = typeNode.fields) !== null && _typeNode$fields !== void 0 ? _typeNode$fields : [];\n    for (const fieldDef of fieldNodes) {\n      var _fieldDef$arguments;\n      const fieldName = fieldDef.name.value; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n\n      const argumentNodes = (_fieldDef$arguments = fieldDef.arguments) !== null && _fieldDef$arguments !== void 0 ? _fieldDef$arguments : [];\n      checkArgUniqueness(`${typeName}.${fieldName}`, argumentNodes);\n    }\n    return false;\n  }\n  function checkArgUniqueness(parentName, argumentNodes) {\n    const seenArgs = groupBy(argumentNodes, arg => arg.name.value);\n    for (const [argName, argNodes] of seenArgs) {\n      if (argNodes.length > 1) {\n        context.reportError(new GraphQLError(`Argument \"${parentName}(${argName}:)\" can only be defined once.`, {\n          nodes: argNodes.map(node => node.name)\n        }));\n      }\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["groupBy", "GraphQLError", "UniqueArgumentDefinitionNamesRule", "context", "DirectiveDefinition", "directiveNode", "_directiveNode$argume", "argumentNodes", "arguments", "checkArgUniqueness", "name", "value", "InterfaceTypeDefinition", "checkArgUniquenessPerField", "InterfaceTypeExtension", "ObjectTypeDefinition", "ObjectTypeExtension", "typeNode", "_typeNode$fields", "typeName", "fieldNodes", "fields", "fieldDef", "_fieldDef$arguments", "fieldName", "parentName", "<PERSON><PERSON><PERSON><PERSON>", "arg", "argName", "argNodes", "length", "reportError", "nodes", "map", "node"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/UniqueArgumentDefinitionNamesRule.mjs"], "sourcesContent": ["import { groupBy } from '../../jsutils/groupBy.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique argument definition names\n *\n * A GraphQL Object or Interface type is only valid if all its fields have uniquely named arguments.\n * A GraphQL Directive is only valid if all its arguments are uniquely named.\n */\nexport function UniqueArgumentDefinitionNamesRule(context) {\n  return {\n    DirectiveDefinition(directiveNode) {\n      var _directiveNode$argume;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const argumentNodes =\n        (_directiveNode$argume = directiveNode.arguments) !== null &&\n        _directiveNode$argume !== void 0\n          ? _directiveNode$argume\n          : [];\n      return checkArgUniqueness(`@${directiveNode.name.value}`, argumentNodes);\n    },\n\n    InterfaceTypeDefinition: checkArgUniquenessPerField,\n    InterfaceTypeExtension: checkArgUniquenessPer<PERSON>ield,\n    ObjectTypeDefinition: checkArgUniquenessPerField,\n    ObjectTypeExtension: checkArgUniquenessPerField,\n  };\n\n  function checkArgUniquenessPerField(typeNode) {\n    var _typeNode$fields;\n\n    const typeName = typeNode.name.value; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n\n    const fieldNodes =\n      (_typeNode$fields = typeNode.fields) !== null &&\n      _typeNode$fields !== void 0\n        ? _typeNode$fields\n        : [];\n\n    for (const fieldDef of fieldNodes) {\n      var _fieldDef$arguments;\n\n      const fieldName = fieldDef.name.value; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n\n      const argumentNodes =\n        (_fieldDef$arguments = fieldDef.arguments) !== null &&\n        _fieldDef$arguments !== void 0\n          ? _fieldDef$arguments\n          : [];\n      checkArgUniqueness(`${typeName}.${fieldName}`, argumentNodes);\n    }\n\n    return false;\n  }\n\n  function checkArgUniqueness(parentName, argumentNodes) {\n    const seenArgs = groupBy(argumentNodes, (arg) => arg.name.value);\n\n    for (const [argName, argNodes] of seenArgs) {\n      if (argNodes.length > 1) {\n        context.reportError(\n          new GraphQLError(\n            `Argument \"${parentName}(${argName}:)\" can only be defined once.`,\n            {\n              nodes: argNodes.map((node) => node.name),\n            },\n          ),\n        );\n      }\n    }\n\n    return false;\n  }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iCAAiCA,CAACC,OAAO,EAAE;EACzD,OAAO;IACLC,mBAAmBA,CAACC,aAAa,EAAE;MACjC,IAAIC,qBAAqB;;MAEzB;;MAEA;MACA,MAAMC,aAAa,GACjB,CAACD,qBAAqB,GAAGD,aAAa,CAACG,SAAS,MAAM,IAAI,IAC1DF,qBAAqB,KAAK,KAAK,CAAC,GAC5BA,qBAAqB,GACrB,EAAE;MACR,OAAOG,kBAAkB,CAAC,IAAIJ,aAAa,CAACK,IAAI,CAACC,KAAK,EAAE,EAAEJ,aAAa,CAAC;IAC1E,CAAC;IAEDK,uBAAuB,EAAEC,0BAA0B;IACnDC,sBAAsB,EAAED,0BAA0B;IAClDE,oBAAoB,EAAEF,0BAA0B;IAChDG,mBAAmB,EAAEH;EACvB,CAAC;EAED,SAASA,0BAA0BA,CAACI,QAAQ,EAAE;IAC5C,IAAIC,gBAAgB;IAEpB,MAAMC,QAAQ,GAAGF,QAAQ,CAACP,IAAI,CAACC,KAAK,CAAC,CAAC;;IAEtC;;IAEA,MAAMS,UAAU,GACd,CAACF,gBAAgB,GAAGD,QAAQ,CAACI,MAAM,MAAM,IAAI,IAC7CH,gBAAgB,KAAK,KAAK,CAAC,GACvBA,gBAAgB,GAChB,EAAE;IAER,KAAK,MAAMI,QAAQ,IAAIF,UAAU,EAAE;MACjC,IAAIG,mBAAmB;MAEvB,MAAMC,SAAS,GAAGF,QAAQ,CAACZ,IAAI,CAACC,KAAK,CAAC,CAAC;;MAEvC;;MAEA,MAAMJ,aAAa,GACjB,CAACgB,mBAAmB,GAAGD,QAAQ,CAACd,SAAS,MAAM,IAAI,IACnDe,mBAAmB,KAAK,KAAK,CAAC,GAC1BA,mBAAmB,GACnB,EAAE;MACRd,kBAAkB,CAAC,GAAGU,QAAQ,IAAIK,SAAS,EAAE,EAAEjB,aAAa,CAAC;IAC/D;IAEA,OAAO,KAAK;EACd;EAEA,SAASE,kBAAkBA,CAACgB,UAAU,EAAElB,aAAa,EAAE;IACrD,MAAMmB,QAAQ,GAAG1B,OAAO,CAACO,aAAa,EAAGoB,GAAG,IAAKA,GAAG,CAACjB,IAAI,CAACC,KAAK,CAAC;IAEhE,KAAK,MAAM,CAACiB,OAAO,EAAEC,QAAQ,CAAC,IAAIH,QAAQ,EAAE;MAC1C,IAAIG,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACvB3B,OAAO,CAAC4B,WAAW,CACjB,IAAI9B,YAAY,CACd,aAAawB,UAAU,IAAIG,OAAO,+BAA+B,EACjE;UACEI,KAAK,EAAEH,QAAQ,CAACI,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACxB,IAAI;QACzC,CACF,CACF,CAAC;MACH;IACF;IAEA,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}