{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/SetupScope.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Typography, TextField, FormControl, InputLabel, Select, MenuItem, Box, Grid, Paper, List, ListItem, ListItemText, Button, IconButton, Chip } from '@mui/material';\nimport { ArrowForward, ArrowBack, Delete } from '@mui/icons-material';\nimport { updateBIAField } from '../../store/biaSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SetupScope = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    currentBIA\n  } = useSelector(state => state.bia);\n  const [users, setUsers] = useState([]);\n  const [functions, setFunctions] = useState([]);\n  const [availableProcesses, setAvailableProcesses] = useState([]);\n  const [selectedProcesses, setSelectedProcesses] = useState([]);\n  useEffect(() => {\n    // Fetch master data\n    fetch('/api/users').then(r => r.json()).then(setUsers);\n    fetch('/api/functions').then(r => r.json()).then(setFunctions);\n  }, []);\n  useEffect(() => {\n    if (currentBIA !== null && currentBIA !== void 0 && currentBIA.function) {\n      fetch(`/api/processes/${currentBIA.function}`).then(r => r.json()).then(setAvailableProcesses);\n    }\n  }, [currentBIA === null || currentBIA === void 0 ? void 0 : currentBIA.function]);\n  useEffect(() => {\n    if (currentBIA !== null && currentBIA !== void 0 && currentBIA.processes) {\n      setSelectedProcesses(currentBIA.processes);\n    }\n  }, [currentBIA === null || currentBIA === void 0 ? void 0 : currentBIA.processes]);\n  const handleFieldChange = (field, value) => {\n    dispatch(updateBIAField({\n      field,\n      value\n    }));\n    if (field === 'function') {\n      // Reset processes when function changes\n      dispatch(updateBIAField({\n        field: 'processes',\n        value: []\n      }));\n      setSelectedProcesses([]);\n    }\n  };\n  const moveToSelected = processId => {\n    const newSelected = [...selectedProcesses, processId];\n    setSelectedProcesses(newSelected);\n    dispatch(updateBIAField({\n      field: 'processes',\n      value: newSelected\n    }));\n  };\n  const removeFromSelected = processId => {\n    const newSelected = selectedProcesses.filter(id => id !== processId);\n    setSelectedProcesses(newSelected);\n    dispatch(updateBIAField({\n      field: 'processes',\n      value: newSelected\n    }));\n  };\n  const getProcessName = processId => {\n    const process = availableProcesses.find(p => p.id === processId);\n    return (process === null || process === void 0 ? void 0 : process.name) || processId;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: \"Setup & Scope\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"BIA Name\",\n          value: (currentBIA === null || currentBIA === void 0 ? void 0 : currentBIA.name) || '',\n          onChange: e => handleFieldChange('name', e.target.value),\n          margin: \"normal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Function/Department\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: (currentBIA === null || currentBIA === void 0 ? void 0 : currentBIA.function) || '',\n            onChange: e => handleFieldChange('function', e.target.value),\n            children: functions.map(func => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: func.id,\n              children: func.name\n            }, func.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"BIA Owner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: (currentBIA === null || currentBIA === void 0 ? void 0 : currentBIA.owner) || '',\n            onChange: e => handleFieldChange('owner', e.target.value),\n            children: users.map(user => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: user.id,\n              children: user.name\n            }, user.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            mt: 3\n          },\n          children: \"Processes to Analyze\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 5,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2,\n                height: 300,\n                overflow: 'auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                gutterBottom: true,\n                children: \"Available Processes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                dense: true,\n                children: availableProcesses.filter(process => !selectedProcesses.includes(process.id)).map(process => /*#__PURE__*/_jsxDEV(ListItem, {\n                  secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                    edge: \"end\",\n                    onClick: () => moveToSelected(process.id),\n                    children: /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 25\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: process.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 23\n                  }, this)\n                }, process.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 2,\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                align: \"center\",\n                children: \"Select processes to move them to analysis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 5,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2,\n                height: 300,\n                overflow: 'auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                gutterBottom: true,\n                children: [\"Selected Processes (\", selectedProcesses.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                dense: true,\n                children: selectedProcesses.map(processId => /*#__PURE__*/_jsxDEV(ListItem, {\n                  secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                    edge: \"end\",\n                    onClick: () => removeFromSelected(processId),\n                    children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 25\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: getProcessName(processId)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this)\n                }, processId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(SetupScope, \"e75y1RwkhNMi572sZNRfpIclDn0=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = SetupScope;\nexport default SetupScope;\nvar _c;\n$RefreshReg$(_c, \"SetupScope\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Typography", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Box", "Grid", "Paper", "List", "ListItem", "ListItemText", "<PERSON><PERSON>", "IconButton", "Chip", "ArrowForward", "ArrowBack", "Delete", "updateBIAField", "jsxDEV", "_jsxDEV", "SetupScope", "_s", "dispatch", "currentBIA", "state", "bia", "users", "setUsers", "functions", "setFunctions", "availableProcesses", "setAvailableProcesses", "selectedProcesses", "setSelectedProcesses", "fetch", "then", "r", "json", "function", "processes", "handleFieldChange", "field", "value", "moveToSelected", "processId", "newSelected", "removeFromSelected", "filter", "id", "getProcessName", "process", "find", "p", "name", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "item", "xs", "md", "fullWidth", "label", "onChange", "e", "target", "margin", "map", "func", "owner", "user", "sx", "mt", "height", "overflow", "dense", "includes", "secondaryAction", "edge", "onClick", "primary", "display", "alignItems", "justifyContent", "align", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/SetupScope.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport {\n  Typography, TextField, FormControl, InputLabel, Select, MenuItem,\n  Box, Grid, Paper, List, ListItem, ListItemText, Button,\n  IconButton, Chip\n} from '@mui/material';\nimport { <PERSON>For<PERSON>, ArrowBack, Delete } from '@mui/icons-material';\nimport { updateBIAField } from '../../store/biaSlice';\n\nconst SetupScope = () => {\n  const dispatch = useDispatch();\n  const { currentBIA } = useSelector(state => state.bia);\n  const [users, setUsers] = useState([]);\n  const [functions, setFunctions] = useState([]);\n  const [availableProcesses, setAvailableProcesses] = useState([]);\n  const [selectedProcesses, setSelectedProcesses] = useState([]);\n\n  useEffect(() => {\n    // Fetch master data\n    fetch('/api/users').then(r => r.json()).then(setUsers);\n    fetch('/api/functions').then(r => r.json()).then(setFunctions);\n  }, []);\n\n  useEffect(() => {\n    if (currentBIA?.function) {\n      fetch(`/api/processes/${currentBIA.function}`)\n        .then(r => r.json())\n        .then(setAvailableProcesses);\n    }\n  }, [currentBIA?.function]);\n\n  useEffect(() => {\n    if (currentBIA?.processes) {\n      setSelectedProcesses(currentBIA.processes);\n    }\n  }, [currentBIA?.processes]);\n\n  const handleFieldChange = (field, value) => {\n    dispatch(updateBIAField({ field, value }));\n    \n    if (field === 'function') {\n      // Reset processes when function changes\n      dispatch(updateBIAField({ field: 'processes', value: [] }));\n      setSelectedProcesses([]);\n    }\n  };\n\n  const moveToSelected = (processId) => {\n    const newSelected = [...selectedProcesses, processId];\n    setSelectedProcesses(newSelected);\n    dispatch(updateBIAField({ field: 'processes', value: newSelected }));\n  };\n\n  const removeFromSelected = (processId) => {\n    const newSelected = selectedProcesses.filter(id => id !== processId);\n    setSelectedProcesses(newSelected);\n    dispatch(updateBIAField({ field: 'processes', value: newSelected }));\n  };\n\n  const getProcessName = (processId) => {\n    const process = availableProcesses.find(p => p.id === processId);\n    return process?.name || processId;\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h5\" gutterBottom>\n        Setup & Scope\n      </Typography>\n      \n      <Grid container spacing={3}>\n        <Grid item xs={12} md={6}>\n          <TextField\n            fullWidth\n            label=\"BIA Name\"\n            value={currentBIA?.name || ''}\n            onChange={(e) => handleFieldChange('name', e.target.value)}\n            margin=\"normal\"\n          />\n\n          <FormControl fullWidth margin=\"normal\">\n            <InputLabel>Function/Department</InputLabel>\n            <Select\n              value={currentBIA?.function || ''}\n              onChange={(e) => handleFieldChange('function', e.target.value)}\n            >\n              {functions.map(func => (\n                <MenuItem key={func.id} value={func.id}>\n                  {func.name}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n\n          <FormControl fullWidth margin=\"normal\">\n            <InputLabel>BIA Owner</InputLabel>\n            <Select\n              value={currentBIA?.owner || ''}\n              onChange={(e) => handleFieldChange('owner', e.target.value)}\n            >\n              {users.map(user => (\n                <MenuItem key={user.id} value={user.id}>\n                  {user.name}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </Grid>\n\n        <Grid item xs={12}>\n          <Typography variant=\"h6\" gutterBottom sx={{ mt: 3 }}>\n            Processes to Analyze\n          </Typography>\n          \n          <Grid container spacing={2}>\n            {/* Available Processes */}\n            <Grid item xs={5}>\n              <Paper sx={{ p: 2, height: 300, overflow: 'auto' }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Available Processes\n                </Typography>\n                <List dense>\n                  {availableProcesses\n                    .filter(process => !selectedProcesses.includes(process.id))\n                    .map(process => (\n                    <ListItem\n                      key={process.id}\n                      secondaryAction={\n                        <IconButton \n                          edge=\"end\" \n                          onClick={() => moveToSelected(process.id)}\n                        >\n                          <ArrowForward />\n                        </IconButton>\n                      }\n                    >\n                      <ListItemText primary={process.name} />\n                    </ListItem>\n                  ))}\n                </List>\n              </Paper>\n            </Grid>\n\n            {/* Controls */}\n            <Grid item xs={2} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n              <Box>\n                <Typography variant=\"body2\" align=\"center\">\n                  Select processes to move them to analysis\n                </Typography>\n              </Box>\n            </Grid>\n\n            {/* Selected Processes */}\n            <Grid item xs={5}>\n              <Paper sx={{ p: 2, height: 300, overflow: 'auto' }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Selected Processes ({selectedProcesses.length})\n                </Typography>\n                <List dense>\n                  {selectedProcesses.map(processId => (\n                    <ListItem\n                      key={processId}\n                      secondaryAction={\n                        <IconButton \n                          edge=\"end\" \n                          onClick={() => removeFromSelected(processId)}\n                        >\n                          <Delete />\n                        </IconButton>\n                      }\n                    >\n                      <ListItemText primary={getProcessName(processId)} />\n                    </ListItem>\n                  ))}\n                </List>\n              </Paper>\n            </Grid>\n          </Grid>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default SetupScope;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAChEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EACtDC,UAAU,EAAEC,IAAI,QACX,eAAe;AACtB,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,QAAQ,qBAAqB;AACrE,SAASC,cAAc,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE0B;EAAW,CAAC,GAAGzB,WAAW,CAAC0B,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EACtD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAE9DD,SAAS,CAAC,MAAM;IACd;IACAuC,KAAK,CAAC,YAAY,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACF,IAAI,CAACR,QAAQ,CAAC;IACtDO,KAAK,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACF,IAAI,CAACN,YAAY,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;EAENlC,SAAS,CAAC,MAAM;IACd,IAAI4B,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEe,QAAQ,EAAE;MACxBJ,KAAK,CAAC,kBAAkBX,UAAU,CAACe,QAAQ,EAAE,CAAC,CAC3CH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CACnBF,IAAI,CAACJ,qBAAqB,CAAC;IAChC;EACF,CAAC,EAAE,CAACR,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEe,QAAQ,CAAC,CAAC;EAE1B3C,SAAS,CAAC,MAAM;IACd,IAAI4B,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEgB,SAAS,EAAE;MACzBN,oBAAoB,CAACV,UAAU,CAACgB,SAAS,CAAC;IAC5C;EACF,CAAC,EAAE,CAAChB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEgB,SAAS,CAAC,CAAC;EAE3B,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CpB,QAAQ,CAACL,cAAc,CAAC;MAAEwB,KAAK;MAAEC;IAAM,CAAC,CAAC,CAAC;IAE1C,IAAID,KAAK,KAAK,UAAU,EAAE;MACxB;MACAnB,QAAQ,CAACL,cAAc,CAAC;QAAEwB,KAAK,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAG,CAAC,CAAC,CAAC;MAC3DT,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;EAED,MAAMU,cAAc,GAAIC,SAAS,IAAK;IACpC,MAAMC,WAAW,GAAG,CAAC,GAAGb,iBAAiB,EAAEY,SAAS,CAAC;IACrDX,oBAAoB,CAACY,WAAW,CAAC;IACjCvB,QAAQ,CAACL,cAAc,CAAC;MAAEwB,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAEG;IAAY,CAAC,CAAC,CAAC;EACtE,CAAC;EAED,MAAMC,kBAAkB,GAAIF,SAAS,IAAK;IACxC,MAAMC,WAAW,GAAGb,iBAAiB,CAACe,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKJ,SAAS,CAAC;IACpEX,oBAAoB,CAACY,WAAW,CAAC;IACjCvB,QAAQ,CAACL,cAAc,CAAC;MAAEwB,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAEG;IAAY,CAAC,CAAC,CAAC;EACtE,CAAC;EAED,MAAMI,cAAc,GAAIL,SAAS,IAAK;IACpC,MAAMM,OAAO,GAAGpB,kBAAkB,CAACqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKJ,SAAS,CAAC;IAChE,OAAO,CAAAM,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,IAAI,KAAIT,SAAS;EACnC,CAAC;EAED,oBACEzB,OAAA,CAACd,GAAG;IAAAiD,QAAA,gBACFnC,OAAA,CAACpB,UAAU;MAACwD,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbzC,OAAA,CAACb,IAAI;MAACuD,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAR,QAAA,gBACzBnC,OAAA,CAACb,IAAI;QAACyD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACvBnC,OAAA,CAACnB,SAAS;UACRkE,SAAS;UACTC,KAAK,EAAC,UAAU;UAChBzB,KAAK,EAAE,CAAAnB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE8B,IAAI,KAAI,EAAG;UAC9Be,QAAQ,EAAGC,CAAC,IAAK7B,iBAAiB,CAAC,MAAM,EAAE6B,CAAC,CAACC,MAAM,CAAC5B,KAAK,CAAE;UAC3D6B,MAAM,EAAC;QAAQ;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEFzC,OAAA,CAAClB,WAAW;UAACiE,SAAS;UAACK,MAAM,EAAC,QAAQ;UAAAjB,QAAA,gBACpCnC,OAAA,CAACjB,UAAU;YAAAoD,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5CzC,OAAA,CAAChB,MAAM;YACLuC,KAAK,EAAE,CAAAnB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEe,QAAQ,KAAI,EAAG;YAClC8B,QAAQ,EAAGC,CAAC,IAAK7B,iBAAiB,CAAC,UAAU,EAAE6B,CAAC,CAACC,MAAM,CAAC5B,KAAK,CAAE;YAAAY,QAAA,EAE9D1B,SAAS,CAAC4C,GAAG,CAACC,IAAI,iBACjBtD,OAAA,CAACf,QAAQ;cAAesC,KAAK,EAAE+B,IAAI,CAACzB,EAAG;cAAAM,QAAA,EACpCmB,IAAI,CAACpB;YAAI,GADGoB,IAAI,CAACzB,EAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEdzC,OAAA,CAAClB,WAAW;UAACiE,SAAS;UAACK,MAAM,EAAC,QAAQ;UAAAjB,QAAA,gBACpCnC,OAAA,CAACjB,UAAU;YAAAoD,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClCzC,OAAA,CAAChB,MAAM;YACLuC,KAAK,EAAE,CAAAnB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEmD,KAAK,KAAI,EAAG;YAC/BN,QAAQ,EAAGC,CAAC,IAAK7B,iBAAiB,CAAC,OAAO,EAAE6B,CAAC,CAACC,MAAM,CAAC5B,KAAK,CAAE;YAAAY,QAAA,EAE3D5B,KAAK,CAAC8C,GAAG,CAACG,IAAI,iBACbxD,OAAA,CAACf,QAAQ;cAAesC,KAAK,EAAEiC,IAAI,CAAC3B,EAAG;cAAAM,QAAA,EACpCqB,IAAI,CAACtB;YAAI,GADGsB,IAAI,CAAC3B,EAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPzC,OAAA,CAACb,IAAI;QAACyD,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAV,QAAA,gBAChBnC,OAAA,CAACpB,UAAU;UAACwD,OAAO,EAAC,IAAI;UAACC,YAAY;UAACoB,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAvB,QAAA,EAAC;QAErD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbzC,OAAA,CAACb,IAAI;UAACuD,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAR,QAAA,gBAEzBnC,OAAA,CAACb,IAAI;YAACyD,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACfnC,OAAA,CAACZ,KAAK;cAACqE,EAAE,EAAE;gBAAExB,CAAC,EAAE,CAAC;gBAAE0B,MAAM,EAAE,GAAG;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,gBACjDnC,OAAA,CAACpB,UAAU;gBAACwD,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAACX,IAAI;gBAACwE,KAAK;gBAAA1B,QAAA,EACRxB,kBAAkB,CAChBiB,MAAM,CAACG,OAAO,IAAI,CAAClB,iBAAiB,CAACiD,QAAQ,CAAC/B,OAAO,CAACF,EAAE,CAAC,CAAC,CAC1DwB,GAAG,CAACtB,OAAO,iBACZ/B,OAAA,CAACV,QAAQ;kBAEPyE,eAAe,eACb/D,OAAA,CAACP,UAAU;oBACTuE,IAAI,EAAC,KAAK;oBACVC,OAAO,EAAEA,CAAA,KAAMzC,cAAc,CAACO,OAAO,CAACF,EAAE,CAAE;oBAAAM,QAAA,eAE1CnC,OAAA,CAACL,YAAY;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACb;kBAAAN,QAAA,eAEDnC,OAAA,CAACT,YAAY;oBAAC2E,OAAO,EAAEnC,OAAO,CAACG;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC,GAVlCV,OAAO,CAACF,EAAE;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWP,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGPzC,OAAA,CAACb,IAAI;YAACyD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACY,EAAE,EAAE;cAAEU,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,cAAc,EAAE;YAAS,CAAE;YAAAlC,QAAA,eACxFnC,OAAA,CAACd,GAAG;cAAAiD,QAAA,eACFnC,OAAA,CAACpB,UAAU;gBAACwD,OAAO,EAAC,OAAO;gBAACkC,KAAK,EAAC,QAAQ;gBAAAnC,QAAA,EAAC;cAE3C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPzC,OAAA,CAACb,IAAI;YAACyD,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACfnC,OAAA,CAACZ,KAAK;cAACqE,EAAE,EAAE;gBAAExB,CAAC,EAAE,CAAC;gBAAE0B,MAAM,EAAE,GAAG;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,gBACjDnC,OAAA,CAACpB,UAAU;gBAACwD,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAAAF,QAAA,GAAC,sBACvB,EAACtB,iBAAiB,CAAC0D,MAAM,EAAC,GAChD;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAACX,IAAI;gBAACwE,KAAK;gBAAA1B,QAAA,EACRtB,iBAAiB,CAACwC,GAAG,CAAC5B,SAAS,iBAC9BzB,OAAA,CAACV,QAAQ;kBAEPyE,eAAe,eACb/D,OAAA,CAACP,UAAU;oBACTuE,IAAI,EAAC,KAAK;oBACVC,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAACF,SAAS,CAAE;oBAAAU,QAAA,eAE7CnC,OAAA,CAACH,MAAM;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CACb;kBAAAN,QAAA,eAEDnC,OAAA,CAACT,YAAY;oBAAC2E,OAAO,EAAEpC,cAAc,CAACL,SAAS;kBAAE;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC,GAV/ChB,SAAS;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWN,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACvC,EAAA,CA7KID,UAAU;EAAA,QACGvB,WAAW,EACLC,WAAW;AAAA;AAAA6F,EAAA,GAF9BvE,UAAU;AA+KhB,eAAeA,UAAU;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}