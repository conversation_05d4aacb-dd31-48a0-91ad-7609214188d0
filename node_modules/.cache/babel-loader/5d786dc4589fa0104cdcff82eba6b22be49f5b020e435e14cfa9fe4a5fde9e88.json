{"ast": null, "code": "/**\n * Returns the first argument it receives.\n */\nexport function identityFunc(x) {\n  return x;\n}", "map": {"version": 3, "names": ["identityFunc", "x"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/jsutils/identityFunc.mjs"], "sourcesContent": ["/**\n * Returns the first argument it receives.\n */\nexport function identityFunc(x) {\n  return x;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,YAAYA,CAACC,CAAC,EAAE;EAC9B,OAAOA,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}