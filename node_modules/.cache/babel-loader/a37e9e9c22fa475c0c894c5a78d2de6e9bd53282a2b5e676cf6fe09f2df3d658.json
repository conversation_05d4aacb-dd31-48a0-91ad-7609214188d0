{"ast": null, "code": "import { keyValMap } from '../jsutils/keyValMap.mjs';\nimport { Kind } from '../language/kinds.mjs';\n/**\n * Produces a JavaScript value given a GraphQL Value AST.\n *\n * Unlike `valueFromAST()`, no type is provided. The resulting JavaScript value\n * will reflect the provided GraphQL value AST.\n *\n * | GraphQL Value        | JavaScript Value |\n * | -------------------- | ---------------- |\n * | Input Object         | Object           |\n * | List                 | Array            |\n * | Boolean              | Boolean          |\n * | String / Enum        | String           |\n * | Int / Float          | Number           |\n * | Null                 | null             |\n *\n */\n\nexport function valueFromASTUntyped(valueNode, variables) {\n  switch (valueNode.kind) {\n    case Kind.NULL:\n      return null;\n    case Kind.INT:\n      return parseInt(valueNode.value, 10);\n    case Kind.FLOAT:\n      return parseFloat(valueNode.value);\n    case Kind.STRING:\n    case Kind.ENUM:\n    case Kind.BOOLEAN:\n      return valueNode.value;\n    case Kind.LIST:\n      return valueNode.values.map(node => valueFromASTUntyped(node, variables));\n    case Kind.OBJECT:\n      return keyValMap(valueNode.fields, field => field.name.value, field => valueFromASTUntyped(field.value, variables));\n    case Kind.VARIABLE:\n      return variables === null || variables === void 0 ? void 0 : variables[valueNode.name.value];\n  }\n}", "map": {"version": 3, "names": ["keyValMap", "Kind", "valueFromASTUntyped", "valueNode", "variables", "kind", "NULL", "INT", "parseInt", "value", "FLOAT", "parseFloat", "STRING", "ENUM", "BOOLEAN", "LIST", "values", "map", "node", "OBJECT", "fields", "field", "name", "VARIABLE"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/utilities/valueFromASTUntyped.mjs"], "sourcesContent": ["import { keyValMap } from '../jsutils/keyValMap.mjs';\nimport { Kind } from '../language/kinds.mjs';\n/**\n * Produces a JavaScript value given a GraphQL Value AST.\n *\n * Unlike `valueFromAST()`, no type is provided. The resulting JavaScript value\n * will reflect the provided GraphQL value AST.\n *\n * | GraphQL Value        | JavaScript Value |\n * | -------------------- | ---------------- |\n * | Input Object         | Object           |\n * | List                 | Array            |\n * | Boolean              | Boolean          |\n * | String / Enum        | String           |\n * | Int / Float          | Number           |\n * | Null                 | null             |\n *\n */\n\nexport function valueFromASTUntyped(valueNode, variables) {\n  switch (valueNode.kind) {\n    case Kind.NULL:\n      return null;\n\n    case Kind.INT:\n      return parseInt(valueNode.value, 10);\n\n    case Kind.FLOAT:\n      return parseFloat(valueNode.value);\n\n    case Kind.STRING:\n    case Kind.ENUM:\n    case Kind.BOOLEAN:\n      return valueNode.value;\n\n    case Kind.LIST:\n      return valueNode.values.map((node) =>\n        valueFromASTUntyped(node, variables),\n      );\n\n    case Kind.OBJECT:\n      return keyValMap(\n        valueNode.fields,\n        (field) => field.name.value,\n        (field) => valueFromASTUntyped(field.value, variables),\n      );\n\n    case Kind.VARIABLE:\n      return variables === null || variables === void 0\n        ? void 0\n        : variables[valueNode.name.value];\n  }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,IAAI,QAAQ,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,SAAS,EAAE;EACxD,QAAQD,SAAS,CAACE,IAAI;IACpB,KAAKJ,IAAI,CAACK,IAAI;MACZ,OAAO,IAAI;IAEb,KAAKL,IAAI,CAACM,GAAG;MACX,OAAOC,QAAQ,CAACL,SAAS,CAACM,KAAK,EAAE,EAAE,CAAC;IAEtC,KAAKR,IAAI,CAACS,KAAK;MACb,OAAOC,UAAU,CAACR,SAAS,CAACM,KAAK,CAAC;IAEpC,KAAKR,IAAI,CAACW,MAAM;IAChB,KAAKX,IAAI,CAACY,IAAI;IACd,KAAKZ,IAAI,CAACa,OAAO;MACf,OAAOX,SAAS,CAACM,KAAK;IAExB,KAAKR,IAAI,CAACc,IAAI;MACZ,OAAOZ,SAAS,CAACa,MAAM,CAACC,GAAG,CAAEC,IAAI,IAC/BhB,mBAAmB,CAACgB,IAAI,EAAEd,SAAS,CACrC,CAAC;IAEH,KAAKH,IAAI,CAACkB,MAAM;MACd,OAAOnB,SAAS,CACdG,SAAS,CAACiB,MAAM,EACfC,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACb,KAAK,EAC1BY,KAAK,IAAKnB,mBAAmB,CAACmB,KAAK,CAACZ,KAAK,EAAEL,SAAS,CACvD,CAAC;IAEH,KAAKH,IAAI,CAACsB,QAAQ;MAChB,OAAOnB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAC7C,KAAK,CAAC,GACNA,SAAS,CAACD,SAAS,CAACmB,IAAI,CAACb,KAAK,CAAC;EACvC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}