{"ast": null, "code": "/**\n * This function transforms a JS object `ObjMap<Promise<T>>` into\n * a `Promise<ObjMap<T>>`\n *\n * This is akin to bluebird's `Promise.props`, but implemented only using\n * `Promise.all` so it will work with any implementation of ES6 promises.\n */\nexport function promiseForObject(object) {\n  return Promise.all(Object.values(object)).then(resolvedValues => {\n    const resolvedObject = Object.create(null);\n    for (const [i, key] of Object.keys(object).entries()) {\n      resolvedObject[key] = resolvedValues[i];\n    }\n    return resolvedObject;\n  });\n}", "map": {"version": 3, "names": ["promiseForObject", "object", "Promise", "all", "Object", "values", "then", "resolvedV<PERSON>ues", "resolvedObject", "create", "i", "key", "keys", "entries"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/jsutils/promiseForObject.mjs"], "sourcesContent": ["/**\n * This function transforms a JS object `ObjMap<Promise<T>>` into\n * a `Promise<ObjMap<T>>`\n *\n * This is akin to bluebird's `Promise.props`, but implemented only using\n * `Promise.all` so it will work with any implementation of ES6 promises.\n */\nexport function promiseForObject(object) {\n  return Promise.all(Object.values(object)).then((resolvedValues) => {\n    const resolvedObject = Object.create(null);\n\n    for (const [i, key] of Object.keys(object).entries()) {\n      resolvedObject[key] = resolvedValues[i];\n    }\n\n    return resolvedObject;\n  });\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,gBAAgBA,CAACC,MAAM,EAAE;EACvC,OAAOC,OAAO,CAACC,GAAG,CAACC,MAAM,CAACC,MAAM,CAACJ,MAAM,CAAC,CAAC,CAACK,IAAI,CAAEC,cAAc,IAAK;IACjE,MAAMC,cAAc,GAAGJ,MAAM,CAACK,MAAM,CAAC,IAAI,CAAC;IAE1C,KAAK,MAAM,CAACC,CAAC,EAAEC,GAAG,CAAC,IAAIP,MAAM,CAACQ,IAAI,CAACX,MAAM,CAAC,CAACY,OAAO,CAAC,CAAC,EAAE;MACpDL,cAAc,CAACG,GAAG,CAAC,GAAGJ,cAAc,CAACG,CAAC,CAAC;IACzC;IAEA,OAAOF,cAAc;EACvB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}