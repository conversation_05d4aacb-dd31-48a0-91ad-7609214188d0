{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Known argument names\n *\n * A GraphQL field is only valid if all supplied arguments are defined by\n * that field.\n *\n * See https://spec.graphql.org/draft/#sec-Argument-Names\n * See https://spec.graphql.org/draft/#sec-Directives-Are-In-Valid-Locations\n */\nexport function KnownArgumentNamesRule(context) {\n  return {\n    // eslint-disable-next-line new-cap\n    ...KnownArgumentNamesOnDirectivesRule(context),\n    Argument(argNode) {\n      const argDef = context.getArgument();\n      const fieldDef = context.getFieldDef();\n      const parentType = context.getParentType();\n      if (!argDef && fieldDef && parentType) {\n        const argName = argNode.name.value;\n        const knownArgsNames = fieldDef.args.map(arg => arg.name);\n        const suggestions = suggestionList(argName, knownArgsNames);\n        context.reportError(new GraphQLError(`Unknown argument \"${argName}\" on field \"${parentType.name}.${fieldDef.name}\".` + didYouMean(suggestions), {\n          nodes: argNode\n        }));\n      }\n    }\n  };\n}\n/**\n * @internal\n */\n\nexport function KnownArgumentNamesOnDirectivesRule(context) {\n  const directiveArgs = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = schema ? schema.getDirectives() : specifiedDirectives;\n  for (const directive of definedDirectives) {\n    directiveArgs[directive.name] = directive.args.map(arg => arg.name);\n  }\n  const astDefinitions = context.getDocument().definitions;\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      var _def$arguments;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const argsNodes = (_def$arguments = def.arguments) !== null && _def$arguments !== void 0 ? _def$arguments : [];\n      directiveArgs[def.name.value] = argsNodes.map(arg => arg.name.value);\n    }\n  }\n  return {\n    Directive(directiveNode) {\n      const directiveName = directiveNode.name.value;\n      const knownArgs = directiveArgs[directiveName];\n      if (directiveNode.arguments && knownArgs) {\n        for (const argNode of directiveNode.arguments) {\n          const argName = argNode.name.value;\n          if (!knownArgs.includes(argName)) {\n            const suggestions = suggestionList(argName, knownArgs);\n            context.reportError(new GraphQLError(`Unknown argument \"${argName}\" on directive \"@${directiveName}\".` + didYouMean(suggestions), {\n              nodes: argNode\n            }));\n          }\n        }\n      }\n      return false;\n    }\n  };\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestionList", "GraphQLError", "Kind", "specifiedDirectives", "KnownArgumentNamesRule", "context", "KnownArgumentNamesOnDirectivesRule", "Argument", "argNode", "argDef", "getArgument", "fieldDef", "getFieldDef", "parentType", "getParentType", "argName", "name", "value", "knownArgsNames", "args", "map", "arg", "suggestions", "reportError", "nodes", "directiveArgs", "Object", "create", "schema", "getSchema", "definedDirectives", "getDirectives", "directive", "astDefinitions", "getDocument", "definitions", "def", "kind", "DIRECTIVE_DEFINITION", "_def$arguments", "argsNodes", "arguments", "Directive", "directiveNode", "directiveName", "knownArgs", "includes"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/KnownArgumentNamesRule.mjs"], "sourcesContent": ["import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Known argument names\n *\n * A GraphQL field is only valid if all supplied arguments are defined by\n * that field.\n *\n * See https://spec.graphql.org/draft/#sec-Argument-Names\n * See https://spec.graphql.org/draft/#sec-Directives-Are-In-Valid-Locations\n */\nexport function KnownArgumentNamesRule(context) {\n  return {\n    // eslint-disable-next-line new-cap\n    ...KnownArgumentNamesOnDirectivesRule(context),\n\n    Argument(argNode) {\n      const argDef = context.getArgument();\n      const fieldDef = context.getFieldDef();\n      const parentType = context.getParentType();\n\n      if (!argDef && fieldDef && parentType) {\n        const argName = argNode.name.value;\n        const knownArgsNames = fieldDef.args.map((arg) => arg.name);\n        const suggestions = suggestionList(argName, knownArgsNames);\n        context.reportError(\n          new GraphQLError(\n            `Unknown argument \"${argName}\" on field \"${parentType.name}.${fieldDef.name}\".` +\n              didYouMean(suggestions),\n            {\n              nodes: argNode,\n            },\n          ),\n        );\n      }\n    },\n  };\n}\n/**\n * @internal\n */\n\nexport function KnownArgumentNamesOnDirectivesRule(context) {\n  const directiveArgs = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = schema\n    ? schema.getDirectives()\n    : specifiedDirectives;\n\n  for (const directive of definedDirectives) {\n    directiveArgs[directive.name] = directive.args.map((arg) => arg.name);\n  }\n\n  const astDefinitions = context.getDocument().definitions;\n\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      var _def$arguments;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const argsNodes =\n        (_def$arguments = def.arguments) !== null && _def$arguments !== void 0\n          ? _def$arguments\n          : [];\n      directiveArgs[def.name.value] = argsNodes.map((arg) => arg.name.value);\n    }\n  }\n\n  return {\n    Directive(directiveNode) {\n      const directiveName = directiveNode.name.value;\n      const knownArgs = directiveArgs[directiveName];\n\n      if (directiveNode.arguments && knownArgs) {\n        for (const argNode of directiveNode.arguments) {\n          const argName = argNode.name.value;\n\n          if (!knownArgs.includes(argName)) {\n            const suggestions = suggestionList(argName, knownArgs);\n            context.reportError(\n              new GraphQLError(\n                `Unknown argument \"${argName}\" on directive \"@${directiveName}\".` +\n                  didYouMean(suggestions),\n                {\n                  nodes: argNode,\n                },\n              ),\n            );\n          }\n        }\n      }\n\n      return false;\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,8BAA8B;AACzD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,mBAAmB,QAAQ,2BAA2B;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,OAAO,EAAE;EAC9C,OAAO;IACL;IACA,GAAGC,kCAAkC,CAACD,OAAO,CAAC;IAE9CE,QAAQA,CAACC,OAAO,EAAE;MAChB,MAAMC,MAAM,GAAGJ,OAAO,CAACK,WAAW,CAAC,CAAC;MACpC,MAAMC,QAAQ,GAAGN,OAAO,CAACO,WAAW,CAAC,CAAC;MACtC,MAAMC,UAAU,GAAGR,OAAO,CAACS,aAAa,CAAC,CAAC;MAE1C,IAAI,CAACL,MAAM,IAAIE,QAAQ,IAAIE,UAAU,EAAE;QACrC,MAAME,OAAO,GAAGP,OAAO,CAACQ,IAAI,CAACC,KAAK;QAClC,MAAMC,cAAc,GAAGP,QAAQ,CAACQ,IAAI,CAACC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACL,IAAI,CAAC;QAC3D,MAAMM,WAAW,GAAGtB,cAAc,CAACe,OAAO,EAAEG,cAAc,CAAC;QAC3Db,OAAO,CAACkB,WAAW,CACjB,IAAItB,YAAY,CACd,qBAAqBc,OAAO,eAAeF,UAAU,CAACG,IAAI,IAAIL,QAAQ,CAACK,IAAI,IAAI,GAC7EjB,UAAU,CAACuB,WAAW,CAAC,EACzB;UACEE,KAAK,EAAEhB;QACT,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH;AACA;AACA;AACA;;AAEA,OAAO,SAASF,kCAAkCA,CAACD,OAAO,EAAE;EAC1D,MAAMoB,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMC,MAAM,GAAGvB,OAAO,CAACwB,SAAS,CAAC,CAAC;EAClC,MAAMC,iBAAiB,GAAGF,MAAM,GAC5BA,MAAM,CAACG,aAAa,CAAC,CAAC,GACtB5B,mBAAmB;EAEvB,KAAK,MAAM6B,SAAS,IAAIF,iBAAiB,EAAE;IACzCL,aAAa,CAACO,SAAS,CAAChB,IAAI,CAAC,GAAGgB,SAAS,CAACb,IAAI,CAACC,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACL,IAAI,CAAC;EACvE;EAEA,MAAMiB,cAAc,GAAG5B,OAAO,CAAC6B,WAAW,CAAC,CAAC,CAACC,WAAW;EAExD,KAAK,MAAMC,GAAG,IAAIH,cAAc,EAAE;IAChC,IAAIG,GAAG,CAACC,IAAI,KAAKnC,IAAI,CAACoC,oBAAoB,EAAE;MAC1C,IAAIC,cAAc;;MAElB;;MAEA;MACA,MAAMC,SAAS,GACb,CAACD,cAAc,GAAGH,GAAG,CAACK,SAAS,MAAM,IAAI,IAAIF,cAAc,KAAK,KAAK,CAAC,GAClEA,cAAc,GACd,EAAE;MACRd,aAAa,CAACW,GAAG,CAACpB,IAAI,CAACC,KAAK,CAAC,GAAGuB,SAAS,CAACpB,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACL,IAAI,CAACC,KAAK,CAAC;IACxE;EACF;EAEA,OAAO;IACLyB,SAASA,CAACC,aAAa,EAAE;MACvB,MAAMC,aAAa,GAAGD,aAAa,CAAC3B,IAAI,CAACC,KAAK;MAC9C,MAAM4B,SAAS,GAAGpB,aAAa,CAACmB,aAAa,CAAC;MAE9C,IAAID,aAAa,CAACF,SAAS,IAAII,SAAS,EAAE;QACxC,KAAK,MAAMrC,OAAO,IAAImC,aAAa,CAACF,SAAS,EAAE;UAC7C,MAAM1B,OAAO,GAAGP,OAAO,CAACQ,IAAI,CAACC,KAAK;UAElC,IAAI,CAAC4B,SAAS,CAACC,QAAQ,CAAC/B,OAAO,CAAC,EAAE;YAChC,MAAMO,WAAW,GAAGtB,cAAc,CAACe,OAAO,EAAE8B,SAAS,CAAC;YACtDxC,OAAO,CAACkB,WAAW,CACjB,IAAItB,YAAY,CACd,qBAAqBc,OAAO,oBAAoB6B,aAAa,IAAI,GAC/D7C,UAAU,CAACuB,WAAW,CAAC,EACzB;cACEE,KAAK,EAAEhB;YACT,CACF,CACF,CAAC;UACH;QACF;MACF;MAEA,OAAO,KAAK;IACd;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}