{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\n\n// src/index.ts\nimport { isNodeProcess } from \"is-node-process\";\nimport { format } from \"outvariant\";\n\n// src/colors.ts\nvar colors_exports = {};\n__export(colors_exports, {\n  blue: () => blue,\n  gray: () => gray,\n  green: () => green,\n  red: () => red,\n  yellow: () => yellow\n});\nfunction yellow(text) {\n  return `\\x1B[33m${text}\\x1B[0m`;\n}\nfunction blue(text) {\n  return `\\x1B[34m${text}\\x1B[0m`;\n}\nfunction gray(text) {\n  return `\\x1B[90m${text}\\x1B[0m`;\n}\nfunction red(text) {\n  return `\\x1B[31m${text}\\x1B[0m`;\n}\nfunction green(text) {\n  return `\\x1B[32m${text}\\x1B[0m`;\n}\n\n// src/index.ts\nvar IS_NODE = isNodeProcess();\nvar Logger = class {\n  constructor(name) {\n    this.name = name;\n    this.prefix = `[${this.name}]`;\n    const LOGGER_NAME = getVariable(\"DEBUG\");\n    const LOGGER_LEVEL = getVariable(\"LOG_LEVEL\");\n    const isLoggingEnabled = LOGGER_NAME === \"1\" || LOGGER_NAME === \"true\" || typeof LOGGER_NAME !== \"undefined\" && this.name.startsWith(LOGGER_NAME);\n    if (isLoggingEnabled) {\n      this.debug = isDefinedAndNotEquals(LOGGER_LEVEL, \"debug\") ? noop : this.debug;\n      this.info = isDefinedAndNotEquals(LOGGER_LEVEL, \"info\") ? noop : this.info;\n      this.success = isDefinedAndNotEquals(LOGGER_LEVEL, \"success\") ? noop : this.success;\n      this.warning = isDefinedAndNotEquals(LOGGER_LEVEL, \"warning\") ? noop : this.warning;\n      this.error = isDefinedAndNotEquals(LOGGER_LEVEL, \"error\") ? noop : this.error;\n    } else {\n      this.info = noop;\n      this.success = noop;\n      this.warning = noop;\n      this.error = noop;\n      this.only = noop;\n    }\n  }\n  prefix;\n  extend(domain) {\n    return new Logger(`${this.name}:${domain}`);\n  }\n  /**\n   * Print a debug message.\n   * @example\n   * logger.debug('no duplicates found, creating a document...')\n   */\n  debug(message, ...positionals) {\n    this.logEntry({\n      level: \"debug\",\n      message: gray(message),\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"gray\"\n      }\n    });\n  }\n  /**\n   * Print an info message.\n   * @example\n   * logger.info('start parsing...')\n   */\n  info(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"blue\"\n      }\n    });\n    const performance2 = new PerformanceEntry();\n    return (message2, ...positionals2) => {\n      performance2.measure();\n      this.logEntry({\n        level: \"info\",\n        message: `${message2} ${gray(`${performance2.deltaTime}ms`)}`,\n        positionals: positionals2,\n        prefix: this.prefix,\n        colors: {\n          prefix: \"blue\"\n        }\n      });\n    };\n  }\n  /**\n   * Print a success message.\n   * @example\n   * logger.success('successfully created document')\n   */\n  success(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: `\\u2714 ${this.prefix}`,\n      colors: {\n        timestamp: \"green\",\n        prefix: \"green\"\n      }\n    });\n  }\n  /**\n   * Print a warning.\n   * @example\n   * logger.warning('found legacy document format')\n   */\n  warning(message, ...positionals) {\n    this.logEntry({\n      level: \"warning\",\n      message,\n      positionals,\n      prefix: `\\u26A0 ${this.prefix}`,\n      colors: {\n        timestamp: \"yellow\",\n        prefix: \"yellow\"\n      }\n    });\n  }\n  /**\n   * Print an error message.\n   * @example\n   * logger.error('something went wrong')\n   */\n  error(message, ...positionals) {\n    this.logEntry({\n      level: \"error\",\n      message,\n      positionals,\n      prefix: `\\u2716 ${this.prefix}`,\n      colors: {\n        timestamp: \"red\",\n        prefix: \"red\"\n      }\n    });\n  }\n  /**\n   * Execute the given callback only when the logging is enabled.\n   * This is skipped in its entirety and has no runtime cost otherwise.\n   * This executes regardless of the log level.\n   * @example\n   * logger.only(() => {\n   *   logger.info('additional info')\n   * })\n   */\n  only(callback) {\n    callback();\n  }\n  createEntry(level, message) {\n    return {\n      timestamp: /* @__PURE__ */new Date(),\n      level,\n      message\n    };\n  }\n  logEntry(args) {\n    const {\n      level,\n      message,\n      prefix,\n      colors: customColors,\n      positionals = []\n    } = args;\n    const entry = this.createEntry(level, message);\n    const timestampColor = customColors?.timestamp || \"gray\";\n    const prefixColor = customColors?.prefix || \"gray\";\n    const colorize = {\n      timestamp: colors_exports[timestampColor],\n      prefix: colors_exports[prefixColor]\n    };\n    const write = this.getWriter(level);\n    write([colorize.timestamp(this.formatTimestamp(entry.timestamp))].concat(prefix != null ? colorize.prefix(prefix) : []).concat(serializeInput(message)).join(\" \"), ...positionals.map(serializeInput));\n  }\n  formatTimestamp(timestamp) {\n    return `${timestamp.toLocaleTimeString(\"en-GB\")}:${timestamp.getMilliseconds()}`;\n  }\n  getWriter(level) {\n    switch (level) {\n      case \"debug\":\n      case \"success\":\n      case \"info\":\n        {\n          return log;\n        }\n      case \"warning\":\n        {\n          return warn;\n        }\n      case \"error\":\n        {\n          return error;\n        }\n    }\n  }\n};\nvar PerformanceEntry = class {\n  startTime;\n  endTime;\n  deltaTime;\n  constructor() {\n    this.startTime = performance.now();\n  }\n  measure() {\n    this.endTime = performance.now();\n    const deltaTime = this.endTime - this.startTime;\n    this.deltaTime = deltaTime.toFixed(2);\n  }\n};\nvar noop = () => void 0;\nfunction log(message, ...positionals) {\n  if (IS_NODE) {\n    process.stdout.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.log(message, ...positionals);\n}\nfunction warn(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.warn(message, ...positionals);\n}\nfunction error(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.error(message, ...positionals);\n}\nfunction getVariable(variableName) {\n  if (IS_NODE) {\n    return process.env[variableName];\n  }\n  return globalThis[variableName]?.toString();\n}\nfunction isDefinedAndNotEquals(value, expected) {\n  return value !== void 0 && value !== expected;\n}\nfunction serializeInput(message) {\n  if (typeof message === \"undefined\") {\n    return \"undefined\";\n  }\n  if (message === null) {\n    return \"null\";\n  }\n  if (typeof message === \"string\") {\n    return message;\n  }\n  if (typeof message === \"object\") {\n    return JSON.stringify(message);\n  }\n  return message.toString();\n}\nexport { Logger };", "map": {"version": 3, "names": ["__defProp", "Object", "defineProperty", "__export", "target", "all", "name", "get", "enumerable", "isNodeProcess", "format", "colors_exports", "blue", "gray", "green", "red", "yellow", "text", "IS_NODE", "<PERSON><PERSON>", "constructor", "prefix", "LOGGER_NAME", "getVariable", "LOGGER_LEVEL", "isLoggingEnabled", "startsWith", "debug", "isDefinedAndNotEquals", "noop", "info", "success", "warning", "error", "only", "extend", "domain", "message", "positionals", "logEntry", "level", "colors", "performance2", "PerformanceEntry", "message2", "positionals2", "measure", "deltaTime", "timestamp", "callback", "createEntry", "Date", "args", "customColors", "entry", "timestampColor", "prefixColor", "colorize", "write", "getWriter", "formatTimestamp", "concat", "serializeInput", "join", "map", "toLocaleTimeString", "getMilliseconds", "log", "warn", "startTime", "endTime", "performance", "now", "toFixed", "process", "stdout", "console", "stderr", "variableName", "env", "globalThis", "toString", "value", "expected", "JSON", "stringify"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@open-draft/logger/lib/index.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/index.ts\nimport { isNodeProcess } from \"is-node-process\";\nimport { format } from \"outvariant\";\n\n// src/colors.ts\nvar colors_exports = {};\n__export(colors_exports, {\n  blue: () => blue,\n  gray: () => gray,\n  green: () => green,\n  red: () => red,\n  yellow: () => yellow\n});\nfunction yellow(text) {\n  return `\\x1B[33m${text}\\x1B[0m`;\n}\nfunction blue(text) {\n  return `\\x1B[34m${text}\\x1B[0m`;\n}\nfunction gray(text) {\n  return `\\x1B[90m${text}\\x1B[0m`;\n}\nfunction red(text) {\n  return `\\x1B[31m${text}\\x1B[0m`;\n}\nfunction green(text) {\n  return `\\x1B[32m${text}\\x1B[0m`;\n}\n\n// src/index.ts\nvar IS_NODE = isNodeProcess();\nvar Logger = class {\n  constructor(name) {\n    this.name = name;\n    this.prefix = `[${this.name}]`;\n    const LOGGER_NAME = getVariable(\"DEBUG\");\n    const LOGGER_LEVEL = getVariable(\"LOG_LEVEL\");\n    const isLoggingEnabled = LOGGER_NAME === \"1\" || LOGGER_NAME === \"true\" || typeof LOGGER_NAME !== \"undefined\" && this.name.startsWith(LOGGER_NAME);\n    if (isLoggingEnabled) {\n      this.debug = isDefinedAndNotEquals(LOGGER_LEVEL, \"debug\") ? noop : this.debug;\n      this.info = isDefinedAndNotEquals(LOGGER_LEVEL, \"info\") ? noop : this.info;\n      this.success = isDefinedAndNotEquals(LOGGER_LEVEL, \"success\") ? noop : this.success;\n      this.warning = isDefinedAndNotEquals(LOGGER_LEVEL, \"warning\") ? noop : this.warning;\n      this.error = isDefinedAndNotEquals(LOGGER_LEVEL, \"error\") ? noop : this.error;\n    } else {\n      this.info = noop;\n      this.success = noop;\n      this.warning = noop;\n      this.error = noop;\n      this.only = noop;\n    }\n  }\n  prefix;\n  extend(domain) {\n    return new Logger(`${this.name}:${domain}`);\n  }\n  /**\n   * Print a debug message.\n   * @example\n   * logger.debug('no duplicates found, creating a document...')\n   */\n  debug(message, ...positionals) {\n    this.logEntry({\n      level: \"debug\",\n      message: gray(message),\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"gray\"\n      }\n    });\n  }\n  /**\n   * Print an info message.\n   * @example\n   * logger.info('start parsing...')\n   */\n  info(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"blue\"\n      }\n    });\n    const performance2 = new PerformanceEntry();\n    return (message2, ...positionals2) => {\n      performance2.measure();\n      this.logEntry({\n        level: \"info\",\n        message: `${message2} ${gray(`${performance2.deltaTime}ms`)}`,\n        positionals: positionals2,\n        prefix: this.prefix,\n        colors: {\n          prefix: \"blue\"\n        }\n      });\n    };\n  }\n  /**\n   * Print a success message.\n   * @example\n   * logger.success('successfully created document')\n   */\n  success(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: `\\u2714 ${this.prefix}`,\n      colors: {\n        timestamp: \"green\",\n        prefix: \"green\"\n      }\n    });\n  }\n  /**\n   * Print a warning.\n   * @example\n   * logger.warning('found legacy document format')\n   */\n  warning(message, ...positionals) {\n    this.logEntry({\n      level: \"warning\",\n      message,\n      positionals,\n      prefix: `\\u26A0 ${this.prefix}`,\n      colors: {\n        timestamp: \"yellow\",\n        prefix: \"yellow\"\n      }\n    });\n  }\n  /**\n   * Print an error message.\n   * @example\n   * logger.error('something went wrong')\n   */\n  error(message, ...positionals) {\n    this.logEntry({\n      level: \"error\",\n      message,\n      positionals,\n      prefix: `\\u2716 ${this.prefix}`,\n      colors: {\n        timestamp: \"red\",\n        prefix: \"red\"\n      }\n    });\n  }\n  /**\n   * Execute the given callback only when the logging is enabled.\n   * This is skipped in its entirety and has no runtime cost otherwise.\n   * This executes regardless of the log level.\n   * @example\n   * logger.only(() => {\n   *   logger.info('additional info')\n   * })\n   */\n  only(callback) {\n    callback();\n  }\n  createEntry(level, message) {\n    return {\n      timestamp: /* @__PURE__ */ new Date(),\n      level,\n      message\n    };\n  }\n  logEntry(args) {\n    const {\n      level,\n      message,\n      prefix,\n      colors: customColors,\n      positionals = []\n    } = args;\n    const entry = this.createEntry(level, message);\n    const timestampColor = customColors?.timestamp || \"gray\";\n    const prefixColor = customColors?.prefix || \"gray\";\n    const colorize = {\n      timestamp: colors_exports[timestampColor],\n      prefix: colors_exports[prefixColor]\n    };\n    const write = this.getWriter(level);\n    write(\n      [colorize.timestamp(this.formatTimestamp(entry.timestamp))].concat(prefix != null ? colorize.prefix(prefix) : []).concat(serializeInput(message)).join(\" \"),\n      ...positionals.map(serializeInput)\n    );\n  }\n  formatTimestamp(timestamp) {\n    return `${timestamp.toLocaleTimeString(\n      \"en-GB\"\n    )}:${timestamp.getMilliseconds()}`;\n  }\n  getWriter(level) {\n    switch (level) {\n      case \"debug\":\n      case \"success\":\n      case \"info\": {\n        return log;\n      }\n      case \"warning\": {\n        return warn;\n      }\n      case \"error\": {\n        return error;\n      }\n    }\n  }\n};\nvar PerformanceEntry = class {\n  startTime;\n  endTime;\n  deltaTime;\n  constructor() {\n    this.startTime = performance.now();\n  }\n  measure() {\n    this.endTime = performance.now();\n    const deltaTime = this.endTime - this.startTime;\n    this.deltaTime = deltaTime.toFixed(2);\n  }\n};\nvar noop = () => void 0;\nfunction log(message, ...positionals) {\n  if (IS_NODE) {\n    process.stdout.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.log(message, ...positionals);\n}\nfunction warn(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.warn(message, ...positionals);\n}\nfunction error(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.error(message, ...positionals);\n}\nfunction getVariable(variableName) {\n  if (IS_NODE) {\n    return process.env[variableName];\n  }\n  return globalThis[variableName]?.toString();\n}\nfunction isDefinedAndNotEquals(value, expected) {\n  return value !== void 0 && value !== expected;\n}\nfunction serializeInput(message) {\n  if (typeof message === \"undefined\") {\n    return \"undefined\";\n  }\n  if (message === null) {\n    return \"null\";\n  }\n  if (typeof message === \"string\") {\n    return message;\n  }\n  if (typeof message === \"object\") {\n    return JSON.stringify(message);\n  }\n  return message.toString();\n}\nexport {\n  Logger\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,QAAQ,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAK;EAC9B,KAAK,IAAIC,IAAI,IAAID,GAAG,EAClBL,SAAS,CAACI,MAAM,EAAEE,IAAI,EAAE;IAAEC,GAAG,EAAEF,GAAG,CAACC,IAAI,CAAC;IAAEE,UAAU,EAAE;EAAK,CAAC,CAAC;AACjE,CAAC;;AAED;AACA,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,MAAM,QAAQ,YAAY;;AAEnC;AACA,IAAIC,cAAc,GAAG,CAAC,CAAC;AACvBR,QAAQ,CAACQ,cAAc,EAAE;EACvBC,IAAI,EAAEA,CAAA,KAAMA,IAAI;EAChBC,IAAI,EAAEA,CAAA,KAAMA,IAAI;EAChBC,KAAK,EAAEA,CAAA,KAAMA,KAAK;EAClBC,GAAG,EAAEA,CAAA,KAAMA,GAAG;EACdC,MAAM,EAAEA,CAAA,KAAMA;AAChB,CAAC,CAAC;AACF,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,OAAO,WAAWA,IAAI,SAAS;AACjC;AACA,SAASL,IAAIA,CAACK,IAAI,EAAE;EAClB,OAAO,WAAWA,IAAI,SAAS;AACjC;AACA,SAASJ,IAAIA,CAACI,IAAI,EAAE;EAClB,OAAO,WAAWA,IAAI,SAAS;AACjC;AACA,SAASF,GAAGA,CAACE,IAAI,EAAE;EACjB,OAAO,WAAWA,IAAI,SAAS;AACjC;AACA,SAASH,KAAKA,CAACG,IAAI,EAAE;EACnB,OAAO,WAAWA,IAAI,SAAS;AACjC;;AAEA;AACA,IAAIC,OAAO,GAAGT,aAAa,CAAC,CAAC;AAC7B,IAAIU,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACd,IAAI,EAAE;IAChB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACe,MAAM,GAAG,IAAI,IAAI,CAACf,IAAI,GAAG;IAC9B,MAAMgB,WAAW,GAAGC,WAAW,CAAC,OAAO,CAAC;IACxC,MAAMC,YAAY,GAAGD,WAAW,CAAC,WAAW,CAAC;IAC7C,MAAME,gBAAgB,GAAGH,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,MAAM,IAAI,OAAOA,WAAW,KAAK,WAAW,IAAI,IAAI,CAAChB,IAAI,CAACoB,UAAU,CAACJ,WAAW,CAAC;IACjJ,IAAIG,gBAAgB,EAAE;MACpB,IAAI,CAACE,KAAK,GAAGC,qBAAqB,CAACJ,YAAY,EAAE,OAAO,CAAC,GAAGK,IAAI,GAAG,IAAI,CAACF,KAAK;MAC7E,IAAI,CAACG,IAAI,GAAGF,qBAAqB,CAACJ,YAAY,EAAE,MAAM,CAAC,GAAGK,IAAI,GAAG,IAAI,CAACC,IAAI;MAC1E,IAAI,CAACC,OAAO,GAAGH,qBAAqB,CAACJ,YAAY,EAAE,SAAS,CAAC,GAAGK,IAAI,GAAG,IAAI,CAACE,OAAO;MACnF,IAAI,CAACC,OAAO,GAAGJ,qBAAqB,CAACJ,YAAY,EAAE,SAAS,CAAC,GAAGK,IAAI,GAAG,IAAI,CAACG,OAAO;MACnF,IAAI,CAACC,KAAK,GAAGL,qBAAqB,CAACJ,YAAY,EAAE,OAAO,CAAC,GAAGK,IAAI,GAAG,IAAI,CAACI,KAAK;IAC/E,CAAC,MAAM;MACL,IAAI,CAACH,IAAI,GAAGD,IAAI;MAChB,IAAI,CAACE,OAAO,GAAGF,IAAI;MACnB,IAAI,CAACG,OAAO,GAAGH,IAAI;MACnB,IAAI,CAACI,KAAK,GAAGJ,IAAI;MACjB,IAAI,CAACK,IAAI,GAAGL,IAAI;IAClB;EACF;EACAR,MAAM;EACNc,MAAMA,CAACC,MAAM,EAAE;IACb,OAAO,IAAIjB,MAAM,CAAC,GAAG,IAAI,CAACb,IAAI,IAAI8B,MAAM,EAAE,CAAC;EAC7C;EACA;AACF;AACA;AACA;AACA;EACET,KAAKA,CAACU,OAAO,EAAE,GAAGC,WAAW,EAAE;IAC7B,IAAI,CAACC,QAAQ,CAAC;MACZC,KAAK,EAAE,OAAO;MACdH,OAAO,EAAExB,IAAI,CAACwB,OAAO,CAAC;MACtBC,WAAW;MACXjB,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBoB,MAAM,EAAE;QACNpB,MAAM,EAAE;MACV;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;EACES,IAAIA,CAACO,OAAO,EAAE,GAAGC,WAAW,EAAE;IAC5B,IAAI,CAACC,QAAQ,CAAC;MACZC,KAAK,EAAE,MAAM;MACbH,OAAO;MACPC,WAAW;MACXjB,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBoB,MAAM,EAAE;QACNpB,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IACF,MAAMqB,YAAY,GAAG,IAAIC,gBAAgB,CAAC,CAAC;IAC3C,OAAO,CAACC,QAAQ,EAAE,GAAGC,YAAY,KAAK;MACpCH,YAAY,CAACI,OAAO,CAAC,CAAC;MACtB,IAAI,CAACP,QAAQ,CAAC;QACZC,KAAK,EAAE,MAAM;QACbH,OAAO,EAAE,GAAGO,QAAQ,IAAI/B,IAAI,CAAC,GAAG6B,YAAY,CAACK,SAAS,IAAI,CAAC,EAAE;QAC7DT,WAAW,EAAEO,YAAY;QACzBxB,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBoB,MAAM,EAAE;UACNpB,MAAM,EAAE;QACV;MACF,CAAC,CAAC;IACJ,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACEU,OAAOA,CAACM,OAAO,EAAE,GAAGC,WAAW,EAAE;IAC/B,IAAI,CAACC,QAAQ,CAAC;MACZC,KAAK,EAAE,MAAM;MACbH,OAAO;MACPC,WAAW;MACXjB,MAAM,EAAE,UAAU,IAAI,CAACA,MAAM,EAAE;MAC/BoB,MAAM,EAAE;QACNO,SAAS,EAAE,OAAO;QAClB3B,MAAM,EAAE;MACV;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;EACEW,OAAOA,CAACK,OAAO,EAAE,GAAGC,WAAW,EAAE;IAC/B,IAAI,CAACC,QAAQ,CAAC;MACZC,KAAK,EAAE,SAAS;MAChBH,OAAO;MACPC,WAAW;MACXjB,MAAM,EAAE,UAAU,IAAI,CAACA,MAAM,EAAE;MAC/BoB,MAAM,EAAE;QACNO,SAAS,EAAE,QAAQ;QACnB3B,MAAM,EAAE;MACV;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;EACEY,KAAKA,CAACI,OAAO,EAAE,GAAGC,WAAW,EAAE;IAC7B,IAAI,CAACC,QAAQ,CAAC;MACZC,KAAK,EAAE,OAAO;MACdH,OAAO;MACPC,WAAW;MACXjB,MAAM,EAAE,UAAU,IAAI,CAACA,MAAM,EAAE;MAC/BoB,MAAM,EAAE;QACNO,SAAS,EAAE,KAAK;QAChB3B,MAAM,EAAE;MACV;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEa,IAAIA,CAACe,QAAQ,EAAE;IACbA,QAAQ,CAAC,CAAC;EACZ;EACAC,WAAWA,CAACV,KAAK,EAAEH,OAAO,EAAE;IAC1B,OAAO;MACLW,SAAS,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrCX,KAAK;MACLH;IACF,CAAC;EACH;EACAE,QAAQA,CAACa,IAAI,EAAE;IACb,MAAM;MACJZ,KAAK;MACLH,OAAO;MACPhB,MAAM;MACNoB,MAAM,EAAEY,YAAY;MACpBf,WAAW,GAAG;IAChB,CAAC,GAAGc,IAAI;IACR,MAAME,KAAK,GAAG,IAAI,CAACJ,WAAW,CAACV,KAAK,EAAEH,OAAO,CAAC;IAC9C,MAAMkB,cAAc,GAAGF,YAAY,EAAEL,SAAS,IAAI,MAAM;IACxD,MAAMQ,WAAW,GAAGH,YAAY,EAAEhC,MAAM,IAAI,MAAM;IAClD,MAAMoC,QAAQ,GAAG;MACfT,SAAS,EAAErC,cAAc,CAAC4C,cAAc,CAAC;MACzClC,MAAM,EAAEV,cAAc,CAAC6C,WAAW;IACpC,CAAC;IACD,MAAME,KAAK,GAAG,IAAI,CAACC,SAAS,CAACnB,KAAK,CAAC;IACnCkB,KAAK,CACH,CAACD,QAAQ,CAACT,SAAS,CAAC,IAAI,CAACY,eAAe,CAACN,KAAK,CAACN,SAAS,CAAC,CAAC,CAAC,CAACa,MAAM,CAACxC,MAAM,IAAI,IAAI,GAAGoC,QAAQ,CAACpC,MAAM,CAACA,MAAM,CAAC,GAAG,EAAE,CAAC,CAACwC,MAAM,CAACC,cAAc,CAACzB,OAAO,CAAC,CAAC,CAAC0B,IAAI,CAAC,GAAG,CAAC,EAC3J,GAAGzB,WAAW,CAAC0B,GAAG,CAACF,cAAc,CACnC,CAAC;EACH;EACAF,eAAeA,CAACZ,SAAS,EAAE;IACzB,OAAO,GAAGA,SAAS,CAACiB,kBAAkB,CACpC,OACF,CAAC,IAAIjB,SAAS,CAACkB,eAAe,CAAC,CAAC,EAAE;EACpC;EACAP,SAASA,CAACnB,KAAK,EAAE;IACf,QAAQA,KAAK;MACX,KAAK,OAAO;MACZ,KAAK,SAAS;MACd,KAAK,MAAM;QAAE;UACX,OAAO2B,GAAG;QACZ;MACA,KAAK,SAAS;QAAE;UACd,OAAOC,IAAI;QACb;MACA,KAAK,OAAO;QAAE;UACZ,OAAOnC,KAAK;QACd;IACF;EACF;AACF,CAAC;AACD,IAAIU,gBAAgB,GAAG,MAAM;EAC3B0B,SAAS;EACTC,OAAO;EACPvB,SAAS;EACT3B,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACiD,SAAS,GAAGE,WAAW,CAACC,GAAG,CAAC,CAAC;EACpC;EACA1B,OAAOA,CAAA,EAAG;IACR,IAAI,CAACwB,OAAO,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IAChC,MAAMzB,SAAS,GAAG,IAAI,CAACuB,OAAO,GAAG,IAAI,CAACD,SAAS;IAC/C,IAAI,CAACtB,SAAS,GAAGA,SAAS,CAAC0B,OAAO,CAAC,CAAC,CAAC;EACvC;AACF,CAAC;AACD,IAAI5C,IAAI,GAAGA,CAAA,KAAM,KAAK,CAAC;AACvB,SAASsC,GAAGA,CAAC9B,OAAO,EAAE,GAAGC,WAAW,EAAE;EACpC,IAAIpB,OAAO,EAAE;IACXwD,OAAO,CAACC,MAAM,CAACjB,KAAK,CAAChD,MAAM,CAAC2B,OAAO,EAAE,GAAGC,WAAW,CAAC,GAAG,IAAI,CAAC;IAC5D;EACF;EACAsC,OAAO,CAACT,GAAG,CAAC9B,OAAO,EAAE,GAAGC,WAAW,CAAC;AACtC;AACA,SAAS8B,IAAIA,CAAC/B,OAAO,EAAE,GAAGC,WAAW,EAAE;EACrC,IAAIpB,OAAO,EAAE;IACXwD,OAAO,CAACG,MAAM,CAACnB,KAAK,CAAChD,MAAM,CAAC2B,OAAO,EAAE,GAAGC,WAAW,CAAC,GAAG,IAAI,CAAC;IAC5D;EACF;EACAsC,OAAO,CAACR,IAAI,CAAC/B,OAAO,EAAE,GAAGC,WAAW,CAAC;AACvC;AACA,SAASL,KAAKA,CAACI,OAAO,EAAE,GAAGC,WAAW,EAAE;EACtC,IAAIpB,OAAO,EAAE;IACXwD,OAAO,CAACG,MAAM,CAACnB,KAAK,CAAChD,MAAM,CAAC2B,OAAO,EAAE,GAAGC,WAAW,CAAC,GAAG,IAAI,CAAC;IAC5D;EACF;EACAsC,OAAO,CAAC3C,KAAK,CAACI,OAAO,EAAE,GAAGC,WAAW,CAAC;AACxC;AACA,SAASf,WAAWA,CAACuD,YAAY,EAAE;EACjC,IAAI5D,OAAO,EAAE;IACX,OAAOwD,OAAO,CAACK,GAAG,CAACD,YAAY,CAAC;EAClC;EACA,OAAOE,UAAU,CAACF,YAAY,CAAC,EAAEG,QAAQ,CAAC,CAAC;AAC7C;AACA,SAASrD,qBAAqBA,CAACsD,KAAK,EAAEC,QAAQ,EAAE;EAC9C,OAAOD,KAAK,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAKC,QAAQ;AAC/C;AACA,SAASrB,cAAcA,CAACzB,OAAO,EAAE;EAC/B,IAAI,OAAOA,OAAO,KAAK,WAAW,EAAE;IAClC,OAAO,WAAW;EACpB;EACA,IAAIA,OAAO,KAAK,IAAI,EAAE;IACpB,OAAO,MAAM;EACf;EACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOA,OAAO;EAChB;EACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAO+C,IAAI,CAACC,SAAS,CAAChD,OAAO,CAAC;EAChC;EACA,OAAOA,OAAO,CAAC4C,QAAQ,CAAC,CAAC;AAC3B;AACA,SACE9D,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}