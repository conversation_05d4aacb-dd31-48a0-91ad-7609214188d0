{"ast": null, "code": "import { invariant } from \"outvariant\";\nimport { isNodeProcess } from \"is-node-process\";\nimport toughCookie from \"@bundled-es-modules/tough-cookie\";\nconst {\n  <PERSON><PERSON>,\n  <PERSON>ieJar,\n  Store,\n  MemoryCookieStore,\n  domainMatch,\n  pathMatch\n} = toughCookie;\nclass WebStorageCookieStore extends Store {\n  storage;\n  storageKey;\n  constructor() {\n    super();\n    invariant(typeof localStorage !== \"undefined\", \"Failed to create a WebStorageCookieStore: `localStorage` is not available in this environment. This is likely an issue with MSW. Please report it on GitHub: https://github.com/mswjs/msw/issues\");\n    this.synchronous = true;\n    this.storage = localStorage;\n    this.storageKey = \"__msw-cookie-store__\";\n  }\n  findCookie(domain, path, key, callback) {\n    try {\n      const store2 = this.getStore();\n      const cookies = this.filterCookiesFromList(store2, {\n        domain,\n        path,\n        key\n      });\n      callback(null, cookies[0] || null);\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error, null);\n      }\n    }\n  }\n  findCookies(domain, path, allowSpecialUseDomain, callback) {\n    if (!domain) {\n      callback(null, []);\n      return;\n    }\n    try {\n      const store2 = this.getStore();\n      const results = this.filterCookiesFromList(store2, {\n        domain,\n        path\n      });\n      callback(null, results);\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error, []);\n      }\n    }\n  }\n  putCookie(cookie, callback) {\n    try {\n      if (cookie.maxAge === 0) {\n        return;\n      }\n      const store2 = this.getStore();\n      store2.push(cookie);\n      this.updateStore(store2);\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error);\n      }\n    }\n  }\n  updateCookie(oldCookie, newCookie, callback) {\n    if (newCookie.maxAge === 0) {\n      this.removeCookie(newCookie.domain || \"\", newCookie.path || \"\", newCookie.key, callback);\n      return;\n    }\n    this.putCookie(newCookie, callback);\n  }\n  removeCookie(domain, path, key, callback) {\n    try {\n      const store2 = this.getStore();\n      const nextStore = this.deleteCookiesFromList(store2, {\n        domain,\n        path,\n        key\n      });\n      this.updateStore(nextStore);\n      callback(null);\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error);\n      }\n    }\n  }\n  removeCookies(domain, path, callback) {\n    try {\n      const store2 = this.getStore();\n      const nextStore = this.deleteCookiesFromList(store2, {\n        domain,\n        path\n      });\n      this.updateStore(nextStore);\n      callback(null);\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error);\n      }\n    }\n  }\n  getAllCookies(callback) {\n    try {\n      callback(null, this.getStore());\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error, []);\n      }\n    }\n  }\n  getStore() {\n    try {\n      const json = this.storage.getItem(this.storageKey);\n      if (json == null) {\n        return [];\n      }\n      const rawCookies = JSON.parse(json);\n      const cookies = [];\n      for (const rawCookie of rawCookies) {\n        const cookie = Cookie.fromJSON(rawCookie);\n        if (cookie != null) {\n          cookies.push(cookie);\n        }\n      }\n      return cookies;\n    } catch {\n      return [];\n    }\n  }\n  updateStore(nextStore) {\n    this.storage.setItem(this.storageKey, JSON.stringify(nextStore.map(cookie => cookie.toJSON())));\n  }\n  filterCookiesFromList(cookies, matches) {\n    const result = [];\n    for (const cookie of cookies) {\n      if (matches.domain && !domainMatch(matches.domain, cookie.domain || \"\")) {\n        continue;\n      }\n      if (matches.path && !pathMatch(matches.path, cookie.path || \"\")) {\n        continue;\n      }\n      if (matches.key && cookie.key !== matches.key) {\n        continue;\n      }\n      result.push(cookie);\n    }\n    return result;\n  }\n  deleteCookiesFromList(cookies, matches) {\n    const matchingCookies = this.filterCookiesFromList(cookies, matches);\n    return cookies.filter(cookie => !matchingCookies.includes(cookie));\n  }\n}\nconst store = isNodeProcess() ? new MemoryCookieStore() : new WebStorageCookieStore();\nconst cookieStore = new CookieJar(store);\nexport { cookieStore };", "map": {"version": 3, "names": ["invariant", "isNodeProcess", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Store", "MemoryCookieStore", "domainMatch", "pathMatch", "WebStorageCookieStore", "storage", "storageKey", "constructor", "localStorage", "synchronous", "<PERSON><PERSON><PERSON><PERSON>", "domain", "path", "key", "callback", "store2", "getStore", "cookies", "filterCookiesFromList", "error", "Error", "findCookies", "allowSpecialUseDomain", "results", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "maxAge", "push", "updateStore", "update<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "newC<PERSON>ie", "<PERSON><PERSON><PERSON><PERSON>", "nextStore", "deleteCookiesFromList", "removeCookies", "getAllCookies", "json", "getItem", "rawCookies", "JSON", "parse", "<PERSON><PERSON><PERSON><PERSON>", "fromJSON", "setItem", "stringify", "map", "toJSON", "matches", "result", "matchingCookies", "filter", "includes", "store", "cookieStore"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/cookieStore.ts"], "sourcesContent": ["import { invariant } from 'outvariant'\nimport { isNodeProcess } from 'is-node-process'\nimport tough<PERSON><PERSON><PERSON>, {\n  type <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,\n} from '@bundled-es-modules/tough-cookie'\n\nconst { <PERSON><PERSON>, CookieJar, Store, MemoryCookieStore, domainMatch, pathMatch } =\n  toughCookie\n\n/**\n * Custom cookie store that uses the Web Storage API.\n * @see https://github.com/expo/tough-cookie-web-storage-store\n */\nclass WebStorageCookieStore extends Store {\n  private storage: Storage\n  private storageKey: string\n\n  constructor() {\n    super()\n\n    invariant(\n      typeof localStorage !== 'undefined',\n      'Failed to create a WebStorageCookieStore: `localStorage` is not available in this environment. This is likely an issue with MSW. Please report it on GitHub: https://github.com/mswjs/msw/issues',\n    )\n\n    this.synchronous = true\n    this.storage = localStorage\n    this.storageKey = '__msw-cookie-store__'\n  }\n\n  findCookie(\n    domain: string,\n    path: string,\n    key: string,\n    callback: (error: Error | null, cookie: CookieInstance | null) => void,\n  ): void {\n    try {\n      const store = this.getStore()\n      const cookies = this.filterCookiesFromList(store, { domain, path, key })\n      callback(null, cookies[0] || null)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error, null)\n      }\n    }\n  }\n\n  findCookies(\n    domain: string,\n    path: string,\n    allowSpecialUseDomain: boolean,\n    callback: (error: Error | null, cookie: Array<CookieInstance>) => void,\n  ): void {\n    if (!domain) {\n      callback(null, [])\n      return\n    }\n\n    try {\n      const store = this.getStore()\n      const results = this.filterCookiesFromList(store, {\n        domain,\n        path,\n      })\n      callback(null, results)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error, [])\n      }\n    }\n  }\n\n  putCookie(\n    cookie: CookieInstance,\n    callback: (error: Error | null) => void,\n  ): void {\n    try {\n      // Never set cookies with `maxAge` of `0`.\n      if (cookie.maxAge === 0) {\n        return\n      }\n\n      const store = this.getStore()\n      store.push(cookie)\n      this.updateStore(store)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error)\n      }\n    }\n  }\n\n  updateCookie(\n    oldCookie: CookieInstance,\n    newCookie: CookieInstance,\n    callback: (error: Error | null) => void,\n  ): void {\n    /**\n     * If updating a cookie with `maxAge` of `0`, remove it from the store.\n     * Otherwise, two cookie entries will be created.\n     * @see https://github.com/mswjs/msw/issues/2272\n     */\n    if (newCookie.maxAge === 0) {\n      this.removeCookie(\n        newCookie.domain || '',\n        newCookie.path || '',\n        newCookie.key,\n        callback,\n      )\n      return\n    }\n\n    this.putCookie(newCookie, callback)\n  }\n\n  removeCookie(\n    domain: string,\n    path: string,\n    key: string,\n    callback: (error: Error | null) => void,\n  ): void {\n    try {\n      const store = this.getStore()\n      const nextStore = this.deleteCookiesFromList(store, { domain, path, key })\n      this.updateStore(nextStore)\n      callback(null)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error)\n      }\n    }\n  }\n\n  removeCookies(\n    domain: string,\n    path: string,\n    callback: (error: Error | null) => void,\n  ): void {\n    try {\n      const store = this.getStore()\n      const nextStore = this.deleteCookiesFromList(store, { domain, path })\n      this.updateStore(nextStore)\n      callback(null)\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error)\n      }\n    }\n  }\n\n  getAllCookies(\n    callback: (error: Error | null, cookie: Array<CookieInstance>) => void,\n  ): void {\n    try {\n      callback(null, this.getStore())\n    } catch (error) {\n      if (error instanceof Error) {\n        callback(error, [])\n      }\n    }\n  }\n\n  private getStore(): Array<CookieInstance> {\n    try {\n      const json = this.storage.getItem(this.storageKey)\n\n      if (json == null) {\n        return []\n      }\n\n      const rawCookies = JSON.parse(json) as Array<Record<string, any>>\n      const cookies: Array<CookieInstance> = []\n      for (const rawCookie of rawCookies) {\n        const cookie = Cookie.fromJSON(rawCookie)\n        if (cookie != null) {\n          cookies.push(cookie)\n        }\n      }\n      return cookies\n    } catch {\n      return []\n    }\n  }\n\n  private updateStore(nextStore: Array<CookieInstance>) {\n    this.storage.setItem(\n      this.storageKey,\n      JSON.stringify(nextStore.map((cookie) => cookie.toJSON())),\n    )\n  }\n\n  private filterCookiesFromList(\n    cookies: Array<CookieInstance>,\n    matches: { domain?: string; path?: string; key?: string },\n  ): Array<CookieInstance> {\n    const result: Array<CookieInstance> = []\n\n    for (const cookie of cookies) {\n      if (matches.domain && !domainMatch(matches.domain, cookie.domain || '')) {\n        continue\n      }\n\n      if (matches.path && !pathMatch(matches.path, cookie.path || '')) {\n        continue\n      }\n\n      if (matches.key && cookie.key !== matches.key) {\n        continue\n      }\n\n      result.push(cookie)\n    }\n\n    return result\n  }\n\n  private deleteCookiesFromList(\n    cookies: Array<CookieInstance>,\n    matches: { domain?: string; path?: string; key?: string },\n  ) {\n    const matchingCookies = this.filterCookiesFromList(cookies, matches)\n    return cookies.filter((cookie) => !matchingCookies.includes(cookie))\n  }\n}\n\nconst store = isNodeProcess()\n  ? new MemoryCookieStore()\n  : new WebStorageCookieStore()\n\nexport const cookieStore = new CookieJar(store)\n"], "mappings": "AAAA,SAASA,SAAA,QAAiB;AAC1B,SAASC,aAAA,QAAqB;AAC9B,OAAOC,WAAA,MAEA;AAEP,MAAM;EAAEC,MAAA;EAAQC,SAAA;EAAWC,KAAA;EAAOC,iBAAA;EAAmBC,WAAA;EAAaC;AAAU,IAC1EN,WAAA;AAMF,MAAMO,qBAAA,SAA8BJ,KAAA,CAAM;EAChCK,OAAA;EACAC,UAAA;EAERC,YAAA,EAAc;IACZ,MAAM;IAENZ,SAAA,CACE,OAAOa,YAAA,KAAiB,aACxB,kMACF;IAEA,KAAKC,WAAA,GAAc;IACnB,KAAKJ,OAAA,GAAUG,YAAA;IACf,KAAKF,UAAA,GAAa;EACpB;EAEAI,WACEC,MAAA,EACAC,IAAA,EACAC,GAAA,EACAC,QAAA,EACM;IACN,IAAI;MACF,MAAMC,MAAA,GAAQ,KAAKC,QAAA,CAAS;MAC5B,MAAMC,OAAA,GAAU,KAAKC,qBAAA,CAAsBH,MAAA,EAAO;QAAEJ,MAAA;QAAQC,IAAA;QAAMC;MAAI,CAAC;MACvEC,QAAA,CAAS,MAAMG,OAAA,CAAQ,CAAC,KAAK,IAAI;IACnC,SAASE,KAAA,EAAO;MACd,IAAIA,KAAA,YAAiBC,KAAA,EAAO;QAC1BN,QAAA,CAASK,KAAA,EAAO,IAAI;MACtB;IACF;EACF;EAEAE,YACEV,MAAA,EACAC,IAAA,EACAU,qBAAA,EACAR,QAAA,EACM;IACN,IAAI,CAACH,MAAA,EAAQ;MACXG,QAAA,CAAS,MAAM,EAAE;MACjB;IACF;IAEA,IAAI;MACF,MAAMC,MAAA,GAAQ,KAAKC,QAAA,CAAS;MAC5B,MAAMO,OAAA,GAAU,KAAKL,qBAAA,CAAsBH,MAAA,EAAO;QAChDJ,MAAA;QACAC;MACF,CAAC;MACDE,QAAA,CAAS,MAAMS,OAAO;IACxB,SAASJ,KAAA,EAAO;MACd,IAAIA,KAAA,YAAiBC,KAAA,EAAO;QAC1BN,QAAA,CAASK,KAAA,EAAO,EAAE;MACpB;IACF;EACF;EAEAK,UACEC,MAAA,EACAX,QAAA,EACM;IACN,IAAI;MAEF,IAAIW,MAAA,CAAOC,MAAA,KAAW,GAAG;QACvB;MACF;MAEA,MAAMX,MAAA,GAAQ,KAAKC,QAAA,CAAS;MAC5BD,MAAA,CAAMY,IAAA,CAAKF,MAAM;MACjB,KAAKG,WAAA,CAAYb,MAAK;IACxB,SAASI,KAAA,EAAO;MACd,IAAIA,KAAA,YAAiBC,KAAA,EAAO;QAC1BN,QAAA,CAASK,KAAK;MAChB;IACF;EACF;EAEAU,aACEC,SAAA,EACAC,SAAA,EACAjB,QAAA,EACM;IAMN,IAAIiB,SAAA,CAAUL,MAAA,KAAW,GAAG;MAC1B,KAAKM,YAAA,CACHD,SAAA,CAAUpB,MAAA,IAAU,IACpBoB,SAAA,CAAUnB,IAAA,IAAQ,IAClBmB,SAAA,CAAUlB,GAAA,EACVC,QACF;MACA;IACF;IAEA,KAAKU,SAAA,CAAUO,SAAA,EAAWjB,QAAQ;EACpC;EAEAkB,aACErB,MAAA,EACAC,IAAA,EACAC,GAAA,EACAC,QAAA,EACM;IACN,IAAI;MACF,MAAMC,MAAA,GAAQ,KAAKC,QAAA,CAAS;MAC5B,MAAMiB,SAAA,GAAY,KAAKC,qBAAA,CAAsBnB,MAAA,EAAO;QAAEJ,MAAA;QAAQC,IAAA;QAAMC;MAAI,CAAC;MACzE,KAAKe,WAAA,CAAYK,SAAS;MAC1BnB,QAAA,CAAS,IAAI;IACf,SAASK,KAAA,EAAO;MACd,IAAIA,KAAA,YAAiBC,KAAA,EAAO;QAC1BN,QAAA,CAASK,KAAK;MAChB;IACF;EACF;EAEAgB,cACExB,MAAA,EACAC,IAAA,EACAE,QAAA,EACM;IACN,IAAI;MACF,MAAMC,MAAA,GAAQ,KAAKC,QAAA,CAAS;MAC5B,MAAMiB,SAAA,GAAY,KAAKC,qBAAA,CAAsBnB,MAAA,EAAO;QAAEJ,MAAA;QAAQC;MAAK,CAAC;MACpE,KAAKgB,WAAA,CAAYK,SAAS;MAC1BnB,QAAA,CAAS,IAAI;IACf,SAASK,KAAA,EAAO;MACd,IAAIA,KAAA,YAAiBC,KAAA,EAAO;QAC1BN,QAAA,CAASK,KAAK;MAChB;IACF;EACF;EAEAiB,cACEtB,QAAA,EACM;IACN,IAAI;MACFA,QAAA,CAAS,MAAM,KAAKE,QAAA,CAAS,CAAC;IAChC,SAASG,KAAA,EAAO;MACd,IAAIA,KAAA,YAAiBC,KAAA,EAAO;QAC1BN,QAAA,CAASK,KAAA,EAAO,EAAE;MACpB;IACF;EACF;EAEQH,SAAA,EAAkC;IACxC,IAAI;MACF,MAAMqB,IAAA,GAAO,KAAKhC,OAAA,CAAQiC,OAAA,CAAQ,KAAKhC,UAAU;MAEjD,IAAI+B,IAAA,IAAQ,MAAM;QAChB,OAAO,EAAC;MACV;MAEA,MAAME,UAAA,GAAaC,IAAA,CAAKC,KAAA,CAAMJ,IAAI;MAClC,MAAMpB,OAAA,GAAiC,EAAC;MACxC,WAAWyB,SAAA,IAAaH,UAAA,EAAY;QAClC,MAAMd,MAAA,GAAS3B,MAAA,CAAO6C,QAAA,CAASD,SAAS;QACxC,IAAIjB,MAAA,IAAU,MAAM;UAClBR,OAAA,CAAQU,IAAA,CAAKF,MAAM;QACrB;MACF;MACA,OAAOR,OAAA;IACT,QAAQ;MACN,OAAO,EAAC;IACV;EACF;EAEQW,YAAYK,SAAA,EAAkC;IACpD,KAAK5B,OAAA,CAAQuC,OAAA,CACX,KAAKtC,UAAA,EACLkC,IAAA,CAAKK,SAAA,CAAUZ,SAAA,CAAUa,GAAA,CAAKrB,MAAA,IAAWA,MAAA,CAAOsB,MAAA,CAAO,CAAC,CAAC,CAC3D;EACF;EAEQ7B,sBACND,OAAA,EACA+B,OAAA,EACuB;IACvB,MAAMC,MAAA,GAAgC,EAAC;IAEvC,WAAWxB,MAAA,IAAUR,OAAA,EAAS;MAC5B,IAAI+B,OAAA,CAAQrC,MAAA,IAAU,CAACT,WAAA,CAAY8C,OAAA,CAAQrC,MAAA,EAAQc,MAAA,CAAOd,MAAA,IAAU,EAAE,GAAG;QACvE;MACF;MAEA,IAAIqC,OAAA,CAAQpC,IAAA,IAAQ,CAACT,SAAA,CAAU6C,OAAA,CAAQpC,IAAA,EAAMa,MAAA,CAAOb,IAAA,IAAQ,EAAE,GAAG;QAC/D;MACF;MAEA,IAAIoC,OAAA,CAAQnC,GAAA,IAAOY,MAAA,CAAOZ,GAAA,KAAQmC,OAAA,CAAQnC,GAAA,EAAK;QAC7C;MACF;MAEAoC,MAAA,CAAOtB,IAAA,CAAKF,MAAM;IACpB;IAEA,OAAOwB,MAAA;EACT;EAEQf,sBACNjB,OAAA,EACA+B,OAAA,EACA;IACA,MAAME,eAAA,GAAkB,KAAKhC,qBAAA,CAAsBD,OAAA,EAAS+B,OAAO;IACnE,OAAO/B,OAAA,CAAQkC,MAAA,CAAQ1B,MAAA,IAAW,CAACyB,eAAA,CAAgBE,QAAA,CAAS3B,MAAM,CAAC;EACrE;AACF;AAEA,MAAM4B,KAAA,GAAQzD,aAAA,CAAc,IACxB,IAAIK,iBAAA,CAAkB,IACtB,IAAIG,qBAAA,CAAsB;AAEvB,MAAMkD,WAAA,GAAc,IAAIvD,SAAA,CAAUsD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}