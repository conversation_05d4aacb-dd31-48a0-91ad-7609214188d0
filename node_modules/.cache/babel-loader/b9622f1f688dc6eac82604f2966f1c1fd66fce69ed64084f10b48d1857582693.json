{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getRatingUtilityClass(slot) {\n  return generateUtilityClass('MuiRating', slot);\n}\nconst ratingClasses = generateUtilityClasses('MuiRating', ['root', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'readOnly', 'disabled', 'focusVisible', 'visuallyHidden', 'pristine', 'label', 'labelEmptyValueActive', 'icon', 'iconEmpty', 'iconFilled', 'iconHover', 'iconFocus', 'iconActive', 'decimal']);\nexport default ratingClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getRatingUtilityClass", "slot", "ratingClasses"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mui/material/Rating/ratingClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getRatingUtilityClass(slot) {\n  return generateUtilityClass('MuiRating', slot);\n}\nconst ratingClasses = generateUtilityClasses('MuiRating', ['root', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'readOnly', 'disabled', 'focusVisible', 'visuallyHidden', 'pristine', 'label', 'labelEmptyValueActive', 'icon', 'iconEmpty', 'iconFilled', 'iconHover', 'iconFocus', 'iconActive', 'decimal']);\nexport default ratingClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOF,oBAAoB,CAAC,WAAW,EAAEE,IAAI,CAAC;AAChD;AACA,MAAMC,aAAa,GAAGJ,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAgB,EAAE,UAAU,EAAE,OAAO,EAAE,uBAAuB,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;AACzS,eAAeI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}