{"ast": null, "code": "function isObject(value) {\n  return value != null && typeof value === \"object\" && !Array.isArray(value);\n}\nexport { isObject };", "map": {"version": 3, "names": ["isObject", "value", "Array", "isArray"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/internal/isObject.ts"], "sourcesContent": ["/**\n * Determines if the given value is an object.\n */\nexport function isObject(value: any): boolean {\n  return value != null && typeof value === 'object' && !Array.isArray(value)\n}\n"], "mappings": "AAGO,SAASA,SAASC,KAAA,EAAqB;EAC5C,OAAOA,KAAA,IAAS,QAAQ,OAAOA,KAAA,KAAU,YAAY,CAACC,KAAA,CAAMC,OAAA,CAAQF,KAAK;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}