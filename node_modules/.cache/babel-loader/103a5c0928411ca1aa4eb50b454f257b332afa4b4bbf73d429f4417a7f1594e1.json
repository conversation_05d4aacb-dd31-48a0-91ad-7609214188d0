{"ast": null, "code": "import { DeferredPromise } from \"@open-draft/deferred-promise\";\nconst DB_NAME = \"msw-websocket-clients\";\nconst DB_STORE_NAME = \"clients\";\nclass WebSocketIndexedDBClientStore {\n  db;\n  constructor() {\n    this.db = this.createDatabase();\n  }\n  async add(client) {\n    const promise = new DeferredPromise();\n    const store = await this.getStore();\n    const request = store.put({\n      id: client.id,\n      url: client.url.href\n    });\n    request.onsuccess = () => {\n      promise.resolve();\n    };\n    request.onerror = () => {\n      console.error(request.error);\n      promise.reject(new Error(`Failed to add WebSocket client \"${client.id}\". There is likely an additional output above.`));\n    };\n    return promise;\n  }\n  async getAll() {\n    const promise = new DeferredPromise();\n    const store = await this.getStore();\n    const request = store.getAll();\n    request.onsuccess = () => {\n      promise.resolve(request.result);\n    };\n    request.onerror = () => {\n      console.log(request.error);\n      promise.reject(new Error(`Failed to get all WebSocket clients. There is likely an additional output above.`));\n    };\n    return promise;\n  }\n  async deleteMany(clientIds) {\n    const promise = new DeferredPromise();\n    const store = await this.getStore();\n    for (const clientId of clientIds) {\n      store.delete(clientId);\n    }\n    store.transaction.oncomplete = () => {\n      promise.resolve();\n    };\n    store.transaction.onerror = () => {\n      console.error(store.transaction.error);\n      promise.reject(new Error(`Failed to delete WebSocket clients [${clientIds.join(\", \")}]. There is likely an additional output above.`));\n    };\n    return promise;\n  }\n  async createDatabase() {\n    const promise = new DeferredPromise();\n    const request = indexedDB.open(DB_NAME, 1);\n    request.onsuccess = ({\n      currentTarget\n    }) => {\n      const db = Reflect.get(currentTarget, \"result\");\n      if (db.objectStoreNames.contains(DB_STORE_NAME)) {\n        return promise.resolve(db);\n      }\n    };\n    request.onupgradeneeded = async ({\n      currentTarget\n    }) => {\n      const db = Reflect.get(currentTarget, \"result\");\n      if (db.objectStoreNames.contains(DB_STORE_NAME)) {\n        return;\n      }\n      const store = db.createObjectStore(DB_STORE_NAME, {\n        keyPath: \"id\"\n      });\n      store.transaction.oncomplete = () => {\n        promise.resolve(db);\n      };\n      store.transaction.onerror = () => {\n        console.error(store.transaction.error);\n        promise.reject(new Error(\"Failed to create WebSocket client store. There is likely an additional output above.\"));\n      };\n    };\n    request.onerror = () => {\n      console.error(request.error);\n      promise.reject(new Error(\"Failed to open an IndexedDB database. There is likely an additional output above.\"));\n    };\n    return promise;\n  }\n  async getStore() {\n    const db = await this.db;\n    return db.transaction(DB_STORE_NAME, \"readwrite\").objectStore(DB_STORE_NAME);\n  }\n}\nexport { WebSocketIndexedDBClientStore };", "map": {"version": 3, "names": ["DeferredPromise", "DB_NAME", "DB_STORE_NAME", "WebSocketIndexedDBClientStore", "db", "constructor", "createDatabase", "add", "client", "promise", "store", "getStore", "request", "put", "id", "url", "href", "onsuccess", "resolve", "onerror", "console", "error", "reject", "Error", "getAll", "result", "log", "deleteMany", "clientIds", "clientId", "delete", "transaction", "oncomplete", "join", "indexedDB", "open", "currentTarget", "Reflect", "get", "objectStoreNames", "contains", "onupgradeneeded", "createObjectStore", "keyP<PERSON>", "objectStore"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/ws/WebSocketIndexedDBClientStore.ts"], "sourcesContent": ["import { DeferredPromise } from '@open-draft/deferred-promise'\nimport { WebSocketClientConnectionProtocol } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\nimport {\n  type SerializedWebSocketClient,\n  WebSocketClientStore,\n} from './WebSocketClientStore'\n\nconst DB_NAME = 'msw-websocket-clients'\nconst DB_STORE_NAME = 'clients'\n\nexport class WebSocketIndexedDBClientStore implements WebSocketClientStore {\n  private db: Promise<IDBDatabase>\n\n  constructor() {\n    this.db = this.createDatabase()\n  }\n\n  public async add(client: WebSocketClientConnectionProtocol): Promise<void> {\n    const promise = new DeferredPromise<void>()\n    const store = await this.getStore()\n\n    /**\n     * @note Use `.put()` instead of `.add()` to allow setting clients\n     * that already exist in the database. This can happen if a single page\n     * has multiple event handlers. Each handler will receive the \"connection\"\n     * event in parallel, and try to set that WebSocket client in the database.\n     */\n    const request = store.put({\n      id: client.id,\n      url: client.url.href,\n    } satisfies SerializedWebSocketClient)\n\n    request.onsuccess = () => {\n      promise.resolve()\n    }\n    request.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.error(request.error)\n      promise.reject(\n        new Error(\n          `Failed to add WebSocket client \"${client.id}\". There is likely an additional output above.`,\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  public async getAll(): Promise<Array<SerializedWebSocketClient>> {\n    const promise = new DeferredPromise<Array<SerializedWebSocketClient>>()\n    const store = await this.getStore()\n    const request = store.getAll() as IDBRequest<\n      Array<SerializedWebSocketClient>\n    >\n\n    request.onsuccess = () => {\n      promise.resolve(request.result)\n    }\n    request.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.log(request.error)\n      promise.reject(\n        new Error(\n          `Failed to get all WebSocket clients. There is likely an additional output above.`,\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  public async deleteMany(clientIds: Array<string>): Promise<void> {\n    const promise = new DeferredPromise<void>()\n    const store = await this.getStore()\n\n    for (const clientId of clientIds) {\n      store.delete(clientId)\n    }\n\n    store.transaction.oncomplete = () => {\n      promise.resolve()\n    }\n    store.transaction.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.error(store.transaction.error)\n      promise.reject(\n        new Error(\n          `Failed to delete WebSocket clients [${clientIds.join(', ')}]. There is likely an additional output above.`,\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  private async createDatabase(): Promise<IDBDatabase> {\n    const promise = new DeferredPromise<IDBDatabase>()\n    const request = indexedDB.open(DB_NAME, 1)\n\n    request.onsuccess = ({ currentTarget }) => {\n      const db = Reflect.get(currentTarget!, 'result') as IDBDatabase\n\n      if (db.objectStoreNames.contains(DB_STORE_NAME)) {\n        return promise.resolve(db)\n      }\n    }\n\n    request.onupgradeneeded = async ({ currentTarget }) => {\n      const db = Reflect.get(currentTarget!, 'result') as IDBDatabase\n      if (db.objectStoreNames.contains(DB_STORE_NAME)) {\n        return\n      }\n\n      const store = db.createObjectStore(DB_STORE_NAME, { keyPath: 'id' })\n      store.transaction.oncomplete = () => {\n        promise.resolve(db)\n      }\n      store.transaction.onerror = () => {\n        // eslint-disable-next-line no-console\n        console.error(store.transaction.error)\n        promise.reject(\n          new Error(\n            'Failed to create WebSocket client store. There is likely an additional output above.',\n          ),\n        )\n      }\n    }\n    request.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.error(request.error)\n      promise.reject(\n        new Error(\n          'Failed to open an IndexedDB database. There is likely an additional output above.',\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  private async getStore(): Promise<IDBObjectStore> {\n    const db = await this.db\n    return db.transaction(DB_STORE_NAME, 'readwrite').objectStore(DB_STORE_NAME)\n  }\n}\n"], "mappings": "AAAA,SAASA,eAAA,QAAuB;AAOhC,MAAMC,OAAA,GAAU;AAChB,MAAMC,aAAA,GAAgB;AAEf,MAAMC,6BAAA,CAA8D;EACjEC,EAAA;EAERC,YAAA,EAAc;IACZ,KAAKD,EAAA,GAAK,KAAKE,cAAA,CAAe;EAChC;EAEA,MAAaC,IAAIC,MAAA,EAA0D;IACzE,MAAMC,OAAA,GAAU,IAAIT,eAAA,CAAsB;IAC1C,MAAMU,KAAA,GAAQ,MAAM,KAAKC,QAAA,CAAS;IAQlC,MAAMC,OAAA,GAAUF,KAAA,CAAMG,GAAA,CAAI;MACxBC,EAAA,EAAIN,MAAA,CAAOM,EAAA;MACXC,GAAA,EAAKP,MAAA,CAAOO,GAAA,CAAIC;IAClB,CAAqC;IAErCJ,OAAA,CAAQK,SAAA,GAAY,MAAM;MACxBR,OAAA,CAAQS,OAAA,CAAQ;IAClB;IACAN,OAAA,CAAQO,OAAA,GAAU,MAAM;MAEtBC,OAAA,CAAQC,KAAA,CAAMT,OAAA,CAAQS,KAAK;MAC3BZ,OAAA,CAAQa,MAAA,CACN,IAAIC,KAAA,CACF,mCAAmCf,MAAA,CAAOM,EAAE,gDAC9C,CACF;IACF;IAEA,OAAOL,OAAA;EACT;EAEA,MAAae,OAAA,EAAoD;IAC/D,MAAMf,OAAA,GAAU,IAAIT,eAAA,CAAkD;IACtE,MAAMU,KAAA,GAAQ,MAAM,KAAKC,QAAA,CAAS;IAClC,MAAMC,OAAA,GAAUF,KAAA,CAAMc,MAAA,CAAO;IAI7BZ,OAAA,CAAQK,SAAA,GAAY,MAAM;MACxBR,OAAA,CAAQS,OAAA,CAAQN,OAAA,CAAQa,MAAM;IAChC;IACAb,OAAA,CAAQO,OAAA,GAAU,MAAM;MAEtBC,OAAA,CAAQM,GAAA,CAAId,OAAA,CAAQS,KAAK;MACzBZ,OAAA,CAAQa,MAAA,CACN,IAAIC,KAAA,CACF,kFACF,CACF;IACF;IAEA,OAAOd,OAAA;EACT;EAEA,MAAakB,WAAWC,SAAA,EAAyC;IAC/D,MAAMnB,OAAA,GAAU,IAAIT,eAAA,CAAsB;IAC1C,MAAMU,KAAA,GAAQ,MAAM,KAAKC,QAAA,CAAS;IAElC,WAAWkB,QAAA,IAAYD,SAAA,EAAW;MAChClB,KAAA,CAAMoB,MAAA,CAAOD,QAAQ;IACvB;IAEAnB,KAAA,CAAMqB,WAAA,CAAYC,UAAA,GAAa,MAAM;MACnCvB,OAAA,CAAQS,OAAA,CAAQ;IAClB;IACAR,KAAA,CAAMqB,WAAA,CAAYZ,OAAA,GAAU,MAAM;MAEhCC,OAAA,CAAQC,KAAA,CAAMX,KAAA,CAAMqB,WAAA,CAAYV,KAAK;MACrCZ,OAAA,CAAQa,MAAA,CACN,IAAIC,KAAA,CACF,uCAAuCK,SAAA,CAAUK,IAAA,CAAK,IAAI,CAAC,gDAC7D,CACF;IACF;IAEA,OAAOxB,OAAA;EACT;EAEA,MAAcH,eAAA,EAAuC;IACnD,MAAMG,OAAA,GAAU,IAAIT,eAAA,CAA6B;IACjD,MAAMY,OAAA,GAAUsB,SAAA,CAAUC,IAAA,CAAKlC,OAAA,EAAS,CAAC;IAEzCW,OAAA,CAAQK,SAAA,GAAY,CAAC;MAAEmB;IAAc,MAAM;MACzC,MAAMhC,EAAA,GAAKiC,OAAA,CAAQC,GAAA,CAAIF,aAAA,EAAgB,QAAQ;MAE/C,IAAIhC,EAAA,CAAGmC,gBAAA,CAAiBC,QAAA,CAAStC,aAAa,GAAG;QAC/C,OAAOO,OAAA,CAAQS,OAAA,CAAQd,EAAE;MAC3B;IACF;IAEAQ,OAAA,CAAQ6B,eAAA,GAAkB,OAAO;MAAEL;IAAc,MAAM;MACrD,MAAMhC,EAAA,GAAKiC,OAAA,CAAQC,GAAA,CAAIF,aAAA,EAAgB,QAAQ;MAC/C,IAAIhC,EAAA,CAAGmC,gBAAA,CAAiBC,QAAA,CAAStC,aAAa,GAAG;QAC/C;MACF;MAEA,MAAMQ,KAAA,GAAQN,EAAA,CAAGsC,iBAAA,CAAkBxC,aAAA,EAAe;QAAEyC,OAAA,EAAS;MAAK,CAAC;MACnEjC,KAAA,CAAMqB,WAAA,CAAYC,UAAA,GAAa,MAAM;QACnCvB,OAAA,CAAQS,OAAA,CAAQd,EAAE;MACpB;MACAM,KAAA,CAAMqB,WAAA,CAAYZ,OAAA,GAAU,MAAM;QAEhCC,OAAA,CAAQC,KAAA,CAAMX,KAAA,CAAMqB,WAAA,CAAYV,KAAK;QACrCZ,OAAA,CAAQa,MAAA,CACN,IAAIC,KAAA,CACF,sFACF,CACF;MACF;IACF;IACAX,OAAA,CAAQO,OAAA,GAAU,MAAM;MAEtBC,OAAA,CAAQC,KAAA,CAAMT,OAAA,CAAQS,KAAK;MAC3BZ,OAAA,CAAQa,MAAA,CACN,IAAIC,KAAA,CACF,mFACF,CACF;IACF;IAEA,OAAOd,OAAA;EACT;EAEA,MAAcE,SAAA,EAAoC;IAChD,MAAMP,EAAA,GAAK,MAAM,KAAKA,EAAA;IACtB,OAAOA,EAAA,CAAG2B,WAAA,CAAY7B,aAAA,EAAe,WAAW,EAAE0C,WAAA,CAAY1C,aAAa;EAC7E;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}