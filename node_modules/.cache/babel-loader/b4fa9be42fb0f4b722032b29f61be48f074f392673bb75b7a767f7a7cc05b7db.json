{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { isCompositeType } from '../../type/definition.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Fragments on composite type\n *\n * Fragments use a type condition to determine if they apply, since fragments\n * can only be spread into a composite type (object, interface, or union), the\n * type condition must also be a composite type.\n *\n * See https://spec.graphql.org/draft/#sec-Fragments-On-Composite-Types\n */\nexport function FragmentsOnCompositeTypesRule(context) {\n  return {\n    InlineFragment(node) {\n      const typeCondition = node.typeCondition;\n      if (typeCondition) {\n        const type = typeFromAST(context.getSchema(), typeCondition);\n        if (type && !isCompositeType(type)) {\n          const typeStr = print(typeCondition);\n          context.reportError(new GraphQLError(`Fragment cannot condition on non composite type \"${typeStr}\".`, {\n            nodes: typeCondition\n          }));\n        }\n      }\n    },\n    FragmentDefinition(node) {\n      const type = typeFromAST(context.getSchema(), node.typeCondition);\n      if (type && !isCompositeType(type)) {\n        const typeStr = print(node.typeCondition);\n        context.reportError(new GraphQLError(`Fragment \"${node.name.value}\" cannot condition on non composite type \"${typeStr}\".`, {\n          nodes: node.typeCondition\n        }));\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "print", "isCompositeType", "typeFromAST", "FragmentsOnCompositeTypesRule", "context", "InlineFragment", "node", "typeCondition", "type", "getSchema", "typeStr", "reportError", "nodes", "FragmentDefinition", "name", "value"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/FragmentsOnCompositeTypesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { isCompositeType } from '../../type/definition.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Fragments on composite type\n *\n * Fragments use a type condition to determine if they apply, since fragments\n * can only be spread into a composite type (object, interface, or union), the\n * type condition must also be a composite type.\n *\n * See https://spec.graphql.org/draft/#sec-Fragments-On-Composite-Types\n */\nexport function FragmentsOnCompositeTypesRule(context) {\n  return {\n    InlineFragment(node) {\n      const typeCondition = node.typeCondition;\n\n      if (typeCondition) {\n        const type = typeFromAST(context.getSchema(), typeCondition);\n\n        if (type && !isCompositeType(type)) {\n          const typeStr = print(typeCondition);\n          context.reportError(\n            new GraphQLError(\n              `Fragment cannot condition on non composite type \"${typeStr}\".`,\n              {\n                nodes: typeCondition,\n              },\n            ),\n          );\n        }\n      }\n    },\n\n    FragmentDefinition(node) {\n      const type = typeFromAST(context.getSchema(), node.typeCondition);\n\n      if (type && !isCompositeType(type)) {\n        const typeStr = print(node.typeCondition);\n        context.reportError(\n          new GraphQLError(\n            `Fragment \"${node.name.value}\" cannot condition on non composite type \"${typeStr}\".`,\n            {\n              nodes: node.typeCondition,\n            },\n          ),\n        );\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,KAAK,QAAQ,4BAA4B;AAClD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,WAAW,QAAQ,iCAAiC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,6BAA6BA,CAACC,OAAO,EAAE;EACrD,OAAO;IACLC,cAAcA,CAACC,IAAI,EAAE;MACnB,MAAMC,aAAa,GAAGD,IAAI,CAACC,aAAa;MAExC,IAAIA,aAAa,EAAE;QACjB,MAAMC,IAAI,GAAGN,WAAW,CAACE,OAAO,CAACK,SAAS,CAAC,CAAC,EAAEF,aAAa,CAAC;QAE5D,IAAIC,IAAI,IAAI,CAACP,eAAe,CAACO,IAAI,CAAC,EAAE;UAClC,MAAME,OAAO,GAAGV,KAAK,CAACO,aAAa,CAAC;UACpCH,OAAO,CAACO,WAAW,CACjB,IAAIZ,YAAY,CACd,oDAAoDW,OAAO,IAAI,EAC/D;YACEE,KAAK,EAAEL;UACT,CACF,CACF,CAAC;QACH;MACF;IACF,CAAC;IAEDM,kBAAkBA,CAACP,IAAI,EAAE;MACvB,MAAME,IAAI,GAAGN,WAAW,CAACE,OAAO,CAACK,SAAS,CAAC,CAAC,EAAEH,IAAI,CAACC,aAAa,CAAC;MAEjE,IAAIC,IAAI,IAAI,CAACP,eAAe,CAACO,IAAI,CAAC,EAAE;QAClC,MAAME,OAAO,GAAGV,KAAK,CAACM,IAAI,CAACC,aAAa,CAAC;QACzCH,OAAO,CAACO,WAAW,CACjB,IAAIZ,YAAY,CACd,aAAaO,IAAI,CAACQ,IAAI,CAACC,KAAK,6CAA6CL,OAAO,IAAI,EACpF;UACEE,KAAK,EAAEN,IAAI,CAACC;QACd,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}