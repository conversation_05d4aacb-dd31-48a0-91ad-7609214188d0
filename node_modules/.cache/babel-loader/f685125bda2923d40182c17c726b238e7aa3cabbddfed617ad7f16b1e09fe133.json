{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _span;\nconst _excluded = [\"children\", \"classes\", \"className\", \"label\", \"notched\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NotchedOutlineRoot = styled('fieldset', {\n  name: 'MuiNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})({\n  textAlign: 'left',\n  position: 'absolute',\n  bottom: 0,\n  right: 0,\n  top: -5,\n  left: 0,\n  margin: 0,\n  padding: '0 8px',\n  pointerEvents: 'none',\n  borderRadius: 'inherit',\n  borderStyle: 'solid',\n  borderWidth: 1,\n  overflow: 'hidden',\n  minWidth: '0%'\n});\nconst NotchedOutlineLegend = styled('legend', {\n  name: 'MuiNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  float: 'unset',\n  // Fix conflict with bootstrap\n  width: 'auto',\n  // Fix conflict with bootstrap\n  overflow: 'hidden'\n}, !ownerState.withLabel && {\n  padding: 0,\n  lineHeight: '11px',\n  // sync with `height` in `legend` styles\n  transition: theme.transitions.create('width', {\n    duration: 150,\n    easing: theme.transitions.easing.easeOut\n  })\n}, ownerState.withLabel && _extends({\n  display: 'block',\n  // Fix conflict with normalize.css and sanitize.css\n  padding: 0,\n  height: 11,\n  // sync with `lineHeight` in `legend` styles\n  fontSize: '0.75em',\n  visibility: 'hidden',\n  maxWidth: 0.01,\n  transition: theme.transitions.create('max-width', {\n    duration: 50,\n    easing: theme.transitions.easing.easeOut\n  }),\n  whiteSpace: 'nowrap',\n  '& > span': {\n    paddingLeft: 5,\n    paddingRight: 5,\n    display: 'inline-block',\n    opacity: 0,\n    visibility: 'visible'\n  }\n}, ownerState.notched && {\n  maxWidth: '100%',\n  transition: theme.transitions.create('max-width', {\n    duration: 100,\n    easing: theme.transitions.easing.easeOut,\n    delay: 50\n  })\n})));\n\n/**\n * @ignore - internal component.\n */\nexport default function NotchedOutline(props) {\n  const {\n      className,\n      label,\n      notched\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const withLabel = label != null && label !== '';\n  const ownerState = _extends({}, props, {\n    notched,\n    withLabel\n  });\n  return /*#__PURE__*/_jsx(NotchedOutlineRoot, _extends({\n    \"aria-hidden\": true,\n    className: className,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(NotchedOutlineLegend, {\n      ownerState: ownerState,\n      children: withLabel ? /*#__PURE__*/_jsx(\"span\", {\n        children: label\n      }) :\n      // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        children: \"\\u200B\"\n      }))\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? NotchedOutline.propTypes = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The label.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, the outline is notched to accommodate the label.\n   */\n  notched: PropTypes.bool.isRequired,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object\n} : void 0;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_span", "_excluded", "React", "PropTypes", "styled", "rootShouldForwardProp", "jsx", "_jsx", "NotchedOutlineRoot", "name", "shouldForwardProp", "textAlign", "position", "bottom", "right", "top", "left", "margin", "padding", "pointerEvents", "borderRadius", "borderStyle", "borderWidth", "overflow", "min<PERSON><PERSON><PERSON>", "NotchedOutlineLegend", "ownerState", "theme", "float", "width", "<PERSON><PERSON><PERSON><PERSON>", "lineHeight", "transition", "transitions", "create", "duration", "easing", "easeOut", "display", "height", "fontSize", "visibility", "max<PERSON><PERSON><PERSON>", "whiteSpace", "paddingLeft", "paddingRight", "opacity", "notched", "delay", "NotchedOutline", "props", "className", "label", "other", "children", "process", "env", "NODE_ENV", "propTypes", "node", "classes", "object", "string", "bool", "isRequired", "style"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mui/material/OutlinedInput/NotchedOutline.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _span;\nconst _excluded = [\"children\", \"classes\", \"className\", \"label\", \"notched\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NotchedOutlineRoot = styled('fieldset', {\n  name: 'MuiNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})({\n  textAlign: 'left',\n  position: 'absolute',\n  bottom: 0,\n  right: 0,\n  top: -5,\n  left: 0,\n  margin: 0,\n  padding: '0 8px',\n  pointerEvents: 'none',\n  borderRadius: 'inherit',\n  borderStyle: 'solid',\n  borderWidth: 1,\n  overflow: 'hidden',\n  minWidth: '0%'\n});\nconst NotchedOutlineLegend = styled('legend', {\n  name: 'MuiNotchedOutlined',\n  shouldForwardProp: rootShouldForwardProp\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  float: 'unset',\n  // Fix conflict with bootstrap\n  width: 'auto',\n  // Fix conflict with bootstrap\n  overflow: 'hidden'\n}, !ownerState.withLabel && {\n  padding: 0,\n  lineHeight: '11px',\n  // sync with `height` in `legend` styles\n  transition: theme.transitions.create('width', {\n    duration: 150,\n    easing: theme.transitions.easing.easeOut\n  })\n}, ownerState.withLabel && _extends({\n  display: 'block',\n  // Fix conflict with normalize.css and sanitize.css\n  padding: 0,\n  height: 11,\n  // sync with `lineHeight` in `legend` styles\n  fontSize: '0.75em',\n  visibility: 'hidden',\n  maxWidth: 0.01,\n  transition: theme.transitions.create('max-width', {\n    duration: 50,\n    easing: theme.transitions.easing.easeOut\n  }),\n  whiteSpace: 'nowrap',\n  '& > span': {\n    paddingLeft: 5,\n    paddingRight: 5,\n    display: 'inline-block',\n    opacity: 0,\n    visibility: 'visible'\n  }\n}, ownerState.notched && {\n  maxWidth: '100%',\n  transition: theme.transitions.create('max-width', {\n    duration: 100,\n    easing: theme.transitions.easing.easeOut,\n    delay: 50\n  })\n})));\n\n/**\n * @ignore - internal component.\n */\nexport default function NotchedOutline(props) {\n  const {\n      className,\n      label,\n      notched\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const withLabel = label != null && label !== '';\n  const ownerState = _extends({}, props, {\n    notched,\n    withLabel\n  });\n  return /*#__PURE__*/_jsx(NotchedOutlineRoot, _extends({\n    \"aria-hidden\": true,\n    className: className,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(NotchedOutlineLegend, {\n      ownerState: ownerState,\n      children: withLabel ? /*#__PURE__*/_jsx(\"span\", {\n        children: label\n      }) : // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        children: \"\\u200B\"\n      }))\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? NotchedOutline.propTypes = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The label.\n   */\n  label: PropTypes.node,\n  /**\n   * If `true`, the outline is notched to accommodate the label.\n   */\n  notched: PropTypes.bool.isRequired,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,KAAK;AACT,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,CAAC;AAC1E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,MAAM,IAAIC,qBAAqB,QAAQ,kBAAkB;AAChE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGJ,MAAM,CAAC,UAAU,EAAE;EAC5CK,IAAI,EAAE,oBAAoB;EAC1BC,iBAAiB,EAAEL;AACrB,CAAC,CAAC,CAAC;EACDM,SAAS,EAAE,MAAM;EACjBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,OAAO;EAChBC,aAAa,EAAE,MAAM;EACrBC,YAAY,EAAE,SAAS;EACvBC,WAAW,EAAE,OAAO;EACpBC,WAAW,EAAE,CAAC;EACdC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMC,oBAAoB,GAAGrB,MAAM,CAAC,QAAQ,EAAE;EAC5CK,IAAI,EAAE,oBAAoB;EAC1BC,iBAAiB,EAAEL;AACrB,CAAC,CAAC,CAAC,CAAC;EACFqB,UAAU;EACVC;AACF,CAAC,KAAK5B,QAAQ,CAAC;EACb6B,KAAK,EAAE,OAAO;EACd;EACAC,KAAK,EAAE,MAAM;EACb;EACAN,QAAQ,EAAE;AACZ,CAAC,EAAE,CAACG,UAAU,CAACI,SAAS,IAAI;EAC1BZ,OAAO,EAAE,CAAC;EACVa,UAAU,EAAE,MAAM;EAClB;EACAC,UAAU,EAAEL,KAAK,CAACM,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;IAC5CC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAET,KAAK,CAACM,WAAW,CAACG,MAAM,CAACC;EACnC,CAAC;AACH,CAAC,EAAEX,UAAU,CAACI,SAAS,IAAI/B,QAAQ,CAAC;EAClCuC,OAAO,EAAE,OAAO;EAChB;EACApB,OAAO,EAAE,CAAC;EACVqB,MAAM,EAAE,EAAE;EACV;EACAC,QAAQ,EAAE,QAAQ;EAClBC,UAAU,EAAE,QAAQ;EACpBC,QAAQ,EAAE,IAAI;EACdV,UAAU,EAAEL,KAAK,CAACM,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;IAChDC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAET,KAAK,CAACM,WAAW,CAACG,MAAM,CAACC;EACnC,CAAC,CAAC;EACFM,UAAU,EAAE,QAAQ;EACpB,UAAU,EAAE;IACVC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfP,OAAO,EAAE,cAAc;IACvBQ,OAAO,EAAE,CAAC;IACVL,UAAU,EAAE;EACd;AACF,CAAC,EAAEf,UAAU,CAACqB,OAAO,IAAI;EACvBL,QAAQ,EAAE,MAAM;EAChBV,UAAU,EAAEL,KAAK,CAACM,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;IAChDC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAET,KAAK,CAACM,WAAW,CAACG,MAAM,CAACC,OAAO;IACxCW,KAAK,EAAE;EACT,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;;AAEJ;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC5C,MAAM;MACFC,SAAS;MACTC,KAAK;MACLL;IACF,CAAC,GAAGG,KAAK;IACTG,KAAK,GAAGvD,6BAA6B,CAACoD,KAAK,EAAEjD,SAAS,CAAC;EACzD,MAAM6B,SAAS,GAAGsB,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,EAAE;EAC/C,MAAM1B,UAAU,GAAG3B,QAAQ,CAAC,CAAC,CAAC,EAAEmD,KAAK,EAAE;IACrCH,OAAO;IACPjB;EACF,CAAC,CAAC;EACF,OAAO,aAAavB,IAAI,CAACC,kBAAkB,EAAET,QAAQ,CAAC;IACpD,aAAa,EAAE,IAAI;IACnBoD,SAAS,EAAEA,SAAS;IACpBzB,UAAU,EAAEA;EACd,CAAC,EAAE2B,KAAK,EAAE;IACRC,QAAQ,EAAE,aAAa/C,IAAI,CAACkB,oBAAoB,EAAE;MAChDC,UAAU,EAAEA,UAAU;MACtB4B,QAAQ,EAAExB,SAAS,GAAG,aAAavB,IAAI,CAAC,MAAM,EAAE;QAC9C+C,QAAQ,EAAEF;MACZ,CAAC,CAAC;MAAG;MACLpD,KAAK,KAAKA,KAAK,GAAG,aAAaO,IAAI,CAAC,MAAM,EAAE;QAC1C4C,SAAS,EAAE,aAAa;QACxBG,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC,CAAC;AACL;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,cAAc,CAACS,SAAS,GAAG;EACjE;AACF;AACA;EACEJ,QAAQ,EAAEnD,SAAS,CAACwD,IAAI;EACxB;AACF;AACA;EACEC,OAAO,EAAEzD,SAAS,CAAC0D,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAEhD,SAAS,CAAC2D,MAAM;EAC3B;AACF;AACA;EACEV,KAAK,EAAEjD,SAAS,CAACwD,IAAI;EACrB;AACF;AACA;EACEZ,OAAO,EAAE5C,SAAS,CAAC4D,IAAI,CAACC,UAAU;EAClC;AACF;AACA;EACEC,KAAK,EAAE9D,SAAS,CAAC0D;AACnB,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}