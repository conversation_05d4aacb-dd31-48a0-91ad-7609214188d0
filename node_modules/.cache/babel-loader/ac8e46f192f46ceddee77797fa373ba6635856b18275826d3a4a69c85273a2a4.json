{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onChange\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport debounce from '../utils/debounce';\nimport { ownerWindow, unstable_useEnhancedEffect as useEnhancedEffect } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst styles = {\n  width: 99,\n  height: 99,\n  position: 'absolute',\n  top: -9999,\n  overflow: 'scroll'\n};\n\n/**\n * @ignore - internal component.\n * The component originates from https://github.com/STORIS/react-scrollbar-size.\n * It has been moved into the core in order to minimize the bundle size.\n */\nexport default function ScrollbarSize(props) {\n  const {\n      onChange\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const scrollbarHeight = React.useRef();\n  const nodeRef = React.useRef(null);\n  const setMeasurements = () => {\n    scrollbarHeight.current = nodeRef.current.offsetHeight - nodeRef.current.clientHeight;\n  };\n  useEnhancedEffect(() => {\n    const handleResize = debounce(() => {\n      const prevHeight = scrollbarHeight.current;\n      setMeasurements();\n      if (prevHeight !== scrollbarHeight.current) {\n        onChange(scrollbarHeight.current);\n      }\n    });\n    const containerWindow = ownerWindow(nodeRef.current);\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [onChange]);\n  React.useEffect(() => {\n    setMeasurements();\n    onChange(scrollbarHeight.current);\n  }, [onChange]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    style: styles\n  }, other, {\n    ref: nodeRef\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? ScrollbarSize.propTypes = {\n  onChange: PropTypes.func.isRequired\n} : void 0;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "debounce", "ownerWindow", "unstable_useEnhancedEffect", "useEnhancedEffect", "jsx", "_jsx", "styles", "width", "height", "position", "top", "overflow", "ScrollbarSize", "props", "onChange", "other", "scrollbarHeight", "useRef", "nodeRef", "setMeasurements", "current", "offsetHeight", "clientHeight", "handleResize", "prevHeight", "containerWindow", "addEventListener", "clear", "removeEventListener", "useEffect", "style", "ref", "process", "env", "NODE_ENV", "propTypes", "func", "isRequired"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mui/material/Tabs/ScrollbarSize.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onChange\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport debounce from '../utils/debounce';\nimport { ownerWindow, unstable_useEnhancedEffect as useEnhancedEffect } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst styles = {\n  width: 99,\n  height: 99,\n  position: 'absolute',\n  top: -9999,\n  overflow: 'scroll'\n};\n\n/**\n * @ignore - internal component.\n * The component originates from https://github.com/STORIS/react-scrollbar-size.\n * It has been moved into the core in order to minimize the bundle size.\n */\nexport default function ScrollbarSize(props) {\n  const {\n      onChange\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const scrollbarHeight = React.useRef();\n  const nodeRef = React.useRef(null);\n  const setMeasurements = () => {\n    scrollbarHeight.current = nodeRef.current.offsetHeight - nodeRef.current.clientHeight;\n  };\n  useEnhancedEffect(() => {\n    const handleResize = debounce(() => {\n      const prevHeight = scrollbarHeight.current;\n      setMeasurements();\n      if (prevHeight !== scrollbarHeight.current) {\n        onChange(scrollbarHeight.current);\n      }\n    });\n    const containerWindow = ownerWindow(nodeRef.current);\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [onChange]);\n  React.useEffect(() => {\n    setMeasurements();\n    onChange(scrollbarHeight.current);\n  }, [onChange]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    style: styles\n  }, other, {\n    ref: nodeRef\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? ScrollbarSize.propTypes = {\n  onChange: PropTypes.func.isRequired\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,CAAC;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,WAAW,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,UAAU;AACvF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,MAAM,GAAG;EACbC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,CAAC,IAAI;EACVC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,MAAM;MACFC;IACF,CAAC,GAAGD,KAAK;IACTE,KAAK,GAAGnB,6BAA6B,CAACiB,KAAK,EAAEhB,SAAS,CAAC;EACzD,MAAMmB,eAAe,GAAGlB,KAAK,CAACmB,MAAM,CAAC,CAAC;EACtC,MAAMC,OAAO,GAAGpB,KAAK,CAACmB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BH,eAAe,CAACI,OAAO,GAAGF,OAAO,CAACE,OAAO,CAACC,YAAY,GAAGH,OAAO,CAACE,OAAO,CAACE,YAAY;EACvF,CAAC;EACDnB,iBAAiB,CAAC,MAAM;IACtB,MAAMoB,YAAY,GAAGvB,QAAQ,CAAC,MAAM;MAClC,MAAMwB,UAAU,GAAGR,eAAe,CAACI,OAAO;MAC1CD,eAAe,CAAC,CAAC;MACjB,IAAIK,UAAU,KAAKR,eAAe,CAACI,OAAO,EAAE;QAC1CN,QAAQ,CAACE,eAAe,CAACI,OAAO,CAAC;MACnC;IACF,CAAC,CAAC;IACF,MAAMK,eAAe,GAAGxB,WAAW,CAACiB,OAAO,CAACE,OAAO,CAAC;IACpDK,eAAe,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACxD,OAAO,MAAM;MACXA,YAAY,CAACI,KAAK,CAAC,CAAC;MACpBF,eAAe,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;EACdhB,KAAK,CAAC+B,SAAS,CAAC,MAAM;IACpBV,eAAe,CAAC,CAAC;IACjBL,QAAQ,CAACE,eAAe,CAACI,OAAO,CAAC;EACnC,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EACd,OAAO,aAAaT,IAAI,CAAC,KAAK,EAAEV,QAAQ,CAAC;IACvCmC,KAAK,EAAExB;EACT,CAAC,EAAES,KAAK,EAAE;IACRgB,GAAG,EAAEb;EACP,CAAC,CAAC,CAAC;AACL;AACAc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,aAAa,CAACuB,SAAS,GAAG;EAChErB,QAAQ,EAAEf,SAAS,CAACqC,IAAI,CAACC;AAC3B,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}