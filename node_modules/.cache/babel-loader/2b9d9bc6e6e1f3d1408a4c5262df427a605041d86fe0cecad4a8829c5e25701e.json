{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nexport const fetchBIAs = createAsyncThunk('bia/fetchBIAs', async () => {\n  const response = await fetch('/api/bias');\n  return response.json();\n});\nexport const saveBIA = createAsyncThunk('bia/saveBIA', async biaData => {\n  const response = await fetch(`/api/bias/${biaData.id}`, {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify(biaData)\n  });\n  return response.json();\n});\nconst biaSlice = createSlice({\n  name: 'bia',\n  initialState: {\n    bias: [],\n    currentBIA: null,\n    selectedProcess: null,\n    status: 'idle'\n  },\n  reducers: {\n    setBIAList: (state, action) => {\n      state.bias = action.payload;\n    },\n    setCurrentBIA: (state, action) => {\n      state.currentBIA = action.payload;\n    },\n    setSelectedProcess: (state, action) => {\n      state.selectedProcess = action.payload;\n    },\n    updateBIAField: (state, action) => {\n      const {\n        field,\n        value\n      } = action.payload;\n      if (state.currentBIA) {\n        state.currentBIA[field] = value;\n      }\n    },\n    updateProcessData: (state, action) => {\n      const {\n        processId,\n        data\n      } = action.payload;\n      if (state.currentBIA) {\n        if (!state.currentBIA.processData) {\n          state.currentBIA.processData = {};\n        }\n        state.currentBIA.processData[processId] = {\n          ...state.currentBIA.processData[processId],\n          ...data\n        };\n      }\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchBIAs.fulfilled, (state, action) => {\n      state.bias = action.payload;\n      state.status = 'succeeded';\n    });\n  }\n});\nexport const {\n  setCurrentBIA,\n  setSelectedProcess,\n  updateBIAField,\n  updateProcessData\n} = biaSlice.actions;\nexport default biaSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "fetchBIAs", "response", "fetch", "json", "saveBIA", "biaData", "id", "method", "headers", "body", "JSON", "stringify", "biaSlice", "name", "initialState", "bias", "currentBIA", "selectedProcess", "status", "reducers", "setBIAList", "state", "action", "payload", "setCurrentBIA", "setSelectedProcess", "updateBIAField", "field", "value", "updateProcessData", "processId", "data", "processData", "extraReducers", "builder", "addCase", "fulfilled", "actions", "reducer"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/store/biaSlice.js"], "sourcesContent": ["import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\n\nexport const fetchBIAs = createAsyncThunk('bia/fetchBIAs', async () => {\n  const response = await fetch('/api/bias');\n  return response.json();\n});\n\nexport const saveBIA = createAsyncThunk('bia/saveBIA', async (biaData) => {\n  const response = await fetch(`/api/bias/${biaData.id}`, {\n    method: 'PUT',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify(biaData)\n  });\n  return response.json();\n});\n\nconst biaSlice = createSlice({\n  name: 'bia',\n  initialState: {\n    bias: [],\n    currentBIA: null,\n    selectedProcess: null,\n    status: 'idle'\n  },\n  reducers: {\n    setBIAList: (state, action) => {\n      state.bias = action.payload;\n    },\n    setCurrentBIA: (state, action) => {\n      state.currentBIA = action.payload;\n    },\n    setSelectedProcess: (state, action) => {\n      state.selectedProcess = action.payload;\n    },\n    updateBIAField: (state, action) => {\n      const { field, value } = action.payload;\n      if (state.currentBIA) {\n        state.currentBIA[field] = value;\n      }\n    },\n    updateProcessData: (state, action) => {\n      const { processId, data } = action.payload;\n      if (state.currentBIA) {\n        if (!state.currentBIA.processData) {\n          state.currentBIA.processData = {};\n        }\n        state.currentBIA.processData[processId] = {\n          ...state.currentBIA.processData[processId],\n          ...data\n        };\n      }\n    }\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchBIAs.fulfilled, (state, action) => {\n        state.bias = action.payload;\n        state.status = 'succeeded';\n      });\n  }\n});\n\nexport const { setCurrentBIA, setSelectedProcess, updateBIAField, updateProcessData } = biaSlice.actions;\nexport default biaSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,kBAAkB;AAEhE,OAAO,MAAMC,SAAS,GAAGD,gBAAgB,CAAC,eAAe,EAAE,YAAY;EACrE,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,WAAW,CAAC;EACzC,OAAOD,QAAQ,CAACE,IAAI,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,OAAO,MAAMC,OAAO,GAAGL,gBAAgB,CAAC,aAAa,EAAE,MAAOM,OAAO,IAAK;EACxE,MAAMJ,QAAQ,GAAG,MAAMC,KAAK,CAAC,aAAaG,OAAO,CAACC,EAAE,EAAE,EAAE;IACtDC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE;MAAE,cAAc,EAAE;IAAmB,CAAC;IAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,OAAO;EAC9B,CAAC,CAAC;EACF,OAAOJ,QAAQ,CAACE,IAAI,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,MAAMS,QAAQ,GAAGd,WAAW,CAAC;EAC3Be,IAAI,EAAE,KAAK;EACXC,YAAY,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,IAAI;IAChBC,eAAe,EAAE,IAAI;IACrBC,MAAM,EAAE;EACV,CAAC;EACDC,QAAQ,EAAE;IACRC,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC7BD,KAAK,CAACN,IAAI,GAAGO,MAAM,CAACC,OAAO;IAC7B,CAAC;IACDC,aAAa,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MAChCD,KAAK,CAACL,UAAU,GAAGM,MAAM,CAACC,OAAO;IACnC,CAAC;IACDE,kBAAkB,EAAEA,CAACJ,KAAK,EAAEC,MAAM,KAAK;MACrCD,KAAK,CAACJ,eAAe,GAAGK,MAAM,CAACC,OAAO;IACxC,CAAC;IACDG,cAAc,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MACjC,MAAM;QAAEK,KAAK;QAAEC;MAAM,CAAC,GAAGN,MAAM,CAACC,OAAO;MACvC,IAAIF,KAAK,CAACL,UAAU,EAAE;QACpBK,KAAK,CAACL,UAAU,CAACW,KAAK,CAAC,GAAGC,KAAK;MACjC;IACF,CAAC;IACDC,iBAAiB,EAAEA,CAACR,KAAK,EAAEC,MAAM,KAAK;MACpC,MAAM;QAAEQ,SAAS;QAAEC;MAAK,CAAC,GAAGT,MAAM,CAACC,OAAO;MAC1C,IAAIF,KAAK,CAACL,UAAU,EAAE;QACpB,IAAI,CAACK,KAAK,CAACL,UAAU,CAACgB,WAAW,EAAE;UACjCX,KAAK,CAACL,UAAU,CAACgB,WAAW,GAAG,CAAC,CAAC;QACnC;QACAX,KAAK,CAACL,UAAU,CAACgB,WAAW,CAACF,SAAS,CAAC,GAAG;UACxC,GAAGT,KAAK,CAACL,UAAU,CAACgB,WAAW,CAACF,SAAS,CAAC;UAC1C,GAAGC;QACL,CAAC;MACH;IACF;EACF,CAAC;EACDE,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACnC,SAAS,CAACoC,SAAS,EAAE,CAACf,KAAK,EAAEC,MAAM,KAAK;MAC/CD,KAAK,CAACN,IAAI,GAAGO,MAAM,CAACC,OAAO;MAC3BF,KAAK,CAACH,MAAM,GAAG,WAAW;IAC5B,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEM,aAAa;EAAEC,kBAAkB;EAAEC,cAAc;EAAEG;AAAkB,CAAC,GAAGjB,QAAQ,CAACyB,OAAO;AACxG,eAAezB,QAAQ,CAAC0B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}