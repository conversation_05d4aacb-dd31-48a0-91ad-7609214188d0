{"ast": null, "code": "/**\n * Given an AsyncIterable and a callback function, return an AsyncIterator\n * which produces values mapped via calling the callback function.\n */\nexport function mapAsyncIterator(iterable, callback) {\n  const iterator = iterable[Symbol.asyncIterator]();\n  async function mapResult(result) {\n    if (result.done) {\n      return result;\n    }\n    try {\n      return {\n        value: await callback(result.value),\n        done: false\n      };\n    } catch (error) {\n      /* c8 ignore start */\n      // FIXME: add test case\n      if (typeof iterator.return === 'function') {\n        try {\n          await iterator.return();\n        } catch (_e) {\n          /* ignore error */\n        }\n      }\n      throw error;\n      /* c8 ignore stop */\n    }\n  }\n  return {\n    async next() {\n      return mapResult(await iterator.next());\n    },\n    async return() {\n      // If iterator.return() does not exist, then type R must be undefined.\n      return typeof iterator.return === 'function' ? mapResult(await iterator.return()) : {\n        value: undefined,\n        done: true\n      };\n    },\n    async throw(error) {\n      if (typeof iterator.throw === 'function') {\n        return mapResult(await iterator.throw(error));\n      }\n      throw error;\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    }\n  };\n}", "map": {"version": 3, "names": ["mapAsyncIterator", "iterable", "callback", "iterator", "Symbol", "asyncIterator", "mapResult", "result", "done", "value", "error", "return", "_e", "next", "undefined", "throw"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/execution/mapAsyncIterator.mjs"], "sourcesContent": ["/**\n * Given an AsyncIterable and a callback function, return an AsyncIterator\n * which produces values mapped via calling the callback function.\n */\nexport function mapAsyncIterator(iterable, callback) {\n  const iterator = iterable[Symbol.asyncIterator]();\n\n  async function mapResult(result) {\n    if (result.done) {\n      return result;\n    }\n\n    try {\n      return {\n        value: await callback(result.value),\n        done: false,\n      };\n    } catch (error) {\n      /* c8 ignore start */\n      // FIXME: add test case\n      if (typeof iterator.return === 'function') {\n        try {\n          await iterator.return();\n        } catch (_e) {\n          /* ignore error */\n        }\n      }\n\n      throw error;\n      /* c8 ignore stop */\n    }\n  }\n\n  return {\n    async next() {\n      return mapResult(await iterator.next());\n    },\n\n    async return() {\n      // If iterator.return() does not exist, then type R must be undefined.\n      return typeof iterator.return === 'function'\n        ? mapResult(await iterator.return())\n        : {\n            value: undefined,\n            done: true,\n          };\n    },\n\n    async throw(error) {\n      if (typeof iterator.throw === 'function') {\n        return mapResult(await iterator.throw(error));\n      }\n\n      throw error;\n    },\n\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n  };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,gBAAgBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EACnD,MAAMC,QAAQ,GAAGF,QAAQ,CAACG,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC;EAEjD,eAAeC,SAASA,CAACC,MAAM,EAAE;IAC/B,IAAIA,MAAM,CAACC,IAAI,EAAE;MACf,OAAOD,MAAM;IACf;IAEA,IAAI;MACF,OAAO;QACLE,KAAK,EAAE,MAAMP,QAAQ,CAACK,MAAM,CAACE,KAAK,CAAC;QACnCD,IAAI,EAAE;MACR,CAAC;IACH,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd;MACA;MACA,IAAI,OAAOP,QAAQ,CAACQ,MAAM,KAAK,UAAU,EAAE;QACzC,IAAI;UACF,MAAMR,QAAQ,CAACQ,MAAM,CAAC,CAAC;QACzB,CAAC,CAAC,OAAOC,EAAE,EAAE;UACX;QAAA;MAEJ;MAEA,MAAMF,KAAK;MACX;IACF;EACF;EAEA,OAAO;IACL,MAAMG,IAAIA,CAAA,EAAG;MACX,OAAOP,SAAS,CAAC,MAAMH,QAAQ,CAACU,IAAI,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,MAAMF,MAAMA,CAAA,EAAG;MACb;MACA,OAAO,OAAOR,QAAQ,CAACQ,MAAM,KAAK,UAAU,GACxCL,SAAS,CAAC,MAAMH,QAAQ,CAACQ,MAAM,CAAC,CAAC,CAAC,GAClC;QACEF,KAAK,EAAEK,SAAS;QAChBN,IAAI,EAAE;MACR,CAAC;IACP,CAAC;IAED,MAAMO,KAAKA,CAACL,KAAK,EAAE;MACjB,IAAI,OAAOP,QAAQ,CAACY,KAAK,KAAK,UAAU,EAAE;QACxC,OAAOT,SAAS,CAAC,MAAMH,QAAQ,CAACY,KAAK,CAACL,KAAK,CAAC,CAAC;MAC/C;MAEA,MAAMA,KAAK;IACb,CAAC;IAED,CAACN,MAAM,CAACC,aAAa,IAAI;MACvB,OAAO,IAAI;IACb;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}