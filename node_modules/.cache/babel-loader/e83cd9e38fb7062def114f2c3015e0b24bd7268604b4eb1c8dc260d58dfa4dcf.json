{"ast": null, "code": "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {\n    exports: {}\n  }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from)) if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n      get: () => from[key],\n      enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n    });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n// If the importer is in node compatibility mode or this is not an ESM\n// file that has been converted to a CommonJS file using a Babel-\n// compatible transform (i.e. \"__esModule\" has not been set), then set\n// \"default\" to the CommonJS \"module.exports\" for node compatibility.\nisNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", {\n  value: mod,\n  enumerable: true\n}) : target, mod));\n\n// node_modules/set-cookie-parser/lib/set-cookie.js\nvar require_set_cookie = __commonJS({\n  \"node_modules/set-cookie-parser/lib/set-cookie.js\"(exports, module) {\n    \"use strict\";\n\n    var defaultParseOptions = {\n      decodeValues: true,\n      map: false,\n      silent: false\n    };\n    function isNonEmptyString(str) {\n      return typeof str === \"string\" && !!str.trim();\n    }\n    function parseString(setCookieValue, options) {\n      var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n      var nameValuePairStr = parts.shift();\n      var parsed = parseNameValuePair(nameValuePairStr);\n      var name = parsed.name;\n      var value = parsed.value;\n      options = options ? Object.assign({}, defaultParseOptions, options) : defaultParseOptions;\n      try {\n        value = options.decodeValues ? decodeURIComponent(value) : value;\n      } catch (e) {\n        console.error(\"set-cookie-parser encountered an error while decoding a cookie with value '\" + value + \"'. Set options.decodeValues to false to disable this feature.\", e);\n      }\n      var cookie = {\n        name,\n        value\n      };\n      parts.forEach(function (part) {\n        var sides = part.split(\"=\");\n        var key = sides.shift().trimLeft().toLowerCase();\n        var value2 = sides.join(\"=\");\n        if (key === \"expires\") {\n          cookie.expires = new Date(value2);\n        } else if (key === \"max-age\") {\n          cookie.maxAge = parseInt(value2, 10);\n        } else if (key === \"secure\") {\n          cookie.secure = true;\n        } else if (key === \"httponly\") {\n          cookie.httpOnly = true;\n        } else if (key === \"samesite\") {\n          cookie.sameSite = value2;\n        } else {\n          cookie[key] = value2;\n        }\n      });\n      return cookie;\n    }\n    function parseNameValuePair(nameValuePairStr) {\n      var name = \"\";\n      var value = \"\";\n      var nameValueArr = nameValuePairStr.split(\"=\");\n      if (nameValueArr.length > 1) {\n        name = nameValueArr.shift();\n        value = nameValueArr.join(\"=\");\n      } else {\n        value = nameValuePairStr;\n      }\n      return {\n        name,\n        value\n      };\n    }\n    function parse(input, options) {\n      options = options ? Object.assign({}, defaultParseOptions, options) : defaultParseOptions;\n      if (!input) {\n        if (!options.map) {\n          return [];\n        } else {\n          return {};\n        }\n      }\n      if (input.headers) {\n        if (typeof input.headers.getSetCookie === \"function\") {\n          input = input.headers.getSetCookie();\n        } else if (input.headers[\"set-cookie\"]) {\n          input = input.headers[\"set-cookie\"];\n        } else {\n          var sch = input.headers[Object.keys(input.headers).find(function (key) {\n            return key.toLowerCase() === \"set-cookie\";\n          })];\n          if (!sch && input.headers.cookie && !options.silent) {\n            console.warn(\"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\");\n          }\n          input = sch;\n        }\n      }\n      if (!Array.isArray(input)) {\n        input = [input];\n      }\n      options = options ? Object.assign({}, defaultParseOptions, options) : defaultParseOptions;\n      if (!options.map) {\n        return input.filter(isNonEmptyString).map(function (str) {\n          return parseString(str, options);\n        });\n      } else {\n        var cookies = {};\n        return input.filter(isNonEmptyString).reduce(function (cookies2, str) {\n          var cookie = parseString(str, options);\n          cookies2[cookie.name] = cookie;\n          return cookies2;\n        }, cookies);\n      }\n    }\n    function splitCookiesString2(cookiesString) {\n      if (Array.isArray(cookiesString)) {\n        return cookiesString;\n      }\n      if (typeof cookiesString !== \"string\") {\n        return [];\n      }\n      var cookiesStrings = [];\n      var pos = 0;\n      var start;\n      var ch;\n      var lastComma;\n      var nextStart;\n      var cookiesSeparatorFound;\n      function skipWhitespace() {\n        while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n          pos += 1;\n        }\n        return pos < cookiesString.length;\n      }\n      function notSpecialChar() {\n        ch = cookiesString.charAt(pos);\n        return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n      }\n      while (pos < cookiesString.length) {\n        start = pos;\n        cookiesSeparatorFound = false;\n        while (skipWhitespace()) {\n          ch = cookiesString.charAt(pos);\n          if (ch === \",\") {\n            lastComma = pos;\n            pos += 1;\n            skipWhitespace();\n            nextStart = pos;\n            while (pos < cookiesString.length && notSpecialChar()) {\n              pos += 1;\n            }\n            if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n              cookiesSeparatorFound = true;\n              pos = nextStart;\n              cookiesStrings.push(cookiesString.substring(start, lastComma));\n              start = pos;\n            } else {\n              pos = lastComma + 1;\n            }\n          } else {\n            pos += 1;\n          }\n        }\n        if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n          cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n        }\n      }\n      return cookiesStrings;\n    }\n    module.exports = parse;\n    module.exports.parse = parse;\n    module.exports.parseString = parseString;\n    module.exports.splitCookiesString = splitCookiesString2;\n  }\n});\n\n// src/Headers.ts\nvar import_set_cookie_parser = __toESM(require_set_cookie());\n\n// src/utils/normalizeHeaderName.ts\nvar HEADERS_INVALID_CHARACTERS = /[^a-z0-9\\-#$%&'*+.^_`|~]/i;\nfunction normalizeHeaderName(name) {\n  if (HEADERS_INVALID_CHARACTERS.test(name) || name.trim() === \"\") {\n    throw new TypeError(\"Invalid character in header field name\");\n  }\n  return name.trim().toLowerCase();\n}\n\n// src/utils/normalizeHeaderValue.ts\nvar charCodesToRemove = [String.fromCharCode(10), String.fromCharCode(13), String.fromCharCode(9), String.fromCharCode(32)];\nvar HEADER_VALUE_REMOVE_REGEXP = new RegExp(`(^[${charCodesToRemove.join(\"\")}]|$[${charCodesToRemove.join(\"\")}])`, \"g\");\nfunction normalizeHeaderValue(value) {\n  const nextValue = value.replace(HEADER_VALUE_REMOVE_REGEXP, \"\");\n  return nextValue;\n}\n\n// src/utils/isValidHeaderName.ts\nfunction isValidHeaderName(value) {\n  if (typeof value !== \"string\") {\n    return false;\n  }\n  if (value.length === 0) {\n    return false;\n  }\n  for (let i = 0; i < value.length; i++) {\n    const character = value.charCodeAt(i);\n    if (character > 127 || !isToken(character)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isToken(value) {\n  return ![127, 32, \"(\", \")\", \"<\", \">\", \"@\", \",\", \";\", \":\", \"\\\\\", '\"', \"/\", \"[\", \"]\", \"?\", \"=\", \"{\", \"}\"].includes(value);\n}\n\n// src/utils/isValidHeaderValue.ts\nfunction isValidHeaderValue(value) {\n  if (typeof value !== \"string\") {\n    return false;\n  }\n  if (value.trim() !== value) {\n    return false;\n  }\n  for (let i = 0; i < value.length; i++) {\n    const character = value.charCodeAt(i);\n    if (\n    // NUL.\n    character === 0 ||\n    // HTTP newline bytes.\n    character === 10 || character === 13) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// src/Headers.ts\nvar NORMALIZED_HEADERS = Symbol(\"normalizedHeaders\");\nvar RAW_HEADER_NAMES = Symbol(\"rawHeaderNames\");\nvar HEADER_VALUE_DELIMITER = \", \";\nvar _a, _b, _c;\nvar Headers = class _Headers {\n  constructor(init) {\n    // Normalized header {\"name\":\"a, b\"} storage.\n    this[_a] = {};\n    // Keeps the mapping between the raw header name\n    // and the normalized header name to ease the lookup.\n    this[_b] = /* @__PURE__ */new Map();\n    this[_c] = \"Headers\";\n    if ([\"Headers\", \"HeadersPolyfill\"].includes(init?.constructor.name) || init instanceof _Headers || typeof globalThis.Headers !== \"undefined\" && init instanceof globalThis.Headers) {\n      const initialHeaders = init;\n      initialHeaders.forEach((value, name) => {\n        this.append(name, value);\n      }, this);\n    } else if (Array.isArray(init)) {\n      init.forEach(([name, value]) => {\n        this.append(name, Array.isArray(value) ? value.join(HEADER_VALUE_DELIMITER) : value);\n      });\n    } else if (init) {\n      Object.getOwnPropertyNames(init).forEach(name => {\n        const value = init[name];\n        this.append(name, Array.isArray(value) ? value.join(HEADER_VALUE_DELIMITER) : value);\n      });\n    }\n  }\n  [(_a = NORMALIZED_HEADERS, _b = RAW_HEADER_NAMES, _c = Symbol.toStringTag, Symbol.iterator)]() {\n    return this.entries();\n  }\n  *keys() {\n    for (const [name] of this.entries()) {\n      yield name;\n    }\n  }\n  *values() {\n    for (const [, value] of this.entries()) {\n      yield value;\n    }\n  }\n  *entries() {\n    let sortedKeys = Object.keys(this[NORMALIZED_HEADERS]).sort((a, b) => a.localeCompare(b));\n    for (const name of sortedKeys) {\n      if (name === \"set-cookie\") {\n        for (const value of this.getSetCookie()) {\n          yield [name, value];\n        }\n      } else {\n        yield [name, this.get(name)];\n      }\n    }\n  }\n  /**\n   * Returns a boolean stating whether a `Headers` object contains a certain header.\n   */\n  has(name) {\n    if (!isValidHeaderName(name)) {\n      throw new TypeError(`Invalid header name \"${name}\"`);\n    }\n    return this[NORMALIZED_HEADERS].hasOwnProperty(normalizeHeaderName(name));\n  }\n  /**\n   * Returns a `ByteString` sequence of all the values of a header with a given name.\n   */\n  get(name) {\n    if (!isValidHeaderName(name)) {\n      throw TypeError(`Invalid header name \"${name}\"`);\n    }\n    return this[NORMALIZED_HEADERS][normalizeHeaderName(name)] ?? null;\n  }\n  /**\n   * Sets a new value for an existing header inside a `Headers` object, or adds the header if it does not already exist.\n   */\n  set(name, value) {\n    if (!isValidHeaderName(name) || !isValidHeaderValue(value)) {\n      return;\n    }\n    const normalizedName = normalizeHeaderName(name);\n    const normalizedValue = normalizeHeaderValue(value);\n    this[NORMALIZED_HEADERS][normalizedName] = normalizeHeaderValue(normalizedValue);\n    this[RAW_HEADER_NAMES].set(normalizedName, name);\n  }\n  /**\n   * Appends a new value onto an existing header inside a `Headers` object, or adds the header if it does not already exist.\n   */\n  append(name, value) {\n    if (!isValidHeaderName(name) || !isValidHeaderValue(value)) {\n      return;\n    }\n    const normalizedName = normalizeHeaderName(name);\n    const normalizedValue = normalizeHeaderValue(value);\n    let resolvedValue = this.has(normalizedName) ? `${this.get(normalizedName)}, ${normalizedValue}` : normalizedValue;\n    this.set(name, resolvedValue);\n  }\n  /**\n   * Deletes a header from the `Headers` object.\n   */\n  delete(name) {\n    if (!isValidHeaderName(name)) {\n      return;\n    }\n    if (!this.has(name)) {\n      return;\n    }\n    const normalizedName = normalizeHeaderName(name);\n    delete this[NORMALIZED_HEADERS][normalizedName];\n    this[RAW_HEADER_NAMES].delete(normalizedName);\n  }\n  /**\n   * Traverses the `Headers` object,\n   * calling the given callback for each header.\n   */\n  forEach(callback, thisArg) {\n    for (const [name, value] of this.entries()) {\n      callback.call(thisArg, value, name, this);\n    }\n  }\n  /**\n   * Returns an array containing the values\n   * of all Set-Cookie headers associated\n   * with a response\n   */\n  getSetCookie() {\n    const setCookieHeader = this.get(\"set-cookie\");\n    if (setCookieHeader === null) {\n      return [];\n    }\n    if (setCookieHeader === \"\") {\n      return [\"\"];\n    }\n    return (0, import_set_cookie_parser.splitCookiesString)(setCookieHeader);\n  }\n};\n\n// src/getRawHeaders.ts\nfunction getRawHeaders(headers) {\n  const rawHeaders = {};\n  for (const [name, value] of headers.entries()) {\n    rawHeaders[headers[RAW_HEADER_NAMES].get(name)] = value;\n  }\n  return rawHeaders;\n}\n\n// src/transformers/headersToList.ts\nfunction headersToList(headers) {\n  const headersList = [];\n  headers.forEach((value, name) => {\n    const resolvedValue = value.includes(\",\") ? value.split(\",\").map(value2 => value2.trim()) : value;\n    headersList.push([name, resolvedValue]);\n  });\n  return headersList;\n}\n\n// src/transformers/headersToString.ts\nfunction headersToString(headers) {\n  const list = headersToList(headers);\n  const lines = list.map(([name, value]) => {\n    const values = [].concat(value);\n    return `${name}: ${values.join(\", \")}`;\n  });\n  return lines.join(\"\\r\\n\");\n}\n\n// src/transformers/headersToObject.ts\nvar singleValueHeaders = [\"user-agent\"];\nfunction headersToObject(headers) {\n  const headersObject = {};\n  headers.forEach((value, name) => {\n    const isMultiValue = !singleValueHeaders.includes(name.toLowerCase()) && value.includes(\",\");\n    headersObject[name] = isMultiValue ? value.split(\",\").map(s => s.trim()) : value;\n  });\n  return headersObject;\n}\n\n// src/transformers/stringToHeaders.ts\nfunction stringToHeaders(str) {\n  const lines = str.trim().split(/[\\r\\n]+/);\n  return lines.reduce((headers, line) => {\n    if (line.trim() === \"\") {\n      return headers;\n    }\n    const parts = line.split(\": \");\n    const name = parts.shift();\n    const value = parts.join(\": \");\n    headers.append(name, value);\n    return headers;\n  }, new Headers());\n}\n\n// src/transformers/listToHeaders.ts\nfunction listToHeaders(list) {\n  const headers = new Headers();\n  list.forEach(([name, value]) => {\n    const values = [].concat(value);\n    values.forEach(value2 => {\n      headers.append(name, value2);\n    });\n  });\n  return headers;\n}\n\n// src/transformers/reduceHeadersObject.ts\nfunction reduceHeadersObject(headers, reducer, initialState) {\n  return Object.keys(headers).reduce((nextHeaders, name) => {\n    return reducer(nextHeaders, name, headers[name]);\n  }, initialState);\n}\n\n// src/transformers/objectToHeaders.ts\nfunction objectToHeaders(headersObject) {\n  return reduceHeadersObject(headersObject, (headers, name, value) => {\n    const values = [].concat(value).filter(Boolean);\n    values.forEach(value2 => {\n      headers.append(name, value2);\n    });\n    return headers;\n  }, new Headers());\n}\n\n// src/transformers/flattenHeadersList.ts\nfunction flattenHeadersList(list) {\n  return list.map(([name, values]) => {\n    return [name, [].concat(values).join(\", \")];\n  });\n}\n\n// src/transformers/flattenHeadersObject.ts\nfunction flattenHeadersObject(headersObject) {\n  return reduceHeadersObject(headersObject, (headers, name, value) => {\n    headers[name] = [].concat(value).join(\", \");\n    return headers;\n  }, {});\n}\nexport { Headers, flattenHeadersList, flattenHeadersObject, getRawHeaders, headersToList, headersToObject, headersToString, listToHeaders, objectToHeaders, reduceHeadersObject, stringToHeaders };", "map": {"version": 3, "names": ["require_set_cookie", "__commonJS", "node_modules/set-cookie-parser/lib/set-cookie.js", "exports", "module", "defaultParseOptions", "decode<PERSON><PERSON><PERSON>", "map", "silent", "isNonEmptyString", "str", "trim", "parseString", "setCookieValue", "options", "parts", "split", "filter", "nameValuePairStr", "shift", "parsed", "parseNameValuePair", "name", "value", "Object", "assign", "decodeURIComponent", "e", "console", "error", "cookie", "for<PERSON>ach", "part", "sides", "key", "trimLeft", "toLowerCase", "value2", "join", "expires", "Date", "maxAge", "parseInt", "secure", "httpOnly", "sameSite", "nameValueArr", "length", "parse", "input", "headers", "getSetCookie", "sch", "keys", "find", "warn", "Array", "isArray", "cookies", "reduce", "cookies2", "splitCookiesString2", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "splitCookiesString", "import_set_cookie_parser", "__toESM", "HEADERS_INVALID_CHARACTERS", "normalizeHeaderName", "TypeError", "charCodesToRemove", "String", "fromCharCode", "HEADER_VALUE_REMOVE_REGEXP", "RegExp", "normalizeHeaderValue", "nextValue", "replace", "isValidHeaderName", "i", "character", "charCodeAt", "isToken", "includes", "isValidHeaderValue", "NORMALIZED_HEADERS", "Symbol", "RAW_HEADER_NAMES", "HEADER_VALUE_DELIMITER", "_a", "_b", "_c", "Headers", "_Headers", "constructor", "init", "Map", "globalThis", "initialHeaders", "append", "getOwnPropertyNames", "toStringTag", "iterator", "entries", "values", "sortedKeys", "sort", "a", "b", "localeCompare", "get", "has", "hasOwnProperty", "set", "normalizedName", "normalizedValue", "resolvedValue", "delete", "callback", "thisArg", "call", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getRawHeaders", "rawHeaders", "headersToList", "headersList", "headersToString", "list", "lines", "concat", "singleValueHeaders", "headersToObject", "headersObject", "isMultiValue", "s", "stringToHeaders", "line", "listToHeaders", "reduceHeadersObject", "reducer", "initialState", "nextHeaders", "objectToHeaders", "Boolean", "flattenHeadersList", "flattenHeadersObject"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/node_modules/set-cookie-parser/lib/set-cookie.js", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/Headers.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/utils/normalizeHeaderName.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/utils/normalizeHeaderValue.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/utils/isValidHeaderName.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/utils/isValidHeaderValue.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/getRawHeaders.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/transformers/headersToList.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/transformers/headersToString.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/transformers/headersToObject.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/transformers/stringToHeaders.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/transformers/listToHeaders.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/transformers/reduceHeadersObject.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/transformers/objectToHeaders.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/transformers/flattenHeadersList.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/headers-polyfill/src/transformers/flattenHeadersObject.ts"], "sourcesContent": ["\"use strict\";\n\nvar defaultParseOptions = {\n  decodeValues: true,\n  map: false,\n  silent: false,\n};\n\nfunction isNonEmptyString(str) {\n  return typeof str === \"string\" && !!str.trim();\n}\n\nfunction parseString(setCookieValue, options) {\n  var parts = setCookieValue.split(\";\").filter(isNonEmptyString);\n\n  var nameValuePairStr = parts.shift();\n  var parsed = parseNameValuePair(nameValuePairStr);\n  var name = parsed.name;\n  var value = parsed.value;\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  try {\n    value = options.decodeValues ? decodeURIComponent(value) : value; // decode cookie value\n  } catch (e) {\n    console.error(\n      \"set-cookie-parser encountered an error while decoding a cookie with value '\" +\n        value +\n        \"'. Set options.decodeValues to false to disable this feature.\",\n      e\n    );\n  }\n\n  var cookie = {\n    name: name,\n    value: value,\n  };\n\n  parts.forEach(function (part) {\n    var sides = part.split(\"=\");\n    var key = sides.shift().trimLeft().toLowerCase();\n    var value = sides.join(\"=\");\n    if (key === \"expires\") {\n      cookie.expires = new Date(value);\n    } else if (key === \"max-age\") {\n      cookie.maxAge = parseInt(value, 10);\n    } else if (key === \"secure\") {\n      cookie.secure = true;\n    } else if (key === \"httponly\") {\n      cookie.httpOnly = true;\n    } else if (key === \"samesite\") {\n      cookie.sameSite = value;\n    } else {\n      cookie[key] = value;\n    }\n  });\n\n  return cookie;\n}\n\nfunction parseNameValuePair(nameValuePairStr) {\n  // Parses name-value-pair according to rfc6265bis draft\n\n  var name = \"\";\n  var value = \"\";\n  var nameValueArr = nameValuePairStr.split(\"=\");\n  if (nameValueArr.length > 1) {\n    name = nameValueArr.shift();\n    value = nameValueArr.join(\"=\"); // everything after the first =, joined by a \"=\" if there was more than one part\n  } else {\n    value = nameValuePairStr;\n  }\n\n  return { name: name, value: value };\n}\n\nfunction parse(input, options) {\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!input) {\n    if (!options.map) {\n      return [];\n    } else {\n      return {};\n    }\n  }\n\n  if (input.headers) {\n    if (typeof input.headers.getSetCookie === \"function\") {\n      // for fetch responses - they combine headers of the same type in the headers array,\n      // but getSetCookie returns an uncombined array\n      input = input.headers.getSetCookie();\n    } else if (input.headers[\"set-cookie\"]) {\n      // fast-path for node.js (which automatically normalizes header names to lower-case\n      input = input.headers[\"set-cookie\"];\n    } else {\n      // slow-path for other environments - see #25\n      var sch =\n        input.headers[\n          Object.keys(input.headers).find(function (key) {\n            return key.toLowerCase() === \"set-cookie\";\n          })\n        ];\n      // warn if called on a request-like object with a cookie header rather than a set-cookie header - see #34, 36\n      if (!sch && input.headers.cookie && !options.silent) {\n        console.warn(\n          \"Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning.\"\n        );\n      }\n      input = sch;\n    }\n  }\n  if (!Array.isArray(input)) {\n    input = [input];\n  }\n\n  options = options\n    ? Object.assign({}, defaultParseOptions, options)\n    : defaultParseOptions;\n\n  if (!options.map) {\n    return input.filter(isNonEmptyString).map(function (str) {\n      return parseString(str, options);\n    });\n  } else {\n    var cookies = {};\n    return input.filter(isNonEmptyString).reduce(function (cookies, str) {\n      var cookie = parseString(str, options);\n      cookies[cookie.name] = cookie;\n      return cookies;\n    }, cookies);\n  }\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n\n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nfunction splitCookiesString(cookiesString) {\n  if (Array.isArray(cookiesString)) {\n    return cookiesString;\n  }\n  if (typeof cookiesString !== \"string\") {\n    return [];\n  }\n\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos;\n        pos += 1;\n\n        skipWhitespace();\n        nextStart = pos;\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          // we found cookies separator\n          cookiesSeparatorFound = true;\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n\n  return cookiesStrings;\n}\n\nmodule.exports = parse;\nmodule.exports.parse = parse;\nmodule.exports.parseString = parseString;\nmodule.exports.splitCookiesString = splitCookiesString;\n", "import { splitCookiesString } from 'set-cookie-parser'\nimport { HeadersList, HeadersObject } from './glossary'\nimport { normalizeHeaderName } from './utils/normalizeHeaderName'\nimport { normalizeHeaderValue } from './utils/normalizeHeaderValue'\nimport { isValidHeaderName } from './utils/isValidHeaderName'\nimport { isValidHeaderValue } from './utils/isValidHeaderValue'\n\nexport const NORMALIZED_HEADERS: unique symbol = Symbol('normalizedHeaders')\n\nexport const RAW_HEADER_NAMES: unique symbol = Symbol('rawHeaderNames')\n\nconst HEADER_VALUE_DELIMITER = ', ' as const\n\nexport class Headers {\n  // Normalized header {\"name\":\"a, b\"} storage.\n  private [NORMALIZED_HEADERS]: Record<string, string> = {}\n\n  // Keeps the mapping between the raw header name\n  // and the normalized header name to ease the lookup.\n  private [RAW_HEADER_NAMES]: Map<string, string> = new Map()\n\n  constructor(init?: HeadersInit | HeadersObject | HeadersList) {\n    /**\n     * @note Cannot necessarily check if the `init` is an instance of the\n     * `Headers` because that class may not be defined in Node or jsdom.\n     */\n    if (\n      ['Headers', 'HeadersPolyfill'].includes(init?.constructor.name) ||\n      init instanceof Headers ||\n      (typeof globalThis.Headers !== 'undefined' &&\n        init instanceof globalThis.Headers)\n    ) {\n      const initialHeaders = init as Headers\n      initialHeaders.forEach((value, name) => {\n        this.append(name, value)\n      }, this)\n    } else if (Array.isArray(init)) {\n      init.forEach(([name, value]) => {\n        this.append(\n          name,\n          Array.isArray(value) ? value.join(HEADER_VALUE_DELIMITER) : value\n        )\n      })\n    } else if (init) {\n      Object.getOwnPropertyNames(init).forEach((name) => {\n        const value = init[name]\n        this.append(\n          name,\n          Array.isArray(value) ? value.join(HEADER_VALUE_DELIMITER) : value\n        )\n      })\n    }\n  }\n\n  [Symbol.toStringTag] = 'Headers';\n\n  [Symbol.iterator]() {\n    return this.entries()\n  }\n\n  *keys(): IterableIterator<string> {\n    for (const [name] of this.entries()) {\n      yield name\n    }\n  }\n\n  *values(): IterableIterator<string> {\n    for (const [, value] of this.entries()) {\n      yield value\n    }\n  }\n\n  *entries(): IterableIterator<[string, string]> {\n    // https://fetch.spec.whatwg.org/#concept-header-list-sort-and-combine\n    let sortedKeys = Object.keys(this[NORMALIZED_HEADERS]).sort((a, b) =>\n      a.localeCompare(b)\n    )\n    for (const name of sortedKeys) {\n      if (name === 'set-cookie') {\n        for (const value of this.getSetCookie()) {\n          yield [name, value]\n        }\n      } else {\n        yield [name, this.get(name)]\n      }\n    }\n  }\n\n  /**\n   * Returns a boolean stating whether a `Headers` object contains a certain header.\n   */\n  has(name: string): boolean {\n    if (!isValidHeaderName(name)) {\n      throw new TypeError(`Invalid header name \"${name}\"`)\n    }\n\n    return this[NORMALIZED_HEADERS].hasOwnProperty(normalizeHeaderName(name))\n  }\n\n  /**\n   * Returns a `ByteString` sequence of all the values of a header with a given name.\n   */\n  get(name: string): string | null {\n    if (!isValidHeaderName(name)) {\n      throw TypeError(`Invalid header name \"${name}\"`)\n    }\n\n    return this[NORMALIZED_HEADERS][normalizeHeaderName(name)] ?? null\n  }\n\n  /**\n   * Sets a new value for an existing header inside a `Headers` object, or adds the header if it does not already exist.\n   */\n  set(name: string, value: string): void {\n    if (!isValidHeaderName(name) || !isValidHeaderValue(value)) {\n      return\n    }\n\n    const normalizedName = normalizeHeaderName(name)\n    const normalizedValue = normalizeHeaderValue(value)\n\n    this[NORMALIZED_HEADERS][normalizedName] =\n      normalizeHeaderValue(normalizedValue)\n    this[RAW_HEADER_NAMES].set(normalizedName, name)\n  }\n\n  /**\n   * Appends a new value onto an existing header inside a `Headers` object, or adds the header if it does not already exist.\n   */\n  append(name: string, value: string): void {\n    if (!isValidHeaderName(name) || !isValidHeaderValue(value)) {\n      return\n    }\n\n    const normalizedName = normalizeHeaderName(name)\n    const normalizedValue = normalizeHeaderValue(value)\n\n    let resolvedValue = this.has(normalizedName)\n      ? `${this.get(normalizedName)}, ${normalizedValue}`\n      : normalizedValue\n\n    this.set(name, resolvedValue)\n  }\n\n  /**\n   * Deletes a header from the `Headers` object.\n   */\n  delete(name: string): void {\n    if (!isValidHeaderName(name)) {\n      return\n    }\n\n    if (!this.has(name)) {\n      return\n    }\n\n    const normalizedName = normalizeHeaderName(name)\n    delete this[NORMALIZED_HEADERS][normalizedName]\n    this[RAW_HEADER_NAMES].delete(normalizedName)\n  }\n\n  /**\n   * Traverses the `Headers` object,\n   * calling the given callback for each header.\n   */\n  forEach<ThisArg = this>(\n    callback: (\n      this: ThisArg,\n      value: string,\n      name: string,\n      parent: this\n    ) => void,\n    thisArg?: ThisArg\n  ) {\n    for (const [name, value] of this.entries()) {\n      callback.call(thisArg, value, name, this)\n    }\n  }\n\n  /**\n   * Returns an array containing the values\n   * of all Set-Cookie headers associated\n   * with a response\n   */\n  getSetCookie(): string[] {\n    const setCookieHeader = this.get('set-cookie')\n\n    if (setCookieHeader === null) {\n      return []\n    }\n\n    if (setCookieHeader === '') {\n      return ['']\n    }\n\n    return splitCookiesString(setCookieHeader)\n  }\n}\n", "const HEADERS_INVALID_CHARACTERS = /[^a-z0-9\\-#$%&'*+.^_`|~]/i\n\nexport function normalizeHeaderName(name: string): string {\n  if (HEADERS_INVALID_CHARACTERS.test(name) || name.trim() === '') {\n    throw new TypeError('Invalid character in header field name')\n  }\n\n  return name.trim().toLowerCase()\n}\n", "const charCodesToRemove = [\n  String.fromCharCode(0x0a),\n  String.fromCharCode(0x0d),\n  String.fromCharCode(0x09),\n  String.fromCharCode(0x20),\n]\n\nconst HEADER_VALUE_REMOVE_REGEXP = new RegExp(\n  `(^[${charCodesToRemove.join('')}]|$[${charCodesToRemove.join('')}])`,\n  'g'\n)\n\n/**\n * Normalize the given header value.\n * @see https://fetch.spec.whatwg.org/#concept-header-value-normalize\n */\nexport function normalizeHeaderValue(value: string): string {\n  const nextValue = value.replace(HEADER_VALUE_REMOVE_REGEXP, '')\n  return nextValue\n}\n", "/**\n * Validate the given header name.\n * @see https://fetch.spec.whatwg.org/#header-name\n */\nexport function isValidHeaderName(value: unknown) {\n  if (typeof value !== 'string') {\n    return false\n  }\n\n  if (value.length === 0) {\n    return false\n  }\n\n  for (let i = 0; i < value.length; i++) {\n    const character = value.charCodeAt(i)\n\n    if (character > 0x7f || !isToken(character)) {\n      return false\n    }\n  }\n\n  return true\n}\n\nfunction isToken(value: string | number): boolean {\n  return ![\n    0x7f,\n    0x20,\n    '(',\n    ')',\n    '<',\n    '>',\n    '@',\n    ',',\n    ';',\n    ':',\n    '\\\\',\n    '\"',\n    '/',\n    '[',\n    ']',\n    '?',\n    '=',\n    '{',\n    '}',\n  ].includes(value)\n}\n", "/**\n * Validate the given header value.\n * @see https://fetch.spec.whatwg.org/#header-value\n */\nexport function isValidHeaderValue(value: unknown): boolean {\n  if (typeof value !== 'string') {\n    return false\n  }\n\n  if (value.trim() !== value) {\n    return false\n  }\n\n  for (let i = 0; i < value.length; i++) {\n    const character = value.charCodeAt(i)\n\n    if (\n      // NUL.\n      character === 0x00 ||\n      // HTTP newline bytes.\n      character === 0x0a ||\n      character === 0x0d\n    ) {\n      return false\n    }\n  }\n\n  return true\n}\n", "import { RAW_HEADER_NAMES } from './Headers'\n\n/**\n * Returns the object of all raw headers.\n */\nexport function getRawHeaders(headers: Headers) {\n  const rawHeaders: Record<string, string> = {}\n\n  for (const [name, value] of headers.entries()) {\n    rawHeaders[headers[RAW_HEADER_NAMES].get(name)] = value\n  }\n\n  return rawHeaders\n}\n", "import { HeadersList } from '../glossary'\n\nexport function headersToList(headers: Headers): HeadersList {\n  const headersList: HeadersList = []\n\n  headers.forEach((value, name) => {\n    const resolvedValue = value.includes(',')\n      ? value.split(',').map((value) => value.trim())\n      : value\n\n    headersList.push([name, resolvedValue])\n  })\n\n  return headersList\n}\n", "import { headersToList } from './headersToList'\n\n/**\n * Converts a given `Headers` instance to its string representation.\n */\nexport function headersToString(headers: Headers): string {\n  const list = headersToList(headers)\n  const lines = list.map(([name, value]) => {\n    const values = ([] as string[]).concat(value)\n    return `${name}: ${values.join(', ')}`\n  })\n\n  return lines.join('\\r\\n')\n}\n", "import { HeadersObject } from '../glossary'\n\n// List of headers that cannot have multiple values,\n// while potentially having a comma in their single value.\nconst singleValueHeaders = ['user-agent']\n\n/**\n * Converts a given `Headers` instance into a plain object.\n * Respects headers with multiple values.\n */\nexport function headersToObject(headers: Headers): HeadersObject {\n  const headersObject: HeadersObject = {}\n\n  headers.forEach((value, name) => {\n    const isMultiValue =\n      !singleValueHeaders.includes(name.toLowerCase()) && value.includes(',')\n    headersObject[name] = isMultiValue\n      ? value.split(',').map((s) => s.trim())\n      : value\n  })\n\n  return headersObject\n}\n", "import { Headers } from '../Headers'\n\n/**\n * Converts a string representation of headers (i.e. from XMLHttpRequest)\n * to a new `Headers` instance.\n */\nexport function stringToHeaders(str: string): Headers {\n  const lines = str.trim().split(/[\\r\\n]+/)\n\n  return lines.reduce((headers, line) => {\n    if (line.trim() === '') {\n      return headers\n    }\n\n    const parts = line.split(': ')\n    const name = parts.shift()\n    const value = parts.join(': ')\n    headers.append(name, value)\n\n    return headers\n  }, new Headers())\n}\n", "import { Headers } from '../Headers'\nimport { HeadersList } from '../glossary'\n\nexport function listToHeaders(list: HeadersList): Headers {\n  const headers = new Headers()\n\n  list.forEach(([name, value]) => {\n    const values = ([] as string[]).concat(value)\n\n    values.forEach((value) => {\n      headers.append(name, value)\n    })\n  })\n\n  return headers\n}\n", "import { HeadersObject } from '../glossary'\n\n/**\n * Reduces given headers object instnace.\n */\nexport function reduceHeadersObject<R>(\n  headers: HeadersObject,\n  reducer: (headers: R, name: string, value: string | string[]) => R,\n  initialState: R\n): R {\n  return Object.keys(headers).reduce<R>((nextHeaders, name) => {\n    return reducer(nextHeaders, name, headers[name])\n  }, initialState)\n}\n", "import { Headers } from '../Headers'\nimport { reduceHeadersObject } from './reduceHeadersObject'\n\n/**\n * Converts a given headers object to a new `Headers` instance.\n */\nexport function objectToHeaders(\n  headersObject: Record<string, string | string[] | undefined>\n): Headers {\n  return reduceHeadersObject(\n    headersObject,\n    (headers, name, value) => {\n      const values = ([] as string[]).concat(value).filter(Boolean)\n\n      values.forEach((value) => {\n        headers.append(name, value)\n      })\n\n      return headers\n    },\n    new Headers()\n  )\n}\n", "import { HeadersList, FlatHeadersList } from '../glossary'\n\nexport function flattenHeadersList(list: HeadersList): FlatHeadersList {\n  return list.map(([name, values]) => {\n    return [name, ([] as string[]).concat(values).join(', ')]\n  })\n}\n", "import { HeadersObject, FlatHeadersObject } from '../glossary'\nimport { reduceHeadersObject } from './reduceHeadersObject'\n\nexport function flattenHeadersObject(\n  headersObject: HeadersObject\n): FlatHeadersObject {\n  return reduceHeadersObject<FlatHeadersObject>(\n    headersObject,\n    (headers, name, value) => {\n      headers[name] = ([] as string[]).concat(value).join(', ')\n      return headers\n    },\n    {}\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,kBAAA,GAAAC,UAAA;EAAA,kDAAAC,CAAAC,OAAA,EAAAC,MAAA;IAAA;;IAEA,IAAIC,mBAAA,GAAsB;MACxBC,YAAA,EAAc;MACdC,GAAA,EAAK;MACLC,MAAA,EAAQ;IACV;IAEA,SAASC,iBAAiBC,GAAA,EAAK;MAC7B,OAAO,OAAOA,GAAA,KAAQ,YAAY,CAAC,CAACA,GAAA,CAAIC,IAAA,CAAK;IAC/C;IAEA,SAASC,YAAYC,cAAA,EAAgBC,OAAA,EAAS;MAC5C,IAAIC,KAAA,GAAQF,cAAA,CAAeG,KAAA,CAAM,GAAG,EAAEC,MAAA,CAAOR,gBAAgB;MAE7D,IAAIS,gBAAA,GAAmBH,KAAA,CAAMI,KAAA,CAAM;MACnC,IAAIC,MAAA,GAASC,kBAAA,CAAmBH,gBAAgB;MAChD,IAAII,IAAA,GAAOF,MAAA,CAAOE,IAAA;MAClB,IAAIC,KAAA,GAAQH,MAAA,CAAOG,KAAA;MAEnBT,OAAA,GAAUA,OAAA,GACNU,MAAA,CAAOC,MAAA,CAAO,CAAC,GAAGpB,mBAAA,EAAqBS,OAAO,IAC9CT,mBAAA;MAEJ,IAAI;QACFkB,KAAA,GAAQT,OAAA,CAAQR,YAAA,GAAeoB,kBAAA,CAAmBH,KAAK,IAAIA,KAAA;MAC7D,SAASI,CAAA,EAAG;QACVC,OAAA,CAAQC,KAAA,CACN,gFACEN,KAAA,GACA,iEACFI,CACF;MACF;MAEA,IAAIG,MAAA,GAAS;QACXR,IAAA;QACAC;MACF;MAEAR,KAAA,CAAMgB,OAAA,CAAQ,UAAUC,IAAA,EAAM;QAC5B,IAAIC,KAAA,GAAQD,IAAA,CAAKhB,KAAA,CAAM,GAAG;QAC1B,IAAIkB,GAAA,GAAMD,KAAA,CAAMd,KAAA,CAAM,EAAEgB,QAAA,CAAS,EAAEC,WAAA,CAAY;QAC/C,IAAIC,MAAA,GAAQJ,KAAA,CAAMK,IAAA,CAAK,GAAG;QAC1B,IAAIJ,GAAA,KAAQ,WAAW;UACrBJ,MAAA,CAAOS,OAAA,GAAU,IAAIC,IAAA,CAAKH,MAAK;QACjC,WAAWH,GAAA,KAAQ,WAAW;UAC5BJ,MAAA,CAAOW,MAAA,GAASC,QAAA,CAASL,MAAA,EAAO,EAAE;QACpC,WAAWH,GAAA,KAAQ,UAAU;UAC3BJ,MAAA,CAAOa,MAAA,GAAS;QAClB,WAAWT,GAAA,KAAQ,YAAY;UAC7BJ,MAAA,CAAOc,QAAA,GAAW;QACpB,WAAWV,GAAA,KAAQ,YAAY;UAC7BJ,MAAA,CAAOe,QAAA,GAAWR,MAAA;QACpB,OAAO;UACLP,MAAA,CAAOI,GAAG,IAAIG,MAAA;QAChB;MACF,CAAC;MAED,OAAOP,MAAA;IACT;IAEA,SAAST,mBAAmBH,gBAAA,EAAkB;MAG5C,IAAII,IAAA,GAAO;MACX,IAAIC,KAAA,GAAQ;MACZ,IAAIuB,YAAA,GAAe5B,gBAAA,CAAiBF,KAAA,CAAM,GAAG;MAC7C,IAAI8B,YAAA,CAAaC,MAAA,GAAS,GAAG;QAC3BzB,IAAA,GAAOwB,YAAA,CAAa3B,KAAA,CAAM;QAC1BI,KAAA,GAAQuB,YAAA,CAAaR,IAAA,CAAK,GAAG;MAC/B,OAAO;QACLf,KAAA,GAAQL,gBAAA;MACV;MAEA,OAAO;QAAEI,IAAA;QAAYC;MAAa;IACpC;IAEA,SAASyB,MAAMC,KAAA,EAAOnC,OAAA,EAAS;MAC7BA,OAAA,GAAUA,OAAA,GACNU,MAAA,CAAOC,MAAA,CAAO,CAAC,GAAGpB,mBAAA,EAAqBS,OAAO,IAC9CT,mBAAA;MAEJ,IAAI,CAAC4C,KAAA,EAAO;QACV,IAAI,CAACnC,OAAA,CAAQP,GAAA,EAAK;UAChB,OAAO,EAAC;QACV,OAAO;UACL,OAAO,CAAC;QACV;MACF;MAEA,IAAI0C,KAAA,CAAMC,OAAA,EAAS;QACjB,IAAI,OAAOD,KAAA,CAAMC,OAAA,CAAQC,YAAA,KAAiB,YAAY;UAGpDF,KAAA,GAAQA,KAAA,CAAMC,OAAA,CAAQC,YAAA,CAAa;QACrC,WAAWF,KAAA,CAAMC,OAAA,CAAQ,YAAY,GAAG;UAEtCD,KAAA,GAAQA,KAAA,CAAMC,OAAA,CAAQ,YAAY;QACpC,OAAO;UAEL,IAAIE,GAAA,GACFH,KAAA,CAAMC,OAAA,CACJ1B,MAAA,CAAO6B,IAAA,CAAKJ,KAAA,CAAMC,OAAO,EAAEI,IAAA,CAAK,UAAUpB,GAAA,EAAK;YAC7C,OAAOA,GAAA,CAAIE,WAAA,CAAY,MAAM;UAC/B,CAAC,CACH;UAEF,IAAI,CAACgB,GAAA,IAAOH,KAAA,CAAMC,OAAA,CAAQpB,MAAA,IAAU,CAAChB,OAAA,CAAQN,MAAA,EAAQ;YACnDoB,OAAA,CAAQ2B,IAAA,CACN,kOACF;UACF;UACAN,KAAA,GAAQG,GAAA;QACV;MACF;MACA,IAAI,CAACI,KAAA,CAAMC,OAAA,CAAQR,KAAK,GAAG;QACzBA,KAAA,GAAQ,CAACA,KAAK;MAChB;MAEAnC,OAAA,GAAUA,OAAA,GACNU,MAAA,CAAOC,MAAA,CAAO,CAAC,GAAGpB,mBAAA,EAAqBS,OAAO,IAC9CT,mBAAA;MAEJ,IAAI,CAACS,OAAA,CAAQP,GAAA,EAAK;QAChB,OAAO0C,KAAA,CAAMhC,MAAA,CAAOR,gBAAgB,EAAEF,GAAA,CAAI,UAAUG,GAAA,EAAK;UACvD,OAAOE,WAAA,CAAYF,GAAA,EAAKI,OAAO;QACjC,CAAC;MACH,OAAO;QACL,IAAI4C,OAAA,GAAU,CAAC;QACf,OAAOT,KAAA,CAAMhC,MAAA,CAAOR,gBAAgB,EAAEkD,MAAA,CAAO,UAAUC,QAAA,EAASlD,GAAA,EAAK;UACnE,IAAIoB,MAAA,GAASlB,WAAA,CAAYF,GAAA,EAAKI,OAAO;UACrC8C,QAAA,CAAQ9B,MAAA,CAAOR,IAAI,IAAIQ,MAAA;UACvB,OAAO8B,QAAA;QACT,GAAGF,OAAO;MACZ;IACF;IAaA,SAASG,oBAAmBC,aAAA,EAAe;MACzC,IAAIN,KAAA,CAAMC,OAAA,CAAQK,aAAa,GAAG;QAChC,OAAOA,aAAA;MACT;MACA,IAAI,OAAOA,aAAA,KAAkB,UAAU;QACrC,OAAO,EAAC;MACV;MAEA,IAAIC,cAAA,GAAiB,EAAC;MACtB,IAAIC,GAAA,GAAM;MACV,IAAIC,KAAA;MACJ,IAAIC,EAAA;MACJ,IAAIC,SAAA;MACJ,IAAIC,SAAA;MACJ,IAAIC,qBAAA;MAEJ,SAASC,eAAA,EAAiB;QACxB,OAAON,GAAA,GAAMF,aAAA,CAAcf,MAAA,IAAU,KAAKwB,IAAA,CAAKT,aAAA,CAAcU,MAAA,CAAOR,GAAG,CAAC,GAAG;UACzEA,GAAA,IAAO;QACT;QACA,OAAOA,GAAA,GAAMF,aAAA,CAAcf,MAAA;MAC7B;MAEA,SAAS0B,eAAA,EAAiB;QACxBP,EAAA,GAAKJ,aAAA,CAAcU,MAAA,CAAOR,GAAG;QAE7B,OAAOE,EAAA,KAAO,OAAOA,EAAA,KAAO,OAAOA,EAAA,KAAO;MAC5C;MAEA,OAAOF,GAAA,GAAMF,aAAA,CAAcf,MAAA,EAAQ;QACjCkB,KAAA,GAAQD,GAAA;QACRK,qBAAA,GAAwB;QAExB,OAAOC,cAAA,CAAe,GAAG;UACvBJ,EAAA,GAAKJ,aAAA,CAAcU,MAAA,CAAOR,GAAG;UAC7B,IAAIE,EAAA,KAAO,KAAK;YAEdC,SAAA,GAAYH,GAAA;YACZA,GAAA,IAAO;YAEPM,cAAA,CAAe;YACfF,SAAA,GAAYJ,GAAA;YAEZ,OAAOA,GAAA,GAAMF,aAAA,CAAcf,MAAA,IAAU0B,cAAA,CAAe,GAAG;cACrDT,GAAA,IAAO;YACT;YAGA,IAAIA,GAAA,GAAMF,aAAA,CAAcf,MAAA,IAAUe,aAAA,CAAcU,MAAA,CAAOR,GAAG,MAAM,KAAK;cAEnEK,qBAAA,GAAwB;cAExBL,GAAA,GAAMI,SAAA;cACNL,cAAA,CAAeW,IAAA,CAAKZ,aAAA,CAAca,SAAA,CAAUV,KAAA,EAAOE,SAAS,CAAC;cAC7DF,KAAA,GAAQD,GAAA;YACV,OAAO;cAGLA,GAAA,GAAMG,SAAA,GAAY;YACpB;UACF,OAAO;YACLH,GAAA,IAAO;UACT;QACF;QAEA,IAAI,CAACK,qBAAA,IAAyBL,GAAA,IAAOF,aAAA,CAAcf,MAAA,EAAQ;UACzDgB,cAAA,CAAeW,IAAA,CAAKZ,aAAA,CAAca,SAAA,CAAUV,KAAA,EAAOH,aAAA,CAAcf,MAAM,CAAC;QAC1E;MACF;MAEA,OAAOgB,cAAA;IACT;IAEA3D,MAAA,CAAOD,OAAA,GAAU6C,KAAA;IACjB5C,MAAA,CAAOD,OAAA,CAAQ6C,KAAA,GAAQA,KAAA;IACvB5C,MAAA,CAAOD,OAAA,CAAQS,WAAA,GAAcA,WAAA;IAC7BR,MAAA,CAAOD,OAAA,CAAQyE,kBAAA,GAAqBf,mBAAA;EAAA;AAAA;;;ACjOpC,IAAAgB,wBAAA,GAAmCC,OAAA,CAAA9E,kBAAA;;;ACAnC,IAAM+E,0BAAA,GAA6B;AAE5B,SAASC,oBAAoB1D,IAAA,EAAsB;EACxD,IAAIyD,0BAAA,CAA2BR,IAAA,CAAKjD,IAAI,KAAKA,IAAA,CAAKX,IAAA,CAAK,MAAM,IAAI;IAC/D,MAAM,IAAIsE,SAAA,CAAU,wCAAwC;EAC9D;EAEA,OAAO3D,IAAA,CAAKX,IAAA,CAAK,EAAEyB,WAAA,CAAY;AACjC;;;ACRA,IAAM8C,iBAAA,GAAoB,CACxBC,MAAA,CAAOC,YAAA,CAAa,EAAI,GACxBD,MAAA,CAAOC,YAAA,CAAa,EAAI,GACxBD,MAAA,CAAOC,YAAA,CAAa,CAAI,GACxBD,MAAA,CAAOC,YAAA,CAAa,EAAI,EAC1B;AAEA,IAAMC,0BAAA,GAA6B,IAAIC,MAAA,CACrC,MAAMJ,iBAAA,CAAkB5C,IAAA,CAAK,EAAE,CAAC,OAAO4C,iBAAA,CAAkB5C,IAAA,CAAK,EAAE,CAAC,MACjE,GACF;AAMO,SAASiD,qBAAqBhE,KAAA,EAAuB;EAC1D,MAAMiE,SAAA,GAAYjE,KAAA,CAAMkE,OAAA,CAAQJ,0BAAA,EAA4B,EAAE;EAC9D,OAAOG,SAAA;AACT;;;ACfO,SAASE,kBAAkBnE,KAAA,EAAgB;EAChD,IAAI,OAAOA,KAAA,KAAU,UAAU;IAC7B,OAAO;EACT;EAEA,IAAIA,KAAA,CAAMwB,MAAA,KAAW,GAAG;IACtB,OAAO;EACT;EAEA,SAAS4C,CAAA,GAAI,GAAGA,CAAA,GAAIpE,KAAA,CAAMwB,MAAA,EAAQ4C,CAAA,IAAK;IACrC,MAAMC,SAAA,GAAYrE,KAAA,CAAMsE,UAAA,CAAWF,CAAC;IAEpC,IAAIC,SAAA,GAAY,OAAQ,CAACE,OAAA,CAAQF,SAAS,GAAG;MAC3C,OAAO;IACT;EACF;EAEA,OAAO;AACT;AAEA,SAASE,QAAQvE,KAAA,EAAiC;EAChD,OAAO,CAAC,CACN,KACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,MACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,IACF,CAAEwE,QAAA,CAASxE,KAAK;AAClB;;;AC1CO,SAASyE,mBAAmBzE,KAAA,EAAyB;EAC1D,IAAI,OAAOA,KAAA,KAAU,UAAU;IAC7B,OAAO;EACT;EAEA,IAAIA,KAAA,CAAMZ,IAAA,CAAK,MAAMY,KAAA,EAAO;IAC1B,OAAO;EACT;EAEA,SAASoE,CAAA,GAAI,GAAGA,CAAA,GAAIpE,KAAA,CAAMwB,MAAA,EAAQ4C,CAAA,IAAK;IACrC,MAAMC,SAAA,GAAYrE,KAAA,CAAMsE,UAAA,CAAWF,CAAC;IAEpC;IAAA;IAEEC,SAAA,KAAc;IAAA;IAEdA,SAAA,KAAc,MACdA,SAAA,KAAc,IACd;MACA,OAAO;IACT;EACF;EAEA,OAAO;AACT;;;AJrBO,IAAMK,kBAAA,GAAoCC,MAAA,CAAO,mBAAmB;AAEpE,IAAMC,gBAAA,GAAkCD,MAAA,CAAO,gBAAgB;AAEtE,IAAME,sBAAA,GAAyB;AAX/B,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;AAaO,IAAMC,OAAA,GAAN,MAAMC,QAAA,CAAQ;EAQnBC,YAAYC,IAAA,EAAkD;IAN9D;IAAA,KAASN,EAAA,IAA8C,CAAC;IAIxD;IAAA;IAAA,KAASC,EAAA,IAAyC,mBAAIM,GAAA,CAAI;IAmC1D,KAACL,EAAA,IAAsB;IA5BrB,IACE,CAAC,WAAW,iBAAiB,EAAER,QAAA,CAASY,IAAA,EAAMD,WAAA,CAAYpF,IAAI,KAC9DqF,IAAA,YAAgBF,QAAA,IACf,OAAOI,UAAA,CAAWL,OAAA,KAAY,eAC7BG,IAAA,YAAgBE,UAAA,CAAWL,OAAA,EAC7B;MACA,MAAMM,cAAA,GAAiBH,IAAA;MACvBG,cAAA,CAAe/E,OAAA,CAAQ,CAACR,KAAA,EAAOD,IAAA,KAAS;QACtC,KAAKyF,MAAA,CAAOzF,IAAA,EAAMC,KAAK;MACzB,GAAG,IAAI;IACT,WAAWiC,KAAA,CAAMC,OAAA,CAAQkD,IAAI,GAAG;MAC9BA,IAAA,CAAK5E,OAAA,CAAQ,CAAC,CAACT,IAAA,EAAMC,KAAK,MAAM;QAC9B,KAAKwF,MAAA,CACHzF,IAAA,EACAkC,KAAA,CAAMC,OAAA,CAAQlC,KAAK,IAAIA,KAAA,CAAMe,IAAA,CAAK8D,sBAAsB,IAAI7E,KAC9D;MACF,CAAC;IACH,WAAWoF,IAAA,EAAM;MACfnF,MAAA,CAAOwF,mBAAA,CAAoBL,IAAI,EAAE5E,OAAA,CAAST,IAAA,IAAS;QACjD,MAAMC,KAAA,GAAQoF,IAAA,CAAKrF,IAAI;QACvB,KAAKyF,MAAA,CACHzF,IAAA,EACAkC,KAAA,CAAMC,OAAA,CAAQlC,KAAK,IAAIA,KAAA,CAAMe,IAAA,CAAK8D,sBAAsB,IAAI7E,KAC9D;MACF,CAAC;IACH;EACF;EAIA,EAzCS8E,EAAA,GAAAJ,kBAAA,EAIAK,EAAA,GAAAH,gBAAA,EAmCRI,EAAA,GAAAL,MAAA,CAAOe,WAAA,EAEPf,MAAA,CAAOgB,QAAA,KAAY;IAClB,OAAO,KAAKC,OAAA,CAAQ;EACtB;EAEA,CAAC9D,KAAA,EAAiC;IAChC,WAAW,CAAC/B,IAAI,KAAK,KAAK6F,OAAA,CAAQ,GAAG;MACnC,MAAM7F,IAAA;IACR;EACF;EAEA,CAAC8F,OAAA,EAAmC;IAClC,WAAW,GAAG7F,KAAK,KAAK,KAAK4F,OAAA,CAAQ,GAAG;MACtC,MAAM5F,KAAA;IACR;EACF;EAEA,CAAC4F,QAAA,EAA8C;IAE7C,IAAIE,UAAA,GAAa7F,MAAA,CAAO6B,IAAA,CAAK,KAAK4C,kBAAkB,CAAC,EAAEqB,IAAA,CAAK,CAACC,CAAA,EAAGC,CAAA,KAC9DD,CAAA,CAAEE,aAAA,CAAcD,CAAC,CACnB;IACA,WAAWlG,IAAA,IAAQ+F,UAAA,EAAY;MAC7B,IAAI/F,IAAA,KAAS,cAAc;QACzB,WAAWC,KAAA,IAAS,KAAK4B,YAAA,CAAa,GAAG;UACvC,MAAM,CAAC7B,IAAA,EAAMC,KAAK;QACpB;MACF,OAAO;QACL,MAAM,CAACD,IAAA,EAAM,KAAKoG,GAAA,CAAIpG,IAAI,CAAC;MAC7B;IACF;EACF;EAAA;AAAA;AAAA;EAKAqG,IAAIrG,IAAA,EAAuB;IACzB,IAAI,CAACoE,iBAAA,CAAkBpE,IAAI,GAAG;MAC5B,MAAM,IAAI2D,SAAA,CAAU,wBAAwB3D,IAAI,GAAG;IACrD;IAEA,OAAO,KAAK2E,kBAAkB,EAAE2B,cAAA,CAAe5C,mBAAA,CAAoB1D,IAAI,CAAC;EAC1E;EAAA;AAAA;AAAA;EAKAoG,IAAIpG,IAAA,EAA6B;IAC/B,IAAI,CAACoE,iBAAA,CAAkBpE,IAAI,GAAG;MAC5B,MAAM2D,SAAA,CAAU,wBAAwB3D,IAAI,GAAG;IACjD;IAEA,OAAO,KAAK2E,kBAAkB,EAAEjB,mBAAA,CAAoB1D,IAAI,CAAC,KAAK;EAChE;EAAA;AAAA;AAAA;EAKAuG,IAAIvG,IAAA,EAAcC,KAAA,EAAqB;IACrC,IAAI,CAACmE,iBAAA,CAAkBpE,IAAI,KAAK,CAAC0E,kBAAA,CAAmBzE,KAAK,GAAG;MAC1D;IACF;IAEA,MAAMuG,cAAA,GAAiB9C,mBAAA,CAAoB1D,IAAI;IAC/C,MAAMyG,eAAA,GAAkBxC,oBAAA,CAAqBhE,KAAK;IAElD,KAAK0E,kBAAkB,EAAE6B,cAAc,IACrCvC,oBAAA,CAAqBwC,eAAe;IACtC,KAAK5B,gBAAgB,EAAE0B,GAAA,CAAIC,cAAA,EAAgBxG,IAAI;EACjD;EAAA;AAAA;AAAA;EAKAyF,OAAOzF,IAAA,EAAcC,KAAA,EAAqB;IACxC,IAAI,CAACmE,iBAAA,CAAkBpE,IAAI,KAAK,CAAC0E,kBAAA,CAAmBzE,KAAK,GAAG;MAC1D;IACF;IAEA,MAAMuG,cAAA,GAAiB9C,mBAAA,CAAoB1D,IAAI;IAC/C,MAAMyG,eAAA,GAAkBxC,oBAAA,CAAqBhE,KAAK;IAElD,IAAIyG,aAAA,GAAgB,KAAKL,GAAA,CAAIG,cAAc,IACvC,GAAG,KAAKJ,GAAA,CAAII,cAAc,CAAC,KAAKC,eAAe,KAC/CA,eAAA;IAEJ,KAAKF,GAAA,CAAIvG,IAAA,EAAM0G,aAAa;EAC9B;EAAA;AAAA;AAAA;EAKAC,OAAO3G,IAAA,EAAoB;IACzB,IAAI,CAACoE,iBAAA,CAAkBpE,IAAI,GAAG;MAC5B;IACF;IAEA,IAAI,CAAC,KAAKqG,GAAA,CAAIrG,IAAI,GAAG;MACnB;IACF;IAEA,MAAMwG,cAAA,GAAiB9C,mBAAA,CAAoB1D,IAAI;IAC/C,OAAO,KAAK2E,kBAAkB,EAAE6B,cAAc;IAC9C,KAAK3B,gBAAgB,EAAE8B,MAAA,CAAOH,cAAc;EAC9C;EAAA;AAAA;AAAA;AAAA;EAMA/F,QACEmG,QAAA,EAMAC,OAAA,EACA;IACA,WAAW,CAAC7G,IAAA,EAAMC,KAAK,KAAK,KAAK4F,OAAA,CAAQ,GAAG;MAC1Ce,QAAA,CAASE,IAAA,CAAKD,OAAA,EAAS5G,KAAA,EAAOD,IAAA,EAAM,IAAI;IAC1C;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA6B,aAAA,EAAyB;IACvB,MAAMkF,eAAA,GAAkB,KAAKX,GAAA,CAAI,YAAY;IAE7C,IAAIW,eAAA,KAAoB,MAAM;MAC5B,OAAO,EAAC;IACV;IAEA,IAAIA,eAAA,KAAoB,IAAI;MAC1B,OAAO,CAAC,EAAE;IACZ;IAEA,WAAOxD,wBAAA,CAAAD,kBAAA,EAAmByD,eAAe;EAC3C;AACF;;;AKhMO,SAASC,cAAcpF,OAAA,EAAkB;EAC9C,MAAMqF,UAAA,GAAqC,CAAC;EAE5C,WAAW,CAACjH,IAAA,EAAMC,KAAK,KAAK2B,OAAA,CAAQiE,OAAA,CAAQ,GAAG;IAC7CoB,UAAA,CAAWrF,OAAA,CAAQiD,gBAAgB,EAAEuB,GAAA,CAAIpG,IAAI,CAAC,IAAIC,KAAA;EACpD;EAEA,OAAOgH,UAAA;AACT;;;ACXO,SAASC,cAActF,OAAA,EAA+B;EAC3D,MAAMuF,WAAA,GAA2B,EAAC;EAElCvF,OAAA,CAAQnB,OAAA,CAAQ,CAACR,KAAA,EAAOD,IAAA,KAAS;IAC/B,MAAM0G,aAAA,GAAgBzG,KAAA,CAAMwE,QAAA,CAAS,GAAG,IACpCxE,KAAA,CAAMP,KAAA,CAAM,GAAG,EAAET,GAAA,CAAK8B,MAAA,IAAUA,MAAA,CAAM1B,IAAA,CAAK,CAAC,IAC5CY,KAAA;IAEJkH,WAAA,CAAY/D,IAAA,CAAK,CAACpD,IAAA,EAAM0G,aAAa,CAAC;EACxC,CAAC;EAED,OAAOS,WAAA;AACT;;;ACTO,SAASC,gBAAgBxF,OAAA,EAA0B;EACxD,MAAMyF,IAAA,GAAOH,aAAA,CAActF,OAAO;EAClC,MAAM0F,KAAA,GAAQD,IAAA,CAAKpI,GAAA,CAAI,CAAC,CAACe,IAAA,EAAMC,KAAK,MAAM;IACxC,MAAM6F,MAAA,GAAU,EAAC,CAAeyB,MAAA,CAAOtH,KAAK;IAC5C,OAAO,GAAGD,IAAI,KAAK8F,MAAA,CAAO9E,IAAA,CAAK,IAAI,CAAC;EACtC,CAAC;EAED,OAAOsG,KAAA,CAAMtG,IAAA,CAAK,MAAM;AAC1B;;;ACTA,IAAMwG,kBAAA,GAAqB,CAAC,YAAY;AAMjC,SAASC,gBAAgB7F,OAAA,EAAiC;EAC/D,MAAM8F,aAAA,GAA+B,CAAC;EAEtC9F,OAAA,CAAQnB,OAAA,CAAQ,CAACR,KAAA,EAAOD,IAAA,KAAS;IAC/B,MAAM2H,YAAA,GACJ,CAACH,kBAAA,CAAmB/C,QAAA,CAASzE,IAAA,CAAKc,WAAA,CAAY,CAAC,KAAKb,KAAA,CAAMwE,QAAA,CAAS,GAAG;IACxEiD,aAAA,CAAc1H,IAAI,IAAI2H,YAAA,GAClB1H,KAAA,CAAMP,KAAA,CAAM,GAAG,EAAET,GAAA,CAAK2I,CAAA,IAAMA,CAAA,CAAEvI,IAAA,CAAK,CAAC,IACpCY,KAAA;EACN,CAAC;EAED,OAAOyH,aAAA;AACT;;;AChBO,SAASG,gBAAgBzI,GAAA,EAAsB;EACpD,MAAMkI,KAAA,GAAQlI,GAAA,CAAIC,IAAA,CAAK,EAAEK,KAAA,CAAM,SAAS;EAExC,OAAO4H,KAAA,CAAMjF,MAAA,CAAO,CAACT,OAAA,EAASkG,IAAA,KAAS;IACrC,IAAIA,IAAA,CAAKzI,IAAA,CAAK,MAAM,IAAI;MACtB,OAAOuC,OAAA;IACT;IAEA,MAAMnC,KAAA,GAAQqI,IAAA,CAAKpI,KAAA,CAAM,IAAI;IAC7B,MAAMM,IAAA,GAAOP,KAAA,CAAMI,KAAA,CAAM;IACzB,MAAMI,KAAA,GAAQR,KAAA,CAAMuB,IAAA,CAAK,IAAI;IAC7BY,OAAA,CAAQ6D,MAAA,CAAOzF,IAAA,EAAMC,KAAK;IAE1B,OAAO2B,OAAA;EACT,GAAG,IAAIsD,OAAA,CAAQ,CAAC;AAClB;;;AClBO,SAAS6C,cAAcV,IAAA,EAA4B;EACxD,MAAMzF,OAAA,GAAU,IAAIsD,OAAA,CAAQ;EAE5BmC,IAAA,CAAK5G,OAAA,CAAQ,CAAC,CAACT,IAAA,EAAMC,KAAK,MAAM;IAC9B,MAAM6F,MAAA,GAAU,EAAC,CAAeyB,MAAA,CAAOtH,KAAK;IAE5C6F,MAAA,CAAOrF,OAAA,CAASM,MAAA,IAAU;MACxBa,OAAA,CAAQ6D,MAAA,CAAOzF,IAAA,EAAMe,MAAK;IAC5B,CAAC;EACH,CAAC;EAED,OAAOa,OAAA;AACT;;;ACVO,SAASoG,oBACdpG,OAAA,EACAqG,OAAA,EACAC,YAAA,EACG;EACH,OAAOhI,MAAA,CAAO6B,IAAA,CAAKH,OAAO,EAAES,MAAA,CAAU,CAAC8F,WAAA,EAAanI,IAAA,KAAS;IAC3D,OAAOiI,OAAA,CAAQE,WAAA,EAAanI,IAAA,EAAM4B,OAAA,CAAQ5B,IAAI,CAAC;EACjD,GAAGkI,YAAY;AACjB;;;ACPO,SAASE,gBACdV,aAAA,EACS;EACT,OAAOM,mBAAA,CACLN,aAAA,EACA,CAAC9F,OAAA,EAAS5B,IAAA,EAAMC,KAAA,KAAU;IACxB,MAAM6F,MAAA,GAAU,EAAC,CAAeyB,MAAA,CAAOtH,KAAK,EAAEN,MAAA,CAAO0I,OAAO;IAE5DvC,MAAA,CAAOrF,OAAA,CAASM,MAAA,IAAU;MACxBa,OAAA,CAAQ6D,MAAA,CAAOzF,IAAA,EAAMe,MAAK;IAC5B,CAAC;IAED,OAAOa,OAAA;EACT,GACA,IAAIsD,OAAA,CAAQ,CACd;AACF;;;ACpBO,SAASoD,mBAAmBjB,IAAA,EAAoC;EACrE,OAAOA,IAAA,CAAKpI,GAAA,CAAI,CAAC,CAACe,IAAA,EAAM8F,MAAM,MAAM;IAClC,OAAO,CAAC9F,IAAA,EAAO,EAAC,CAAeuH,MAAA,CAAOzB,MAAM,EAAE9E,IAAA,CAAK,IAAI,CAAC;EAC1D,CAAC;AACH;;;ACHO,SAASuH,qBACdb,aAAA,EACmB;EACnB,OAAOM,mBAAA,CACLN,aAAA,EACA,CAAC9F,OAAA,EAAS5B,IAAA,EAAMC,KAAA,KAAU;IACxB2B,OAAA,CAAQ5B,IAAI,IAAK,EAAC,CAAeuH,MAAA,CAAOtH,KAAK,EAAEe,IAAA,CAAK,IAAI;IACxD,OAAOY,OAAA;EACT,GACA,CAAC,CACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}