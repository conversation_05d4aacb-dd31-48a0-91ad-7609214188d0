{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { visit, visitInParallel } from '../language/visitor.mjs';\nimport { assertValidSchema } from '../type/validate.mjs';\nimport { TypeInfo, visitWithTypeInfo } from '../utilities/TypeInfo.mjs';\nimport { specifiedRules, specifiedSDLRules } from './specifiedRules.mjs';\nimport { SDLValidationContext, ValidationContext } from './ValidationContext.mjs';\n/**\n * Implements the \"Validation\" section of the spec.\n *\n * Validation runs synchronously, returning an array of encountered errors, or\n * an empty array if no errors were encountered and the document is valid.\n *\n * A list of specific validation rules may be provided. If not provided, the\n * default list of rules defined by the GraphQL specification will be used.\n *\n * Each validation rules is a function which returns a visitor\n * (see the language/visitor API). Visitor methods are expected to return\n * GraphQLErrors, or Arrays of GraphQLErrors when invalid.\n *\n * Validate will stop validation after a `maxErrors` limit has been reached.\n * Attackers can send pathologically invalid queries to induce a DoS attack,\n * so by default `maxErrors` set to 100 errors.\n *\n * Optionally a custom TypeInfo instance may be provided. If not provided, one\n * will be created from the provided schema.\n */\n\nexport function validate(schema, documentAST, rules = specifiedRules, options, /** @deprecated will be removed in 17.0.0 */\ntypeInfo = new TypeInfo(schema)) {\n  var _options$maxErrors;\n  const maxErrors = (_options$maxErrors = options === null || options === void 0 ? void 0 : options.maxErrors) !== null && _options$maxErrors !== void 0 ? _options$maxErrors : 100;\n  documentAST || devAssert(false, 'Must provide document.'); // If the schema used for validation is invalid, throw an error.\n\n  assertValidSchema(schema);\n  const abortObj = Object.freeze({});\n  const errors = [];\n  const context = new ValidationContext(schema, documentAST, typeInfo, error => {\n    if (errors.length >= maxErrors) {\n      errors.push(new GraphQLError('Too many validation errors, error limit reached. Validation aborted.')); // eslint-disable-next-line @typescript-eslint/no-throw-literal\n\n      throw abortObj;\n    }\n    errors.push(error);\n  }); // This uses a specialized visitor which runs multiple visitors in parallel,\n  // while maintaining the visitor skip and break API.\n\n  const visitor = visitInParallel(rules.map(rule => rule(context))); // Visit the whole document with each instance of all provided rules.\n\n  try {\n    visit(documentAST, visitWithTypeInfo(typeInfo, visitor));\n  } catch (e) {\n    if (e !== abortObj) {\n      throw e;\n    }\n  }\n  return errors;\n}\n/**\n * @internal\n */\n\nexport function validateSDL(documentAST, schemaToExtend, rules = specifiedSDLRules) {\n  const errors = [];\n  const context = new SDLValidationContext(documentAST, schemaToExtend, error => {\n    errors.push(error);\n  });\n  const visitors = rules.map(rule => rule(context));\n  visit(documentAST, visitInParallel(visitors));\n  return errors;\n}\n/**\n * Utility function which asserts a SDL document is valid by throwing an error\n * if it is invalid.\n *\n * @internal\n */\n\nexport function assertValidSDL(documentAST) {\n  const errors = validateSDL(documentAST);\n  if (errors.length !== 0) {\n    throw new Error(errors.map(error => error.message).join('\\n\\n'));\n  }\n}\n/**\n * Utility function which asserts a SDL document is valid by throwing an error\n * if it is invalid.\n *\n * @internal\n */\n\nexport function assertValidSDLExtension(documentAST, schema) {\n  const errors = validateSDL(documentAST, schema);\n  if (errors.length !== 0) {\n    throw new Error(errors.map(error => error.message).join('\\n\\n'));\n  }\n}", "map": {"version": 3, "names": ["devAssert", "GraphQLError", "visit", "visitInParallel", "assertValidSchema", "TypeInfo", "visitWithTypeInfo", "specifiedRules", "specifiedSDLRules", "SDLValidationContext", "ValidationContext", "validate", "schema", "documentAST", "rules", "options", "typeInfo", "_options$maxErrors", "maxErrors", "abortObj", "Object", "freeze", "errors", "context", "error", "length", "push", "visitor", "map", "rule", "e", "validateSDL", "schemaToExtend", "visitors", "assertValidSDL", "Error", "message", "join", "assertValidSDLExtension"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/validate.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { visit, visitInParallel } from '../language/visitor.mjs';\nimport { assertValidSchema } from '../type/validate.mjs';\nimport { TypeInfo, visitWithTypeInfo } from '../utilities/TypeInfo.mjs';\nimport { specifiedRules, specifiedSDLRules } from './specifiedRules.mjs';\nimport {\n  SDLValidationContext,\n  ValidationContext,\n} from './ValidationContext.mjs';\n/**\n * Implements the \"Validation\" section of the spec.\n *\n * Validation runs synchronously, returning an array of encountered errors, or\n * an empty array if no errors were encountered and the document is valid.\n *\n * A list of specific validation rules may be provided. If not provided, the\n * default list of rules defined by the GraphQL specification will be used.\n *\n * Each validation rules is a function which returns a visitor\n * (see the language/visitor API). Visitor methods are expected to return\n * GraphQLErrors, or Arrays of GraphQLErrors when invalid.\n *\n * Validate will stop validation after a `maxErrors` limit has been reached.\n * Attackers can send pathologically invalid queries to induce a DoS attack,\n * so by default `maxErrors` set to 100 errors.\n *\n * Optionally a custom TypeInfo instance may be provided. If not provided, one\n * will be created from the provided schema.\n */\n\nexport function validate(\n  schema,\n  documentAST,\n  rules = specifiedRules,\n  options,\n  /** @deprecated will be removed in 17.0.0 */\n  typeInfo = new TypeInfo(schema),\n) {\n  var _options$maxErrors;\n\n  const maxErrors =\n    (_options$maxErrors =\n      options === null || options === void 0 ? void 0 : options.maxErrors) !==\n      null && _options$maxErrors !== void 0\n      ? _options$maxErrors\n      : 100;\n  documentAST || devAssert(false, 'Must provide document.'); // If the schema used for validation is invalid, throw an error.\n\n  assertValidSchema(schema);\n  const abortObj = Object.freeze({});\n  const errors = [];\n  const context = new ValidationContext(\n    schema,\n    documentAST,\n    typeInfo,\n    (error) => {\n      if (errors.length >= maxErrors) {\n        errors.push(\n          new GraphQLError(\n            'Too many validation errors, error limit reached. Validation aborted.',\n          ),\n        ); // eslint-disable-next-line @typescript-eslint/no-throw-literal\n\n        throw abortObj;\n      }\n\n      errors.push(error);\n    },\n  ); // This uses a specialized visitor which runs multiple visitors in parallel,\n  // while maintaining the visitor skip and break API.\n\n  const visitor = visitInParallel(rules.map((rule) => rule(context))); // Visit the whole document with each instance of all provided rules.\n\n  try {\n    visit(documentAST, visitWithTypeInfo(typeInfo, visitor));\n  } catch (e) {\n    if (e !== abortObj) {\n      throw e;\n    }\n  }\n\n  return errors;\n}\n/**\n * @internal\n */\n\nexport function validateSDL(\n  documentAST,\n  schemaToExtend,\n  rules = specifiedSDLRules,\n) {\n  const errors = [];\n  const context = new SDLValidationContext(\n    documentAST,\n    schemaToExtend,\n    (error) => {\n      errors.push(error);\n    },\n  );\n  const visitors = rules.map((rule) => rule(context));\n  visit(documentAST, visitInParallel(visitors));\n  return errors;\n}\n/**\n * Utility function which asserts a SDL document is valid by throwing an error\n * if it is invalid.\n *\n * @internal\n */\n\nexport function assertValidSDL(documentAST) {\n  const errors = validateSDL(documentAST);\n\n  if (errors.length !== 0) {\n    throw new Error(errors.map((error) => error.message).join('\\n\\n'));\n  }\n}\n/**\n * Utility function which asserts a SDL document is valid by throwing an error\n * if it is invalid.\n *\n * @internal\n */\n\nexport function assertValidSDLExtension(documentAST, schema) {\n  const errors = validateSDL(documentAST, schema);\n\n  if (errors.length !== 0) {\n    throw new Error(errors.map((error) => error.message).join('\\n\\n'));\n  }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,KAAK,EAAEC,eAAe,QAAQ,yBAAyB;AAChE,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,2BAA2B;AACvE,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,sBAAsB;AACxE,SACEC,oBAAoB,EACpBC,iBAAiB,QACZ,yBAAyB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,QAAQA,CACtBC,MAAM,EACNC,WAAW,EACXC,KAAK,GAAGP,cAAc,EACtBQ,OAAO,EACP;AACAC,QAAQ,GAAG,IAAIX,QAAQ,CAACO,MAAM,CAAC,EAC/B;EACA,IAAIK,kBAAkB;EAEtB,MAAMC,SAAS,GACb,CAACD,kBAAkB,GACjBF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,SAAS,MACnE,IAAI,IAAID,kBAAkB,KAAK,KAAK,CAAC,GACnCA,kBAAkB,GAClB,GAAG;EACTJ,WAAW,IAAIb,SAAS,CAAC,KAAK,EAAE,wBAAwB,CAAC,CAAC,CAAC;;EAE3DI,iBAAiB,CAACQ,MAAM,CAAC;EACzB,MAAMO,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;EAClC,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,OAAO,GAAG,IAAIb,iBAAiB,CACnCE,MAAM,EACNC,WAAW,EACXG,QAAQ,EACPQ,KAAK,IAAK;IACT,IAAIF,MAAM,CAACG,MAAM,IAAIP,SAAS,EAAE;MAC9BI,MAAM,CAACI,IAAI,CACT,IAAIzB,YAAY,CACd,sEACF,CACF,CAAC,CAAC,CAAC;;MAEH,MAAMkB,QAAQ;IAChB;IAEAG,MAAM,CAACI,IAAI,CAACF,KAAK,CAAC;EACpB,CACF,CAAC,CAAC,CAAC;EACH;;EAEA,MAAMG,OAAO,GAAGxB,eAAe,CAACW,KAAK,CAACc,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACN,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErE,IAAI;IACFrB,KAAK,CAACW,WAAW,EAAEP,iBAAiB,CAACU,QAAQ,EAAEW,OAAO,CAAC,CAAC;EAC1D,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,IAAIA,CAAC,KAAKX,QAAQ,EAAE;MAClB,MAAMW,CAAC;IACT;EACF;EAEA,OAAOR,MAAM;AACf;AACA;AACA;AACA;;AAEA,OAAO,SAASS,WAAWA,CACzBlB,WAAW,EACXmB,cAAc,EACdlB,KAAK,GAAGN,iBAAiB,EACzB;EACA,MAAMc,MAAM,GAAG,EAAE;EACjB,MAAMC,OAAO,GAAG,IAAId,oBAAoB,CACtCI,WAAW,EACXmB,cAAc,EACbR,KAAK,IAAK;IACTF,MAAM,CAACI,IAAI,CAACF,KAAK,CAAC;EACpB,CACF,CAAC;EACD,MAAMS,QAAQ,GAAGnB,KAAK,CAACc,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACN,OAAO,CAAC,CAAC;EACnDrB,KAAK,CAACW,WAAW,EAAEV,eAAe,CAAC8B,QAAQ,CAAC,CAAC;EAC7C,OAAOX,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASY,cAAcA,CAACrB,WAAW,EAAE;EAC1C,MAAMS,MAAM,GAAGS,WAAW,CAAClB,WAAW,CAAC;EAEvC,IAAIS,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACvB,MAAM,IAAIU,KAAK,CAACb,MAAM,CAACM,GAAG,CAAEJ,KAAK,IAAKA,KAAK,CAACY,OAAO,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC,CAAC;EACpE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,uBAAuBA,CAACzB,WAAW,EAAED,MAAM,EAAE;EAC3D,MAAMU,MAAM,GAAGS,WAAW,CAAClB,WAAW,EAAED,MAAM,CAAC;EAE/C,IAAIU,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACvB,MAAM,IAAIU,KAAK,CAACb,MAAM,CAACM,GAAG,CAAEJ,KAAK,IAAKA,KAAK,CAACY,OAAO,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC,CAAC;EACpE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}