{"ast": null, "code": "import { isObject } from './isObject.mjs';\nfunction mergeRight(left, right) {\n  return Object.entries(right).reduce((result, [key, rightValue]) => {\n    const leftValue = result[key];\n    if (Array.isArray(leftValue) && Array.isArray(rightValue)) {\n      result[key] = leftValue.concat(rightValue);\n      return result;\n    }\n    if (isObject(leftValue) && isObject(rightValue)) {\n      result[key] = mergeRight(leftValue, rightValue);\n      return result;\n    }\n    result[key] = rightValue;\n    return result;\n  }, Object.assign({}, left));\n}\nexport { mergeRight };", "map": {"version": 3, "names": ["isObject", "mergeRight", "left", "right", "Object", "entries", "reduce", "result", "key", "rightValue", "leftValue", "Array", "isArray", "concat", "assign"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/internal/mergeRight.ts"], "sourcesContent": ["import { isObject } from './isObject'\n\n/**\n * Deeply merges two given objects with the right one\n * having a priority during property assignment.\n */\nexport function mergeRight(\n  left: Record<string, any>,\n  right: Record<string, any>,\n) {\n  return Object.entries(right).reduce(\n    (result, [key, rightValue]) => {\n      const leftValue = result[key]\n\n      if (Array.isArray(leftValue) && Array.isArray(rightValue)) {\n        result[key] = leftValue.concat(rightValue)\n        return result\n      }\n\n      if (isObject(leftValue) && isObject(rightValue)) {\n        result[key] = mergeRight(leftValue, rightValue)\n        return result\n      }\n\n      result[key] = rightValue\n      return result\n    },\n    Object.assign({}, left),\n  )\n}\n"], "mappings": "AAAA,SAASA,QAAA,QAAgB;AAMlB,SAASC,WACdC,IAAA,EACAC,KAAA,EACA;EACA,OAAOC,MAAA,CAAOC,OAAA,CAAQF,KAAK,EAAEG,MAAA,CAC3B,CAACC,MAAA,EAAQ,CAACC,GAAA,EAAKC,UAAU,MAAM;IAC7B,MAAMC,SAAA,GAAYH,MAAA,CAAOC,GAAG;IAE5B,IAAIG,KAAA,CAAMC,OAAA,CAAQF,SAAS,KAAKC,KAAA,CAAMC,OAAA,CAAQH,UAAU,GAAG;MACzDF,MAAA,CAAOC,GAAG,IAAIE,SAAA,CAAUG,MAAA,CAAOJ,UAAU;MACzC,OAAOF,MAAA;IACT;IAEA,IAAIP,QAAA,CAASU,SAAS,KAAKV,QAAA,CAASS,UAAU,GAAG;MAC/CF,MAAA,CAAOC,GAAG,IAAIP,UAAA,CAAWS,SAAA,EAAWD,UAAU;MAC9C,OAAOF,MAAA;IACT;IAEAA,MAAA,CAAOC,GAAG,IAAIC,UAAA;IACd,OAAOF,MAAA;EACT,GACAH,MAAA,CAAOU,MAAA,CAAO,CAAC,GAAGZ,IAAI,CACxB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}