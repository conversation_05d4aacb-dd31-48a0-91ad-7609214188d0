{"ast": null, "code": "const MAX_ARRAY_LENGTH = 10;\nconst MAX_RECURSIVE_DEPTH = 2;\n/**\n * Used to print values in error messages.\n */\n\nexport function inspect(value) {\n  return formatValue(value, []);\n}\nfunction formatValue(value, seenValues) {\n  switch (typeof value) {\n    case 'string':\n      return JSON.stringify(value);\n    case 'function':\n      return value.name ? `[function ${value.name}]` : '[function]';\n    case 'object':\n      return formatObjectValue(value, seenValues);\n    default:\n      return String(value);\n  }\n}\nfunction formatObjectValue(value, previouslySeenValues) {\n  if (value === null) {\n    return 'null';\n  }\n  if (previouslySeenValues.includes(value)) {\n    return '[Circular]';\n  }\n  const seenValues = [...previouslySeenValues, value];\n  if (isJSONable(value)) {\n    const jsonValue = value.toJSON(); // check for infinite recursion\n\n    if (jsonValue !== value) {\n      return typeof jsonValue === 'string' ? jsonValue : formatValue(jsonValue, seenValues);\n    }\n  } else if (Array.isArray(value)) {\n    return formatArray(value, seenValues);\n  }\n  return formatObject(value, seenValues);\n}\nfunction isJSONable(value) {\n  return typeof value.toJSON === 'function';\n}\nfunction formatObject(object, seenValues) {\n  const entries = Object.entries(object);\n  if (entries.length === 0) {\n    return '{}';\n  }\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[' + getObjectTag(object) + ']';\n  }\n  const properties = entries.map(([key, value]) => key + ': ' + formatValue(value, seenValues));\n  return '{ ' + properties.join(', ') + ' }';\n}\nfunction formatArray(array, seenValues) {\n  if (array.length === 0) {\n    return '[]';\n  }\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[Array]';\n  }\n  const len = Math.min(MAX_ARRAY_LENGTH, array.length);\n  const remaining = array.length - len;\n  const items = [];\n  for (let i = 0; i < len; ++i) {\n    items.push(formatValue(array[i], seenValues));\n  }\n  if (remaining === 1) {\n    items.push('... 1 more item');\n  } else if (remaining > 1) {\n    items.push(`... ${remaining} more items`);\n  }\n  return '[' + items.join(', ') + ']';\n}\nfunction getObjectTag(object) {\n  const tag = Object.prototype.toString.call(object).replace(/^\\[object /, '').replace(/]$/, '');\n  if (tag === 'Object' && typeof object.constructor === 'function') {\n    const name = object.constructor.name;\n    if (typeof name === 'string' && name !== '') {\n      return name;\n    }\n  }\n  return tag;\n}", "map": {"version": 3, "names": ["MAX_ARRAY_LENGTH", "MAX_RECURSIVE_DEPTH", "inspect", "value", "formatValue", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "name", "formatObjectValue", "String", "previouslySeenV<PERSON>ues", "includes", "isJSONable", "jsonValue", "toJSON", "Array", "isArray", "formatArray", "formatObject", "object", "entries", "Object", "length", "getObjectTag", "properties", "map", "key", "join", "array", "len", "Math", "min", "remaining", "items", "i", "push", "tag", "prototype", "toString", "call", "replace", "constructor"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/jsutils/inspect.mjs"], "sourcesContent": ["const MAX_ARRAY_LENGTH = 10;\nconst MAX_RECURSIVE_DEPTH = 2;\n/**\n * Used to print values in error messages.\n */\n\nexport function inspect(value) {\n  return formatValue(value, []);\n}\n\nfunction formatValue(value, seenValues) {\n  switch (typeof value) {\n    case 'string':\n      return JSON.stringify(value);\n\n    case 'function':\n      return value.name ? `[function ${value.name}]` : '[function]';\n\n    case 'object':\n      return formatObjectValue(value, seenValues);\n\n    default:\n      return String(value);\n  }\n}\n\nfunction formatObjectValue(value, previouslySeenValues) {\n  if (value === null) {\n    return 'null';\n  }\n\n  if (previouslySeenValues.includes(value)) {\n    return '[Circular]';\n  }\n\n  const seenValues = [...previouslySeenValues, value];\n\n  if (isJSONable(value)) {\n    const jsonValue = value.toJSON(); // check for infinite recursion\n\n    if (jsonValue !== value) {\n      return typeof jsonValue === 'string'\n        ? jsonValue\n        : formatValue(jsonValue, seenValues);\n    }\n  } else if (Array.isArray(value)) {\n    return formatArray(value, seenValues);\n  }\n\n  return formatObject(value, seenValues);\n}\n\nfunction isJSONable(value) {\n  return typeof value.toJSON === 'function';\n}\n\nfunction formatObject(object, seenValues) {\n  const entries = Object.entries(object);\n\n  if (entries.length === 0) {\n    return '{}';\n  }\n\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[' + getObjectTag(object) + ']';\n  }\n\n  const properties = entries.map(\n    ([key, value]) => key + ': ' + formatValue(value, seenValues),\n  );\n  return '{ ' + properties.join(', ') + ' }';\n}\n\nfunction formatArray(array, seenValues) {\n  if (array.length === 0) {\n    return '[]';\n  }\n\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[Array]';\n  }\n\n  const len = Math.min(MAX_ARRAY_LENGTH, array.length);\n  const remaining = array.length - len;\n  const items = [];\n\n  for (let i = 0; i < len; ++i) {\n    items.push(formatValue(array[i], seenValues));\n  }\n\n  if (remaining === 1) {\n    items.push('... 1 more item');\n  } else if (remaining > 1) {\n    items.push(`... ${remaining} more items`);\n  }\n\n  return '[' + items.join(', ') + ']';\n}\n\nfunction getObjectTag(object) {\n  const tag = Object.prototype.toString\n    .call(object)\n    .replace(/^\\[object /, '')\n    .replace(/]$/, '');\n\n  if (tag === 'Object' && typeof object.constructor === 'function') {\n    const name = object.constructor.name;\n\n    if (typeof name === 'string' && name !== '') {\n      return name;\n    }\n  }\n\n  return tag;\n}\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,mBAAmB,GAAG,CAAC;AAC7B;AACA;AACA;;AAEA,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAOC,WAAW,CAACD,KAAK,EAAE,EAAE,CAAC;AAC/B;AAEA,SAASC,WAAWA,CAACD,KAAK,EAAEE,UAAU,EAAE;EACtC,QAAQ,OAAOF,KAAK;IAClB,KAAK,QAAQ;MACX,OAAOG,IAAI,CAACC,SAAS,CAACJ,KAAK,CAAC;IAE9B,KAAK,UAAU;MACb,OAAOA,KAAK,CAACK,IAAI,GAAG,aAAaL,KAAK,CAACK,IAAI,GAAG,GAAG,YAAY;IAE/D,KAAK,QAAQ;MACX,OAAOC,iBAAiB,CAACN,KAAK,EAAEE,UAAU,CAAC;IAE7C;MACE,OAAOK,MAAM,CAACP,KAAK,CAAC;EACxB;AACF;AAEA,SAASM,iBAAiBA,CAACN,KAAK,EAAEQ,oBAAoB,EAAE;EACtD,IAAIR,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,MAAM;EACf;EAEA,IAAIQ,oBAAoB,CAACC,QAAQ,CAACT,KAAK,CAAC,EAAE;IACxC,OAAO,YAAY;EACrB;EAEA,MAAME,UAAU,GAAG,CAAC,GAAGM,oBAAoB,EAAER,KAAK,CAAC;EAEnD,IAAIU,UAAU,CAACV,KAAK,CAAC,EAAE;IACrB,MAAMW,SAAS,GAAGX,KAAK,CAACY,MAAM,CAAC,CAAC,CAAC,CAAC;;IAElC,IAAID,SAAS,KAAKX,KAAK,EAAE;MACvB,OAAO,OAAOW,SAAS,KAAK,QAAQ,GAChCA,SAAS,GACTV,WAAW,CAACU,SAAS,EAAET,UAAU,CAAC;IACxC;EACF,CAAC,MAAM,IAAIW,KAAK,CAACC,OAAO,CAACd,KAAK,CAAC,EAAE;IAC/B,OAAOe,WAAW,CAACf,KAAK,EAAEE,UAAU,CAAC;EACvC;EAEA,OAAOc,YAAY,CAAChB,KAAK,EAAEE,UAAU,CAAC;AACxC;AAEA,SAASQ,UAAUA,CAACV,KAAK,EAAE;EACzB,OAAO,OAAOA,KAAK,CAACY,MAAM,KAAK,UAAU;AAC3C;AAEA,SAASI,YAAYA,CAACC,MAAM,EAAEf,UAAU,EAAE;EACxC,MAAMgB,OAAO,GAAGC,MAAM,CAACD,OAAO,CAACD,MAAM,CAAC;EAEtC,IAAIC,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,IAAIlB,UAAU,CAACkB,MAAM,GAAGtB,mBAAmB,EAAE;IAC3C,OAAO,GAAG,GAAGuB,YAAY,CAACJ,MAAM,CAAC,GAAG,GAAG;EACzC;EAEA,MAAMK,UAAU,GAAGJ,OAAO,CAACK,GAAG,CAC5B,CAAC,CAACC,GAAG,EAAExB,KAAK,CAAC,KAAKwB,GAAG,GAAG,IAAI,GAAGvB,WAAW,CAACD,KAAK,EAAEE,UAAU,CAC9D,CAAC;EACD,OAAO,IAAI,GAAGoB,UAAU,CAACG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;AAC5C;AAEA,SAASV,WAAWA,CAACW,KAAK,EAAExB,UAAU,EAAE;EACtC,IAAIwB,KAAK,CAACN,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EAEA,IAAIlB,UAAU,CAACkB,MAAM,GAAGtB,mBAAmB,EAAE;IAC3C,OAAO,SAAS;EAClB;EAEA,MAAM6B,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAChC,gBAAgB,EAAE6B,KAAK,CAACN,MAAM,CAAC;EACpD,MAAMU,SAAS,GAAGJ,KAAK,CAACN,MAAM,GAAGO,GAAG;EACpC,MAAMI,KAAK,GAAG,EAAE;EAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,EAAE,EAAEK,CAAC,EAAE;IAC5BD,KAAK,CAACE,IAAI,CAAChC,WAAW,CAACyB,KAAK,CAACM,CAAC,CAAC,EAAE9B,UAAU,CAAC,CAAC;EAC/C;EAEA,IAAI4B,SAAS,KAAK,CAAC,EAAE;IACnBC,KAAK,CAACE,IAAI,CAAC,iBAAiB,CAAC;EAC/B,CAAC,MAAM,IAAIH,SAAS,GAAG,CAAC,EAAE;IACxBC,KAAK,CAACE,IAAI,CAAC,OAAOH,SAAS,aAAa,CAAC;EAC3C;EAEA,OAAO,GAAG,GAAGC,KAAK,CAACN,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;AACrC;AAEA,SAASJ,YAAYA,CAACJ,MAAM,EAAE;EAC5B,MAAMiB,GAAG,GAAGf,MAAM,CAACgB,SAAS,CAACC,QAAQ,CAClCC,IAAI,CAACpB,MAAM,CAAC,CACZqB,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBA,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;EAEpB,IAAIJ,GAAG,KAAK,QAAQ,IAAI,OAAOjB,MAAM,CAACsB,WAAW,KAAK,UAAU,EAAE;IAChE,MAAMlC,IAAI,GAAGY,MAAM,CAACsB,WAAW,CAAClC,IAAI;IAEpC,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,EAAE,EAAE;MAC3C,OAAOA,IAAI;IACb;EACF;EAEA,OAAO6B,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}