{"ast": null, "code": "import { inspect } from './inspect.mjs';\n/* c8 ignore next 3 */\n\nconst isProduction = globalThis.process &&\n// eslint-disable-next-line no-undef\nprocess.env.NODE_ENV === 'production';\n/**\n * A replacement for instanceof which includes an error warning when multi-realm\n * constructors are detected.\n * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production\n * See: https://webpack.js.org/guides/production/\n */\n\nexport const instanceOf = /* c8 ignore next 6 */\n// FIXME: https://github.com/graphql/graphql-js/issues/2317\nisProduction ? function instanceOf(value, constructor) {\n  return value instanceof constructor;\n} : function instanceOf(value, constructor) {\n  if (value instanceof constructor) {\n    return true;\n  }\n  if (typeof value === 'object' && value !== null) {\n    var _value$constructor;\n\n    // Prefer Symbol.toStringTag since it is immune to minification.\n    const className = constructor.prototype[Symbol.toStringTag];\n    const valueClassName =\n    // We still need to support constructor's name to detect conflicts with older versions of this library.\n    Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009\n    ? value[Symbol.toStringTag] : (_value$constructor = value.constructor) === null || _value$constructor === void 0 ? void 0 : _value$constructor.name;\n    if (className === valueClassName) {\n      const stringifiedValue = inspect(value);\n      throw new Error(`Cannot use ${className} \"${stringifiedValue}\" from another module or realm.\n\nEnsure that there is only one instance of \"graphql\" in the node_modules\ndirectory. If different versions of \"graphql\" are the dependencies of other\nrelied on modules, use \"resolutions\" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate \"graphql\" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`);\n    }\n  }\n  return false;\n};", "map": {"version": 3, "names": ["inspect", "isProduction", "globalThis", "process", "env", "NODE_ENV", "instanceOf", "value", "constructor", "_value$constructor", "className", "prototype", "Symbol", "toStringTag", "valueClassName", "name", "stringifiedValue", "Error"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/jsutils/instanceOf.mjs"], "sourcesContent": ["import { inspect } from './inspect.mjs';\n/* c8 ignore next 3 */\n\nconst isProduction =\n  globalThis.process && // eslint-disable-next-line no-undef\n  process.env.NODE_ENV === 'production';\n/**\n * A replacement for instanceof which includes an error warning when multi-realm\n * constructors are detected.\n * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production\n * See: https://webpack.js.org/guides/production/\n */\n\nexport const instanceOf =\n  /* c8 ignore next 6 */\n  // FIXME: https://github.com/graphql/graphql-js/issues/2317\n  isProduction\n    ? function instanceOf(value, constructor) {\n        return value instanceof constructor;\n      }\n    : function instanceOf(value, constructor) {\n        if (value instanceof constructor) {\n          return true;\n        }\n\n        if (typeof value === 'object' && value !== null) {\n          var _value$constructor;\n\n          // Prefer Symbol.toStringTag since it is immune to minification.\n          const className = constructor.prototype[Symbol.toStringTag];\n          const valueClassName = // We still need to support constructor's name to detect conflicts with older versions of this library.\n            Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009\n              ? value[Symbol.toStringTag]\n              : (_value$constructor = value.constructor) === null ||\n                _value$constructor === void 0\n              ? void 0\n              : _value$constructor.name;\n\n          if (className === valueClassName) {\n            const stringifiedValue = inspect(value);\n            throw new Error(`Cannot use ${className} \"${stringifiedValue}\" from another module or realm.\n\nEnsure that there is only one instance of \"graphql\" in the node_modules\ndirectory. If different versions of \"graphql\" are the dependencies of other\nrelied on modules, use \"resolutions\" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate \"graphql\" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`);\n          }\n        }\n\n        return false;\n      };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC;;AAEA,MAAMC,YAAY,GAChBC,UAAU,CAACC,OAAO;AAAI;AACtBA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACvC;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,UAAU,GACrB;AACA;AACAL,YAAY,GACR,SAASK,UAAUA,CAACC,KAAK,EAAEC,WAAW,EAAE;EACtC,OAAOD,KAAK,YAAYC,WAAW;AACrC,CAAC,GACD,SAASF,UAAUA,CAACC,KAAK,EAAEC,WAAW,EAAE;EACtC,IAAID,KAAK,YAAYC,WAAW,EAAE;IAChC,OAAO,IAAI;EACb;EAEA,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;IAC/C,IAAIE,kBAAkB;;IAEtB;IACA,MAAMC,SAAS,GAAGF,WAAW,CAACG,SAAS,CAACC,MAAM,CAACC,WAAW,CAAC;IAC3D,MAAMC,cAAc;IAAG;IACrBF,MAAM,CAACC,WAAW,IAAIN,KAAK,CAAC;IAAA,EACxBA,KAAK,CAACK,MAAM,CAACC,WAAW,CAAC,GACzB,CAACJ,kBAAkB,GAAGF,KAAK,CAACC,WAAW,MAAM,IAAI,IACjDC,kBAAkB,KAAK,KAAK,CAAC,GAC7B,KAAK,CAAC,GACNA,kBAAkB,CAACM,IAAI;IAE7B,IAAIL,SAAS,KAAKI,cAAc,EAAE;MAChC,MAAME,gBAAgB,GAAGhB,OAAO,CAACO,KAAK,CAAC;MACvC,MAAM,IAAIU,KAAK,CAAC,cAAcP,SAAS,KAAKM,gBAAgB;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;IACT;EACF;EAEA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}