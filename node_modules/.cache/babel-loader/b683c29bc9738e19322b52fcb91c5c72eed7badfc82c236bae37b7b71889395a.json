{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { invariant } from '../../jsutils/invariant.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { OperationTypeNode } from '../../language/ast.mjs';\nimport { DirectiveLocation } from '../../language/directiveLocation.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Known directives\n *\n * A GraphQL document is only valid if all `@directives` are known by the\n * schema and legally positioned.\n *\n * See https://spec.graphql.org/draft/#sec-Directives-Are-Defined\n */\nexport function KnownDirectivesRule(context) {\n  const locationsMap = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = schema ? schema.getDirectives() : specifiedDirectives;\n  for (const directive of definedDirectives) {\n    locationsMap[directive.name] = directive.locations;\n  }\n  const astDefinitions = context.getDocument().definitions;\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      locationsMap[def.name.value] = def.locations.map(name => name.value);\n    }\n  }\n  return {\n    Directive(node, _key, _parent, _path, ancestors) {\n      const name = node.name.value;\n      const locations = locationsMap[name];\n      if (!locations) {\n        context.reportError(new GraphQLError(`Unknown directive \"@${name}\".`, {\n          nodes: node\n        }));\n        return;\n      }\n      const candidateLocation = getDirectiveLocationForASTPath(ancestors);\n      if (candidateLocation && !locations.includes(candidateLocation)) {\n        context.reportError(new GraphQLError(`Directive \"@${name}\" may not be used on ${candidateLocation}.`, {\n          nodes: node\n        }));\n      }\n    }\n  };\n}\nfunction getDirectiveLocationForASTPath(ancestors) {\n  const appliedTo = ancestors[ancestors.length - 1];\n  'kind' in appliedTo || invariant(false);\n  switch (appliedTo.kind) {\n    case Kind.OPERATION_DEFINITION:\n      return getDirectiveLocationForOperation(appliedTo.operation);\n    case Kind.FIELD:\n      return DirectiveLocation.FIELD;\n    case Kind.FRAGMENT_SPREAD:\n      return DirectiveLocation.FRAGMENT_SPREAD;\n    case Kind.INLINE_FRAGMENT:\n      return DirectiveLocation.INLINE_FRAGMENT;\n    case Kind.FRAGMENT_DEFINITION:\n      return DirectiveLocation.FRAGMENT_DEFINITION;\n    case Kind.VARIABLE_DEFINITION:\n      return DirectiveLocation.VARIABLE_DEFINITION;\n    case Kind.SCHEMA_DEFINITION:\n    case Kind.SCHEMA_EXTENSION:\n      return DirectiveLocation.SCHEMA;\n    case Kind.SCALAR_TYPE_DEFINITION:\n    case Kind.SCALAR_TYPE_EXTENSION:\n      return DirectiveLocation.SCALAR;\n    case Kind.OBJECT_TYPE_DEFINITION:\n    case Kind.OBJECT_TYPE_EXTENSION:\n      return DirectiveLocation.OBJECT;\n    case Kind.FIELD_DEFINITION:\n      return DirectiveLocation.FIELD_DEFINITION;\n    case Kind.INTERFACE_TYPE_DEFINITION:\n    case Kind.INTERFACE_TYPE_EXTENSION:\n      return DirectiveLocation.INTERFACE;\n    case Kind.UNION_TYPE_DEFINITION:\n    case Kind.UNION_TYPE_EXTENSION:\n      return DirectiveLocation.UNION;\n    case Kind.ENUM_TYPE_DEFINITION:\n    case Kind.ENUM_TYPE_EXTENSION:\n      return DirectiveLocation.ENUM;\n    case Kind.ENUM_VALUE_DEFINITION:\n      return DirectiveLocation.ENUM_VALUE;\n    case Kind.INPUT_OBJECT_TYPE_DEFINITION:\n    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n      return DirectiveLocation.INPUT_OBJECT;\n    case Kind.INPUT_VALUE_DEFINITION:\n      {\n        const parentNode = ancestors[ancestors.length - 3];\n        'kind' in parentNode || invariant(false);\n        return parentNode.kind === Kind.INPUT_OBJECT_TYPE_DEFINITION ? DirectiveLocation.INPUT_FIELD_DEFINITION : DirectiveLocation.ARGUMENT_DEFINITION;\n      }\n    // Not reachable, all possible types have been considered.\n\n    /* c8 ignore next */\n\n    default:\n      false || invariant(false, 'Unexpected kind: ' + inspect(appliedTo.kind));\n  }\n}\nfunction getDirectiveLocationForOperation(operation) {\n  switch (operation) {\n    case OperationTypeNode.QUERY:\n      return DirectiveLocation.QUERY;\n    case OperationTypeNode.MUTATION:\n      return DirectiveLocation.MUTATION;\n    case OperationTypeNode.SUBSCRIPTION:\n      return DirectiveLocation.SUBSCRIPTION;\n  }\n}", "map": {"version": 3, "names": ["inspect", "invariant", "GraphQLError", "OperationTypeNode", "DirectiveLocation", "Kind", "specifiedDirectives", "KnownDirectivesRule", "context", "locationsMap", "Object", "create", "schema", "getSchema", "definedDirectives", "getDirectives", "directive", "name", "locations", "astDefinitions", "getDocument", "definitions", "def", "kind", "DIRECTIVE_DEFINITION", "value", "map", "Directive", "node", "_key", "_parent", "_path", "ancestors", "reportError", "nodes", "candidateLocation", "getDirectiveLocationForASTPath", "includes", "appliedTo", "length", "OPERATION_DEFINITION", "getDirectiveLocationForOperation", "operation", "FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT", "FRAGMENT_DEFINITION", "VARIABLE_DEFINITION", "SCHEMA_DEFINITION", "SCHEMA_EXTENSION", "SCHEMA", "SCALAR_TYPE_DEFINITION", "SCALAR_TYPE_EXTENSION", "SCALAR", "OBJECT_TYPE_DEFINITION", "OBJECT_TYPE_EXTENSION", "OBJECT", "FIELD_DEFINITION", "INTERFACE_TYPE_DEFINITION", "INTERFACE_TYPE_EXTENSION", "INTERFACE", "UNION_TYPE_DEFINITION", "UNION_TYPE_EXTENSION", "UNION", "ENUM_TYPE_DEFINITION", "ENUM_TYPE_EXTENSION", "ENUM", "ENUM_VALUE_DEFINITION", "ENUM_VALUE", "INPUT_OBJECT_TYPE_DEFINITION", "INPUT_OBJECT_TYPE_EXTENSION", "INPUT_OBJECT", "INPUT_VALUE_DEFINITION", "parentNode", "INPUT_FIELD_DEFINITION", "ARGUMENT_DEFINITION", "QUERY", "MUTATION", "SUBSCRIPTION"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/KnownDirectivesRule.mjs"], "sourcesContent": ["import { inspect } from '../../jsutils/inspect.mjs';\nimport { invariant } from '../../jsutils/invariant.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { OperationTypeNode } from '../../language/ast.mjs';\nimport { DirectiveLocation } from '../../language/directiveLocation.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Known directives\n *\n * A GraphQL document is only valid if all `@directives` are known by the\n * schema and legally positioned.\n *\n * See https://spec.graphql.org/draft/#sec-Directives-Are-Defined\n */\nexport function KnownDirectivesRule(context) {\n  const locationsMap = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = schema\n    ? schema.getDirectives()\n    : specifiedDirectives;\n\n  for (const directive of definedDirectives) {\n    locationsMap[directive.name] = directive.locations;\n  }\n\n  const astDefinitions = context.getDocument().definitions;\n\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      locationsMap[def.name.value] = def.locations.map((name) => name.value);\n    }\n  }\n\n  return {\n    Directive(node, _key, _parent, _path, ancestors) {\n      const name = node.name.value;\n      const locations = locationsMap[name];\n\n      if (!locations) {\n        context.reportError(\n          new GraphQLError(`Unknown directive \"@${name}\".`, {\n            nodes: node,\n          }),\n        );\n        return;\n      }\n\n      const candidateLocation = getDirectiveLocationForASTPath(ancestors);\n\n      if (candidateLocation && !locations.includes(candidateLocation)) {\n        context.reportError(\n          new GraphQLError(\n            `Directive \"@${name}\" may not be used on ${candidateLocation}.`,\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n  };\n}\n\nfunction getDirectiveLocationForASTPath(ancestors) {\n  const appliedTo = ancestors[ancestors.length - 1];\n  'kind' in appliedTo || invariant(false);\n\n  switch (appliedTo.kind) {\n    case Kind.OPERATION_DEFINITION:\n      return getDirectiveLocationForOperation(appliedTo.operation);\n\n    case Kind.FIELD:\n      return DirectiveLocation.FIELD;\n\n    case Kind.FRAGMENT_SPREAD:\n      return DirectiveLocation.FRAGMENT_SPREAD;\n\n    case Kind.INLINE_FRAGMENT:\n      return DirectiveLocation.INLINE_FRAGMENT;\n\n    case Kind.FRAGMENT_DEFINITION:\n      return DirectiveLocation.FRAGMENT_DEFINITION;\n\n    case Kind.VARIABLE_DEFINITION:\n      return DirectiveLocation.VARIABLE_DEFINITION;\n\n    case Kind.SCHEMA_DEFINITION:\n    case Kind.SCHEMA_EXTENSION:\n      return DirectiveLocation.SCHEMA;\n\n    case Kind.SCALAR_TYPE_DEFINITION:\n    case Kind.SCALAR_TYPE_EXTENSION:\n      return DirectiveLocation.SCALAR;\n\n    case Kind.OBJECT_TYPE_DEFINITION:\n    case Kind.OBJECT_TYPE_EXTENSION:\n      return DirectiveLocation.OBJECT;\n\n    case Kind.FIELD_DEFINITION:\n      return DirectiveLocation.FIELD_DEFINITION;\n\n    case Kind.INTERFACE_TYPE_DEFINITION:\n    case Kind.INTERFACE_TYPE_EXTENSION:\n      return DirectiveLocation.INTERFACE;\n\n    case Kind.UNION_TYPE_DEFINITION:\n    case Kind.UNION_TYPE_EXTENSION:\n      return DirectiveLocation.UNION;\n\n    case Kind.ENUM_TYPE_DEFINITION:\n    case Kind.ENUM_TYPE_EXTENSION:\n      return DirectiveLocation.ENUM;\n\n    case Kind.ENUM_VALUE_DEFINITION:\n      return DirectiveLocation.ENUM_VALUE;\n\n    case Kind.INPUT_OBJECT_TYPE_DEFINITION:\n    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n      return DirectiveLocation.INPUT_OBJECT;\n\n    case Kind.INPUT_VALUE_DEFINITION: {\n      const parentNode = ancestors[ancestors.length - 3];\n      'kind' in parentNode || invariant(false);\n      return parentNode.kind === Kind.INPUT_OBJECT_TYPE_DEFINITION\n        ? DirectiveLocation.INPUT_FIELD_DEFINITION\n        : DirectiveLocation.ARGUMENT_DEFINITION;\n    }\n    // Not reachable, all possible types have been considered.\n\n    /* c8 ignore next */\n\n    default:\n      false || invariant(false, 'Unexpected kind: ' + inspect(appliedTo.kind));\n  }\n}\n\nfunction getDirectiveLocationForOperation(operation) {\n  switch (operation) {\n    case OperationTypeNode.QUERY:\n      return DirectiveLocation.QUERY;\n\n    case OperationTypeNode.MUTATION:\n      return DirectiveLocation.MUTATION;\n\n    case OperationTypeNode.SUBSCRIPTION:\n      return DirectiveLocation.SUBSCRIPTION;\n  }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,mBAAmB,QAAQ,2BAA2B;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAC3C,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMC,MAAM,GAAGJ,OAAO,CAACK,SAAS,CAAC,CAAC;EAClC,MAAMC,iBAAiB,GAAGF,MAAM,GAC5BA,MAAM,CAACG,aAAa,CAAC,CAAC,GACtBT,mBAAmB;EAEvB,KAAK,MAAMU,SAAS,IAAIF,iBAAiB,EAAE;IACzCL,YAAY,CAACO,SAAS,CAACC,IAAI,CAAC,GAAGD,SAAS,CAACE,SAAS;EACpD;EAEA,MAAMC,cAAc,GAAGX,OAAO,CAACY,WAAW,CAAC,CAAC,CAACC,WAAW;EAExD,KAAK,MAAMC,GAAG,IAAIH,cAAc,EAAE;IAChC,IAAIG,GAAG,CAACC,IAAI,KAAKlB,IAAI,CAACmB,oBAAoB,EAAE;MAC1Cf,YAAY,CAACa,GAAG,CAACL,IAAI,CAACQ,KAAK,CAAC,GAAGH,GAAG,CAACJ,SAAS,CAACQ,GAAG,CAAET,IAAI,IAAKA,IAAI,CAACQ,KAAK,CAAC;IACxE;EACF;EAEA,OAAO;IACLE,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAE;MAC/C,MAAMf,IAAI,GAAGW,IAAI,CAACX,IAAI,CAACQ,KAAK;MAC5B,MAAMP,SAAS,GAAGT,YAAY,CAACQ,IAAI,CAAC;MAEpC,IAAI,CAACC,SAAS,EAAE;QACdV,OAAO,CAACyB,WAAW,CACjB,IAAI/B,YAAY,CAAC,uBAAuBe,IAAI,IAAI,EAAE;UAChDiB,KAAK,EAAEN;QACT,CAAC,CACH,CAAC;QACD;MACF;MAEA,MAAMO,iBAAiB,GAAGC,8BAA8B,CAACJ,SAAS,CAAC;MAEnE,IAAIG,iBAAiB,IAAI,CAACjB,SAAS,CAACmB,QAAQ,CAACF,iBAAiB,CAAC,EAAE;QAC/D3B,OAAO,CAACyB,WAAW,CACjB,IAAI/B,YAAY,CACd,eAAee,IAAI,wBAAwBkB,iBAAiB,GAAG,EAC/D;UACED,KAAK,EAAEN;QACT,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH;AAEA,SAASQ,8BAA8BA,CAACJ,SAAS,EAAE;EACjD,MAAMM,SAAS,GAAGN,SAAS,CAACA,SAAS,CAACO,MAAM,GAAG,CAAC,CAAC;EACjD,MAAM,IAAID,SAAS,IAAIrC,SAAS,CAAC,KAAK,CAAC;EAEvC,QAAQqC,SAAS,CAACf,IAAI;IACpB,KAAKlB,IAAI,CAACmC,oBAAoB;MAC5B,OAAOC,gCAAgC,CAACH,SAAS,CAACI,SAAS,CAAC;IAE9D,KAAKrC,IAAI,CAACsC,KAAK;MACb,OAAOvC,iBAAiB,CAACuC,KAAK;IAEhC,KAAKtC,IAAI,CAACuC,eAAe;MACvB,OAAOxC,iBAAiB,CAACwC,eAAe;IAE1C,KAAKvC,IAAI,CAACwC,eAAe;MACvB,OAAOzC,iBAAiB,CAACyC,eAAe;IAE1C,KAAKxC,IAAI,CAACyC,mBAAmB;MAC3B,OAAO1C,iBAAiB,CAAC0C,mBAAmB;IAE9C,KAAKzC,IAAI,CAAC0C,mBAAmB;MAC3B,OAAO3C,iBAAiB,CAAC2C,mBAAmB;IAE9C,KAAK1C,IAAI,CAAC2C,iBAAiB;IAC3B,KAAK3C,IAAI,CAAC4C,gBAAgB;MACxB,OAAO7C,iBAAiB,CAAC8C,MAAM;IAEjC,KAAK7C,IAAI,CAAC8C,sBAAsB;IAChC,KAAK9C,IAAI,CAAC+C,qBAAqB;MAC7B,OAAOhD,iBAAiB,CAACiD,MAAM;IAEjC,KAAKhD,IAAI,CAACiD,sBAAsB;IAChC,KAAKjD,IAAI,CAACkD,qBAAqB;MAC7B,OAAOnD,iBAAiB,CAACoD,MAAM;IAEjC,KAAKnD,IAAI,CAACoD,gBAAgB;MACxB,OAAOrD,iBAAiB,CAACqD,gBAAgB;IAE3C,KAAKpD,IAAI,CAACqD,yBAAyB;IACnC,KAAKrD,IAAI,CAACsD,wBAAwB;MAChC,OAAOvD,iBAAiB,CAACwD,SAAS;IAEpC,KAAKvD,IAAI,CAACwD,qBAAqB;IAC/B,KAAKxD,IAAI,CAACyD,oBAAoB;MAC5B,OAAO1D,iBAAiB,CAAC2D,KAAK;IAEhC,KAAK1D,IAAI,CAAC2D,oBAAoB;IAC9B,KAAK3D,IAAI,CAAC4D,mBAAmB;MAC3B,OAAO7D,iBAAiB,CAAC8D,IAAI;IAE/B,KAAK7D,IAAI,CAAC8D,qBAAqB;MAC7B,OAAO/D,iBAAiB,CAACgE,UAAU;IAErC,KAAK/D,IAAI,CAACgE,4BAA4B;IACtC,KAAKhE,IAAI,CAACiE,2BAA2B;MACnC,OAAOlE,iBAAiB,CAACmE,YAAY;IAEvC,KAAKlE,IAAI,CAACmE,sBAAsB;MAAE;QAChC,MAAMC,UAAU,GAAGzC,SAAS,CAACA,SAAS,CAACO,MAAM,GAAG,CAAC,CAAC;QAClD,MAAM,IAAIkC,UAAU,IAAIxE,SAAS,CAAC,KAAK,CAAC;QACxC,OAAOwE,UAAU,CAAClD,IAAI,KAAKlB,IAAI,CAACgE,4BAA4B,GACxDjE,iBAAiB,CAACsE,sBAAsB,GACxCtE,iBAAiB,CAACuE,mBAAmB;MAC3C;IACA;;IAEA;;IAEA;MACE,KAAK,IAAI1E,SAAS,CAAC,KAAK,EAAE,mBAAmB,GAAGD,OAAO,CAACsC,SAAS,CAACf,IAAI,CAAC,CAAC;EAC5E;AACF;AAEA,SAASkB,gCAAgCA,CAACC,SAAS,EAAE;EACnD,QAAQA,SAAS;IACf,KAAKvC,iBAAiB,CAACyE,KAAK;MAC1B,OAAOxE,iBAAiB,CAACwE,KAAK;IAEhC,KAAKzE,iBAAiB,CAAC0E,QAAQ;MAC7B,OAAOzE,iBAAiB,CAACyE,QAAQ;IAEnC,KAAK1E,iBAAiB,CAAC2E,YAAY;MACjC,OAAO1E,iBAAiB,CAAC0E,YAAY;EACzC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}