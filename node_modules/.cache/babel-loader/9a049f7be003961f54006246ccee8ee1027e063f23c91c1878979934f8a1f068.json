{"ast": null, "code": "function toResponseInit(response) {\n  return {\n    status: response.status,\n    statusText: response.statusText,\n    headers: Object.fromEntries(response.headers.entries())\n  };\n}\nexport { toResponseInit };", "map": {"version": 3, "names": ["toResponseInit", "response", "status", "statusText", "headers", "Object", "fromEntries", "entries"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/toResponseInit.ts"], "sourcesContent": ["export function toResponseInit(response: Response): ResponseInit {\n  return {\n    status: response.status,\n    statusText: response.statusText,\n    headers: Object.fromEntries(response.headers.entries()),\n  }\n}\n"], "mappings": "AAAO,SAASA,eAAeC,QAAA,EAAkC;EAC/D,OAAO;IACLC,MAAA,EAAQD,QAAA,CAASC,MAAA;IACjBC,UAAA,EAAYF,QAAA,CAASE,UAAA;IACrBC,OAAA,EAASC,MAAA,CAAOC,WAAA,CAAYL,QAAA,CAASG,OAAA,CAAQG,OAAA,CAAQ,CAAC;EACxD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}