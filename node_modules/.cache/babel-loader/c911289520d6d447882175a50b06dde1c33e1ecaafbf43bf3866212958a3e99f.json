{"ast": null, "code": "import { isAbstractType, isInterfaceType, isListType, isNonNullType, isObjectType } from '../type/definition.mjs';\n\n/**\n * Provided two types, return true if the types are equal (invariant).\n */\nexport function isEqualType(typeA, typeB) {\n  // Equivalent types are equal.\n  if (typeA === typeB) {\n    return true;\n  } // If either type is non-null, the other must also be non-null.\n\n  if (isNonNullType(typeA) && isNonNullType(typeB)) {\n    return isEqualType(typeA.ofType, typeB.ofType);\n  } // If either type is a list, the other must also be a list.\n\n  if (isListType(typeA) && isListType(typeB)) {\n    return isEqualType(typeA.ofType, typeB.ofType);\n  } // Otherwise the types are not equal.\n\n  return false;\n}\n/**\n * Provided a type and a super type, return true if the first type is either\n * equal or a subset of the second super type (covariant).\n */\n\nexport function isTypeSubTypeOf(schema, maybeSubType, superType) {\n  // Equivalent type is a valid subtype\n  if (maybeSubType === superType) {\n    return true;\n  } // If superType is non-null, maybeSubType must also be non-null.\n\n  if (isNonNullType(superType)) {\n    if (isNonNullType(maybeSubType)) {\n      return isTypeSubTypeOf(schema, maybeSubType.ofType, superType.ofType);\n    }\n    return false;\n  }\n  if (isNonNullType(maybeSubType)) {\n    // If superType is nullable, maybeSubType may be non-null or nullable.\n    return isTypeSubTypeOf(schema, maybeSubType.ofType, superType);\n  } // If superType type is a list, maybeSubType type must also be a list.\n\n  if (isListType(superType)) {\n    if (isListType(maybeSubType)) {\n      return isTypeSubTypeOf(schema, maybeSubType.ofType, superType.ofType);\n    }\n    return false;\n  }\n  if (isListType(maybeSubType)) {\n    // If superType is not a list, maybeSubType must also be not a list.\n    return false;\n  } // If superType type is an abstract type, check if it is super type of maybeSubType.\n  // Otherwise, the child type is not a valid subtype of the parent type.\n\n  return isAbstractType(superType) && (isInterfaceType(maybeSubType) || isObjectType(maybeSubType)) && schema.isSubType(superType, maybeSubType);\n}\n/**\n * Provided two composite types, determine if they \"overlap\". Two composite\n * types overlap when the Sets of possible concrete types for each intersect.\n *\n * This is often used to determine if a fragment of a given type could possibly\n * be visited in a context of another type.\n *\n * This function is commutative.\n */\n\nexport function doTypesOverlap(schema, typeA, typeB) {\n  // Equivalent types overlap\n  if (typeA === typeB) {\n    return true;\n  }\n  if (isAbstractType(typeA)) {\n    if (isAbstractType(typeB)) {\n      // If both types are abstract, then determine if there is any intersection\n      // between possible concrete types of each.\n      return schema.getPossibleTypes(typeA).some(type => schema.isSubType(typeB, type));\n    } // Determine if the latter type is a possible concrete type of the former.\n\n    return schema.isSubType(typeA, typeB);\n  }\n  if (isAbstractType(typeB)) {\n    // Determine if the former type is a possible concrete type of the latter.\n    return schema.isSubType(typeB, typeA);\n  } // Otherwise the types do not overlap.\n\n  return false;\n}", "map": {"version": 3, "names": ["isAbstractType", "isInterfaceType", "isListType", "isNonNullType", "isObjectType", "isEqualType", "typeA", "typeB", "ofType", "isTypeSubTypeOf", "schema", "maybeSubType", "superType", "isSubType", "doTypesOverlap", "getPossibleTypes", "some", "type"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/utilities/typeComparators.mjs"], "sourcesContent": ["import {\n  isAbstractType,\n  isInterfaceType,\n  isListType,\n  isNonNullType,\n  isObjectType,\n} from '../type/definition.mjs';\n\n/**\n * Provided two types, return true if the types are equal (invariant).\n */\nexport function isEqualType(typeA, typeB) {\n  // Equivalent types are equal.\n  if (typeA === typeB) {\n    return true;\n  } // If either type is non-null, the other must also be non-null.\n\n  if (isNonNullType(typeA) && isNonNullType(typeB)) {\n    return isEqualType(typeA.ofType, typeB.ofType);\n  } // If either type is a list, the other must also be a list.\n\n  if (isListType(typeA) && isListType(typeB)) {\n    return isEqualType(typeA.ofType, typeB.ofType);\n  } // Otherwise the types are not equal.\n\n  return false;\n}\n/**\n * Provided a type and a super type, return true if the first type is either\n * equal or a subset of the second super type (covariant).\n */\n\nexport function isTypeSubTypeOf(schema, maybeSubType, superType) {\n  // Equivalent type is a valid subtype\n  if (maybeSubType === superType) {\n    return true;\n  } // If superType is non-null, maybeSubType must also be non-null.\n\n  if (isNonNullType(superType)) {\n    if (isNonNullType(maybeSubType)) {\n      return isTypeSubTypeOf(schema, maybeSubType.ofType, superType.ofType);\n    }\n\n    return false;\n  }\n\n  if (isNonNullType(maybeSubType)) {\n    // If superType is nullable, maybeSubType may be non-null or nullable.\n    return isTypeSubTypeOf(schema, maybeSubType.ofType, superType);\n  } // If superType type is a list, maybeSubType type must also be a list.\n\n  if (isListType(superType)) {\n    if (isListType(maybeSubType)) {\n      return isTypeSubTypeOf(schema, maybeSubType.ofType, superType.ofType);\n    }\n\n    return false;\n  }\n\n  if (isListType(maybeSubType)) {\n    // If superType is not a list, maybeSubType must also be not a list.\n    return false;\n  } // If superType type is an abstract type, check if it is super type of maybeSubType.\n  // Otherwise, the child type is not a valid subtype of the parent type.\n\n  return (\n    isAbstractType(superType) &&\n    (isInterfaceType(maybeSubType) || isObjectType(maybeSubType)) &&\n    schema.isSubType(superType, maybeSubType)\n  );\n}\n/**\n * Provided two composite types, determine if they \"overlap\". Two composite\n * types overlap when the Sets of possible concrete types for each intersect.\n *\n * This is often used to determine if a fragment of a given type could possibly\n * be visited in a context of another type.\n *\n * This function is commutative.\n */\n\nexport function doTypesOverlap(schema, typeA, typeB) {\n  // Equivalent types overlap\n  if (typeA === typeB) {\n    return true;\n  }\n\n  if (isAbstractType(typeA)) {\n    if (isAbstractType(typeB)) {\n      // If both types are abstract, then determine if there is any intersection\n      // between possible concrete types of each.\n      return schema\n        .getPossibleTypes(typeA)\n        .some((type) => schema.isSubType(typeB, type));\n    } // Determine if the latter type is a possible concrete type of the former.\n\n    return schema.isSubType(typeA, typeB);\n  }\n\n  if (isAbstractType(typeB)) {\n    // Determine if the former type is a possible concrete type of the latter.\n    return schema.isSubType(typeB, typeA);\n  } // Otherwise the types do not overlap.\n\n  return false;\n}\n"], "mappings": "AAAA,SACEA,cAAc,EACdC,eAAe,EACfC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,wBAAwB;;AAE/B;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACxC;EACA,IAAID,KAAK,KAAKC,KAAK,EAAE;IACnB,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF,IAAIJ,aAAa,CAACG,KAAK,CAAC,IAAIH,aAAa,CAACI,KAAK,CAAC,EAAE;IAChD,OAAOF,WAAW,CAACC,KAAK,CAACE,MAAM,EAAED,KAAK,CAACC,MAAM,CAAC;EAChD,CAAC,CAAC;;EAEF,IAAIN,UAAU,CAACI,KAAK,CAAC,IAAIJ,UAAU,CAACK,KAAK,CAAC,EAAE;IAC1C,OAAOF,WAAW,CAACC,KAAK,CAACE,MAAM,EAAED,KAAK,CAACC,MAAM,CAAC;EAChD,CAAC,CAAC;;EAEF,OAAO,KAAK;AACd;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAE;EAC/D;EACA,IAAID,YAAY,KAAKC,SAAS,EAAE;IAC9B,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF,IAAIT,aAAa,CAACS,SAAS,CAAC,EAAE;IAC5B,IAAIT,aAAa,CAACQ,YAAY,CAAC,EAAE;MAC/B,OAAOF,eAAe,CAACC,MAAM,EAAEC,YAAY,CAACH,MAAM,EAAEI,SAAS,CAACJ,MAAM,CAAC;IACvE;IAEA,OAAO,KAAK;EACd;EAEA,IAAIL,aAAa,CAACQ,YAAY,CAAC,EAAE;IAC/B;IACA,OAAOF,eAAe,CAACC,MAAM,EAAEC,YAAY,CAACH,MAAM,EAAEI,SAAS,CAAC;EAChE,CAAC,CAAC;;EAEF,IAAIV,UAAU,CAACU,SAAS,CAAC,EAAE;IACzB,IAAIV,UAAU,CAACS,YAAY,CAAC,EAAE;MAC5B,OAAOF,eAAe,CAACC,MAAM,EAAEC,YAAY,CAACH,MAAM,EAAEI,SAAS,CAACJ,MAAM,CAAC;IACvE;IAEA,OAAO,KAAK;EACd;EAEA,IAAIN,UAAU,CAACS,YAAY,CAAC,EAAE;IAC5B;IACA,OAAO,KAAK;EACd,CAAC,CAAC;EACF;;EAEA,OACEX,cAAc,CAACY,SAAS,CAAC,KACxBX,eAAe,CAACU,YAAY,CAAC,IAAIP,YAAY,CAACO,YAAY,CAAC,CAAC,IAC7DD,MAAM,CAACG,SAAS,CAACD,SAAS,EAAED,YAAY,CAAC;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASG,cAAcA,CAACJ,MAAM,EAAEJ,KAAK,EAAEC,KAAK,EAAE;EACnD;EACA,IAAID,KAAK,KAAKC,KAAK,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAIP,cAAc,CAACM,KAAK,CAAC,EAAE;IACzB,IAAIN,cAAc,CAACO,KAAK,CAAC,EAAE;MACzB;MACA;MACA,OAAOG,MAAM,CACVK,gBAAgB,CAACT,KAAK,CAAC,CACvBU,IAAI,CAAEC,IAAI,IAAKP,MAAM,CAACG,SAAS,CAACN,KAAK,EAAEU,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;;IAEF,OAAOP,MAAM,CAACG,SAAS,CAACP,KAAK,EAAEC,KAAK,CAAC;EACvC;EAEA,IAAIP,cAAc,CAACO,KAAK,CAAC,EAAE;IACzB;IACA,OAAOG,MAAM,CAACG,SAAS,CAACN,KAAK,EAAED,KAAK,CAAC;EACvC,CAAC,CAAC;;EAEF,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}