{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { isNameContinue, isNameStart } from '../language/characterClasses.mjs';\n/**\n * Upholds the spec rules about naming.\n */\n\nexport function assertName(name) {\n  name != null || devAssert(false, 'Must provide name.');\n  typeof name === 'string' || devAssert(false, 'Expected name to be a string.');\n  if (name.length === 0) {\n    throw new GraphQLError('Expected name to be a non-empty string.');\n  }\n  for (let i = 1; i < name.length; ++i) {\n    if (!isNameContinue(name.charCodeAt(i))) {\n      throw new GraphQLError(`Names must only contain [_a-zA-Z0-9] but \"${name}\" does not.`);\n    }\n  }\n  if (!isNameStart(name.charCodeAt(0))) {\n    throw new GraphQLError(`Names must start with [_a-zA-Z] but \"${name}\" does not.`);\n  }\n  return name;\n}\n/**\n * Upholds the spec rules about naming enum values.\n *\n * @internal\n */\n\nexport function assertEnumValueName(name) {\n  if (name === 'true' || name === 'false' || name === 'null') {\n    throw new GraphQLError(`Enum values cannot be named: ${name}`);\n  }\n  return assertName(name);\n}", "map": {"version": 3, "names": ["devAssert", "GraphQLError", "isNameContinue", "isNameStart", "assertName", "name", "length", "i", "charCodeAt", "assertEnumValueName"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/type/assertName.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { isNameContinue, isNameStart } from '../language/characterClasses.mjs';\n/**\n * Upholds the spec rules about naming.\n */\n\nexport function assertName(name) {\n  name != null || devAssert(false, 'Must provide name.');\n  typeof name === 'string' || devAssert(false, 'Expected name to be a string.');\n\n  if (name.length === 0) {\n    throw new GraphQLError('Expected name to be a non-empty string.');\n  }\n\n  for (let i = 1; i < name.length; ++i) {\n    if (!isNameContinue(name.charCodeAt(i))) {\n      throw new GraphQLError(\n        `Names must only contain [_a-zA-Z0-9] but \"${name}\" does not.`,\n      );\n    }\n  }\n\n  if (!isNameStart(name.charCodeAt(0))) {\n    throw new GraphQLError(\n      `Names must start with [_a-zA-Z] but \"${name}\" does not.`,\n    );\n  }\n\n  return name;\n}\n/**\n * Upholds the spec rules about naming enum values.\n *\n * @internal\n */\n\nexport function assertEnumValueName(name) {\n  if (name === 'true' || name === 'false' || name === 'null') {\n    throw new GraphQLError(`Enum values cannot be named: ${name}`);\n  }\n\n  return assertName(name);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,cAAc,EAAEC,WAAW,QAAQ,kCAAkC;AAC9E;AACA;AACA;;AAEA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/BA,IAAI,IAAI,IAAI,IAAIL,SAAS,CAAC,KAAK,EAAE,oBAAoB,CAAC;EACtD,OAAOK,IAAI,KAAK,QAAQ,IAAIL,SAAS,CAAC,KAAK,EAAE,+BAA+B,CAAC;EAE7E,IAAIK,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IACrB,MAAM,IAAIL,YAAY,CAAC,yCAAyC,CAAC;EACnE;EAEA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACC,MAAM,EAAE,EAAEC,CAAC,EAAE;IACpC,IAAI,CAACL,cAAc,CAACG,IAAI,CAACG,UAAU,CAACD,CAAC,CAAC,CAAC,EAAE;MACvC,MAAM,IAAIN,YAAY,CACpB,6CAA6CI,IAAI,aACnD,CAAC;IACH;EACF;EAEA,IAAI,CAACF,WAAW,CAACE,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IACpC,MAAM,IAAIP,YAAY,CACpB,wCAAwCI,IAAI,aAC9C,CAAC;EACH;EAEA,OAAOA,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASI,mBAAmBA,CAACJ,IAAI,EAAE;EACxC,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,MAAM,EAAE;IAC1D,MAAM,IAAIJ,YAAY,CAAC,gCAAgCI,IAAI,EAAE,CAAC;EAChE;EAEA,OAAOD,UAAU,CAACC,IAAI,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}