{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\nimport { visit } from '../language/visitor.mjs';\nimport { TypeInfo, visitWithTypeInfo } from '../utilities/TypeInfo.mjs';\n\n/**\n * An instance of this class is passed as the \"this\" context to all validators,\n * allowing access to commonly useful contextual information from within a\n * validation rule.\n */\nexport class ASTValidationContext {\n  constructor(ast, onError) {\n    this._ast = ast;\n    this._fragments = undefined;\n    this._fragmentSpreads = new Map();\n    this._recursivelyReferencedFragments = new Map();\n    this._onError = onError;\n  }\n  get [Symbol.toStringTag]() {\n    return 'ASTValidationContext';\n  }\n  reportError(error) {\n    this._onError(error);\n  }\n  getDocument() {\n    return this._ast;\n  }\n  getFragment(name) {\n    let fragments;\n    if (this._fragments) {\n      fragments = this._fragments;\n    } else {\n      fragments = Object.create(null);\n      for (const defNode of this.getDocument().definitions) {\n        if (defNode.kind === Kind.FRAGMENT_DEFINITION) {\n          fragments[defNode.name.value] = defNode;\n        }\n      }\n      this._fragments = fragments;\n    }\n    return fragments[name];\n  }\n  getFragmentSpreads(node) {\n    let spreads = this._fragmentSpreads.get(node);\n    if (!spreads) {\n      spreads = [];\n      const setsToVisit = [node];\n      let set;\n      while (set = setsToVisit.pop()) {\n        for (const selection of set.selections) {\n          if (selection.kind === Kind.FRAGMENT_SPREAD) {\n            spreads.push(selection);\n          } else if (selection.selectionSet) {\n            setsToVisit.push(selection.selectionSet);\n          }\n        }\n      }\n      this._fragmentSpreads.set(node, spreads);\n    }\n    return spreads;\n  }\n  getRecursivelyReferencedFragments(operation) {\n    let fragments = this._recursivelyReferencedFragments.get(operation);\n    if (!fragments) {\n      fragments = [];\n      const collectedNames = Object.create(null);\n      const nodesToVisit = [operation.selectionSet];\n      let node;\n      while (node = nodesToVisit.pop()) {\n        for (const spread of this.getFragmentSpreads(node)) {\n          const fragName = spread.name.value;\n          if (collectedNames[fragName] !== true) {\n            collectedNames[fragName] = true;\n            const fragment = this.getFragment(fragName);\n            if (fragment) {\n              fragments.push(fragment);\n              nodesToVisit.push(fragment.selectionSet);\n            }\n          }\n        }\n      }\n      this._recursivelyReferencedFragments.set(operation, fragments);\n    }\n    return fragments;\n  }\n}\nexport class SDLValidationContext extends ASTValidationContext {\n  constructor(ast, schema, onError) {\n    super(ast, onError);\n    this._schema = schema;\n  }\n  get [Symbol.toStringTag]() {\n    return 'SDLValidationContext';\n  }\n  getSchema() {\n    return this._schema;\n  }\n}\nexport class ValidationContext extends ASTValidationContext {\n  constructor(schema, ast, typeInfo, onError) {\n    super(ast, onError);\n    this._schema = schema;\n    this._typeInfo = typeInfo;\n    this._variableUsages = new Map();\n    this._recursiveVariableUsages = new Map();\n  }\n  get [Symbol.toStringTag]() {\n    return 'ValidationContext';\n  }\n  getSchema() {\n    return this._schema;\n  }\n  getVariableUsages(node) {\n    let usages = this._variableUsages.get(node);\n    if (!usages) {\n      const newUsages = [];\n      const typeInfo = new TypeInfo(this._schema);\n      visit(node, visitWithTypeInfo(typeInfo, {\n        VariableDefinition: () => false,\n        Variable(variable) {\n          newUsages.push({\n            node: variable,\n            type: typeInfo.getInputType(),\n            defaultValue: typeInfo.getDefaultValue(),\n            parentType: typeInfo.getParentInputType()\n          });\n        }\n      }));\n      usages = newUsages;\n      this._variableUsages.set(node, usages);\n    }\n    return usages;\n  }\n  getRecursiveVariableUsages(operation) {\n    let usages = this._recursiveVariableUsages.get(operation);\n    if (!usages) {\n      usages = this.getVariableUsages(operation);\n      for (const frag of this.getRecursivelyReferencedFragments(operation)) {\n        usages = usages.concat(this.getVariableUsages(frag));\n      }\n      this._recursiveVariableUsages.set(operation, usages);\n    }\n    return usages;\n  }\n  getType() {\n    return this._typeInfo.getType();\n  }\n  getParentType() {\n    return this._typeInfo.getParentType();\n  }\n  getInputType() {\n    return this._typeInfo.getInputType();\n  }\n  getParentInputType() {\n    return this._typeInfo.getParentInputType();\n  }\n  getFieldDef() {\n    return this._typeInfo.getFieldDef();\n  }\n  getDirective() {\n    return this._typeInfo.getDirective();\n  }\n  getArgument() {\n    return this._typeInfo.getArgument();\n  }\n  getEnumValue() {\n    return this._typeInfo.getEnumValue();\n  }\n}", "map": {"version": 3, "names": ["Kind", "visit", "TypeInfo", "visitWithTypeInfo", "ASTValidationContext", "constructor", "ast", "onError", "_ast", "_fragments", "undefined", "_fragmentSpreads", "Map", "_recursivelyReferencedFragments", "_onError", "Symbol", "toStringTag", "reportError", "error", "getDocument", "getFragment", "name", "fragments", "Object", "create", "defNode", "definitions", "kind", "FRAGMENT_DEFINITION", "value", "getFragmentSpreads", "node", "spreads", "get", "setsToVisit", "set", "pop", "selection", "selections", "FRAGMENT_SPREAD", "push", "selectionSet", "getRecursivelyReferencedFragments", "operation", "collectedNames", "nodesToVisit", "spread", "fragName", "fragment", "SDLValidationContext", "schema", "_schema", "getSchema", "ValidationContext", "typeInfo", "_typeInfo", "_variableUsages", "_recursiveVariableUsages", "getVariableUsages", "usages", "newUsages", "VariableDefinition", "Variable", "variable", "type", "getInputType", "defaultValue", "getDefaultValue", "parentType", "getParentInputType", "getRecursiveVariableUsages", "frag", "concat", "getType", "getParentType", "getFieldDef", "getDirective", "getArgument", "getEnumValue"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/ValidationContext.mjs"], "sourcesContent": ["import { Kind } from '../language/kinds.mjs';\nimport { visit } from '../language/visitor.mjs';\nimport { TypeInfo, visitWithTypeInfo } from '../utilities/TypeInfo.mjs';\n\n/**\n * An instance of this class is passed as the \"this\" context to all validators,\n * allowing access to commonly useful contextual information from within a\n * validation rule.\n */\nexport class ASTValidationContext {\n  constructor(ast, onError) {\n    this._ast = ast;\n    this._fragments = undefined;\n    this._fragmentSpreads = new Map();\n    this._recursivelyReferencedFragments = new Map();\n    this._onError = onError;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'ASTValidationContext';\n  }\n\n  reportError(error) {\n    this._onError(error);\n  }\n\n  getDocument() {\n    return this._ast;\n  }\n\n  getFragment(name) {\n    let fragments;\n\n    if (this._fragments) {\n      fragments = this._fragments;\n    } else {\n      fragments = Object.create(null);\n\n      for (const defNode of this.getDocument().definitions) {\n        if (defNode.kind === Kind.FRAGMENT_DEFINITION) {\n          fragments[defNode.name.value] = defNode;\n        }\n      }\n\n      this._fragments = fragments;\n    }\n\n    return fragments[name];\n  }\n\n  getFragmentSpreads(node) {\n    let spreads = this._fragmentSpreads.get(node);\n\n    if (!spreads) {\n      spreads = [];\n      const setsToVisit = [node];\n      let set;\n\n      while ((set = setsToVisit.pop())) {\n        for (const selection of set.selections) {\n          if (selection.kind === Kind.FRAGMENT_SPREAD) {\n            spreads.push(selection);\n          } else if (selection.selectionSet) {\n            setsToVisit.push(selection.selectionSet);\n          }\n        }\n      }\n\n      this._fragmentSpreads.set(node, spreads);\n    }\n\n    return spreads;\n  }\n\n  getRecursivelyReferencedFragments(operation) {\n    let fragments = this._recursivelyReferencedFragments.get(operation);\n\n    if (!fragments) {\n      fragments = [];\n      const collectedNames = Object.create(null);\n      const nodesToVisit = [operation.selectionSet];\n      let node;\n\n      while ((node = nodesToVisit.pop())) {\n        for (const spread of this.getFragmentSpreads(node)) {\n          const fragName = spread.name.value;\n\n          if (collectedNames[fragName] !== true) {\n            collectedNames[fragName] = true;\n            const fragment = this.getFragment(fragName);\n\n            if (fragment) {\n              fragments.push(fragment);\n              nodesToVisit.push(fragment.selectionSet);\n            }\n          }\n        }\n      }\n\n      this._recursivelyReferencedFragments.set(operation, fragments);\n    }\n\n    return fragments;\n  }\n}\nexport class SDLValidationContext extends ASTValidationContext {\n  constructor(ast, schema, onError) {\n    super(ast, onError);\n    this._schema = schema;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'SDLValidationContext';\n  }\n\n  getSchema() {\n    return this._schema;\n  }\n}\nexport class ValidationContext extends ASTValidationContext {\n  constructor(schema, ast, typeInfo, onError) {\n    super(ast, onError);\n    this._schema = schema;\n    this._typeInfo = typeInfo;\n    this._variableUsages = new Map();\n    this._recursiveVariableUsages = new Map();\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'ValidationContext';\n  }\n\n  getSchema() {\n    return this._schema;\n  }\n\n  getVariableUsages(node) {\n    let usages = this._variableUsages.get(node);\n\n    if (!usages) {\n      const newUsages = [];\n      const typeInfo = new TypeInfo(this._schema);\n      visit(\n        node,\n        visitWithTypeInfo(typeInfo, {\n          VariableDefinition: () => false,\n\n          Variable(variable) {\n            newUsages.push({\n              node: variable,\n              type: typeInfo.getInputType(),\n              defaultValue: typeInfo.getDefaultValue(),\n              parentType: typeInfo.getParentInputType(),\n            });\n          },\n        }),\n      );\n      usages = newUsages;\n\n      this._variableUsages.set(node, usages);\n    }\n\n    return usages;\n  }\n\n  getRecursiveVariableUsages(operation) {\n    let usages = this._recursiveVariableUsages.get(operation);\n\n    if (!usages) {\n      usages = this.getVariableUsages(operation);\n\n      for (const frag of this.getRecursivelyReferencedFragments(operation)) {\n        usages = usages.concat(this.getVariableUsages(frag));\n      }\n\n      this._recursiveVariableUsages.set(operation, usages);\n    }\n\n    return usages;\n  }\n\n  getType() {\n    return this._typeInfo.getType();\n  }\n\n  getParentType() {\n    return this._typeInfo.getParentType();\n  }\n\n  getInputType() {\n    return this._typeInfo.getInputType();\n  }\n\n  getParentInputType() {\n    return this._typeInfo.getParentInputType();\n  }\n\n  getFieldDef() {\n    return this._typeInfo.getFieldDef();\n  }\n\n  getDirective() {\n    return this._typeInfo.getDirective();\n  }\n\n  getArgument() {\n    return this._typeInfo.getArgument();\n  }\n\n  getEnumValue() {\n    return this._typeInfo.getEnumValue();\n  }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,uBAAuB;AAC5C,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,2BAA2B;;AAEvE;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,CAAC;EAChCC,WAAWA,CAACC,GAAG,EAAEC,OAAO,EAAE;IACxB,IAAI,CAACC,IAAI,GAAGF,GAAG;IACf,IAAI,CAACG,UAAU,GAAGC,SAAS;IAC3B,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,+BAA+B,GAAG,IAAID,GAAG,CAAC,CAAC;IAChD,IAAI,CAACE,QAAQ,GAAGP,OAAO;EACzB;EAEA,KAAKQ,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,sBAAsB;EAC/B;EAEAC,WAAWA,CAACC,KAAK,EAAE;IACjB,IAAI,CAACJ,QAAQ,CAACI,KAAK,CAAC;EACtB;EAEAC,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACX,IAAI;EAClB;EAEAY,WAAWA,CAACC,IAAI,EAAE;IAChB,IAAIC,SAAS;IAEb,IAAI,IAAI,CAACb,UAAU,EAAE;MACnBa,SAAS,GAAG,IAAI,CAACb,UAAU;IAC7B,CAAC,MAAM;MACLa,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAE/B,KAAK,MAAMC,OAAO,IAAI,IAAI,CAACN,WAAW,CAAC,CAAC,CAACO,WAAW,EAAE;QACpD,IAAID,OAAO,CAACE,IAAI,KAAK3B,IAAI,CAAC4B,mBAAmB,EAAE;UAC7CN,SAAS,CAACG,OAAO,CAACJ,IAAI,CAACQ,KAAK,CAAC,GAAGJ,OAAO;QACzC;MACF;MAEA,IAAI,CAAChB,UAAU,GAAGa,SAAS;IAC7B;IAEA,OAAOA,SAAS,CAACD,IAAI,CAAC;EACxB;EAEAS,kBAAkBA,CAACC,IAAI,EAAE;IACvB,IAAIC,OAAO,GAAG,IAAI,CAACrB,gBAAgB,CAACsB,GAAG,CAACF,IAAI,CAAC;IAE7C,IAAI,CAACC,OAAO,EAAE;MACZA,OAAO,GAAG,EAAE;MACZ,MAAME,WAAW,GAAG,CAACH,IAAI,CAAC;MAC1B,IAAII,GAAG;MAEP,OAAQA,GAAG,GAAGD,WAAW,CAACE,GAAG,CAAC,CAAC,EAAG;QAChC,KAAK,MAAMC,SAAS,IAAIF,GAAG,CAACG,UAAU,EAAE;UACtC,IAAID,SAAS,CAACV,IAAI,KAAK3B,IAAI,CAACuC,eAAe,EAAE;YAC3CP,OAAO,CAACQ,IAAI,CAACH,SAAS,CAAC;UACzB,CAAC,MAAM,IAAIA,SAAS,CAACI,YAAY,EAAE;YACjCP,WAAW,CAACM,IAAI,CAACH,SAAS,CAACI,YAAY,CAAC;UAC1C;QACF;MACF;MAEA,IAAI,CAAC9B,gBAAgB,CAACwB,GAAG,CAACJ,IAAI,EAAEC,OAAO,CAAC;IAC1C;IAEA,OAAOA,OAAO;EAChB;EAEAU,iCAAiCA,CAACC,SAAS,EAAE;IAC3C,IAAIrB,SAAS,GAAG,IAAI,CAACT,+BAA+B,CAACoB,GAAG,CAACU,SAAS,CAAC;IAEnE,IAAI,CAACrB,SAAS,EAAE;MACdA,SAAS,GAAG,EAAE;MACd,MAAMsB,cAAc,GAAGrB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC1C,MAAMqB,YAAY,GAAG,CAACF,SAAS,CAACF,YAAY,CAAC;MAC7C,IAAIV,IAAI;MAER,OAAQA,IAAI,GAAGc,YAAY,CAACT,GAAG,CAAC,CAAC,EAAG;QAClC,KAAK,MAAMU,MAAM,IAAI,IAAI,CAAChB,kBAAkB,CAACC,IAAI,CAAC,EAAE;UAClD,MAAMgB,QAAQ,GAAGD,MAAM,CAACzB,IAAI,CAACQ,KAAK;UAElC,IAAIe,cAAc,CAACG,QAAQ,CAAC,KAAK,IAAI,EAAE;YACrCH,cAAc,CAACG,QAAQ,CAAC,GAAG,IAAI;YAC/B,MAAMC,QAAQ,GAAG,IAAI,CAAC5B,WAAW,CAAC2B,QAAQ,CAAC;YAE3C,IAAIC,QAAQ,EAAE;cACZ1B,SAAS,CAACkB,IAAI,CAACQ,QAAQ,CAAC;cACxBH,YAAY,CAACL,IAAI,CAACQ,QAAQ,CAACP,YAAY,CAAC;YAC1C;UACF;QACF;MACF;MAEA,IAAI,CAAC5B,+BAA+B,CAACsB,GAAG,CAACQ,SAAS,EAAErB,SAAS,CAAC;IAChE;IAEA,OAAOA,SAAS;EAClB;AACF;AACA,OAAO,MAAM2B,oBAAoB,SAAS7C,oBAAoB,CAAC;EAC7DC,WAAWA,CAACC,GAAG,EAAE4C,MAAM,EAAE3C,OAAO,EAAE;IAChC,KAAK,CAACD,GAAG,EAAEC,OAAO,CAAC;IACnB,IAAI,CAAC4C,OAAO,GAAGD,MAAM;EACvB;EAEA,KAAKnC,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,sBAAsB;EAC/B;EAEAoC,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,OAAO;EACrB;AACF;AACA,OAAO,MAAME,iBAAiB,SAASjD,oBAAoB,CAAC;EAC1DC,WAAWA,CAAC6C,MAAM,EAAE5C,GAAG,EAAEgD,QAAQ,EAAE/C,OAAO,EAAE;IAC1C,KAAK,CAACD,GAAG,EAAEC,OAAO,CAAC;IACnB,IAAI,CAAC4C,OAAO,GAAGD,MAAM;IACrB,IAAI,CAACK,SAAS,GAAGD,QAAQ;IACzB,IAAI,CAACE,eAAe,GAAG,IAAI5C,GAAG,CAAC,CAAC;IAChC,IAAI,CAAC6C,wBAAwB,GAAG,IAAI7C,GAAG,CAAC,CAAC;EAC3C;EAEA,KAAKG,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,mBAAmB;EAC5B;EAEAoC,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACD,OAAO;EACrB;EAEAO,iBAAiBA,CAAC3B,IAAI,EAAE;IACtB,IAAI4B,MAAM,GAAG,IAAI,CAACH,eAAe,CAACvB,GAAG,CAACF,IAAI,CAAC;IAE3C,IAAI,CAAC4B,MAAM,EAAE;MACX,MAAMC,SAAS,GAAG,EAAE;MACpB,MAAMN,QAAQ,GAAG,IAAIpD,QAAQ,CAAC,IAAI,CAACiD,OAAO,CAAC;MAC3ClD,KAAK,CACH8B,IAAI,EACJ5B,iBAAiB,CAACmD,QAAQ,EAAE;QAC1BO,kBAAkB,EAAEA,CAAA,KAAM,KAAK;QAE/BC,QAAQA,CAACC,QAAQ,EAAE;UACjBH,SAAS,CAACpB,IAAI,CAAC;YACbT,IAAI,EAAEgC,QAAQ;YACdC,IAAI,EAAEV,QAAQ,CAACW,YAAY,CAAC,CAAC;YAC7BC,YAAY,EAAEZ,QAAQ,CAACa,eAAe,CAAC,CAAC;YACxCC,UAAU,EAAEd,QAAQ,CAACe,kBAAkB,CAAC;UAC1C,CAAC,CAAC;QACJ;MACF,CAAC,CACH,CAAC;MACDV,MAAM,GAAGC,SAAS;MAElB,IAAI,CAACJ,eAAe,CAACrB,GAAG,CAACJ,IAAI,EAAE4B,MAAM,CAAC;IACxC;IAEA,OAAOA,MAAM;EACf;EAEAW,0BAA0BA,CAAC3B,SAAS,EAAE;IACpC,IAAIgB,MAAM,GAAG,IAAI,CAACF,wBAAwB,CAACxB,GAAG,CAACU,SAAS,CAAC;IAEzD,IAAI,CAACgB,MAAM,EAAE;MACXA,MAAM,GAAG,IAAI,CAACD,iBAAiB,CAACf,SAAS,CAAC;MAE1C,KAAK,MAAM4B,IAAI,IAAI,IAAI,CAAC7B,iCAAiC,CAACC,SAAS,CAAC,EAAE;QACpEgB,MAAM,GAAGA,MAAM,CAACa,MAAM,CAAC,IAAI,CAACd,iBAAiB,CAACa,IAAI,CAAC,CAAC;MACtD;MAEA,IAAI,CAACd,wBAAwB,CAACtB,GAAG,CAACQ,SAAS,EAAEgB,MAAM,CAAC;IACtD;IAEA,OAAOA,MAAM;EACf;EAEAc,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAAClB,SAAS,CAACkB,OAAO,CAAC,CAAC;EACjC;EAEAC,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACnB,SAAS,CAACmB,aAAa,CAAC,CAAC;EACvC;EAEAT,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACV,SAAS,CAACU,YAAY,CAAC,CAAC;EACtC;EAEAI,kBAAkBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACd,SAAS,CAACc,kBAAkB,CAAC,CAAC;EAC5C;EAEAM,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpB,SAAS,CAACoB,WAAW,CAAC,CAAC;EACrC;EAEAC,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrB,SAAS,CAACqB,YAAY,CAAC,CAAC;EACtC;EAEAC,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACtB,SAAS,CAACsB,WAAW,CAAC,CAAC;EACrC;EAEAC,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACvB,SAAS,CAACuB,YAAY,CAAC,CAAC;EACtC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}