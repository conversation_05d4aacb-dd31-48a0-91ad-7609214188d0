{"ast": null, "code": "function isHandlerKind(kind) {\n  return input => {\n    return input != null && typeof input === \"object\" && \"__kind\" in input && input.__kind === kind;\n  };\n}\nexport { isHandlerKind };", "map": {"version": 3, "names": ["isHandlerKind", "kind", "input", "__kind"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/internal/isHandlerKind.ts"], "sourcesContent": ["import type { HandlerKind } from '../../handlers/common'\nimport type { RequestHandler } from '../../handlers/RequestHandler'\nimport type { WebSocketHandler } from '../../handlers/WebSocketHandler'\n\n/**\n * A filter function that ensures that the provided argument\n * is a handler of the given kind. This helps differentiate\n * between different kinds of handlers, e.g. request and event handlers.\n */\nexport function isHandlerKind<K extends HandlerKind>(kind: K) {\n  return (\n    input: unknown,\n  ): input is K extends 'EventHandler' ? WebSocketHandler : RequestHandler => {\n    return (\n      input != null &&\n      typeof input === 'object' &&\n      '__kind' in input &&\n      input.__kind === kind\n    )\n  }\n}\n"], "mappings": "AASO,SAASA,cAAqCC,IAAA,EAAS;EAC5D,OACEC,KAAA,IAC0E;IAC1E,OACEA,KAAA,IAAS,QACT,OAAOA,KAAA,KAAU,YACjB,YAAYA,KAAA,IACZA,KAAA,CAAMC,MAAA,KAAWF,IAAA;EAErB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}