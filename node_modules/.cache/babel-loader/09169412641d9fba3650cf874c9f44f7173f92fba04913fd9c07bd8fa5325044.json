{"ast": null, "code": "function getMessageLength(data) {\n  if (data instanceof Blob) {\n    return data.size;\n  }\n  if (data instanceof ArrayBuffer) {\n    return data.byteLength;\n  }\n  return new Blob([data]).size;\n}\nexport { getMessageLength };", "map": {"version": 3, "names": ["getMessageLength", "data", "Blob", "size", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/ws/utils/getMessageLength.ts"], "sourcesContent": ["import type { WebSocketData } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\n\n/**\n * Returns the byte length of the given WebSocket message.\n * @example\n * getMessageLength('hello') // 5\n * getMessageLength(new Blob(['hello'])) // 5\n */\nexport function getMessageLength(data: WebSocketData): number {\n  if (data instanceof Blob) {\n    return data.size\n  }\n\n  if (data instanceof ArrayBuffer) {\n    return data.byteLength\n  }\n\n  return new Blob([data as any]).size\n}\n"], "mappings": "AAQO,SAASA,iBAAiBC,IAAA,EAA6B;EAC5D,IAAIA,IAAA,YAAgBC,IAAA,EAAM;IACxB,OAAOD,IAAA,CAAKE,IAAA;EACd;EAEA,IAAIF,IAAA,YAAgBG,WAAA,EAAa;IAC/B,OAAOH,IAAA,CAAKI,UAAA;EACd;EAEA,OAAO,IAAIH,IAAA,CAAK,CAACD,IAAW,CAAC,EAAEE,IAAA;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}