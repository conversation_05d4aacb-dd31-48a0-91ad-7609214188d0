{"ast": null, "code": "// Spec Section: \"Executable Definitions\"\nimport { ExecutableDefinitionsRule } from './rules/ExecutableDefinitionsRule.mjs'; // Spec Section: \"Field Selections on Objects, Interfaces, and Unions Types\"\n\nimport { FieldsOnCorrectTypeRule } from './rules/FieldsOnCorrectTypeRule.mjs'; // Spec Section: \"Fragments on Composite Types\"\n\nimport { FragmentsOnCompositeTypesRule } from './rules/FragmentsOnCompositeTypesRule.mjs'; // Spec Section: \"Argument Names\"\n\nimport { KnownArgumentNamesOnDirectivesRule, KnownArgumentNamesRule } from './rules/KnownArgumentNamesRule.mjs'; // Spec Section: \"Directives Are Defined\"\n\nimport { KnownDirectivesRule } from './rules/KnownDirectivesRule.mjs'; // Spec Section: \"Fragment spread target defined\"\n\nimport { KnownFragmentNamesRule } from './rules/KnownFragmentNamesRule.mjs'; // Spec Section: \"Fragment Spread Type Existence\"\n\nimport { KnownTypeNamesRule } from './rules/KnownTypeNamesRule.mjs'; // Spec Section: \"Lone Anonymous Operation\"\n\nimport { LoneAnonymousOperationRule } from './rules/LoneAnonymousOperationRule.mjs'; // SDL-specific validation rules\n\nimport { LoneSchemaDefinitionRule } from './rules/LoneSchemaDefinitionRule.mjs'; // TODO: Spec Section\n\nimport { MaxIntrospectionDepthRule } from './rules/MaxIntrospectionDepthRule.mjs'; // Spec Section: \"Fragments must not form cycles\"\n\nimport { NoFragmentCyclesRule } from './rules/NoFragmentCyclesRule.mjs'; // Spec Section: \"All Variable Used Defined\"\n\nimport { NoUndefinedVariablesRule } from './rules/NoUndefinedVariablesRule.mjs'; // Spec Section: \"Fragments must be used\"\n\nimport { NoUnusedFragmentsRule } from './rules/NoUnusedFragmentsRule.mjs'; // Spec Section: \"All Variables Used\"\n\nimport { NoUnusedVariablesRule } from './rules/NoUnusedVariablesRule.mjs'; // Spec Section: \"Field Selection Merging\"\n\nimport { OverlappingFieldsCanBeMergedRule } from './rules/OverlappingFieldsCanBeMergedRule.mjs'; // Spec Section: \"Fragment spread is possible\"\n\nimport { PossibleFragmentSpreadsRule } from './rules/PossibleFragmentSpreadsRule.mjs';\nimport { PossibleTypeExtensionsRule } from './rules/PossibleTypeExtensionsRule.mjs'; // Spec Section: \"Argument Optionality\"\n\nimport { ProvidedRequiredArgumentsOnDirectivesRule, ProvidedRequiredArgumentsRule } from './rules/ProvidedRequiredArgumentsRule.mjs'; // Spec Section: \"Leaf Field Selections\"\n\nimport { ScalarLeafsRule } from './rules/ScalarLeafsRule.mjs'; // Spec Section: \"Subscriptions with Single Root Field\"\n\nimport { SingleFieldSubscriptionsRule } from './rules/SingleFieldSubscriptionsRule.mjs';\nimport { UniqueArgumentDefinitionNamesRule } from './rules/UniqueArgumentDefinitionNamesRule.mjs'; // Spec Section: \"Argument Uniqueness\"\n\nimport { UniqueArgumentNamesRule } from './rules/UniqueArgumentNamesRule.mjs';\nimport { UniqueDirectiveNamesRule } from './rules/UniqueDirectiveNamesRule.mjs'; // Spec Section: \"Directives Are Unique Per Location\"\n\nimport { UniqueDirectivesPerLocationRule } from './rules/UniqueDirectivesPerLocationRule.mjs';\nimport { UniqueEnumValueNamesRule } from './rules/UniqueEnumValueNamesRule.mjs';\nimport { UniqueFieldDefinitionNamesRule } from './rules/UniqueFieldDefinitionNamesRule.mjs'; // Spec Section: \"Fragment Name Uniqueness\"\n\nimport { UniqueFragmentNamesRule } from './rules/UniqueFragmentNamesRule.mjs'; // Spec Section: \"Input Object Field Uniqueness\"\n\nimport { UniqueInputFieldNamesRule } from './rules/UniqueInputFieldNamesRule.mjs'; // Spec Section: \"Operation Name Uniqueness\"\n\nimport { UniqueOperationNamesRule } from './rules/UniqueOperationNamesRule.mjs';\nimport { UniqueOperationTypesRule } from './rules/UniqueOperationTypesRule.mjs';\nimport { UniqueTypeNamesRule } from './rules/UniqueTypeNamesRule.mjs'; // Spec Section: \"Variable Uniqueness\"\n\nimport { UniqueVariableNamesRule } from './rules/UniqueVariableNamesRule.mjs'; // Spec Section: \"Value Type Correctness\"\n\nimport { ValuesOfCorrectTypeRule } from './rules/ValuesOfCorrectTypeRule.mjs'; // Spec Section: \"Variables are Input Types\"\n\nimport { VariablesAreInputTypesRule } from './rules/VariablesAreInputTypesRule.mjs'; // Spec Section: \"All Variable Usages Are Allowed\"\n\nimport { VariablesInAllowedPositionRule } from './rules/VariablesInAllowedPositionRule.mjs';\n\n/**\n * Technically these aren't part of the spec but they are strongly encouraged\n * validation rules.\n */\nexport const recommendedRules = Object.freeze([MaxIntrospectionDepthRule]);\n/**\n * This set includes all validation rules defined by the GraphQL spec.\n *\n * The order of the rules in this list has been adjusted to lead to the\n * most clear output when encountering multiple validation errors.\n */\n\nexport const specifiedRules = Object.freeze([ExecutableDefinitionsRule, UniqueOperationNamesRule, LoneAnonymousOperationRule, SingleFieldSubscriptionsRule, KnownTypeNamesRule, FragmentsOnCompositeTypesRule, VariablesAreInputTypesRule, ScalarLeafsRule, FieldsOnCorrectTypeRule, UniqueFragmentNamesRule, KnownFragmentNamesRule, NoUnusedFragmentsRule, PossibleFragmentSpreadsRule, NoFragmentCyclesRule, UniqueVariableNamesRule, NoUndefinedVariablesRule, NoUnusedVariablesRule, KnownDirectivesRule, UniqueDirectivesPerLocationRule, KnownArgumentNamesRule, UniqueArgumentNamesRule, ValuesOfCorrectTypeRule, ProvidedRequiredArgumentsRule, VariablesInAllowedPositionRule, OverlappingFieldsCanBeMergedRule, UniqueInputFieldNamesRule, ...recommendedRules]);\n/**\n * @internal\n */\n\nexport const specifiedSDLRules = Object.freeze([LoneSchemaDefinitionRule, UniqueOperationTypesRule, UniqueTypeNamesRule, UniqueEnumValueNamesRule, UniqueFieldDefinitionNamesRule, UniqueArgumentDefinitionNamesRule, UniqueDirectiveNamesRule, KnownTypeNamesRule, KnownDirectivesRule, UniqueDirectivesPerLocationRule, PossibleTypeExtensionsRule, KnownArgumentNamesOnDirectivesRule, UniqueArgumentNamesRule, UniqueInputFieldNamesRule, ProvidedRequiredArgumentsOnDirectivesRule]);", "map": {"version": 3, "names": ["ExecutableDefinitionsRule", "FieldsOnCorrectTypeRule", "FragmentsOnCompositeTypesRule", "KnownArgumentNamesOnDirectivesRule", "KnownArgumentNamesRule", "KnownDirectivesRule", "KnownFragmentNamesRule", "KnownTypeNamesRule", "LoneAnonymousOperationRule", "LoneSchemaDefinitionRule", "MaxIntrospectionDepthRule", "NoFragmentCyclesRule", "NoUndefinedVariablesRule", "NoUnusedFragmentsRule", "NoUnusedVariablesRule", "OverlappingFieldsCanBeMergedRule", "PossibleFragmentSpreadsRule", "PossibleTypeExtensionsRule", "ProvidedRequiredArgumentsOnDirectivesRule", "ProvidedRequiredArgumentsRule", "ScalarLeafsRule", "SingleFieldSubscriptionsRule", "UniqueArgumentDefinitionNamesRule", "UniqueArgumentNamesRule", "UniqueDirectiveNamesRule", "UniqueDirectivesPerLocationRule", "UniqueEnumValueNamesRule", "UniqueFieldDefinitionNamesRule", "UniqueFragmentNamesRule", "UniqueInputFieldNamesRule", "UniqueOperationNamesRule", "UniqueOperationTypesRule", "UniqueTypeNamesRule", "UniqueVariableNamesRule", "ValuesOfCorrectTypeRule", "VariablesAreInputTypesRule", "VariablesInAllowedPositionRule", "recommendedRules", "Object", "freeze", "specifiedRules", "specifiedSDLRules"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/specifiedRules.mjs"], "sourcesContent": ["// Spec Section: \"Executable Definitions\"\nimport { ExecutableDefinitionsRule } from './rules/ExecutableDefinitionsRule.mjs'; // Spec Section: \"Field Selections on Objects, Interfaces, and Unions Types\"\n\nimport { FieldsOnCorrectTypeRule } from './rules/FieldsOnCorrectTypeRule.mjs'; // Spec Section: \"Fragments on Composite Types\"\n\nimport { FragmentsOnCompositeTypesRule } from './rules/FragmentsOnCompositeTypesRule.mjs'; // Spec Section: \"Argument Names\"\n\nimport {\n  KnownArgumentNamesOnDirectivesRule,\n  KnownArgumentNamesRule,\n} from './rules/KnownArgumentNamesRule.mjs'; // Spec Section: \"Directives Are Defined\"\n\nimport { KnownDirectivesRule } from './rules/KnownDirectivesRule.mjs'; // Spec Section: \"Fragment spread target defined\"\n\nimport { KnownFragmentNamesRule } from './rules/KnownFragmentNamesRule.mjs'; // Spec Section: \"Fragment Spread Type Existence\"\n\nimport { KnownTypeNamesRule } from './rules/KnownTypeNamesRule.mjs'; // Spec Section: \"Lone Anonymous Operation\"\n\nimport { LoneAnonymousOperationRule } from './rules/LoneAnonymousOperationRule.mjs'; // SDL-specific validation rules\n\nimport { LoneSchemaDefinitionRule } from './rules/LoneSchemaDefinitionRule.mjs'; // TODO: Spec Section\n\nimport { MaxIntrospectionDepthRule } from './rules/MaxIntrospectionDepthRule.mjs'; // Spec Section: \"Fragments must not form cycles\"\n\nimport { NoFragmentCyclesRule } from './rules/NoFragmentCyclesRule.mjs'; // Spec Section: \"All Variable Used Defined\"\n\nimport { NoUndefinedVariablesRule } from './rules/NoUndefinedVariablesRule.mjs'; // Spec Section: \"Fragments must be used\"\n\nimport { NoUnusedFragmentsRule } from './rules/NoUnusedFragmentsRule.mjs'; // Spec Section: \"All Variables Used\"\n\nimport { NoUnusedVariablesRule } from './rules/NoUnusedVariablesRule.mjs'; // Spec Section: \"Field Selection Merging\"\n\nimport { OverlappingFieldsCanBeMergedRule } from './rules/OverlappingFieldsCanBeMergedRule.mjs'; // Spec Section: \"Fragment spread is possible\"\n\nimport { PossibleFragmentSpreadsRule } from './rules/PossibleFragmentSpreadsRule.mjs';\nimport { PossibleTypeExtensionsRule } from './rules/PossibleTypeExtensionsRule.mjs'; // Spec Section: \"Argument Optionality\"\n\nimport {\n  ProvidedRequiredArgumentsOnDirectivesRule,\n  ProvidedRequiredArgumentsRule,\n} from './rules/ProvidedRequiredArgumentsRule.mjs'; // Spec Section: \"Leaf Field Selections\"\n\nimport { ScalarLeafsRule } from './rules/ScalarLeafsRule.mjs'; // Spec Section: \"Subscriptions with Single Root Field\"\n\nimport { SingleFieldSubscriptionsRule } from './rules/SingleFieldSubscriptionsRule.mjs';\nimport { UniqueArgumentDefinitionNamesRule } from './rules/UniqueArgumentDefinitionNamesRule.mjs'; // Spec Section: \"Argument Uniqueness\"\n\nimport { UniqueArgumentNamesRule } from './rules/UniqueArgumentNamesRule.mjs';\nimport { UniqueDirectiveNamesRule } from './rules/UniqueDirectiveNamesRule.mjs'; // Spec Section: \"Directives Are Unique Per Location\"\n\nimport { UniqueDirectivesPerLocationRule } from './rules/UniqueDirectivesPerLocationRule.mjs';\nimport { UniqueEnumValueNamesRule } from './rules/UniqueEnumValueNamesRule.mjs';\nimport { UniqueFieldDefinitionNamesRule } from './rules/UniqueFieldDefinitionNamesRule.mjs'; // Spec Section: \"Fragment Name Uniqueness\"\n\nimport { UniqueFragmentNamesRule } from './rules/UniqueFragmentNamesRule.mjs'; // Spec Section: \"Input Object Field Uniqueness\"\n\nimport { UniqueInputFieldNamesRule } from './rules/UniqueInputFieldNamesRule.mjs'; // Spec Section: \"Operation Name Uniqueness\"\n\nimport { UniqueOperationNamesRule } from './rules/UniqueOperationNamesRule.mjs';\nimport { UniqueOperationTypesRule } from './rules/UniqueOperationTypesRule.mjs';\nimport { UniqueTypeNamesRule } from './rules/UniqueTypeNamesRule.mjs'; // Spec Section: \"Variable Uniqueness\"\n\nimport { UniqueVariableNamesRule } from './rules/UniqueVariableNamesRule.mjs'; // Spec Section: \"Value Type Correctness\"\n\nimport { ValuesOfCorrectTypeRule } from './rules/ValuesOfCorrectTypeRule.mjs'; // Spec Section: \"Variables are Input Types\"\n\nimport { VariablesAreInputTypesRule } from './rules/VariablesAreInputTypesRule.mjs'; // Spec Section: \"All Variable Usages Are Allowed\"\n\nimport { VariablesInAllowedPositionRule } from './rules/VariablesInAllowedPositionRule.mjs';\n\n/**\n * Technically these aren't part of the spec but they are strongly encouraged\n * validation rules.\n */\nexport const recommendedRules = Object.freeze([MaxIntrospectionDepthRule]);\n/**\n * This set includes all validation rules defined by the GraphQL spec.\n *\n * The order of the rules in this list has been adjusted to lead to the\n * most clear output when encountering multiple validation errors.\n */\n\nexport const specifiedRules = Object.freeze([\n  ExecutableDefinitionsRule,\n  UniqueOperationNamesRule,\n  LoneAnonymousOperationRule,\n  SingleFieldSubscriptionsRule,\n  KnownTypeNamesRule,\n  FragmentsOnCompositeTypesRule,\n  VariablesAreInputTypesRule,\n  ScalarLeafsRule,\n  FieldsOnCorrectTypeRule,\n  UniqueFragmentNamesRule,\n  KnownFragmentNamesRule,\n  NoUnusedFragmentsRule,\n  PossibleFragmentSpreadsRule,\n  NoFragmentCyclesRule,\n  UniqueVariableNamesRule,\n  NoUndefinedVariablesRule,\n  NoUnusedVariablesRule,\n  KnownDirectivesRule,\n  UniqueDirectivesPerLocationRule,\n  KnownArgumentNamesRule,\n  UniqueArgumentNamesRule,\n  ValuesOfCorrectTypeRule,\n  ProvidedRequiredArgumentsRule,\n  VariablesInAllowedPositionRule,\n  OverlappingFieldsCanBeMergedRule,\n  UniqueInputFieldNamesRule,\n  ...recommendedRules,\n]);\n/**\n * @internal\n */\n\nexport const specifiedSDLRules = Object.freeze([\n  LoneSchemaDefinitionRule,\n  UniqueOperationTypesRule,\n  UniqueTypeNamesRule,\n  UniqueEnumValueNamesRule,\n  UniqueFieldDefinitionNamesRule,\n  UniqueArgumentDefinitionNamesRule,\n  UniqueDirectiveNamesRule,\n  KnownTypeNamesRule,\n  KnownDirectivesRule,\n  UniqueDirectivesPerLocationRule,\n  PossibleTypeExtensionsRule,\n  KnownArgumentNamesOnDirectivesRule,\n  UniqueArgumentNamesRule,\n  UniqueInputFieldNamesRule,\n  ProvidedRequiredArgumentsOnDirectivesRule,\n]);\n"], "mappings": "AAAA;AACA,SAASA,yBAAyB,QAAQ,uCAAuC,CAAC,CAAC;;AAEnF,SAASC,uBAAuB,QAAQ,qCAAqC,CAAC,CAAC;;AAE/E,SAASC,6BAA6B,QAAQ,2CAA2C,CAAC,CAAC;;AAE3F,SACEC,kCAAkC,EAClCC,sBAAsB,QACjB,oCAAoC,CAAC,CAAC;;AAE7C,SAASC,mBAAmB,QAAQ,iCAAiC,CAAC,CAAC;;AAEvE,SAASC,sBAAsB,QAAQ,oCAAoC,CAAC,CAAC;;AAE7E,SAASC,kBAAkB,QAAQ,gCAAgC,CAAC,CAAC;;AAErE,SAASC,0BAA0B,QAAQ,wCAAwC,CAAC,CAAC;;AAErF,SAASC,wBAAwB,QAAQ,sCAAsC,CAAC,CAAC;;AAEjF,SAASC,yBAAyB,QAAQ,uCAAuC,CAAC,CAAC;;AAEnF,SAASC,oBAAoB,QAAQ,kCAAkC,CAAC,CAAC;;AAEzE,SAASC,wBAAwB,QAAQ,sCAAsC,CAAC,CAAC;;AAEjF,SAASC,qBAAqB,QAAQ,mCAAmC,CAAC,CAAC;;AAE3E,SAASC,qBAAqB,QAAQ,mCAAmC,CAAC,CAAC;;AAE3E,SAASC,gCAAgC,QAAQ,8CAA8C,CAAC,CAAC;;AAEjG,SAASC,2BAA2B,QAAQ,yCAAyC;AACrF,SAASC,0BAA0B,QAAQ,wCAAwC,CAAC,CAAC;;AAErF,SACEC,yCAAyC,EACzCC,6BAA6B,QACxB,2CAA2C,CAAC,CAAC;;AAEpD,SAASC,eAAe,QAAQ,6BAA6B,CAAC,CAAC;;AAE/D,SAASC,4BAA4B,QAAQ,0CAA0C;AACvF,SAASC,iCAAiC,QAAQ,+CAA+C,CAAC,CAAC;;AAEnG,SAASC,uBAAuB,QAAQ,qCAAqC;AAC7E,SAASC,wBAAwB,QAAQ,sCAAsC,CAAC,CAAC;;AAEjF,SAASC,+BAA+B,QAAQ,6CAA6C;AAC7F,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,8BAA8B,QAAQ,4CAA4C,CAAC,CAAC;;AAE7F,SAASC,uBAAuB,QAAQ,qCAAqC,CAAC,CAAC;;AAE/E,SAASC,yBAAyB,QAAQ,uCAAuC,CAAC,CAAC;;AAEnF,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,mBAAmB,QAAQ,iCAAiC,CAAC,CAAC;;AAEvE,SAASC,uBAAuB,QAAQ,qCAAqC,CAAC,CAAC;;AAE/E,SAASC,uBAAuB,QAAQ,qCAAqC,CAAC,CAAC;;AAE/E,SAASC,0BAA0B,QAAQ,wCAAwC,CAAC,CAAC;;AAErF,SAASC,8BAA8B,QAAQ,4CAA4C;;AAE3F;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC7B,yBAAyB,CAAC,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAM8B,cAAc,GAAGF,MAAM,CAACC,MAAM,CAAC,CAC1CvC,yBAAyB,EACzB8B,wBAAwB,EACxBtB,0BAA0B,EAC1Ba,4BAA4B,EAC5Bd,kBAAkB,EAClBL,6BAA6B,EAC7BiC,0BAA0B,EAC1Bf,eAAe,EACfnB,uBAAuB,EACvB2B,uBAAuB,EACvBtB,sBAAsB,EACtBO,qBAAqB,EACrBG,2BAA2B,EAC3BL,oBAAoB,EACpBsB,uBAAuB,EACvBrB,wBAAwB,EACxBE,qBAAqB,EACrBT,mBAAmB,EACnBoB,+BAA+B,EAC/BrB,sBAAsB,EACtBmB,uBAAuB,EACvBW,uBAAuB,EACvBf,6BAA6B,EAC7BiB,8BAA8B,EAC9BrB,gCAAgC,EAChCc,yBAAyB,EACzB,GAAGQ,gBAAgB,CACpB,CAAC;AACF;AACA;AACA;;AAEA,OAAO,MAAMI,iBAAiB,GAAGH,MAAM,CAACC,MAAM,CAAC,CAC7C9B,wBAAwB,EACxBsB,wBAAwB,EACxBC,mBAAmB,EACnBN,wBAAwB,EACxBC,8BAA8B,EAC9BL,iCAAiC,EACjCE,wBAAwB,EACxBjB,kBAAkB,EAClBF,mBAAmB,EACnBoB,+BAA+B,EAC/BR,0BAA0B,EAC1Bd,kCAAkC,EAClCoB,uBAAuB,EACvBM,yBAAyB,EACzBX,yCAAyC,CAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}