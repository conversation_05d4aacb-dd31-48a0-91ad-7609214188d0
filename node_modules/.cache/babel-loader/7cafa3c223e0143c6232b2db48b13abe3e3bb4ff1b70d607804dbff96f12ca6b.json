{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isInputObjectType, isInterfaceType, isObjectType } from '../../type/definition.mjs';\n\n/**\n * Unique field definition names\n *\n * A GraphQL complex type is only valid if all its fields are uniquely named.\n */\nexport function UniqueFieldDefinitionNamesRule(context) {\n  const schema = context.getSchema();\n  const existingTypeMap = schema ? schema.getTypeMap() : Object.create(null);\n  const knownFieldNames = Object.create(null);\n  return {\n    InputObjectTypeDefinition: checkFieldUniqueness,\n    InputObjectTypeExtension: checkFieldUniqueness,\n    InterfaceTypeDefinition: checkFieldUniqueness,\n    InterfaceTypeExtension: checkFieldUniqueness,\n    ObjectTypeDefinition: checkFieldUniqueness,\n    ObjectTypeExtension: checkFieldUniqueness\n  };\n  function checkFieldUniqueness(node) {\n    var _node$fields;\n    const typeName = node.name.value;\n    if (!knownFieldNames[typeName]) {\n      knownFieldNames[typeName] = Object.create(null);\n    } // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n\n    const fieldNodes = (_node$fields = node.fields) !== null && _node$fields !== void 0 ? _node$fields : [];\n    const fieldNames = knownFieldNames[typeName];\n    for (const fieldDef of fieldNodes) {\n      const fieldName = fieldDef.name.value;\n      if (hasField(existingTypeMap[typeName], fieldName)) {\n        context.reportError(new GraphQLError(`Field \"${typeName}.${fieldName}\" already exists in the schema. It cannot also be defined in this type extension.`, {\n          nodes: fieldDef.name\n        }));\n      } else if (fieldNames[fieldName]) {\n        context.reportError(new GraphQLError(`Field \"${typeName}.${fieldName}\" can only be defined once.`, {\n          nodes: [fieldNames[fieldName], fieldDef.name]\n        }));\n      } else {\n        fieldNames[fieldName] = fieldDef.name;\n      }\n    }\n    return false;\n  }\n}\nfunction hasField(type, fieldName) {\n  if (isObjectType(type) || isInterfaceType(type) || isInputObjectType(type)) {\n    return type.getFields()[fieldName] != null;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["GraphQLError", "isInputObjectType", "isInterfaceType", "isObjectType", "UniqueFieldDefinitionNamesRule", "context", "schema", "getSchema", "existingTypeMap", "getTypeMap", "Object", "create", "knownFieldNames", "InputObjectTypeDefinition", "checkFieldUniqueness", "InputObjectTypeExtension", "InterfaceTypeDefinition", "InterfaceTypeExtension", "ObjectTypeDefinition", "ObjectTypeExtension", "node", "_node$fields", "typeName", "name", "value", "fieldNodes", "fields", "fieldNames", "fieldDef", "fieldName", "<PERSON><PERSON><PERSON>", "reportError", "nodes", "type", "getFields"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/UniqueFieldDefinitionNamesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport {\n  isInputObjectType,\n  isInterfaceType,\n  isObjectType,\n} from '../../type/definition.mjs';\n\n/**\n * Unique field definition names\n *\n * A GraphQL complex type is only valid if all its fields are uniquely named.\n */\nexport function UniqueFieldDefinitionNamesRule(context) {\n  const schema = context.getSchema();\n  const existingTypeMap = schema ? schema.getTypeMap() : Object.create(null);\n  const knownFieldNames = Object.create(null);\n  return {\n    InputObjectTypeDefinition: checkFieldUniqueness,\n    InputObjectTypeExtension: checkFieldUniqueness,\n    InterfaceTypeDefinition: checkFieldUniqueness,\n    InterfaceTypeExtension: checkFieldUniqueness,\n    ObjectTypeDefinition: checkFieldUniqueness,\n    ObjectTypeExtension: checkFieldUniqueness,\n  };\n\n  function checkFieldUniqueness(node) {\n    var _node$fields;\n\n    const typeName = node.name.value;\n\n    if (!knownFieldNames[typeName]) {\n      knownFieldNames[typeName] = Object.create(null);\n    } // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n\n    const fieldNodes =\n      (_node$fields = node.fields) !== null && _node$fields !== void 0\n        ? _node$fields\n        : [];\n    const fieldNames = knownFieldNames[typeName];\n\n    for (const fieldDef of fieldNodes) {\n      const fieldName = fieldDef.name.value;\n\n      if (hasField(existingTypeMap[typeName], fieldName)) {\n        context.reportError(\n          new GraphQLError(\n            `Field \"${typeName}.${fieldName}\" already exists in the schema. It cannot also be defined in this type extension.`,\n            {\n              nodes: fieldDef.name,\n            },\n          ),\n        );\n      } else if (fieldNames[fieldName]) {\n        context.reportError(\n          new GraphQLError(\n            `Field \"${typeName}.${fieldName}\" can only be defined once.`,\n            {\n              nodes: [fieldNames[fieldName], fieldDef.name],\n            },\n          ),\n        );\n      } else {\n        fieldNames[fieldName] = fieldDef.name;\n      }\n    }\n\n    return false;\n  }\n}\n\nfunction hasField(type, fieldName) {\n  if (isObjectType(type) || isInterfaceType(type) || isInputObjectType(type)) {\n    return type.getFields()[fieldName] != null;\n  }\n\n  return false;\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,QACP,2BAA2B;;AAElC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,8BAA8BA,CAACC,OAAO,EAAE;EACtD,MAAMC,MAAM,GAAGD,OAAO,CAACE,SAAS,CAAC,CAAC;EAClC,MAAMC,eAAe,GAAGF,MAAM,GAAGA,MAAM,CAACG,UAAU,CAAC,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC1E,MAAMC,eAAe,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC3C,OAAO;IACLE,yBAAyB,EAAEC,oBAAoB;IAC/CC,wBAAwB,EAAED,oBAAoB;IAC9CE,uBAAuB,EAAEF,oBAAoB;IAC7CG,sBAAsB,EAAEH,oBAAoB;IAC5CI,oBAAoB,EAAEJ,oBAAoB;IAC1CK,mBAAmB,EAAEL;EACvB,CAAC;EAED,SAASA,oBAAoBA,CAACM,IAAI,EAAE;IAClC,IAAIC,YAAY;IAEhB,MAAMC,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK;IAEhC,IAAI,CAACZ,eAAe,CAACU,QAAQ,CAAC,EAAE;MAC9BV,eAAe,CAACU,QAAQ,CAAC,GAAGZ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjD,CAAC,CAAC;;IAEF;;IAEA,MAAMc,UAAU,GACd,CAACJ,YAAY,GAAGD,IAAI,CAACM,MAAM,MAAM,IAAI,IAAIL,YAAY,KAAK,KAAK,CAAC,GAC5DA,YAAY,GACZ,EAAE;IACR,MAAMM,UAAU,GAAGf,eAAe,CAACU,QAAQ,CAAC;IAE5C,KAAK,MAAMM,QAAQ,IAAIH,UAAU,EAAE;MACjC,MAAMI,SAAS,GAAGD,QAAQ,CAACL,IAAI,CAACC,KAAK;MAErC,IAAIM,QAAQ,CAACtB,eAAe,CAACc,QAAQ,CAAC,EAAEO,SAAS,CAAC,EAAE;QAClDxB,OAAO,CAAC0B,WAAW,CACjB,IAAI/B,YAAY,CACd,UAAUsB,QAAQ,IAAIO,SAAS,mFAAmF,EAClH;UACEG,KAAK,EAAEJ,QAAQ,CAACL;QAClB,CACF,CACF,CAAC;MACH,CAAC,MAAM,IAAII,UAAU,CAACE,SAAS,CAAC,EAAE;QAChCxB,OAAO,CAAC0B,WAAW,CACjB,IAAI/B,YAAY,CACd,UAAUsB,QAAQ,IAAIO,SAAS,6BAA6B,EAC5D;UACEG,KAAK,EAAE,CAACL,UAAU,CAACE,SAAS,CAAC,EAAED,QAAQ,CAACL,IAAI;QAC9C,CACF,CACF,CAAC;MACH,CAAC,MAAM;QACLI,UAAU,CAACE,SAAS,CAAC,GAAGD,QAAQ,CAACL,IAAI;MACvC;IACF;IAEA,OAAO,KAAK;EACd;AACF;AAEA,SAASO,QAAQA,CAACG,IAAI,EAAEJ,SAAS,EAAE;EACjC,IAAI1B,YAAY,CAAC8B,IAAI,CAAC,IAAI/B,eAAe,CAAC+B,IAAI,CAAC,IAAIhC,iBAAiB,CAACgC,IAAI,CAAC,EAAE;IAC1E,OAAOA,IAAI,CAACC,SAAS,CAAC,CAAC,CAACL,SAAS,CAAC,IAAI,IAAI;EAC5C;EAEA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}