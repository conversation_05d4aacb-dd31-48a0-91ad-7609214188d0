{"ast": null, "code": "export { GraphQLError, printError, formatError } from './GraphQLError.mjs';\nexport { syntaxError } from './syntaxError.mjs';\nexport { locatedError } from './locatedError.mjs';", "map": {"version": 3, "names": ["GraphQLError", "printError", "formatError", "syntaxError", "locatedError"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/error/index.mjs"], "sourcesContent": ["export { GraphQLError, printError, formatError } from './GraphQLError.mjs';\nexport { syntaxError } from './syntaxError.mjs';\nexport { locatedError } from './locatedError.mjs';\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,UAAU,EAAEC,WAAW,QAAQ,oBAAoB;AAC1E,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,YAAY,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}