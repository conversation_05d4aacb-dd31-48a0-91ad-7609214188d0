{"ast": null, "code": "// src/utils/hasConfigurableGlobal.ts\nfunction hasConfigurableGlobal(propertyName) {\n  const descriptor = Object.getOwnPropertyDescriptor(globalThis, propertyName);\n  if (typeof descriptor === \"undefined\") {\n    return false;\n  }\n  if (typeof descriptor.get === \"function\" && typeof descriptor.get() === \"undefined\") {\n    return false;\n  }\n  if (typeof descriptor.get === \"undefined\" && descriptor.value == null) {\n    return false;\n  }\n  if (typeof descriptor.set === \"undefined\" && !descriptor.configurable) {\n    console.error(`[MSW] Failed to apply interceptor: the global \\`${propertyName}\\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`);\n    return false;\n  }\n  return true;\n}\nexport { hasConfigurableGlobal };", "map": {"version": 3, "names": ["hasConfigurableGlobal", "propertyName", "descriptor", "Object", "getOwnPropertyDescriptor", "globalThis", "get", "value", "set", "configurable", "console", "error"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/utils/hasConfigurableGlobal.ts"], "sourcesContent": ["/**\n * Returns a boolean indicating whether the given global property\n * is defined and is configurable.\n */\nexport function hasConfigurableGlobal(propertyName: string): boolean {\n  const descriptor = Object.getOwnPropertyDescriptor(globalThis, propertyName)\n\n  // The property is not set at all.\n  if (typeof descriptor === 'undefined') {\n    return false\n  }\n\n  // The property is set to a getter that returns undefined.\n  if (\n    typeof descriptor.get === 'function' &&\n    typeof descriptor.get() === 'undefined'\n  ) {\n    return false\n  }\n\n  // The property is set to a value equal to undefined.\n  if (typeof descriptor.get === 'undefined' && descriptor.value == null) {\n    return false\n  }\n\n  if (typeof descriptor.set === 'undefined' && !descriptor.configurable) {\n    console.error(\n      `[MSW] Failed to apply interceptor: the global \\`${propertyName}\\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`\n    )\n    return false\n  }\n\n  return true\n}\n"], "mappings": ";AAIO,SAASA,sBAAsBC,YAAA,EAA+B;EACnE,MAAMC,UAAA,GAAaC,MAAA,CAAOC,wBAAA,CAAyBC,UAAA,EAAYJ,YAAY;EAG3E,IAAI,OAAOC,UAAA,KAAe,aAAa;IACrC,OAAO;EACT;EAGA,IACE,OAAOA,UAAA,CAAWI,GAAA,KAAQ,cAC1B,OAAOJ,UAAA,CAAWI,GAAA,CAAI,MAAM,aAC5B;IACA,OAAO;EACT;EAGA,IAAI,OAAOJ,UAAA,CAAWI,GAAA,KAAQ,eAAeJ,UAAA,CAAWK,KAAA,IAAS,MAAM;IACrE,OAAO;EACT;EAEA,IAAI,OAAOL,UAAA,CAAWM,GAAA,KAAQ,eAAe,CAACN,UAAA,CAAWO,YAAA,EAAc;IACrEC,OAAA,CAAQC,KAAA,CACN,mDAAmDV,YAAA,oKACrD;IACA,OAAO;EACT;EAEA,OAAO;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}