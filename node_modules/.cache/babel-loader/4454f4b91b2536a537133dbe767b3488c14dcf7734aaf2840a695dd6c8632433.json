{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isCompositeType } from '../../type/definition.mjs';\nimport { doTypesOverlap } from '../../utilities/typeComparators.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Possible fragment spread\n *\n * A fragment spread is only valid if the type condition could ever possibly\n * be true: if there is a non-empty intersection of the possible parent types,\n * and possible types which pass the type condition.\n */\nexport function PossibleFragmentSpreadsRule(context) {\n  return {\n    InlineFragment(node) {\n      const fragType = context.getType();\n      const parentType = context.getParentType();\n      if (isCompositeType(fragType) && isCompositeType(parentType) && !doTypesOverlap(context.getSchema(), fragType, parentType)) {\n        const parentTypeStr = inspect(parentType);\n        const fragTypeStr = inspect(fragType);\n        context.reportError(new GraphQLError(`Fragment cannot be spread here as objects of type \"${parentTypeStr}\" can never be of type \"${fragTypeStr}\".`, {\n          nodes: node\n        }));\n      }\n    },\n    FragmentSpread(node) {\n      const fragName = node.name.value;\n      const fragType = getFragmentType(context, fragName);\n      const parentType = context.getParentType();\n      if (fragType && parentType && !doTypesOverlap(context.getSchema(), fragType, parentType)) {\n        const parentTypeStr = inspect(parentType);\n        const fragTypeStr = inspect(fragType);\n        context.reportError(new GraphQLError(`Fragment \"${fragName}\" cannot be spread here as objects of type \"${parentTypeStr}\" can never be of type \"${fragTypeStr}\".`, {\n          nodes: node\n        }));\n      }\n    }\n  };\n}\nfunction getFragmentType(context, name) {\n  const frag = context.getFragment(name);\n  if (frag) {\n    const type = typeFromAST(context.getSchema(), frag.typeCondition);\n    if (isCompositeType(type)) {\n      return type;\n    }\n  }\n}", "map": {"version": 3, "names": ["inspect", "GraphQLError", "isCompositeType", "doTypesOverlap", "typeFromAST", "PossibleFragmentSpreadsRule", "context", "InlineFragment", "node", "fragType", "getType", "parentType", "getParentType", "getSchema", "parentTypeStr", "fragTypeStr", "reportError", "nodes", "FragmentSpread", "fragName", "name", "value", "getFragmentType", "frag", "getFragment", "type", "typeCondition"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/PossibleFragmentSpreadsRule.mjs"], "sourcesContent": ["import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isCompositeType } from '../../type/definition.mjs';\nimport { doTypesOverlap } from '../../utilities/typeComparators.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Possible fragment spread\n *\n * A fragment spread is only valid if the type condition could ever possibly\n * be true: if there is a non-empty intersection of the possible parent types,\n * and possible types which pass the type condition.\n */\nexport function PossibleFragmentSpreadsRule(context) {\n  return {\n    InlineFragment(node) {\n      const fragType = context.getType();\n      const parentType = context.getParentType();\n\n      if (\n        isCompositeType(fragType) &&\n        isCompositeType(parentType) &&\n        !doTypesOverlap(context.getSchema(), fragType, parentType)\n      ) {\n        const parentTypeStr = inspect(parentType);\n        const fragTypeStr = inspect(fragType);\n        context.reportError(\n          new GraphQLError(\n            `Fragment cannot be spread here as objects of type \"${parentTypeStr}\" can never be of type \"${fragTypeStr}\".`,\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n\n    FragmentSpread(node) {\n      const fragName = node.name.value;\n      const fragType = getFragmentType(context, fragName);\n      const parentType = context.getParentType();\n\n      if (\n        fragType &&\n        parentType &&\n        !doTypesOverlap(context.getSchema(), fragType, parentType)\n      ) {\n        const parentTypeStr = inspect(parentType);\n        const fragTypeStr = inspect(fragType);\n        context.reportError(\n          new GraphQLError(\n            `Fragment \"${fragName}\" cannot be spread here as objects of type \"${parentTypeStr}\" can never be of type \"${fragTypeStr}\".`,\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n  };\n}\n\nfunction getFragmentType(context, name) {\n  const frag = context.getFragment(name);\n\n  if (frag) {\n    const type = typeFromAST(context.getSchema(), frag.typeCondition);\n\n    if (isCompositeType(type)) {\n      return type;\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,WAAW,QAAQ,iCAAiC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,2BAA2BA,CAACC,OAAO,EAAE;EACnD,OAAO;IACLC,cAAcA,CAACC,IAAI,EAAE;MACnB,MAAMC,QAAQ,GAAGH,OAAO,CAACI,OAAO,CAAC,CAAC;MAClC,MAAMC,UAAU,GAAGL,OAAO,CAACM,aAAa,CAAC,CAAC;MAE1C,IACEV,eAAe,CAACO,QAAQ,CAAC,IACzBP,eAAe,CAACS,UAAU,CAAC,IAC3B,CAACR,cAAc,CAACG,OAAO,CAACO,SAAS,CAAC,CAAC,EAAEJ,QAAQ,EAAEE,UAAU,CAAC,EAC1D;QACA,MAAMG,aAAa,GAAGd,OAAO,CAACW,UAAU,CAAC;QACzC,MAAMI,WAAW,GAAGf,OAAO,CAACS,QAAQ,CAAC;QACrCH,OAAO,CAACU,WAAW,CACjB,IAAIf,YAAY,CACd,sDAAsDa,aAAa,2BAA2BC,WAAW,IAAI,EAC7G;UACEE,KAAK,EAAET;QACT,CACF,CACF,CAAC;MACH;IACF,CAAC;IAEDU,cAAcA,CAACV,IAAI,EAAE;MACnB,MAAMW,QAAQ,GAAGX,IAAI,CAACY,IAAI,CAACC,KAAK;MAChC,MAAMZ,QAAQ,GAAGa,eAAe,CAAChB,OAAO,EAAEa,QAAQ,CAAC;MACnD,MAAMR,UAAU,GAAGL,OAAO,CAACM,aAAa,CAAC,CAAC;MAE1C,IACEH,QAAQ,IACRE,UAAU,IACV,CAACR,cAAc,CAACG,OAAO,CAACO,SAAS,CAAC,CAAC,EAAEJ,QAAQ,EAAEE,UAAU,CAAC,EAC1D;QACA,MAAMG,aAAa,GAAGd,OAAO,CAACW,UAAU,CAAC;QACzC,MAAMI,WAAW,GAAGf,OAAO,CAACS,QAAQ,CAAC;QACrCH,OAAO,CAACU,WAAW,CACjB,IAAIf,YAAY,CACd,aAAakB,QAAQ,+CAA+CL,aAAa,2BAA2BC,WAAW,IAAI,EAC3H;UACEE,KAAK,EAAET;QACT,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH;AAEA,SAASc,eAAeA,CAAChB,OAAO,EAAEc,IAAI,EAAE;EACtC,MAAMG,IAAI,GAAGjB,OAAO,CAACkB,WAAW,CAACJ,IAAI,CAAC;EAEtC,IAAIG,IAAI,EAAE;IACR,MAAME,IAAI,GAAGrB,WAAW,CAACE,OAAO,CAACO,SAAS,CAAC,CAAC,EAAEU,IAAI,CAACG,aAAa,CAAC;IAEjE,IAAIxB,eAAe,CAACuB,IAAI,CAAC,EAAE;MACzB,OAAOA,IAAI;IACb;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}