{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/ReviewSubmit.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { Box, Typography, Grid, Paper, Table, TableBody, TableCell, TableHead, TableRow, Button, Alert, Accordion, AccordionSummary, AccordionDetails, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, Divider } from '@mui/material';\nimport { ExpandMore, CheckCircle, Warning, Error, Send as SendIcon, Download as DownloadIcon, Visibility as ViewIcon } from '@mui/icons-material';\nimport { update<PERSON>AField, submitBIA } from '../../store/biaSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReviewSubmit = () => {\n  _s();\n  var _currentBIA$processes3, _currentBIA$processes4, _currentBIA$processes5, _currentBIA$processes6, _currentBIA$processes7;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    currentBIA\n  } = useSelector(state => state.bia);\n  const [validationResults, setValidationResults] = useState([]);\n  const [submitDialogOpen, setSubmitDialogOpen] = useState(false);\n  const [approvers, setApprovers] = useState([]);\n  const [selectedApprover, setSelectedApprover] = useState('');\n  const [submitComments, setSubmitComments] = useState('');\n  useEffect(() => {\n    // Fetch approvers list\n    fetch('/api/approvers').then(r => r.json()).then(setApprovers);\n\n    // Run validation\n    runValidation();\n  }, [currentBIA]);\n  const runValidation = () => {\n    var _currentBIA$name, _currentBIA$processes, _currentBIA$processes2;\n    const results = [];\n    if (!currentBIA) return;\n\n    // Check basic info\n    if (!((_currentBIA$name = currentBIA.name) !== null && _currentBIA$name !== void 0 && _currentBIA$name.trim())) {\n      results.push({\n        type: 'error',\n        message: 'BIA name is required'\n      });\n    }\n    if (!currentBIA.function) {\n      results.push({\n        type: 'error',\n        message: 'Function/Department must be selected'\n      });\n    }\n    if (!currentBIA.owner) {\n      results.push({\n        type: 'error',\n        message: 'BIA owner must be assigned'\n      });\n    }\n\n    // Check processes\n    if (!((_currentBIA$processes = currentBIA.processes) !== null && _currentBIA$processes !== void 0 && _currentBIA$processes.length)) {\n      results.push({\n        type: 'error',\n        message: 'At least one process must be selected for analysis'\n      });\n    }\n\n    // Check each process analysis\n    let processesWithoutRTO = 0;\n    let processesWithoutRPO = 0;\n    let processesWithConflicts = 0;\n    let processesWithoutResources = 0;\n    (_currentBIA$processes2 = currentBIA.processes) === null || _currentBIA$processes2 === void 0 ? void 0 : _currentBIA$processes2.forEach(processId => {\n      var _currentBIA$processDa, _processData$resource, _processData$resource2, _processData$resource3, _processData$resource4, _processData$resource5, _processData$resource6;\n      const processData = (_currentBIA$processDa = currentBIA.processData) === null || _currentBIA$processDa === void 0 ? void 0 : _currentBIA$processDa[processId];\n      if (!(processData !== null && processData !== void 0 && processData.finalRTO)) processesWithoutRTO++;\n      if (!(processData !== null && processData !== void 0 && processData.finalRPO)) processesWithoutRPO++;\n      if ((processData === null || processData === void 0 ? void 0 : processData.dependencyConflicts) > 0) processesWithConflicts++;\n      const hasResources = (processData === null || processData === void 0 ? void 0 : (_processData$resource = processData.resources) === null || _processData$resource === void 0 ? void 0 : (_processData$resource2 = _processData$resource.staff) === null || _processData$resource2 === void 0 ? void 0 : _processData$resource2.length) > 0 || (processData === null || processData === void 0 ? void 0 : (_processData$resource3 = processData.resources) === null || _processData$resource3 === void 0 ? void 0 : (_processData$resource4 = _processData$resource3.it) === null || _processData$resource4 === void 0 ? void 0 : _processData$resource4.length) > 0 || (processData === null || processData === void 0 ? void 0 : (_processData$resource5 = processData.resources) === null || _processData$resource5 === void 0 ? void 0 : (_processData$resource6 = _processData$resource5.vitalRecords) === null || _processData$resource6 === void 0 ? void 0 : _processData$resource6.length) > 0;\n      if (!hasResources) processesWithoutResources++;\n    });\n    if (processesWithoutRTO > 0) {\n      results.push({\n        type: 'error',\n        message: `${processesWithoutRTO} process(es) missing final RTO`\n      });\n    }\n    if (processesWithoutRPO > 0) {\n      results.push({\n        type: 'error',\n        message: `${processesWithoutRPO} process(es) missing final RPO`\n      });\n    }\n    if (processesWithConflicts > 0) {\n      results.push({\n        type: 'warning',\n        message: `${processesWithConflicts} process(es) have dependency conflicts`\n      });\n    }\n    if (processesWithoutResources > 0) {\n      results.push({\n        type: 'warning',\n        message: `${processesWithoutResources} process(es) have no resources defined`\n      });\n    }\n\n    // Success message if no errors\n    if (results.filter(r => r.type === 'error').length === 0) {\n      results.push({\n        type: 'success',\n        message: 'BIA is ready for submission'\n      });\n    }\n    setValidationResults(results);\n  };\n  const handleSubmit = async () => {\n    const submissionData = {\n      ...currentBIA,\n      submittedAt: new Date().toISOString(),\n      submittedTo: selectedApprover,\n      submitComments,\n      status: 'Pending Approval'\n    };\n    dispatch(updateBIAField({\n      field: 'status',\n      value: 'Pending Approval'\n    }));\n    dispatch(submitBIA(submissionData));\n    setSubmitDialogOpen(false);\n    navigate('/dashboard');\n  };\n  const handleExportPDF = () => {\n    // Mock PDF export\n    const link = document.createElement('a');\n    link.href = `/api/bia/${currentBIA.id}/export`;\n    link.download = `BIA_${currentBIA.name}_${new Date().toISOString().split('T')[0]}.pdf`;\n    link.click();\n  };\n  const getProcessName = processId => {\n    const processes = {\n      'proc-f-01': 'Accounts Payable',\n      'proc-f-02': 'Payroll Processing',\n      'proc-t-01': 'Oracle Financials DB',\n      'proc-t-02': 'Core Network Services',\n      'proc-h-01': 'Employee Onboarding'\n    };\n    return processes[processId] || processId;\n  };\n  const getUserName = userId => {\n    const users = {\n      'user-01': 'Alice Johnson',\n      'user-02': 'Bob Williams'\n    };\n    return users[userId] || userId;\n  };\n  const getFunctionName = functionId => {\n    const functions = {\n      'func-01': 'Finance',\n      'func-02': 'Technology'\n    };\n    return functions[functionId] || functionId;\n  };\n  const hasErrors = validationResults.some(r => r.type === 'error');\n  const hasWarnings = validationResults.some(r => r.type === 'warning');\n  if (!currentBIA) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 27\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: \"Review & Submit\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Validation Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), validationResults.map((result, index) => /*#__PURE__*/_jsxDEV(Alert, {\n        severity: result.type === 'error' ? 'error' : result.type === 'warning' ? 'warning' : 'success',\n        sx: {\n          mb: 1\n        },\n        icon: result.type === 'error' ? /*#__PURE__*/_jsxDEV(Error, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 45\n        }, this) : result.type === 'warning' ? /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 85\n        }, this) : /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 99\n        }, this),\n        children: result.message\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"BIA Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(TableBody, {\n              children: [/*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 30\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: currentBIA.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Function\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 30\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: getFunctionName(currentBIA.function)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Owner\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 30\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: getUserName(currentBIA.owner)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 30\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: currentBIA.status,\n                    color: currentBIA.status === 'Draft' ? 'default' : 'primary'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Processes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 30\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ((_currentBIA$processes3 = currentBIA.processes) === null || _currentBIA$processes3 === void 0 ? void 0 : _currentBIA$processes3.length) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Completion Summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Processes with RTO/RPO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: [((_currentBIA$processes4 = currentBIA.processes) === null || _currentBIA$processes4 === void 0 ? void 0 : _currentBIA$processes4.filter(processId => {\n                var _currentBIA$processDa2;\n                const data = (_currentBIA$processDa2 = currentBIA.processData) === null || _currentBIA$processDa2 === void 0 ? void 0 : _currentBIA$processDa2[processId];\n                return (data === null || data === void 0 ? void 0 : data.finalRTO) && (data === null || data === void 0 ? void 0 : data.finalRPO);\n              }).length) || 0, \" / \", ((_currentBIA$processes5 = currentBIA.processes) === null || _currentBIA$processes5 === void 0 ? void 0 : _currentBIA$processes5.length) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Dependency Conflicts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: hasWarnings ? \"warning.main\" : \"success.main\",\n              children: ((_currentBIA$processes6 = currentBIA.processes) === null || _currentBIA$processes6 === void 0 ? void 0 : _currentBIA$processes6.reduce((total, processId) => {\n                var _currentBIA$processDa3, _currentBIA$processDa4;\n                return total + (((_currentBIA$processDa3 = currentBIA.processData) === null || _currentBIA$processDa3 === void 0 ? void 0 : (_currentBIA$processDa4 = _currentBIA$processDa3[processId]) === null || _currentBIA$processDa4 === void 0 ? void 0 : _currentBIA$processDa4.dependencyConflicts) || 0);\n              }, 0)) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          p: 2\n        },\n        children: \"Process Analysis Details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), (_currentBIA$processes7 = currentBIA.processes) === null || _currentBIA$processes7 === void 0 ? void 0 : _currentBIA$processes7.map(processId => {\n        var _currentBIA$processDa5, _processData$dependen, _processData$dependen2, _processData$dependen3, _processData$resource7, _processData$resource8, _processData$resource9, _processData$resource0, _processData$resource1, _processData$resource10;\n        const processData = ((_currentBIA$processDa5 = currentBIA.processData) === null || _currentBIA$processDa5 === void 0 ? void 0 : _currentBIA$processDa5[processId]) || {};\n        const hasConflicts = processData.dependencyConflicts > 0;\n        return /*#__PURE__*/_jsxDEV(Accordion, {\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 45\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              width: \"100%\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  flexGrow: 1\n                },\n                children: getProcessName(processId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: `RTO: ${processData.finalRTO || 'Not Set'}`,\n                size: \"small\",\n                color: processData.finalRTO ? 'success' : 'error'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: `RPO: ${processData.finalRPO || 'Not Set'}`,\n                size: \"small\",\n                color: processData.finalRPO ? 'success' : 'error'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), hasConflicts && /*#__PURE__*/_jsxDEV(Chip, {\n                label: `${processData.dependencyConflicts} Conflicts`,\n                size: \"small\",\n                color: \"warning\",\n                icon: /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 29\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 4,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Recovery Objectives\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"RTO:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this), \" \", processData.finalRTO || 'Not set']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"RPO:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this), \" \", processData.finalRPO || 'Not set']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), processData.rtoJustification && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"RTO Justification:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 25\n                  }, this), \" \", processData.rtoJustification]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 4,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: [\"Dependencies (\", ((_processData$dependen = processData.dependencies) === null || _processData$dependen === void 0 ? void 0 : _processData$dependen.length) || 0, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  dense: true,\n                  children: [(_processData$dependen2 = processData.dependencies) === null || _processData$dependen2 === void 0 ? void 0 : _processData$dependen2.slice(0, 3).map(dep => /*#__PURE__*/_jsxDEV(ListItem, {\n                    sx: {\n                      px: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: dep.type === 'internal' ? dep.processName : dep.externalName,\n                      secondary: dep.direction\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 27\n                    }, this)\n                  }, dep.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 25\n                  }, this)), (((_processData$dependen3 = processData.dependencies) === null || _processData$dependen3 === void 0 ? void 0 : _processData$dependen3.length) || 0) > 3 && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    children: [\"+\", processData.dependencies.length - 3, \" more...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 4,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Resources\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Staff: \", ((_processData$resource7 = processData.resources) === null || _processData$resource7 === void 0 ? void 0 : (_processData$resource8 = _processData$resource7.staff) === null || _processData$resource8 === void 0 ? void 0 : _processData$resource8.length) || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"IT Apps: \", ((_processData$resource9 = processData.resources) === null || _processData$resource9 === void 0 ? void 0 : (_processData$resource0 = _processData$resource9.it) === null || _processData$resource0 === void 0 ? void 0 : _processData$resource0.length) || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Records: \", ((_processData$resource1 = processData.resources) === null || _processData$resource1 === void 0 ? void 0 : (_processData$resource10 = _processData$resource1.vitalRecords) === null || _processData$resource10 === void 0 ? void 0 : _processData$resource10.length) || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this)]\n        }, processId, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 2,\n      justifyContent: \"flex-end\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 22\n        }, this),\n        onClick: handleExportPDF,\n        children: \"Export PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 22\n        }, this),\n        onClick: () => window.open(`/bia/${currentBIA.id}/preview`, '_blank'),\n        children: \"Preview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 22\n        }, this),\n        onClick: () => setSubmitDialogOpen(true),\n        disabled: hasErrors,\n        color: hasWarnings ? 'warning' : 'primary',\n        children: hasWarnings ? 'Submit with Warnings' : 'Submit for Approval'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: submitDialogOpen,\n      onClose: () => setSubmitDialogOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Submit BIA for Approval\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Select Approver\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedApprover,\n            onChange: e => setSelectedApprover(e.target.value),\n            children: approvers.map(approver => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: approver.id,\n              children: [approver.name, \" - \", approver.role]\n            }, approver.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          multiline: true,\n          rows: 4,\n          label: \"Comments (Optional)\",\n          value: submitComments,\n          onChange: e => setSubmitComments(e.target.value),\n          margin: \"normal\",\n          placeholder: \"Add any comments for the approver...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), hasWarnings && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"This BIA has warnings. Are you sure you want to submit?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setSubmitDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          disabled: !selectedApprover,\n          children: \"Submit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(ReviewSubmit, \"e29burTD2+0T8XIqDWMBu6ctOek=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c = ReviewSubmit;\nexport default ReviewSubmit;\nvar _c;\n$RefreshReg$(_c, \"ReviewSubmit\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "useNavigate", "Box", "Typography", "Grid", "Paper", "Table", "TableBody", "TableCell", "TableHead", "TableRow", "<PERSON><PERSON>", "<PERSON><PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "Divider", "ExpandMore", "CheckCircle", "Warning", "Error", "Send", "SendIcon", "Download", "DownloadIcon", "Visibility", "ViewIcon", "updateBIAField", "submitBIA", "jsxDEV", "_jsxDEV", "ReviewSubmit", "_s", "_currentBIA$processes3", "_currentBIA$processes4", "_currentBIA$processes5", "_currentBIA$processes6", "_currentBIA$processes7", "dispatch", "navigate", "currentBIA", "state", "bia", "validationResults", "setValidationResults", "submitDialogOpen", "setSubmitDialogOpen", "approvers", "setApprovers", "selectedApprover", "setSelectedApprover", "submitComments", "setSubmitComments", "fetch", "then", "r", "json", "runValidation", "_currentBIA$name", "_currentBIA$processes", "_currentBIA$processes2", "results", "name", "trim", "push", "type", "message", "function", "owner", "processes", "length", "processesWithoutRTO", "processesWithoutRPO", "processesWithConflicts", "processesWithoutResources", "for<PERSON>ach", "processId", "_currentBIA$processDa", "_processData$resource", "_processData$resource2", "_processData$resource3", "_processData$resource4", "_processData$resource5", "_processData$resource6", "processData", "finalRTO", "finalRPO", "dependencyConflicts", "hasResources", "resources", "staff", "it", "vitalRecords", "filter", "handleSubmit", "submissionData", "submittedAt", "Date", "toISOString", "submittedTo", "status", "field", "value", "handleExportPDF", "link", "document", "createElement", "href", "id", "download", "split", "click", "getProcessName", "getUserName", "userId", "users", "getFunctionName", "functionId", "functions", "hasErrors", "some", "hasWarnings", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "sx", "p", "mb", "map", "result", "index", "severity", "icon", "container", "spacing", "item", "xs", "md", "size", "label", "color", "_currentBIA$processDa2", "data", "reduce", "total", "_currentBIA$processDa3", "_currentBIA$processDa4", "_currentBIA$processDa5", "_processData$dependen", "_processData$dependen2", "_processData$dependen3", "_processData$resource7", "_processData$resource8", "_processData$resource9", "_processData$resource0", "_processData$resource1", "_processData$resource10", "hasConflicts", "expandIcon", "display", "alignItems", "gap", "width", "flexGrow", "rtoJustification", "mt", "dependencies", "dense", "slice", "dep", "px", "primary", "processName", "externalName", "secondary", "direction", "justifyContent", "startIcon", "onClick", "window", "open", "disabled", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "margin", "onChange", "e", "target", "approver", "role", "multiline", "rows", "placeholder", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/ReviewSubmit.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Box, Typography, Grid, Paper, Table, TableBody, TableCell, TableHead, TableRow,\n  Button, Alert, Accordion, AccordionSummary, AccordionDetails, Chip,\n  Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl,\n  InputLabel, Select, MenuItem, List, ListItem, ListItemText, Divider\n} from '@mui/material';\nimport {\n  ExpandMore, CheckCircle, Warning, Error, Send as SendIcon,\n  Download as DownloadIcon, Visibility as ViewIcon\n} from '@mui/icons-material';\nimport { updateBIAField, submitBIA } from '../../store/biaSlice';\n\nconst ReviewSubmit = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const { currentBIA } = useSelector(state => state.bia);\n  \n  const [validationResults, setValidationResults] = useState([]);\n  const [submitDialogOpen, setSubmitDialogOpen] = useState(false);\n  const [approvers, setApprovers] = useState([]);\n  const [selectedApprover, setSelectedApprover] = useState('');\n  const [submitComments, setSubmitComments] = useState('');\n\n  useEffect(() => {\n    // Fetch approvers list\n    fetch('/api/approvers').then(r => r.json()).then(setApprovers);\n    \n    // Run validation\n    runValidation();\n  }, [currentBIA]);\n\n  const runValidation = () => {\n    const results = [];\n    \n    if (!currentBIA) return;\n\n    // Check basic info\n    if (!currentBIA.name?.trim()) {\n      results.push({ type: 'error', message: 'BIA name is required' });\n    }\n    if (!currentBIA.function) {\n      results.push({ type: 'error', message: 'Function/Department must be selected' });\n    }\n    if (!currentBIA.owner) {\n      results.push({ type: 'error', message: 'BIA owner must be assigned' });\n    }\n\n    // Check processes\n    if (!currentBIA.processes?.length) {\n      results.push({ type: 'error', message: 'At least one process must be selected for analysis' });\n    }\n\n    // Check each process analysis\n    let processesWithoutRTO = 0;\n    let processesWithoutRPO = 0;\n    let processesWithConflicts = 0;\n    let processesWithoutResources = 0;\n\n    currentBIA.processes?.forEach(processId => {\n      const processData = currentBIA.processData?.[processId];\n      \n      if (!processData?.finalRTO) processesWithoutRTO++;\n      if (!processData?.finalRPO) processesWithoutRPO++;\n      if (processData?.dependencyConflicts > 0) processesWithConflicts++;\n      \n      const hasResources = processData?.resources?.staff?.length > 0 ||\n                          processData?.resources?.it?.length > 0 ||\n                          processData?.resources?.vitalRecords?.length > 0;\n      if (!hasResources) processesWithoutResources++;\n    });\n\n    if (processesWithoutRTO > 0) {\n      results.push({ \n        type: 'error', \n        message: `${processesWithoutRTO} process(es) missing final RTO` \n      });\n    }\n    if (processesWithoutRPO > 0) {\n      results.push({ \n        type: 'error', \n        message: `${processesWithoutRPO} process(es) missing final RPO` \n      });\n    }\n    if (processesWithConflicts > 0) {\n      results.push({ \n        type: 'warning', \n        message: `${processesWithConflicts} process(es) have dependency conflicts` \n      });\n    }\n    if (processesWithoutResources > 0) {\n      results.push({ \n        type: 'warning', \n        message: `${processesWithoutResources} process(es) have no resources defined` \n      });\n    }\n\n    // Success message if no errors\n    if (results.filter(r => r.type === 'error').length === 0) {\n      results.push({ \n        type: 'success', \n        message: 'BIA is ready for submission' \n      });\n    }\n\n    setValidationResults(results);\n  };\n\n  const handleSubmit = async () => {\n    const submissionData = {\n      ...currentBIA,\n      submittedAt: new Date().toISOString(),\n      submittedTo: selectedApprover,\n      submitComments,\n      status: 'Pending Approval'\n    };\n\n    dispatch(updateBIAField({ field: 'status', value: 'Pending Approval' }));\n    dispatch(submitBIA(submissionData));\n    \n    setSubmitDialogOpen(false);\n    navigate('/dashboard');\n  };\n\n  const handleExportPDF = () => {\n    // Mock PDF export\n    const link = document.createElement('a');\n    link.href = `/api/bia/${currentBIA.id}/export`;\n    link.download = `BIA_${currentBIA.name}_${new Date().toISOString().split('T')[0]}.pdf`;\n    link.click();\n  };\n\n  const getProcessName = (processId) => {\n    const processes = {\n      'proc-f-01': 'Accounts Payable',\n      'proc-f-02': 'Payroll Processing',\n      'proc-t-01': 'Oracle Financials DB',\n      'proc-t-02': 'Core Network Services',\n      'proc-h-01': 'Employee Onboarding'\n    };\n    return processes[processId] || processId;\n  };\n\n  const getUserName = (userId) => {\n    const users = { 'user-01': 'Alice Johnson', 'user-02': 'Bob Williams' };\n    return users[userId] || userId;\n  };\n\n  const getFunctionName = (functionId) => {\n    const functions = { 'func-01': 'Finance', 'func-02': 'Technology' };\n    return functions[functionId] || functionId;\n  };\n\n  const hasErrors = validationResults.some(r => r.type === 'error');\n  const hasWarnings = validationResults.some(r => r.type === 'warning');\n\n  if (!currentBIA) return <div>Loading...</div>;\n\n  return (\n    <Box>\n      <Typography variant=\"h5\" gutterBottom>\n        Review & Submit\n      </Typography>\n\n      {/* Validation Results */}\n      <Paper sx={{ p: 2, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Validation Results\n        </Typography>\n        \n        {validationResults.map((result, index) => (\n          <Alert \n            key={index}\n            severity={result.type === 'error' ? 'error' : result.type === 'warning' ? 'warning' : 'success'}\n            sx={{ mb: 1 }}\n            icon={result.type === 'error' ? <Error /> : result.type === 'warning' ? <Warning /> : <CheckCircle />}\n          >\n            {result.message}\n          </Alert>\n        ))}\n      </Paper>\n\n      {/* BIA Summary */}\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              BIA Overview\n            </Typography>\n            \n            <Table size=\"small\">\n              <TableBody>\n                <TableRow>\n                  <TableCell><strong>Name</strong></TableCell>\n                  <TableCell>{currentBIA.name}</TableCell>\n                </TableRow>\n                <TableRow>\n                  <TableCell><strong>Function</strong></TableCell>\n                  <TableCell>{getFunctionName(currentBIA.function)}</TableCell>\n                </TableRow>\n                <TableRow>\n                  <TableCell><strong>Owner</strong></TableCell>\n                  <TableCell>{getUserName(currentBIA.owner)}</TableCell>\n                </TableRow>\n                <TableRow>\n                  <TableCell><strong>Status</strong></TableCell>\n                  <TableCell>\n                    <Chip \n                      label={currentBIA.status} \n                      color={currentBIA.status === 'Draft' ? 'default' : 'primary'}\n                    />\n                  </TableCell>\n                </TableRow>\n                <TableRow>\n                  <TableCell><strong>Processes</strong></TableCell>\n                  <TableCell>{currentBIA.processes?.length || 0}</TableCell>\n                </TableRow>\n              </TableBody>\n            </Table>\n          </Paper>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Completion Summary\n            </Typography>\n            \n            <Box mb={2}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Processes with RTO/RPO\n              </Typography>\n              <Typography variant=\"h4\" color=\"primary\">\n                {currentBIA.processes?.filter(processId => {\n                  const data = currentBIA.processData?.[processId];\n                  return data?.finalRTO && data?.finalRPO;\n                }).length || 0} / {currentBIA.processes?.length || 0}\n              </Typography>\n            </Box>\n\n            <Box mb={2}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Dependency Conflicts\n              </Typography>\n              <Typography variant=\"h4\" color={hasWarnings ? \"warning.main\" : \"success.main\"}>\n                {currentBIA.processes?.reduce((total, processId) => {\n                  return total + (currentBIA.processData?.[processId]?.dependencyConflicts || 0);\n                }, 0) || 0}\n              </Typography>\n            </Box>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Process Details */}\n      <Paper sx={{ mb: 3 }}>\n        <Typography variant=\"h6\" sx={{ p: 2 }}>\n          Process Analysis Details\n        </Typography>\n        \n        {currentBIA.processes?.map(processId => {\n          const processData = currentBIA.processData?.[processId] || {};\n          const hasConflicts = processData.dependencyConflicts > 0;\n          \n          return (\n            <Accordion key={processId}>\n              <AccordionSummary expandIcon={<ExpandMore />}>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} width=\"100%\">\n                  <Typography sx={{ flexGrow: 1 }}>\n                    {getProcessName(processId)}\n                  </Typography>\n                  <Chip \n                    label={`RTO: ${processData.finalRTO || 'Not Set'}`}\n                    size=\"small\"\n                    color={processData.finalRTO ? 'success' : 'error'}\n                  />\n                  <Chip \n                    label={`RPO: ${processData.finalRPO || 'Not Set'}`}\n                    size=\"small\"\n                    color={processData.finalRPO ? 'success' : 'error'}\n                  />\n                  {hasConflicts && (\n                    <Chip \n                      label={`${processData.dependencyConflicts} Conflicts`}\n                      size=\"small\"\n                      color=\"warning\"\n                      icon={<Warning />}\n                    />\n                  )}\n                </Box>\n              </AccordionSummary>\n              <AccordionDetails>\n                <Grid container spacing={2}>\n                  <Grid item xs={12} md={4}>\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      Recovery Objectives\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      <strong>RTO:</strong> {processData.finalRTO || 'Not set'}\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      <strong>RPO:</strong> {processData.finalRPO || 'Not set'}\n                    </Typography>\n                    {processData.rtoJustification && (\n                      <Typography variant=\"caption\" display=\"block\" sx={{ mt: 1 }}>\n                        <strong>RTO Justification:</strong> {processData.rtoJustification}\n                      </Typography>\n                    )}\n                  </Grid>\n                  \n                  <Grid item xs={12} md={4}>\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      Dependencies ({processData.dependencies?.length || 0})\n                    </Typography>\n                    <List dense>\n                      {processData.dependencies?.slice(0, 3).map(dep => (\n                        <ListItem key={dep.id} sx={{ px: 0 }}>\n                          <ListItemText \n                            primary={dep.type === 'internal' ? dep.processName : dep.externalName}\n                            secondary={dep.direction}\n                          />\n                        </ListItem>\n                      ))}\n                      {(processData.dependencies?.length || 0) > 3 && (\n                        <Typography variant=\"caption\">\n                          +{processData.dependencies.length - 3} more...\n                        </Typography>\n                      )}\n                    </List>\n                  </Grid>\n                  \n                  <Grid item xs={12} md={4}>\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      Resources\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Staff: {processData.resources?.staff?.length || 0}\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      IT Apps: {processData.resources?.it?.length || 0}\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Records: {processData.resources?.vitalRecords?.length || 0}\n                    </Typography>\n                  </Grid>\n                </Grid>\n              </AccordionDetails>\n            </Accordion>\n          );\n        })}\n      </Paper>\n\n      {/* Actions */}\n      <Box display=\"flex\" gap={2} justifyContent=\"flex-end\">\n        <Button\n          variant=\"outlined\"\n          startIcon={<DownloadIcon />}\n          onClick={handleExportPDF}\n        >\n          Export PDF\n        </Button>\n        \n        <Button\n          variant=\"outlined\"\n          startIcon={<ViewIcon />}\n          onClick={() => window.open(`/bia/${currentBIA.id}/preview`, '_blank')}\n        >\n          Preview\n        </Button>\n        \n        <Button\n          variant=\"contained\"\n          startIcon={<SendIcon />}\n          onClick={() => setSubmitDialogOpen(true)}\n          disabled={hasErrors}\n          color={hasWarnings ? 'warning' : 'primary'}\n        >\n          {hasWarnings ? 'Submit with Warnings' : 'Submit for Approval'}\n        </Button>\n      </Box>\n\n      {/* Submit Dialog */}\n      <Dialog open={submitDialogOpen} onClose={() => setSubmitDialogOpen(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>Submit BIA for Approval</DialogTitle>\n        <DialogContent>\n          <FormControl fullWidth margin=\"normal\">\n            <InputLabel>Select Approver</InputLabel>\n            <Select\n              value={selectedApprover}\n              onChange={(e) => setSelectedApprover(e.target.value)}\n            >\n              {approvers.map(approver => (\n                <MenuItem key={approver.id} value={approver.id}>\n                  {approver.name} - {approver.role}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n\n          <TextField\n            fullWidth\n            multiline\n            rows={4}\n            label=\"Comments (Optional)\"\n            value={submitComments}\n            onChange={(e) => setSubmitComments(e.target.value)}\n            margin=\"normal\"\n            placeholder=\"Add any comments for the approver...\"\n          />\n\n          {hasWarnings && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              This BIA has warnings. Are you sure you want to submit?\n            </Alert>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setSubmitDialogOpen(false)}>Cancel</Button>\n          <Button \n            onClick={handleSubmit}\n            variant=\"contained\"\n            disabled={!selectedApprover}\n          >\n            Submit\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ReviewSubmit;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAC9EC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,IAAI,EAClEC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAEC,SAAS,EAAEC,WAAW,EACzEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,OAAO,QAC9D,eAAe;AACtB,SACEC,UAAU,EAAEC,WAAW,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,IAAIC,QAAQ,EACzDC,QAAQ,IAAIC,YAAY,EAAEC,UAAU,IAAIC,QAAQ,QAC3C,qBAAqB;AAC5B,SAASC,cAAc,EAAEC,SAAS,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzB,MAAMC,QAAQ,GAAGpD,WAAW,CAAC,CAAC;EAC9B,MAAMqD,QAAQ,GAAGnD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoD;EAAW,CAAC,GAAGrD,WAAW,CAACsD,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EAEtD,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+D,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAExDC,SAAS,CAAC,MAAM;IACd;IACAoE,KAAK,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACF,IAAI,CAACN,YAAY,CAAC;;IAE9D;IACAS,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACjB,UAAU,CAAC,CAAC;EAEhB,MAAMiB,aAAa,GAAGA,CAAA,KAAM;IAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IAC1B,MAAMC,OAAO,GAAG,EAAE;IAElB,IAAI,CAACrB,UAAU,EAAE;;IAEjB;IACA,IAAI,GAAAkB,gBAAA,GAAClB,UAAU,CAACsB,IAAI,cAAAJ,gBAAA,eAAfA,gBAAA,CAAiBK,IAAI,CAAC,CAAC,GAAE;MAC5BF,OAAO,CAACG,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAuB,CAAC,CAAC;IAClE;IACA,IAAI,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE;MACxBN,OAAO,CAACG,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAuC,CAAC,CAAC;IAClF;IACA,IAAI,CAAC1B,UAAU,CAAC4B,KAAK,EAAE;MACrBP,OAAO,CAACG,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,OAAO,EAAE;MAA6B,CAAC,CAAC;IACxE;;IAEA;IACA,IAAI,GAAAP,qBAAA,GAACnB,UAAU,CAAC6B,SAAS,cAAAV,qBAAA,eAApBA,qBAAA,CAAsBW,MAAM,GAAE;MACjCT,OAAO,CAACG,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAqD,CAAC,CAAC;IAChG;;IAEA;IACA,IAAIK,mBAAmB,GAAG,CAAC;IAC3B,IAAIC,mBAAmB,GAAG,CAAC;IAC3B,IAAIC,sBAAsB,GAAG,CAAC;IAC9B,IAAIC,yBAAyB,GAAG,CAAC;IAEjC,CAAAd,sBAAA,GAAApB,UAAU,CAAC6B,SAAS,cAAAT,sBAAA,uBAApBA,sBAAA,CAAsBe,OAAO,CAACC,SAAS,IAAI;MAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACzC,MAAMC,WAAW,IAAAP,qBAAA,GAAGrC,UAAU,CAAC4C,WAAW,cAAAP,qBAAA,uBAAtBA,qBAAA,CAAyBD,SAAS,CAAC;MAEvD,IAAI,EAACQ,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEC,QAAQ,GAAEd,mBAAmB,EAAE;MACjD,IAAI,EAACa,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,QAAQ,GAAEd,mBAAmB,EAAE;MACjD,IAAI,CAAAY,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEG,mBAAmB,IAAG,CAAC,EAAEd,sBAAsB,EAAE;MAElE,MAAMe,YAAY,GAAG,CAAAJ,WAAW,aAAXA,WAAW,wBAAAN,qBAAA,GAAXM,WAAW,CAAEK,SAAS,cAAAX,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBY,KAAK,cAAAX,sBAAA,uBAA7BA,sBAAA,CAA+BT,MAAM,IAAG,CAAC,IAC1C,CAAAc,WAAW,aAAXA,WAAW,wBAAAJ,sBAAA,GAAXI,WAAW,CAAEK,SAAS,cAAAT,sBAAA,wBAAAC,sBAAA,GAAtBD,sBAAA,CAAwBW,EAAE,cAAAV,sBAAA,uBAA1BA,sBAAA,CAA4BX,MAAM,IAAG,CAAC,IACtC,CAAAc,WAAW,aAAXA,WAAW,wBAAAF,sBAAA,GAAXE,WAAW,CAAEK,SAAS,cAAAP,sBAAA,wBAAAC,sBAAA,GAAtBD,sBAAA,CAAwBU,YAAY,cAAAT,sBAAA,uBAApCA,sBAAA,CAAsCb,MAAM,IAAG,CAAC;MACpE,IAAI,CAACkB,YAAY,EAAEd,yBAAyB,EAAE;IAChD,CAAC,CAAC;IAEF,IAAIH,mBAAmB,GAAG,CAAC,EAAE;MAC3BV,OAAO,CAACG,IAAI,CAAC;QACXC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE,GAAGK,mBAAmB;MACjC,CAAC,CAAC;IACJ;IACA,IAAIC,mBAAmB,GAAG,CAAC,EAAE;MAC3BX,OAAO,CAACG,IAAI,CAAC;QACXC,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE,GAAGM,mBAAmB;MACjC,CAAC,CAAC;IACJ;IACA,IAAIC,sBAAsB,GAAG,CAAC,EAAE;MAC9BZ,OAAO,CAACG,IAAI,CAAC;QACXC,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE,GAAGO,sBAAsB;MACpC,CAAC,CAAC;IACJ;IACA,IAAIC,yBAAyB,GAAG,CAAC,EAAE;MACjCb,OAAO,CAACG,IAAI,CAAC;QACXC,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE,GAAGQ,yBAAyB;MACvC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIb,OAAO,CAACgC,MAAM,CAACtC,CAAC,IAAIA,CAAC,CAACU,IAAI,KAAK,OAAO,CAAC,CAACK,MAAM,KAAK,CAAC,EAAE;MACxDT,OAAO,CAACG,IAAI,CAAC;QACXC,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEAtB,oBAAoB,CAACiB,OAAO,CAAC;EAC/B,CAAC;EAED,MAAMiC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMC,cAAc,GAAG;MACrB,GAAGvD,UAAU;MACbwD,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACrCC,WAAW,EAAElD,gBAAgB;MAC7BE,cAAc;MACdiD,MAAM,EAAE;IACV,CAAC;IAED9D,QAAQ,CAACX,cAAc,CAAC;MAAE0E,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAmB,CAAC,CAAC,CAAC;IACxEhE,QAAQ,CAACV,SAAS,CAACmE,cAAc,CAAC,CAAC;IAEnCjD,mBAAmB,CAAC,KAAK,CAAC;IAC1BP,QAAQ,CAAC,YAAY,CAAC;EACxB,CAAC;EAED,MAAMgE,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,YAAYnE,UAAU,CAACoE,EAAE,SAAS;IAC9CJ,IAAI,CAACK,QAAQ,GAAG,OAAOrE,UAAU,CAACsB,IAAI,IAAI,IAAImC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACY,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IACtFN,IAAI,CAACO,KAAK,CAAC,CAAC;EACd,CAAC;EAED,MAAMC,cAAc,GAAIpC,SAAS,IAAK;IACpC,MAAMP,SAAS,GAAG;MAChB,WAAW,EAAE,kBAAkB;MAC/B,WAAW,EAAE,oBAAoB;MACjC,WAAW,EAAE,sBAAsB;MACnC,WAAW,EAAE,uBAAuB;MACpC,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,SAAS,CAACO,SAAS,CAAC,IAAIA,SAAS;EAC1C,CAAC;EAED,MAAMqC,WAAW,GAAIC,MAAM,IAAK;IAC9B,MAAMC,KAAK,GAAG;MAAE,SAAS,EAAE,eAAe;MAAE,SAAS,EAAE;IAAe,CAAC;IACvE,OAAOA,KAAK,CAACD,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAME,eAAe,GAAIC,UAAU,IAAK;IACtC,MAAMC,SAAS,GAAG;MAAE,SAAS,EAAE,SAAS;MAAE,SAAS,EAAE;IAAa,CAAC;IACnE,OAAOA,SAAS,CAACD,UAAU,CAAC,IAAIA,UAAU;EAC5C,CAAC;EAED,MAAME,SAAS,GAAG5E,iBAAiB,CAAC6E,IAAI,CAACjE,CAAC,IAAIA,CAAC,CAACU,IAAI,KAAK,OAAO,CAAC;EACjE,MAAMwD,WAAW,GAAG9E,iBAAiB,CAAC6E,IAAI,CAACjE,CAAC,IAAIA,CAAC,CAACU,IAAI,KAAK,SAAS,CAAC;EAErE,IAAI,CAACzB,UAAU,EAAE,oBAAOV,OAAA;IAAA4F,QAAA,EAAK;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE7C,oBACEhG,OAAA,CAACzC,GAAG;IAAAqI,QAAA,gBACF5F,OAAA,CAACxC,UAAU;MAACyI,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAN,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbhG,OAAA,CAACtC,KAAK;MAACyI,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzB5F,OAAA,CAACxC,UAAU;QAACyI,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAN,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZnF,iBAAiB,CAACyF,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACnCxG,OAAA,CAAC/B,KAAK;QAEJwI,QAAQ,EAAEF,MAAM,CAACpE,IAAI,KAAK,OAAO,GAAG,OAAO,GAAGoE,MAAM,CAACpE,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAU;QAChGgE,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QACdK,IAAI,EAAEH,MAAM,CAACpE,IAAI,KAAK,OAAO,gBAAGnC,OAAA,CAACV,KAAK;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAGO,MAAM,CAACpE,IAAI,KAAK,SAAS,gBAAGnC,OAAA,CAACX,OAAO;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGhG,OAAA,CAACZ,WAAW;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAErGW,MAAM,CAACnE;MAAO,GALVoE,KAAK;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRhG,OAAA,CAACvC,IAAI;MAACkJ,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAhB,QAAA,gBACzB5F,OAAA,CAACvC,IAAI;QAACoJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAnB,QAAA,eACvB5F,OAAA,CAACtC,KAAK;UAACyI,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACzB5F,OAAA,CAACxC,UAAU;YAACyI,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAN,QAAA,EAAC;UAEtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbhG,OAAA,CAACrC,KAAK;YAACqJ,IAAI,EAAC,OAAO;YAAApB,QAAA,eACjB5F,OAAA,CAACpC,SAAS;cAAAgI,QAAA,gBACR5F,OAAA,CAACjC,QAAQ;gBAAA6H,QAAA,gBACP5F,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,eAAC5F,OAAA;oBAAA4F,QAAA,EAAQ;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5ChG,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,EAAElF,UAAU,CAACsB;gBAAI;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACXhG,OAAA,CAACjC,QAAQ;gBAAA6H,QAAA,gBACP5F,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,eAAC5F,OAAA;oBAAA4F,QAAA,EAAQ;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChDhG,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,EAAEN,eAAe,CAAC5E,UAAU,CAAC2B,QAAQ;gBAAC;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACXhG,OAAA,CAACjC,QAAQ;gBAAA6H,QAAA,gBACP5F,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,eAAC5F,OAAA;oBAAA4F,QAAA,EAAQ;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7ChG,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,EAAET,WAAW,CAACzE,UAAU,CAAC4B,KAAK;gBAAC;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACXhG,OAAA,CAACjC,QAAQ;gBAAA6H,QAAA,gBACP5F,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,eAAC5F,OAAA;oBAAA4F,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9ChG,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,eACR5F,OAAA,CAAC3B,IAAI;oBACH4I,KAAK,EAAEvG,UAAU,CAAC4D,MAAO;oBACzB4C,KAAK,EAAExG,UAAU,CAAC4D,MAAM,KAAK,OAAO,GAAG,SAAS,GAAG;kBAAU;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACXhG,OAAA,CAACjC,QAAQ;gBAAA6H,QAAA,gBACP5F,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,eAAC5F,OAAA;oBAAA4F,QAAA,EAAQ;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjDhG,OAAA,CAACnC,SAAS;kBAAA+H,QAAA,EAAE,EAAAzF,sBAAA,GAAAO,UAAU,CAAC6B,SAAS,cAAApC,sBAAA,uBAApBA,sBAAA,CAAsBqC,MAAM,KAAI;gBAAC;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEPhG,OAAA,CAACvC,IAAI;QAACoJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAnB,QAAA,eACvB5F,OAAA,CAACtC,KAAK;UAACyI,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACzB5F,OAAA,CAACxC,UAAU;YAACyI,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAN,QAAA,EAAC;UAEtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbhG,OAAA,CAACzC,GAAG;YAAC8I,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACT5F,OAAA,CAACxC,UAAU;cAACyI,OAAO,EAAC,OAAO;cAACiB,KAAK,EAAC,gBAAgB;cAAAtB,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhG,OAAA,CAACxC,UAAU;cAACyI,OAAO,EAAC,IAAI;cAACiB,KAAK,EAAC,SAAS;cAAAtB,QAAA,GACrC,EAAAxF,sBAAA,GAAAM,UAAU,CAAC6B,SAAS,cAAAnC,sBAAA,uBAApBA,sBAAA,CAAsB2D,MAAM,CAACjB,SAAS,IAAI;gBAAA,IAAAqE,sBAAA;gBACzC,MAAMC,IAAI,IAAAD,sBAAA,GAAGzG,UAAU,CAAC4C,WAAW,cAAA6D,sBAAA,uBAAtBA,sBAAA,CAAyBrE,SAAS,CAAC;gBAChD,OAAO,CAAAsE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE7D,QAAQ,MAAI6D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE5D,QAAQ;cACzC,CAAC,CAAC,CAAChB,MAAM,KAAI,CAAC,EAAC,KAAG,EAAC,EAAAnC,sBAAA,GAAAK,UAAU,CAAC6B,SAAS,cAAAlC,sBAAA,uBAApBA,sBAAA,CAAsBmC,MAAM,KAAI,CAAC;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENhG,OAAA,CAACzC,GAAG;YAAC8I,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACT5F,OAAA,CAACxC,UAAU;cAACyI,OAAO,EAAC,OAAO;cAACiB,KAAK,EAAC,gBAAgB;cAAAtB,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhG,OAAA,CAACxC,UAAU;cAACyI,OAAO,EAAC,IAAI;cAACiB,KAAK,EAAEvB,WAAW,GAAG,cAAc,GAAG,cAAe;cAAAC,QAAA,EAC3E,EAAAtF,sBAAA,GAAAI,UAAU,CAAC6B,SAAS,cAAAjC,sBAAA,uBAApBA,sBAAA,CAAsB+G,MAAM,CAAC,CAACC,KAAK,EAAExE,SAAS,KAAK;gBAAA,IAAAyE,sBAAA,EAAAC,sBAAA;gBAClD,OAAOF,KAAK,IAAI,EAAAC,sBAAA,GAAA7G,UAAU,CAAC4C,WAAW,cAAAiE,sBAAA,wBAAAC,sBAAA,GAAtBD,sBAAA,CAAyBzE,SAAS,CAAC,cAAA0E,sBAAA,uBAAnCA,sBAAA,CAAqC/D,mBAAmB,KAAI,CAAC,CAAC;cAChF,CAAC,EAAE,CAAC,CAAC,KAAI;YAAC;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPhG,OAAA,CAACtC,KAAK;MAACyI,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACnB5F,OAAA,CAACxC,UAAU;QAACyI,OAAO,EAAC,IAAI;QAACE,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,EAAC;MAEvC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,GAAAzF,sBAAA,GAEZG,UAAU,CAAC6B,SAAS,cAAAhC,sBAAA,uBAApBA,sBAAA,CAAsB+F,GAAG,CAACxD,SAAS,IAAI;QAAA,IAAA2E,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;QACtC,MAAM5E,WAAW,GAAG,EAAAmE,sBAAA,GAAA/G,UAAU,CAAC4C,WAAW,cAAAmE,sBAAA,uBAAtBA,sBAAA,CAAyB3E,SAAS,CAAC,KAAI,CAAC,CAAC;QAC7D,MAAMqF,YAAY,GAAG7E,WAAW,CAACG,mBAAmB,GAAG,CAAC;QAExD,oBACEzD,OAAA,CAAC9B,SAAS;UAAA0H,QAAA,gBACR5F,OAAA,CAAC7B,gBAAgB;YAACiK,UAAU,eAAEpI,OAAA,CAACb,UAAU;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,eAC3C5F,OAAA,CAACzC,GAAG;cAAC8K,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACC,KAAK,EAAC,MAAM;cAAA5C,QAAA,gBAC1D5F,OAAA,CAACxC,UAAU;gBAAC2I,EAAE,EAAE;kBAAEsC,QAAQ,EAAE;gBAAE,CAAE;gBAAA7C,QAAA,EAC7BV,cAAc,CAACpC,SAAS;cAAC;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACbhG,OAAA,CAAC3B,IAAI;gBACH4I,KAAK,EAAE,QAAQ3D,WAAW,CAACC,QAAQ,IAAI,SAAS,EAAG;gBACnDyD,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAE5D,WAAW,CAACC,QAAQ,GAAG,SAAS,GAAG;cAAQ;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACFhG,OAAA,CAAC3B,IAAI;gBACH4I,KAAK,EAAE,QAAQ3D,WAAW,CAACE,QAAQ,IAAI,SAAS,EAAG;gBACnDwD,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAE5D,WAAW,CAACE,QAAQ,GAAG,SAAS,GAAG;cAAQ;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,EACDmC,YAAY,iBACXnI,OAAA,CAAC3B,IAAI;gBACH4I,KAAK,EAAE,GAAG3D,WAAW,CAACG,mBAAmB,YAAa;gBACtDuD,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAC,SAAS;gBACfR,IAAI,eAAE1G,OAAA,CAACX,OAAO;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACnBhG,OAAA,CAAC5B,gBAAgB;YAAAwH,QAAA,eACf5F,OAAA,CAACvC,IAAI;cAACkJ,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAhB,QAAA,gBACzB5F,OAAA,CAACvC,IAAI;gBAACoJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAnB,QAAA,gBACvB5F,OAAA,CAACxC,UAAU;kBAACyI,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAAAN,QAAA,EAAC;gBAE7C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbhG,OAAA,CAACxC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAAAL,QAAA,gBACzB5F,OAAA;oBAAA4F,QAAA,EAAQ;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC1C,WAAW,CAACC,QAAQ,IAAI,SAAS;gBAAA;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACbhG,OAAA,CAACxC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAAAL,QAAA,gBACzB5F,OAAA;oBAAA4F,QAAA,EAAQ;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC1C,WAAW,CAACE,QAAQ,IAAI,SAAS;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,EACZ1C,WAAW,CAACoF,gBAAgB,iBAC3B1I,OAAA,CAACxC,UAAU;kBAACyI,OAAO,EAAC,SAAS;kBAACoC,OAAO,EAAC,OAAO;kBAAClC,EAAE,EAAE;oBAAEwC,EAAE,EAAE;kBAAE,CAAE;kBAAA/C,QAAA,gBAC1D5F,OAAA;oBAAA4F,QAAA,EAAQ;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC1C,WAAW,CAACoF,gBAAgB;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eAEPhG,OAAA,CAACvC,IAAI;gBAACoJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAnB,QAAA,gBACvB5F,OAAA,CAACxC,UAAU;kBAACyI,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAAAN,QAAA,GAAC,gBAC7B,EAAC,EAAA8B,qBAAA,GAAApE,WAAW,CAACsF,YAAY,cAAAlB,qBAAA,uBAAxBA,qBAAA,CAA0BlF,MAAM,KAAI,CAAC,EAAC,GACvD;gBAAA;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbhG,OAAA,CAACjB,IAAI;kBAAC8J,KAAK;kBAAAjD,QAAA,IAAA+B,sBAAA,GACRrE,WAAW,CAACsF,YAAY,cAAAjB,sBAAA,uBAAxBA,sBAAA,CAA0BmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACxC,GAAG,CAACyC,GAAG,iBAC5C/I,OAAA,CAAChB,QAAQ;oBAAcmH,EAAE,EAAE;sBAAE6C,EAAE,EAAE;oBAAE,CAAE;oBAAApD,QAAA,eACnC5F,OAAA,CAACf,YAAY;sBACXgK,OAAO,EAAEF,GAAG,CAAC5G,IAAI,KAAK,UAAU,GAAG4G,GAAG,CAACG,WAAW,GAAGH,GAAG,CAACI,YAAa;sBACtEC,SAAS,EAAEL,GAAG,CAACM;oBAAU;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC,GAJW+C,GAAG,CAACjE,EAAE;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKX,CACX,CAAC,EACD,CAAC,EAAA4B,sBAAA,GAAAtE,WAAW,CAACsF,YAAY,cAAAhB,sBAAA,uBAAxBA,sBAAA,CAA0BpF,MAAM,KAAI,CAAC,IAAI,CAAC,iBAC1CxC,OAAA,CAACxC,UAAU;oBAACyI,OAAO,EAAC,SAAS;oBAAAL,QAAA,GAAC,GAC3B,EAACtC,WAAW,CAACsF,YAAY,CAACpG,MAAM,GAAG,CAAC,EAAC,UACxC;kBAAA;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEPhG,OAAA,CAACvC,IAAI;gBAACoJ,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAnB,QAAA,gBACvB5F,OAAA,CAACxC,UAAU;kBAACyI,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAAAN,QAAA,EAAC;gBAE7C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbhG,OAAA,CAACxC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAAAL,QAAA,GAAC,SACnB,EAAC,EAAAiC,sBAAA,GAAAvE,WAAW,CAACK,SAAS,cAAAkE,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBjE,KAAK,cAAAkE,sBAAA,uBAA5BA,sBAAA,CAA8BtF,MAAM,KAAI,CAAC;gBAAA;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACbhG,OAAA,CAACxC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAAAL,QAAA,GAAC,WACjB,EAAC,EAAAmC,sBAAA,GAAAzE,WAAW,CAACK,SAAS,cAAAoE,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuBlE,EAAE,cAAAmE,sBAAA,uBAAzBA,sBAAA,CAA2BxF,MAAM,KAAI,CAAC;gBAAA;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACbhG,OAAA,CAACxC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAAAL,QAAA,GAAC,WACjB,EAAC,EAAAqC,sBAAA,GAAA3E,WAAW,CAACK,SAAS,cAAAsE,sBAAA,wBAAAC,uBAAA,GAArBD,sBAAA,CAAuBnE,YAAY,cAAAoE,uBAAA,uBAAnCA,uBAAA,CAAqC1F,MAAM,KAAI,CAAC;gBAAA;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA,GAjFLlD,SAAS;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkFd,CAAC;MAEhB,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRhG,OAAA,CAACzC,GAAG;MAAC8K,OAAO,EAAC,MAAM;MAACE,GAAG,EAAE,CAAE;MAACe,cAAc,EAAC,UAAU;MAAA1D,QAAA,gBACnD5F,OAAA,CAAChC,MAAM;QACLiI,OAAO,EAAC,UAAU;QAClBsD,SAAS,eAAEvJ,OAAA,CAACN,YAAY;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5BwD,OAAO,EAAE/E,eAAgB;QAAAmB,QAAA,EAC1B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAEThG,OAAA,CAAChC,MAAM;QACLiI,OAAO,EAAC,UAAU;QAClBsD,SAAS,eAAEvJ,OAAA,CAACJ,QAAQ;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBwD,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,IAAI,CAAC,QAAQhJ,UAAU,CAACoE,EAAE,UAAU,EAAE,QAAQ,CAAE;QAAAc,QAAA,EACvE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAEThG,OAAA,CAAChC,MAAM;QACLiI,OAAO,EAAC,WAAW;QACnBsD,SAAS,eAAEvJ,OAAA,CAACR,QAAQ;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBwD,OAAO,EAAEA,CAAA,KAAMxI,mBAAmB,CAAC,IAAI,CAAE;QACzC2I,QAAQ,EAAElE,SAAU;QACpByB,KAAK,EAAEvB,WAAW,GAAG,SAAS,GAAG,SAAU;QAAAC,QAAA,EAE1CD,WAAW,GAAG,sBAAsB,GAAG;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhG,OAAA,CAAC1B,MAAM;MAACoL,IAAI,EAAE3I,gBAAiB;MAAC6I,OAAO,EAAEA,CAAA,KAAM5I,mBAAmB,CAAC,KAAK,CAAE;MAAC6I,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAlE,QAAA,gBAChG5F,OAAA,CAACzB,WAAW;QAAAqH,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAClDhG,OAAA,CAACxB,aAAa;QAAAoH,QAAA,gBACZ5F,OAAA,CAACrB,WAAW;UAACmL,SAAS;UAACC,MAAM,EAAC,QAAQ;UAAAnE,QAAA,gBACpC5F,OAAA,CAACpB,UAAU;YAAAgH,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxChG,OAAA,CAACnB,MAAM;YACL2F,KAAK,EAAErD,gBAAiB;YACxB6I,QAAQ,EAAGC,CAAC,IAAK7I,mBAAmB,CAAC6I,CAAC,CAACC,MAAM,CAAC1F,KAAK,CAAE;YAAAoB,QAAA,EAEpD3E,SAAS,CAACqF,GAAG,CAAC6D,QAAQ,iBACrBnK,OAAA,CAAClB,QAAQ;cAAmB0F,KAAK,EAAE2F,QAAQ,CAACrF,EAAG;cAAAc,QAAA,GAC5CuE,QAAQ,CAACnI,IAAI,EAAC,KAAG,EAACmI,QAAQ,CAACC,IAAI;YAAA,GADnBD,QAAQ,CAACrF,EAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEdhG,OAAA,CAACtB,SAAS;UACRoL,SAAS;UACTO,SAAS;UACTC,IAAI,EAAE,CAAE;UACRrD,KAAK,EAAC,qBAAqB;UAC3BzC,KAAK,EAAEnD,cAAe;UACtB2I,QAAQ,EAAGC,CAAC,IAAK3I,iBAAiB,CAAC2I,CAAC,CAACC,MAAM,CAAC1F,KAAK,CAAE;UACnDuF,MAAM,EAAC,QAAQ;UACfQ,WAAW,EAAC;QAAsC;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,EAEDL,WAAW,iBACV3F,OAAA,CAAC/B,KAAK;UAACwI,QAAQ,EAAC,SAAS;UAACN,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBhG,OAAA,CAACvB,aAAa;QAAAmH,QAAA,gBACZ5F,OAAA,CAAChC,MAAM;UAACwL,OAAO,EAAEA,CAAA,KAAMxI,mBAAmB,CAAC,KAAK,CAAE;UAAA4E,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClEhG,OAAA,CAAChC,MAAM;UACLwL,OAAO,EAAExF,YAAa;UACtBiC,OAAO,EAAC,WAAW;UACnB0D,QAAQ,EAAE,CAACxI,gBAAiB;UAAAyE,QAAA,EAC7B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC9F,EAAA,CAhaID,YAAY;EAAA,QACC7C,WAAW,EACXE,WAAW,EACLD,WAAW;AAAA;AAAAmN,EAAA,GAH9BvK,YAAY;AAkalB,eAAeA,YAAY;AAAC,IAAAuK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}