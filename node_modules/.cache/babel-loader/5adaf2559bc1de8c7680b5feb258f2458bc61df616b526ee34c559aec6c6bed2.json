{"ast": null, "code": "import { WebSocketInterceptor } from \"@mswjs/interceptors/WebSocket\";\nconst webSocketInterceptor = new WebSocketInterceptor();\nexport { webSocketInterceptor };", "map": {"version": 3, "names": ["WebSocketInterceptor", "webSocketInterceptor"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/ws/webSocketInterceptor.ts"], "sourcesContent": ["import { WebSocketInterceptor } from '@mswjs/interceptors/WebSocket'\n\nexport const webSocketInterceptor = new WebSocketInterceptor()\n"], "mappings": "AAAA,SAASA,oBAAA,QAA4B;AAE9B,MAAMC,oBAAA,GAAuB,IAAID,oBAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}