{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/bia-grc/src/components/BIAWorkspace.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams, useLocation } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Box, AppBar, Toolbar, Typography, Button, Drawer, List, ListItem, ListItemButton, ListItemText, Paper, Divider } from '@mui/material';\nimport { Save as SaveIcon } from '@mui/icons-material';\nimport { setCurrentBIA, saveBIA } from '../store/biaSlice';\nimport SetupScope from './workspace/SetupScope';\nimport ProcessAnalysis from './workspace/ProcessAnalysis';\nimport ReviewSubmit from './workspace/ReviewSubmit';\nimport Scorecard from './workspace/Scorecard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DRAWER_WIDTH = 240;\nconst SCORECARD_WIDTH = 300;\nconst BIAWorkspace = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    currentBIA\n  } = useSelector(state => state.bia);\n  const [activeSection, setActiveSection] = useState('setup');\n  useEffect(() => {\n    var _location$state;\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.newBIA) {\n      dispatch(setCurrentBIA(location.state.newBIA));\n    } else {\n      // Load existing BIA from API\n      // For now, create a mock BIA\n      const mockBIA = {\n        id,\n        name: 'Sample BIA',\n        status: 'Draft',\n        owner: 'user-01',\n        function: '',\n        processes: [],\n        processData: {}\n      };\n      dispatch(setCurrentBIA(mockBIA));\n    }\n  }, [id, location.state, dispatch]);\n  const handleSave = () => {\n    if (currentBIA) {\n      dispatch(saveBIA(currentBIA));\n    }\n  };\n  const navigationItems = [{\n    id: 'setup',\n    label: 'Setup & Scope'\n  }, {\n    id: 'analysis',\n    label: 'Process Analysis'\n  }, {\n    id: 'review',\n    label: 'Review & Submit'\n  }];\n  const renderMainContent = () => {\n    switch (activeSection) {\n      case 'setup':\n        return /*#__PURE__*/_jsxDEV(SetupScope, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 16\n        }, this);\n      case 'analysis':\n        return /*#__PURE__*/_jsxDEV(ProcessAnalysis, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 16\n        }, this);\n      case 'review':\n        return /*#__PURE__*/_jsxDEV(ReviewSubmit, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(SetupScope, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  if (!currentBIA) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 27\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      height: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      sx: {\n        zIndex: theme => theme.zIndex.drawer + 1,\n        width: `calc(100% - ${SCORECARD_WIDTH}px)`\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            flexGrow: 1\n          },\n          children: [currentBIA.name, \" - \", currentBIA.status]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 24\n          }, this),\n          onClick: handleSave,\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"permanent\",\n      sx: {\n        width: DRAWER_WIDTH,\n        flexShrink: 0,\n        '& .MuiDrawer-paper': {\n          width: DRAWER_WIDTH,\n          boxSizing: 'border-box'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Toolbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              selected: activeSection === item.id,\n              onClick: () => setActiveSection(item.id),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this)\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        width: `calc(100% - ${DRAWER_WIDTH}px - ${SCORECARD_WIDTH}px)`,\n        ml: `${DRAWER_WIDTH}px`,\n        mt: '64px'\n      },\n      children: renderMainContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: SCORECARD_WIDTH,\n        position: 'fixed',\n        right: 0,\n        top: '64px',\n        height: 'calc(100vh - 64px)',\n        overflow: 'auto',\n        borderRadius: 0\n      },\n      elevation: 3,\n      children: /*#__PURE__*/_jsxDEV(Scorecard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(BIAWorkspace, \"tTEiLs17tykNa/99hzA11AT8vL8=\", false, function () {\n  return [useParams, useLocation, useDispatch, useSelector];\n});\n_c = BIAWorkspace;\nexport default BIAWorkspace;\nvar _c;\n$RefreshReg$(_c, \"BIAWorkspace\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "useLocation", "useDispatch", "useSelector", "Box", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Drawer", "List", "ListItem", "ListItemButton", "ListItemText", "Paper", "Divider", "Save", "SaveIcon", "setCurrentBIA", "saveBIA", "SetupScope", "ProcessAnalysis", "ReviewSubmit", "Scorecard", "jsxDEV", "_jsxDEV", "DRAWER_WIDTH", "SCORECARD_WIDTH", "BIAWorkspace", "_s", "id", "location", "dispatch", "currentBIA", "state", "bia", "activeSection", "setActiveSection", "_location$state", "newBIA", "mockBIA", "name", "status", "owner", "function", "processes", "processData", "handleSave", "navigationItems", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "sx", "display", "height", "position", "zIndex", "theme", "drawer", "width", "variant", "flexGrow", "color", "startIcon", "onClick", "flexShrink", "boxSizing", "overflow", "map", "item", "disablePadding", "selected", "primary", "component", "p", "ml", "mt", "right", "top", "borderRadius", "elevation", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/components/BIAWorkspace.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams, useLocation } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport {\n  Box, AppBar, Too<PERSON><PERSON>, Typo<PERSON>, But<PERSON>, Drawer, List, ListItem,\n  ListItemButton, ListItemText, Paper, Divider\n} from '@mui/material';\nimport { Save as SaveIcon } from '@mui/icons-material';\nimport { setCurrentBIA, saveBIA } from '../store/biaSlice';\nimport SetupScope from './workspace/SetupScope';\nimport ProcessAnalysis from './workspace/ProcessAnalysis';\nimport ReviewSubmit from './workspace/ReviewSubmit';\nimport Scorecard from './workspace/Scorecard';\n\nconst DRAWER_WIDTH = 240;\nconst SCORECARD_WIDTH = 300;\n\nconst BIAWorkspace = () => {\n  const { id } = useParams();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const { currentBIA } = useSelector(state => state.bia);\n  const [activeSection, setActiveSection] = useState('setup');\n\n  useEffect(() => {\n    if (location.state?.newBIA) {\n      dispatch(setCurrentBIA(location.state.newBIA));\n    } else {\n      // Load existing BIA from API\n      // For now, create a mock BIA\n      const mockBIA = {\n        id,\n        name: 'Sample BIA',\n        status: 'Draft',\n        owner: 'user-01',\n        function: '',\n        processes: [],\n        processData: {}\n      };\n      dispatch(setCurrentBIA(mockBIA));\n    }\n  }, [id, location.state, dispatch]);\n\n  const handleSave = () => {\n    if (currentBIA) {\n      dispatch(saveBIA(currentBIA));\n    }\n  };\n\n  const navigationItems = [\n    { id: 'setup', label: 'Setup & Scope' },\n    { id: 'analysis', label: 'Process Analysis' },\n    { id: 'review', label: 'Review & Submit' }\n  ];\n\n  const renderMainContent = () => {\n    switch (activeSection) {\n      case 'setup':\n        return <SetupScope />;\n      case 'analysis':\n        return <ProcessAnalysis />;\n      case 'review':\n        return <ReviewSubmit />;\n      default:\n        return <SetupScope />;\n    }\n  };\n\n  if (!currentBIA) return <div>Loading...</div>;\n\n  return (\n    <Box sx={{ display: 'flex', height: '100vh' }}>\n      {/* Top Bar */}\n      <AppBar \n        position=\"fixed\" \n        sx={{ \n          zIndex: (theme) => theme.zIndex.drawer + 1,\n          width: `calc(100% - ${SCORECARD_WIDTH}px)`\n        }}\n      >\n        <Toolbar>\n          <Typography variant=\"h6\" sx={{ flexGrow: 1 }}>\n            {currentBIA.name} - {currentBIA.status}\n          </Typography>\n          <Button\n            color=\"inherit\"\n            startIcon={<SaveIcon />}\n            onClick={handleSave}\n          >\n            Save\n          </Button>\n        </Toolbar>\n      </AppBar>\n\n      {/* Left Navigation */}\n      <Drawer\n        variant=\"permanent\"\n        sx={{\n          width: DRAWER_WIDTH,\n          flexShrink: 0,\n          '& .MuiDrawer-paper': {\n            width: DRAWER_WIDTH,\n            boxSizing: 'border-box',\n          },\n        }}\n      >\n        <Toolbar />\n        <Box sx={{ overflow: 'auto' }}>\n          <List>\n            {navigationItems.map((item) => (\n              <ListItem key={item.id} disablePadding>\n                <ListItemButton\n                  selected={activeSection === item.id}\n                  onClick={() => setActiveSection(item.id)}\n                >\n                  <ListItemText primary={item.label} />\n                </ListItemButton>\n              </ListItem>\n            ))}\n          </List>\n        </Box>\n      </Drawer>\n\n      {/* Main Content */}\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: `calc(100% - ${DRAWER_WIDTH}px - ${SCORECARD_WIDTH}px)`,\n          ml: `${DRAWER_WIDTH}px`,\n          mt: '64px'\n        }}\n      >\n        {renderMainContent()}\n      </Box>\n\n      {/* Right Scorecard */}\n      <Paper\n        sx={{\n          width: SCORECARD_WIDTH,\n          position: 'fixed',\n          right: 0,\n          top: '64px',\n          height: 'calc(100vh - 64px)',\n          overflow: 'auto',\n          borderRadius: 0\n        }}\n        elevation={3}\n      >\n        <Scorecard />\n      </Paper>\n    </Box>\n  );\n};\n\nexport default BIAWorkspace;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAChEC,cAAc,EAAEC,YAAY,EAAEC,KAAK,EAAEC,OAAO,QACvC,eAAe;AACtB,SAASC,IAAI,IAAIC,QAAQ,QAAQ,qBAAqB;AACtD,SAASC,aAAa,EAAEC,OAAO,QAAQ,mBAAmB;AAC1D,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,SAAS,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,YAAY,GAAG,GAAG;AACxB,MAAMC,eAAe,GAAG,GAAG;AAE3B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAG,CAAC,GAAG9B,SAAS,CAAC,CAAC;EAC1B,MAAM+B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM+B,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+B;EAAW,CAAC,GAAG9B,WAAW,CAAC+B,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EACtD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,OAAO,CAAC;EAE3DD,SAAS,CAAC,MAAM;IAAA,IAAAwC,eAAA;IACd,KAAAA,eAAA,GAAIP,QAAQ,CAACG,KAAK,cAAAI,eAAA,eAAdA,eAAA,CAAgBC,MAAM,EAAE;MAC1BP,QAAQ,CAACd,aAAa,CAACa,QAAQ,CAACG,KAAK,CAACK,MAAM,CAAC,CAAC;IAChD,CAAC,MAAM;MACL;MACA;MACA,MAAMC,OAAO,GAAG;QACdV,EAAE;QACFW,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,CAAC;MAChB,CAAC;MACDd,QAAQ,CAACd,aAAa,CAACsB,OAAO,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,CAACV,EAAE,EAAEC,QAAQ,CAACG,KAAK,EAAEF,QAAQ,CAAC,CAAC;EAElC,MAAMe,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAId,UAAU,EAAE;MACdD,QAAQ,CAACb,OAAO,CAACc,UAAU,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMe,eAAe,GAAG,CACtB;IAAElB,EAAE,EAAE,OAAO;IAAEmB,KAAK,EAAE;EAAgB,CAAC,EACvC;IAAEnB,EAAE,EAAE,UAAU;IAAEmB,KAAK,EAAE;EAAmB,CAAC,EAC7C;IAAEnB,EAAE,EAAE,QAAQ;IAAEmB,KAAK,EAAE;EAAkB,CAAC,CAC3C;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQd,aAAa;MACnB,KAAK,OAAO;QACV,oBAAOX,OAAA,CAACL,UAAU;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvB,KAAK,UAAU;QACb,oBAAO7B,OAAA,CAACJ,eAAe;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5B,KAAK,QAAQ;QACX,oBAAO7B,OAAA,CAACH,YAAY;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB;QACE,oBAAO7B,OAAA,CAACL,UAAU;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACzB;EACF,CAAC;EAED,IAAI,CAACrB,UAAU,EAAE,oBAAOR,OAAA;IAAA8B,QAAA,EAAK;EAAU;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE7C,oBACE7B,OAAA,CAACrB,GAAG;IAACoD,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAH,QAAA,gBAE5C9B,OAAA,CAACpB,MAAM;MACLsD,QAAQ,EAAC,OAAO;MAChBH,EAAE,EAAE;QACFI,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAACE,MAAM,GAAG,CAAC;QAC1CC,KAAK,EAAE,eAAepC,eAAe;MACvC,CAAE;MAAA4B,QAAA,eAEF9B,OAAA,CAACnB,OAAO;QAAAiD,QAAA,gBACN9B,OAAA,CAAClB,UAAU;UAACyD,OAAO,EAAC,IAAI;UAACR,EAAE,EAAE;YAAES,QAAQ,EAAE;UAAE,CAAE;UAAAV,QAAA,GAC1CtB,UAAU,CAACQ,IAAI,EAAC,KAAG,EAACR,UAAU,CAACS,MAAM;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACb7B,OAAA,CAACjB,MAAM;UACL0D,KAAK,EAAC,SAAS;UACfC,SAAS,eAAE1C,OAAA,CAACR,QAAQ;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBc,OAAO,EAAErB,UAAW;UAAAQ,QAAA,EACrB;QAED;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGT7B,OAAA,CAAChB,MAAM;MACLuD,OAAO,EAAC,WAAW;MACnBR,EAAE,EAAE;QACFO,KAAK,EAAErC,YAAY;QACnB2C,UAAU,EAAE,CAAC;QACb,oBAAoB,EAAE;UACpBN,KAAK,EAAErC,YAAY;UACnB4C,SAAS,EAAE;QACb;MACF,CAAE;MAAAf,QAAA,gBAEF9B,OAAA,CAACnB,OAAO;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACX7B,OAAA,CAACrB,GAAG;QAACoD,EAAE,EAAE;UAAEe,QAAQ,EAAE;QAAO,CAAE;QAAAhB,QAAA,eAC5B9B,OAAA,CAACf,IAAI;UAAA6C,QAAA,EACFP,eAAe,CAACwB,GAAG,CAAEC,IAAI,iBACxBhD,OAAA,CAACd,QAAQ;YAAe+D,cAAc;YAAAnB,QAAA,eACpC9B,OAAA,CAACb,cAAc;cACb+D,QAAQ,EAAEvC,aAAa,KAAKqC,IAAI,CAAC3C,EAAG;cACpCsC,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAACoC,IAAI,CAAC3C,EAAE,CAAE;cAAAyB,QAAA,eAEzC9B,OAAA,CAACZ,YAAY;gBAAC+D,OAAO,EAAEH,IAAI,CAACxB;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC,GANJmB,IAAI,CAAC3C,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOZ,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT7B,OAAA,CAACrB,GAAG;MACFyE,SAAS,EAAC,MAAM;MAChBrB,EAAE,EAAE;QACFS,QAAQ,EAAE,CAAC;QACXa,CAAC,EAAE,CAAC;QACJf,KAAK,EAAE,eAAerC,YAAY,QAAQC,eAAe,KAAK;QAC9DoD,EAAE,EAAE,GAAGrD,YAAY,IAAI;QACvBsD,EAAE,EAAE;MACN,CAAE;MAAAzB,QAAA,EAEDL,iBAAiB,CAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGN7B,OAAA,CAACX,KAAK;MACJ0C,EAAE,EAAE;QACFO,KAAK,EAAEpC,eAAe;QACtBgC,QAAQ,EAAE,OAAO;QACjBsB,KAAK,EAAE,CAAC;QACRC,GAAG,EAAE,MAAM;QACXxB,MAAM,EAAE,oBAAoB;QAC5Ba,QAAQ,EAAE,MAAM;QAChBY,YAAY,EAAE;MAChB,CAAE;MACFC,SAAS,EAAE,CAAE;MAAA7B,QAAA,eAEb9B,OAAA,CAACF,SAAS;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACzB,EAAA,CAzIID,YAAY;EAAA,QACD5B,SAAS,EACPC,WAAW,EACXC,WAAW,EACLC,WAAW;AAAA;AAAAkF,EAAA,GAJ9BzD,YAAY;AA2IlB,eAAeA,YAAY;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}