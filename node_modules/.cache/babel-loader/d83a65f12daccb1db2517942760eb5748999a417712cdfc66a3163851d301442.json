{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nconst MAX_LISTS_DEPTH = 3;\nexport function MaxIntrospectionDepthRule(context) {\n  /**\n   * Counts the depth of list fields in \"__Type\" recursively and\n   * returns `true` if the limit has been reached.\n   */\n  function checkDepth(node, visitedFragments = Object.create(null), depth = 0) {\n    if (node.kind === Kind.FRAGMENT_SPREAD) {\n      const fragmentName = node.name.value;\n      if (visitedFragments[fragmentName] === true) {\n        // Fragment cycles are handled by `NoFragmentCyclesRule`.\n        return false;\n      }\n      const fragment = context.getFragment(fragmentName);\n      if (!fragment) {\n        // Missing fragments checks are handled by `KnownFragmentNamesRule`.\n        return false;\n      } // Rather than following an immutable programming pattern which has\n      // significant memory and garbage collection overhead, we've opted to\n      // take a mutable approach for efficiency's sake. Importantly visiting a\n      // fragment twice is fine, so long as you don't do one visit inside the\n      // other.\n\n      try {\n        visitedFragments[fragmentName] = true;\n        return checkDepth(fragment, visitedFragments, depth);\n      } finally {\n        visitedFragments[fragmentName] = undefined;\n      }\n    }\n    if (node.kind === Kind.FIELD && (\n    // check all introspection lists\n    node.name.value === 'fields' || node.name.value === 'interfaces' || node.name.value === 'possibleTypes' || node.name.value === 'inputFields')) {\n      // eslint-disable-next-line no-param-reassign\n      depth++;\n      if (depth >= MAX_LISTS_DEPTH) {\n        return true;\n      }\n    } // handles fields and inline fragments\n\n    if ('selectionSet' in node && node.selectionSet) {\n      for (const child of node.selectionSet.selections) {\n        if (checkDepth(child, visitedFragments, depth)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  return {\n    Field(node) {\n      if (node.name.value === '__schema' || node.name.value === '__type') {\n        if (checkDepth(node)) {\n          context.reportError(new GraphQLError('Maximum introspection depth exceeded', {\n            nodes: [node]\n          }));\n          return false;\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "Kind", "MAX_LISTS_DEPTH", "MaxIntrospectionDepthRule", "context", "checkDepth", "node", "visitedFragments", "Object", "create", "depth", "kind", "FRAGMENT_SPREAD", "fragmentName", "name", "value", "fragment", "getFragment", "undefined", "FIELD", "selectionSet", "child", "selections", "Field", "reportError", "nodes"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/MaxIntrospectionDepthRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nconst MAX_LISTS_DEPTH = 3;\nexport function MaxIntrospectionDepthRule(context) {\n  /**\n   * Counts the depth of list fields in \"__Type\" recursively and\n   * returns `true` if the limit has been reached.\n   */\n  function checkDepth(node, visitedFragments = Object.create(null), depth = 0) {\n    if (node.kind === Kind.FRAGMENT_SPREAD) {\n      const fragmentName = node.name.value;\n\n      if (visitedFragments[fragmentName] === true) {\n        // Fragment cycles are handled by `NoFragmentCyclesRule`.\n        return false;\n      }\n\n      const fragment = context.getFragment(fragmentName);\n\n      if (!fragment) {\n        // Missing fragments checks are handled by `KnownFragmentNamesRule`.\n        return false;\n      } // Rather than following an immutable programming pattern which has\n      // significant memory and garbage collection overhead, we've opted to\n      // take a mutable approach for efficiency's sake. Importantly visiting a\n      // fragment twice is fine, so long as you don't do one visit inside the\n      // other.\n\n      try {\n        visitedFragments[fragmentName] = true;\n        return checkDepth(fragment, visitedFragments, depth);\n      } finally {\n        visitedFragments[fragmentName] = undefined;\n      }\n    }\n\n    if (\n      node.kind === Kind.FIELD && // check all introspection lists\n      (node.name.value === 'fields' ||\n        node.name.value === 'interfaces' ||\n        node.name.value === 'possibleTypes' ||\n        node.name.value === 'inputFields')\n    ) {\n      // eslint-disable-next-line no-param-reassign\n      depth++;\n\n      if (depth >= MAX_LISTS_DEPTH) {\n        return true;\n      }\n    } // handles fields and inline fragments\n\n    if ('selectionSet' in node && node.selectionSet) {\n      for (const child of node.selectionSet.selections) {\n        if (checkDepth(child, visitedFragments, depth)) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  }\n\n  return {\n    Field(node) {\n      if (node.name.value === '__schema' || node.name.value === '__type') {\n        if (checkDepth(node)) {\n          context.reportError(\n            new GraphQLError('Maximum introspection depth exceeded', {\n              nodes: [node],\n            }),\n          );\n          return false;\n        }\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,MAAMC,eAAe,GAAG,CAAC;AACzB,OAAO,SAASC,yBAAyBA,CAACC,OAAO,EAAE;EACjD;AACF;AACA;AACA;EACE,SAASC,UAAUA,CAACC,IAAI,EAAEC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAE;IAC3E,IAAIJ,IAAI,CAACK,IAAI,KAAKV,IAAI,CAACW,eAAe,EAAE;MACtC,MAAMC,YAAY,GAAGP,IAAI,CAACQ,IAAI,CAACC,KAAK;MAEpC,IAAIR,gBAAgB,CAACM,YAAY,CAAC,KAAK,IAAI,EAAE;QAC3C;QACA,OAAO,KAAK;MACd;MAEA,MAAMG,QAAQ,GAAGZ,OAAO,CAACa,WAAW,CAACJ,YAAY,CAAC;MAElD,IAAI,CAACG,QAAQ,EAAE;QACb;QACA,OAAO,KAAK;MACd,CAAC,CAAC;MACF;MACA;MACA;MACA;;MAEA,IAAI;QACFT,gBAAgB,CAACM,YAAY,CAAC,GAAG,IAAI;QACrC,OAAOR,UAAU,CAACW,QAAQ,EAAET,gBAAgB,EAAEG,KAAK,CAAC;MACtD,CAAC,SAAS;QACRH,gBAAgB,CAACM,YAAY,CAAC,GAAGK,SAAS;MAC5C;IACF;IAEA,IACEZ,IAAI,CAACK,IAAI,KAAKV,IAAI,CAACkB,KAAK;IAAI;IAC3Bb,IAAI,CAACQ,IAAI,CAACC,KAAK,KAAK,QAAQ,IAC3BT,IAAI,CAACQ,IAAI,CAACC,KAAK,KAAK,YAAY,IAChCT,IAAI,CAACQ,IAAI,CAACC,KAAK,KAAK,eAAe,IACnCT,IAAI,CAACQ,IAAI,CAACC,KAAK,KAAK,aAAa,CAAC,EACpC;MACA;MACAL,KAAK,EAAE;MAEP,IAAIA,KAAK,IAAIR,eAAe,EAAE;QAC5B,OAAO,IAAI;MACb;IACF,CAAC,CAAC;;IAEF,IAAI,cAAc,IAAII,IAAI,IAAIA,IAAI,CAACc,YAAY,EAAE;MAC/C,KAAK,MAAMC,KAAK,IAAIf,IAAI,CAACc,YAAY,CAACE,UAAU,EAAE;QAChD,IAAIjB,UAAU,CAACgB,KAAK,EAAEd,gBAAgB,EAAEG,KAAK,CAAC,EAAE;UAC9C,OAAO,IAAI;QACb;MACF;IACF;IAEA,OAAO,KAAK;EACd;EAEA,OAAO;IACLa,KAAKA,CAACjB,IAAI,EAAE;MACV,IAAIA,IAAI,CAACQ,IAAI,CAACC,KAAK,KAAK,UAAU,IAAIT,IAAI,CAACQ,IAAI,CAACC,KAAK,KAAK,QAAQ,EAAE;QAClE,IAAIV,UAAU,CAACC,IAAI,CAAC,EAAE;UACpBF,OAAO,CAACoB,WAAW,CACjB,IAAIxB,YAAY,CAAC,sCAAsC,EAAE;YACvDyB,KAAK,EAAE,CAACnB,IAAI;UACd,CAAC,CACH,CAAC;UACD,OAAO,KAAK;QACd;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}