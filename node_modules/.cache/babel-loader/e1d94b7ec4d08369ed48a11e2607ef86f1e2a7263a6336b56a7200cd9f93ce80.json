{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nvar _span;\nconst _excluded = [\"aria-describedby\", \"aria-label\", \"autoFocus\", \"autoWidth\", \"children\", \"className\", \"defaultOpen\", \"defaultValue\", \"disabled\", \"displayEmpty\", \"error\", \"IconComponent\", \"inputRef\", \"labelId\", \"MenuProps\", \"multiple\", \"name\", \"onBlur\", \"onChange\", \"onClose\", \"onFocus\", \"onOpen\", \"open\", \"readOnly\", \"renderValue\", \"SelectDisplayProps\", \"tabIndex\", \"type\", \"value\", \"variant\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport refType from '@mui/utils/refType';\nimport ownerDocument from '../utils/ownerDocument';\nimport capitalize from '../utils/capitalize';\nimport Menu from '../Menu/Menu';\nimport { nativeSelectSelectStyles, nativeSelectIconStyles } from '../NativeSelect/NativeSelectInput';\nimport { isFilled } from '../InputBase/utils';\nimport styled, { slotShouldForwardProp } from '../styles/styled';\nimport useForkRef from '../utils/useForkRef';\nimport useControlled from '../utils/useControlled';\nimport selectClasses, { getSelectUtilityClasses } from './selectClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SelectSelect = styled('div', {\n  name: 'MuiSelect',\n  slot: 'Select',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [\n    // Win specificity over the input base\n    {\n      [`&.${selectClasses.select}`]: styles.select\n    }, {\n      [`&.${selectClasses.select}`]: styles[ownerState.variant]\n    }, {\n      [`&.${selectClasses.error}`]: styles.error\n    }, {\n      [`&.${selectClasses.multiple}`]: styles.multiple\n    }];\n  }\n})(nativeSelectSelectStyles, {\n  // Win specificity over the input base\n  [`&.${selectClasses.select}`]: {\n    height: 'auto',\n    // Resets for multiple select with chips\n    minHeight: '1.4375em',\n    // Required for select\\text-field height consistency\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden'\n  }\n});\nconst SelectIcon = styled('svg', {\n  name: 'MuiSelect',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.variant && styles[`icon${capitalize(ownerState.variant)}`], ownerState.open && styles.iconOpen];\n  }\n})(nativeSelectIconStyles);\nconst SelectNativeInput = styled('input', {\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'classes',\n  name: 'MuiSelect',\n  slot: 'NativeInput',\n  overridesResolver: (props, styles) => styles.nativeInput\n})({\n  bottom: 0,\n  left: 0,\n  position: 'absolute',\n  opacity: 0,\n  pointerEvents: 'none',\n  width: '100%',\n  boxSizing: 'border-box'\n});\nfunction areEqualValues(a, b) {\n  if (typeof b === 'object' && b !== null) {\n    return a === b;\n  }\n\n  // The value could be a number, the DOM will stringify it anyway.\n  return String(a) === String(b);\n}\nfunction isEmpty(display) {\n  return display == null || typeof display === 'string' && !display.trim();\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    disabled,\n    multiple,\n    open,\n    error\n  } = ownerState;\n  const slots = {\n    select: ['select', variant, disabled && 'disabled', multiple && 'multiple', error && 'error'],\n    icon: ['icon', `icon${capitalize(variant)}`, open && 'iconOpen', disabled && 'disabled'],\n    nativeInput: ['nativeInput']\n  };\n  return composeClasses(slots, getSelectUtilityClasses, classes);\n};\n\n/**\n * @ignore - internal component.\n */\nconst SelectInput = /*#__PURE__*/React.forwardRef(function SelectInput(props, ref) {\n  var _MenuProps$slotProps;\n  const {\n      'aria-describedby': ariaDescribedby,\n      'aria-label': ariaLabel,\n      autoFocus,\n      autoWidth,\n      children,\n      className,\n      defaultOpen,\n      defaultValue,\n      disabled,\n      displayEmpty,\n      error = false,\n      IconComponent,\n      inputRef: inputRefProp,\n      labelId,\n      MenuProps = {},\n      multiple,\n      name,\n      onBlur,\n      onChange,\n      onClose,\n      onFocus,\n      onOpen,\n      open: openProp,\n      readOnly,\n      renderValue,\n      SelectDisplayProps = {},\n      tabIndex: tabIndexProp\n      // catching `type` from Input which makes no sense for SelectInput\n      ,\n\n      value: valueProp,\n      variant = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Select'\n  });\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: defaultOpen,\n    name: 'Select'\n  });\n  const inputRef = React.useRef(null);\n  const displayRef = React.useRef(null);\n  const [displayNode, setDisplayNode] = React.useState(null);\n  const {\n    current: isOpenControlled\n  } = React.useRef(openProp != null);\n  const [menuMinWidthState, setMenuMinWidthState] = React.useState();\n  const handleRef = useForkRef(ref, inputRefProp);\n  const handleDisplayRef = React.useCallback(node => {\n    displayRef.current = node;\n    if (node) {\n      setDisplayNode(node);\n    }\n  }, []);\n  const anchorElement = displayNode == null ? void 0 : displayNode.parentNode;\n  React.useImperativeHandle(handleRef, () => ({\n    focus: () => {\n      displayRef.current.focus();\n    },\n    node: inputRef.current,\n    value\n  }), [value]);\n\n  // Resize menu on `defaultOpen` automatic toggle.\n  React.useEffect(() => {\n    if (defaultOpen && openState && displayNode && !isOpenControlled) {\n      setMenuMinWidthState(autoWidth ? null : anchorElement.clientWidth);\n      displayRef.current.focus();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [displayNode, autoWidth]);\n  // `isOpenControlled` is ignored because the component should never switch between controlled and uncontrolled modes.\n  // `defaultOpen` and `openState` are ignored to avoid unnecessary callbacks.\n  React.useEffect(() => {\n    if (autoFocus) {\n      displayRef.current.focus();\n    }\n  }, [autoFocus]);\n  React.useEffect(() => {\n    if (!labelId) {\n      return undefined;\n    }\n    const label = ownerDocument(displayRef.current).getElementById(labelId);\n    if (label) {\n      const handler = () => {\n        if (getSelection().isCollapsed) {\n          displayRef.current.focus();\n        }\n      };\n      label.addEventListener('click', handler);\n      return () => {\n        label.removeEventListener('click', handler);\n      };\n    }\n    return undefined;\n  }, [labelId]);\n  const update = (open, event) => {\n    if (open) {\n      if (onOpen) {\n        onOpen(event);\n      }\n    } else if (onClose) {\n      onClose(event);\n    }\n    if (!isOpenControlled) {\n      setMenuMinWidthState(autoWidth ? null : anchorElement.clientWidth);\n      setOpenState(open);\n    }\n  };\n  const handleMouseDown = event => {\n    // Ignore everything but left-click\n    if (event.button !== 0) {\n      return;\n    }\n    // Hijack the default focus behavior.\n    event.preventDefault();\n    displayRef.current.focus();\n    update(true, event);\n  };\n  const handleClose = event => {\n    update(false, event);\n  };\n  const childrenArray = React.Children.toArray(children);\n\n  // Support autofill.\n  const handleChange = event => {\n    const child = childrenArray.find(childItem => childItem.props.value === event.target.value);\n    if (child === undefined) {\n      return;\n    }\n    setValueState(child.props.value);\n    if (onChange) {\n      onChange(event, child);\n    }\n  };\n  const handleItemClick = child => event => {\n    let newValue;\n\n    // We use the tabindex attribute to signal the available options.\n    if (!event.currentTarget.hasAttribute('tabindex')) {\n      return;\n    }\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      const itemIndex = value.indexOf(child.props.value);\n      if (itemIndex === -1) {\n        newValue.push(child.props.value);\n      } else {\n        newValue.splice(itemIndex, 1);\n      }\n    } else {\n      newValue = child.props.value;\n    }\n    if (child.props.onClick) {\n      child.props.onClick(event);\n    }\n    if (value !== newValue) {\n      setValueState(newValue);\n      if (onChange) {\n        // Redefine target to allow name and value to be read.\n        // This allows seamless integration with the most popular form libraries.\n        // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n        // Clone the event to not override `target` of the original event.\n        const nativeEvent = event.nativeEvent || event;\n        const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n        Object.defineProperty(clonedEvent, 'target', {\n          writable: true,\n          value: {\n            value: newValue,\n            name\n          }\n        });\n        onChange(clonedEvent, child);\n      }\n    }\n    if (!multiple) {\n      update(false, event);\n    }\n  };\n  const handleKeyDown = event => {\n    if (!readOnly) {\n      const validKeys = [' ', 'ArrowUp', 'ArrowDown',\n      // The native select doesn't respond to enter on macOS, but it's recommended by\n      // https://www.w3.org/WAI/ARIA/apg/patterns/combobox/examples/combobox-select-only/\n      'Enter'];\n      if (validKeys.indexOf(event.key) !== -1) {\n        event.preventDefault();\n        update(true, event);\n      }\n    }\n  };\n  const open = displayNode !== null && openState;\n  const handleBlur = event => {\n    // if open event.stopImmediatePropagation\n    if (!open && onBlur) {\n      // Preact support, target is read only property on a native event.\n      Object.defineProperty(event, 'target', {\n        writable: true,\n        value: {\n          value,\n          name\n        }\n      });\n      onBlur(event);\n    }\n  };\n  delete other['aria-invalid'];\n  let display;\n  let displaySingle;\n  const displayMultiple = [];\n  let computeDisplay = false;\n  let foundMatch = false;\n\n  // No need to display any value if the field is empty.\n  if (isFilled({\n    value\n  }) || displayEmpty) {\n    if (renderValue) {\n      display = renderValue(value);\n    } else {\n      computeDisplay = true;\n    }\n  }\n  const items = childrenArray.map(child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Select component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    let selected;\n    if (multiple) {\n      if (!Array.isArray(value)) {\n        throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The \\`value\\` prop must be an array when using the \\`Select\\` component with \\`multiple\\`.` : _formatMuiErrorMessage(2));\n      }\n      selected = value.some(v => areEqualValues(v, child.props.value));\n      if (selected && computeDisplay) {\n        displayMultiple.push(child.props.children);\n      }\n    } else {\n      selected = areEqualValues(value, child.props.value);\n      if (selected && computeDisplay) {\n        displaySingle = child.props.children;\n      }\n    }\n    if (selected) {\n      foundMatch = true;\n    }\n    return /*#__PURE__*/React.cloneElement(child, {\n      'aria-selected': selected ? 'true' : 'false',\n      onClick: handleItemClick(child),\n      onKeyUp: event => {\n        if (event.key === ' ') {\n          // otherwise our MenuItems dispatches a click event\n          // it's not behavior of the native <option> and causes\n          // the select to close immediately since we open on space keydown\n          event.preventDefault();\n        }\n        if (child.props.onKeyUp) {\n          child.props.onKeyUp(event);\n        }\n      },\n      role: 'option',\n      selected,\n      value: undefined,\n      // The value is most likely not a valid HTML attribute.\n      'data-value': child.props.value // Instead, we provide it as a data attribute.\n    });\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!foundMatch && !multiple && value !== '') {\n        const values = childrenArray.map(child => child.props.value);\n        console.warn([`MUI: You have provided an out-of-range value \\`${value}\\` for the select ${name ? `(name=\"${name}\") ` : ''}component.`, \"Consider providing a value that matches one of the available options or ''.\", `The available values are ${values.filter(x => x != null).map(x => `\\`${x}\\``).join(', ') || '\"\"'}.`].join('\\n'));\n      }\n    }, [foundMatch, childrenArray, multiple, name, value]);\n  }\n  if (computeDisplay) {\n    if (multiple) {\n      if (displayMultiple.length === 0) {\n        display = null;\n      } else {\n        display = displayMultiple.reduce((output, child, index) => {\n          output.push(child);\n          if (index < displayMultiple.length - 1) {\n            output.push(', ');\n          }\n          return output;\n        }, []);\n      }\n    } else {\n      display = displaySingle;\n    }\n  }\n\n  // Avoid performing a layout computation in the render method.\n  let menuMinWidth = menuMinWidthState;\n  if (!autoWidth && isOpenControlled && displayNode) {\n    menuMinWidth = anchorElement.clientWidth;\n  }\n  let tabIndex;\n  if (typeof tabIndexProp !== 'undefined') {\n    tabIndex = tabIndexProp;\n  } else {\n    tabIndex = disabled ? null : 0;\n  }\n  const buttonId = SelectDisplayProps.id || (name ? `mui-component-select-${name}` : undefined);\n  const ownerState = _extends({}, props, {\n    variant,\n    value,\n    open,\n    error\n  });\n  const classes = useUtilityClasses(ownerState);\n  const paperProps = _extends({}, MenuProps.PaperProps, (_MenuProps$slotProps = MenuProps.slotProps) == null ? void 0 : _MenuProps$slotProps.paper);\n  const listboxId = useId();\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(SelectSelect, _extends({\n      ref: handleDisplayRef,\n      tabIndex: tabIndex,\n      role: \"combobox\",\n      \"aria-controls\": listboxId,\n      \"aria-disabled\": disabled ? 'true' : undefined,\n      \"aria-expanded\": open ? 'true' : 'false',\n      \"aria-haspopup\": \"listbox\",\n      \"aria-label\": ariaLabel,\n      \"aria-labelledby\": [labelId, buttonId].filter(Boolean).join(' ') || undefined,\n      \"aria-describedby\": ariaDescribedby,\n      onKeyDown: handleKeyDown,\n      onMouseDown: disabled || readOnly ? null : handleMouseDown,\n      onBlur: handleBlur,\n      onFocus: onFocus\n    }, SelectDisplayProps, {\n      ownerState: ownerState,\n      className: clsx(SelectDisplayProps.className, classes.select, className)\n      // The id is required for proper a11y\n      ,\n\n      id: buttonId,\n      children: isEmpty(display) ?\n      // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        children: \"\\u200B\"\n      })) : display\n    })), /*#__PURE__*/_jsx(SelectNativeInput, _extends({\n      \"aria-invalid\": error,\n      value: Array.isArray(value) ? value.join(',') : value,\n      name: name,\n      ref: inputRef,\n      \"aria-hidden\": true,\n      onChange: handleChange,\n      tabIndex: -1,\n      disabled: disabled,\n      className: classes.nativeInput,\n      autoFocus: autoFocus,\n      ownerState: ownerState\n    }, other)), /*#__PURE__*/_jsx(SelectIcon, {\n      as: IconComponent,\n      className: classes.icon,\n      ownerState: ownerState\n    }), /*#__PURE__*/_jsx(Menu, _extends({\n      id: `menu-${name || ''}`,\n      anchorEl: anchorElement,\n      open: open,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      transformOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      }\n    }, MenuProps, {\n      MenuListProps: _extends({\n        'aria-labelledby': labelId,\n        role: 'listbox',\n        'aria-multiselectable': multiple ? 'true' : undefined,\n        disableListWrap: true,\n        id: listboxId\n      }, MenuProps.MenuListProps),\n      slotProps: _extends({}, MenuProps.slotProps, {\n        paper: _extends({}, paperProps, {\n          style: _extends({\n            minWidth: menuMinWidth\n          }, paperProps != null ? paperProps.style : null)\n        })\n      }),\n      children: items\n    }))]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SelectInput.propTypes = {\n  /**\n   * @ignore\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * @ignore\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * @ignore\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * If `true`, the width of the popover will automatically be set according to the items inside the\n   * menu, otherwise it will be at least the width of the select input.\n   */\n  autoWidth: PropTypes.bool,\n  /**\n   * The option elements to populate the select with.\n   * Can be some `<MenuItem>` elements.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The CSS class name of the select element.\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the component is toggled on mount. Use when the component open state is not controlled.\n   * You can only use it when the `native` prop is `false` (default).\n   */\n  defaultOpen: PropTypes.bool,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the select is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the selected item is displayed even if its value is empty.\n   */\n  displayEmpty: PropTypes.bool,\n  /**\n   * If `true`, the `select input` will indicate an error.\n   */\n  error: PropTypes.bool,\n  /**\n   * The icon that displays the arrow.\n   */\n  IconComponent: PropTypes.elementType.isRequired,\n  /**\n   * Imperative handle implementing `{ value: T, node: HTMLElement, focus(): void }`\n   * Equivalent to `ref`\n   */\n  inputRef: refType,\n  /**\n   * The ID of an element that acts as an additional label. The Select will\n   * be labelled by the additional label and the selected value.\n   */\n  labelId: PropTypes.string,\n  /**\n   * Props applied to the [`Menu`](/material-ui/api/menu/) element.\n   */\n  MenuProps: PropTypes.object,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Name attribute of the `select` or hidden `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when a menu item is selected.\n   *\n   * @param {object} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (any).\n   * @param {object} [child] The react element that was selected.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {object} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {object} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the selected value.\n   *\n   * @param {any} value The `value` provided to the component.\n   * @returns {ReactNode}\n   */\n  renderValue: PropTypes.func,\n  /**\n   * Props applied to the clickable div element.\n   */\n  SelectDisplayProps: PropTypes.object,\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  type: PropTypes.any,\n  /**\n   * The input value.\n   */\n  value: PropTypes.any,\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes.oneOf(['standard', 'outlined', 'filled'])\n} : void 0;\nexport default SelectInput;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_formatMuiErrorMessage", "_span", "_excluded", "React", "isFragment", "PropTypes", "clsx", "composeClasses", "useId", "refType", "ownerDocument", "capitalize", "<PERSON><PERSON>", "nativeSelectSelectStyles", "nativeSelectIconStyles", "isFilled", "styled", "slotShouldForwardProp", "useForkRef", "useControlled", "selectClasses", "getSelectUtilityClasses", "jsx", "_jsx", "jsxs", "_jsxs", "SelectSelect", "name", "slot", "overridesResolver", "props", "styles", "ownerState", "select", "variant", "error", "multiple", "height", "minHeight", "textOverflow", "whiteSpace", "overflow", "SelectIcon", "icon", "open", "iconOpen", "SelectNativeInput", "shouldForwardProp", "prop", "nativeInput", "bottom", "left", "position", "opacity", "pointerEvents", "width", "boxSizing", "areEqualValues", "a", "b", "String", "isEmpty", "display", "trim", "useUtilityClasses", "classes", "disabled", "slots", "SelectInput", "forwardRef", "ref", "_MenuProps$slotProps", "aria<PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "autoFocus", "autoWidth", "children", "className", "defaultOpen", "defaultValue", "displayEmpty", "IconComponent", "inputRef", "inputRefProp", "labelId", "MenuProps", "onBlur", "onChange", "onClose", "onFocus", "onOpen", "openProp", "readOnly", "renderValue", "SelectDisplayProps", "tabIndex", "tabIndexProp", "value", "valueProp", "other", "setValueState", "controlled", "default", "openState", "setOpenState", "useRef", "displayRef", "displayNode", "setDisplayNode", "useState", "current", "isOpenControlled", "menuMinWidthState", "setMenuMinWidthState", "handleRef", "handleDisplayRef", "useCallback", "node", "anchorElement", "parentNode", "useImperativeHandle", "focus", "useEffect", "clientWidth", "undefined", "label", "getElementById", "handler", "getSelection", "isCollapsed", "addEventListener", "removeEventListener", "update", "event", "handleMouseDown", "button", "preventDefault", "handleClose", "childrenA<PERSON>y", "Children", "toArray", "handleChange", "child", "find", "childItem", "target", "handleItemClick", "newValue", "currentTarget", "hasAttribute", "Array", "isArray", "slice", "itemIndex", "indexOf", "push", "splice", "onClick", "nativeEvent", "clonedEvent", "constructor", "type", "Object", "defineProperty", "writable", "handleKeyDown", "validKeys", "key", "handleBlur", "displaySingle", "displayMultiple", "computeDisplay", "foundMatch", "items", "map", "isValidElement", "process", "env", "NODE_ENV", "console", "join", "selected", "Error", "some", "v", "cloneElement", "onKeyUp", "role", "values", "warn", "filter", "x", "length", "reduce", "output", "index", "menu<PERSON>in<PERSON>idth", "buttonId", "id", "paperProps", "PaperProps", "slotProps", "paper", "listboxId", "Fragment", "Boolean", "onKeyDown", "onMouseDown", "as", "anchorEl", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "MenuListProps", "disableListWrap", "style", "min<PERSON><PERSON><PERSON>", "propTypes", "string", "bool", "object", "any", "elementType", "isRequired", "func", "oneOfType", "number", "oneOf"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mui/material/Select/SelectInput.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nvar _span;\nconst _excluded = [\"aria-describedby\", \"aria-label\", \"autoFocus\", \"autoWidth\", \"children\", \"className\", \"defaultOpen\", \"defaultValue\", \"disabled\", \"displayEmpty\", \"error\", \"IconComponent\", \"inputRef\", \"labelId\", \"MenuProps\", \"multiple\", \"name\", \"onBlur\", \"onChange\", \"onClose\", \"onFocus\", \"onOpen\", \"open\", \"readOnly\", \"renderValue\", \"SelectDisplayProps\", \"tabIndex\", \"type\", \"value\", \"variant\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport refType from '@mui/utils/refType';\nimport ownerDocument from '../utils/ownerDocument';\nimport capitalize from '../utils/capitalize';\nimport Menu from '../Menu/Menu';\nimport { nativeSelectSelectStyles, nativeSelectIconStyles } from '../NativeSelect/NativeSelectInput';\nimport { isFilled } from '../InputBase/utils';\nimport styled, { slotShouldForwardProp } from '../styles/styled';\nimport useForkRef from '../utils/useForkRef';\nimport useControlled from '../utils/useControlled';\nimport selectClasses, { getSelectUtilityClasses } from './selectClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SelectSelect = styled('div', {\n  name: 'MuiSelect',\n  slot: 'Select',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [\n    // Win specificity over the input base\n    {\n      [`&.${selectClasses.select}`]: styles.select\n    }, {\n      [`&.${selectClasses.select}`]: styles[ownerState.variant]\n    }, {\n      [`&.${selectClasses.error}`]: styles.error\n    }, {\n      [`&.${selectClasses.multiple}`]: styles.multiple\n    }];\n  }\n})(nativeSelectSelectStyles, {\n  // Win specificity over the input base\n  [`&.${selectClasses.select}`]: {\n    height: 'auto',\n    // Resets for multiple select with chips\n    minHeight: '1.4375em',\n    // Required for select\\text-field height consistency\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden'\n  }\n});\nconst SelectIcon = styled('svg', {\n  name: 'MuiSelect',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.variant && styles[`icon${capitalize(ownerState.variant)}`], ownerState.open && styles.iconOpen];\n  }\n})(nativeSelectIconStyles);\nconst SelectNativeInput = styled('input', {\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'classes',\n  name: 'MuiSelect',\n  slot: 'NativeInput',\n  overridesResolver: (props, styles) => styles.nativeInput\n})({\n  bottom: 0,\n  left: 0,\n  position: 'absolute',\n  opacity: 0,\n  pointerEvents: 'none',\n  width: '100%',\n  boxSizing: 'border-box'\n});\nfunction areEqualValues(a, b) {\n  if (typeof b === 'object' && b !== null) {\n    return a === b;\n  }\n\n  // The value could be a number, the DOM will stringify it anyway.\n  return String(a) === String(b);\n}\nfunction isEmpty(display) {\n  return display == null || typeof display === 'string' && !display.trim();\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    disabled,\n    multiple,\n    open,\n    error\n  } = ownerState;\n  const slots = {\n    select: ['select', variant, disabled && 'disabled', multiple && 'multiple', error && 'error'],\n    icon: ['icon', `icon${capitalize(variant)}`, open && 'iconOpen', disabled && 'disabled'],\n    nativeInput: ['nativeInput']\n  };\n  return composeClasses(slots, getSelectUtilityClasses, classes);\n};\n\n/**\n * @ignore - internal component.\n */\nconst SelectInput = /*#__PURE__*/React.forwardRef(function SelectInput(props, ref) {\n  var _MenuProps$slotProps;\n  const {\n      'aria-describedby': ariaDescribedby,\n      'aria-label': ariaLabel,\n      autoFocus,\n      autoWidth,\n      children,\n      className,\n      defaultOpen,\n      defaultValue,\n      disabled,\n      displayEmpty,\n      error = false,\n      IconComponent,\n      inputRef: inputRefProp,\n      labelId,\n      MenuProps = {},\n      multiple,\n      name,\n      onBlur,\n      onChange,\n      onClose,\n      onFocus,\n      onOpen,\n      open: openProp,\n      readOnly,\n      renderValue,\n      SelectDisplayProps = {},\n      tabIndex: tabIndexProp\n      // catching `type` from Input which makes no sense for SelectInput\n      ,\n\n      value: valueProp,\n      variant = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Select'\n  });\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: defaultOpen,\n    name: 'Select'\n  });\n  const inputRef = React.useRef(null);\n  const displayRef = React.useRef(null);\n  const [displayNode, setDisplayNode] = React.useState(null);\n  const {\n    current: isOpenControlled\n  } = React.useRef(openProp != null);\n  const [menuMinWidthState, setMenuMinWidthState] = React.useState();\n  const handleRef = useForkRef(ref, inputRefProp);\n  const handleDisplayRef = React.useCallback(node => {\n    displayRef.current = node;\n    if (node) {\n      setDisplayNode(node);\n    }\n  }, []);\n  const anchorElement = displayNode == null ? void 0 : displayNode.parentNode;\n  React.useImperativeHandle(handleRef, () => ({\n    focus: () => {\n      displayRef.current.focus();\n    },\n    node: inputRef.current,\n    value\n  }), [value]);\n\n  // Resize menu on `defaultOpen` automatic toggle.\n  React.useEffect(() => {\n    if (defaultOpen && openState && displayNode && !isOpenControlled) {\n      setMenuMinWidthState(autoWidth ? null : anchorElement.clientWidth);\n      displayRef.current.focus();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [displayNode, autoWidth]);\n  // `isOpenControlled` is ignored because the component should never switch between controlled and uncontrolled modes.\n  // `defaultOpen` and `openState` are ignored to avoid unnecessary callbacks.\n  React.useEffect(() => {\n    if (autoFocus) {\n      displayRef.current.focus();\n    }\n  }, [autoFocus]);\n  React.useEffect(() => {\n    if (!labelId) {\n      return undefined;\n    }\n    const label = ownerDocument(displayRef.current).getElementById(labelId);\n    if (label) {\n      const handler = () => {\n        if (getSelection().isCollapsed) {\n          displayRef.current.focus();\n        }\n      };\n      label.addEventListener('click', handler);\n      return () => {\n        label.removeEventListener('click', handler);\n      };\n    }\n    return undefined;\n  }, [labelId]);\n  const update = (open, event) => {\n    if (open) {\n      if (onOpen) {\n        onOpen(event);\n      }\n    } else if (onClose) {\n      onClose(event);\n    }\n    if (!isOpenControlled) {\n      setMenuMinWidthState(autoWidth ? null : anchorElement.clientWidth);\n      setOpenState(open);\n    }\n  };\n  const handleMouseDown = event => {\n    // Ignore everything but left-click\n    if (event.button !== 0) {\n      return;\n    }\n    // Hijack the default focus behavior.\n    event.preventDefault();\n    displayRef.current.focus();\n    update(true, event);\n  };\n  const handleClose = event => {\n    update(false, event);\n  };\n  const childrenArray = React.Children.toArray(children);\n\n  // Support autofill.\n  const handleChange = event => {\n    const child = childrenArray.find(childItem => childItem.props.value === event.target.value);\n    if (child === undefined) {\n      return;\n    }\n    setValueState(child.props.value);\n    if (onChange) {\n      onChange(event, child);\n    }\n  };\n  const handleItemClick = child => event => {\n    let newValue;\n\n    // We use the tabindex attribute to signal the available options.\n    if (!event.currentTarget.hasAttribute('tabindex')) {\n      return;\n    }\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      const itemIndex = value.indexOf(child.props.value);\n      if (itemIndex === -1) {\n        newValue.push(child.props.value);\n      } else {\n        newValue.splice(itemIndex, 1);\n      }\n    } else {\n      newValue = child.props.value;\n    }\n    if (child.props.onClick) {\n      child.props.onClick(event);\n    }\n    if (value !== newValue) {\n      setValueState(newValue);\n      if (onChange) {\n        // Redefine target to allow name and value to be read.\n        // This allows seamless integration with the most popular form libraries.\n        // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n        // Clone the event to not override `target` of the original event.\n        const nativeEvent = event.nativeEvent || event;\n        const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n        Object.defineProperty(clonedEvent, 'target', {\n          writable: true,\n          value: {\n            value: newValue,\n            name\n          }\n        });\n        onChange(clonedEvent, child);\n      }\n    }\n    if (!multiple) {\n      update(false, event);\n    }\n  };\n  const handleKeyDown = event => {\n    if (!readOnly) {\n      const validKeys = [' ', 'ArrowUp', 'ArrowDown',\n      // The native select doesn't respond to enter on macOS, but it's recommended by\n      // https://www.w3.org/WAI/ARIA/apg/patterns/combobox/examples/combobox-select-only/\n      'Enter'];\n      if (validKeys.indexOf(event.key) !== -1) {\n        event.preventDefault();\n        update(true, event);\n      }\n    }\n  };\n  const open = displayNode !== null && openState;\n  const handleBlur = event => {\n    // if open event.stopImmediatePropagation\n    if (!open && onBlur) {\n      // Preact support, target is read only property on a native event.\n      Object.defineProperty(event, 'target', {\n        writable: true,\n        value: {\n          value,\n          name\n        }\n      });\n      onBlur(event);\n    }\n  };\n  delete other['aria-invalid'];\n  let display;\n  let displaySingle;\n  const displayMultiple = [];\n  let computeDisplay = false;\n  let foundMatch = false;\n\n  // No need to display any value if the field is empty.\n  if (isFilled({\n    value\n  }) || displayEmpty) {\n    if (renderValue) {\n      display = renderValue(value);\n    } else {\n      computeDisplay = true;\n    }\n  }\n  const items = childrenArray.map(child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Select component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    let selected;\n    if (multiple) {\n      if (!Array.isArray(value)) {\n        throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The \\`value\\` prop must be an array when using the \\`Select\\` component with \\`multiple\\`.` : _formatMuiErrorMessage(2));\n      }\n      selected = value.some(v => areEqualValues(v, child.props.value));\n      if (selected && computeDisplay) {\n        displayMultiple.push(child.props.children);\n      }\n    } else {\n      selected = areEqualValues(value, child.props.value);\n      if (selected && computeDisplay) {\n        displaySingle = child.props.children;\n      }\n    }\n    if (selected) {\n      foundMatch = true;\n    }\n    return /*#__PURE__*/React.cloneElement(child, {\n      'aria-selected': selected ? 'true' : 'false',\n      onClick: handleItemClick(child),\n      onKeyUp: event => {\n        if (event.key === ' ') {\n          // otherwise our MenuItems dispatches a click event\n          // it's not behavior of the native <option> and causes\n          // the select to close immediately since we open on space keydown\n          event.preventDefault();\n        }\n        if (child.props.onKeyUp) {\n          child.props.onKeyUp(event);\n        }\n      },\n      role: 'option',\n      selected,\n      value: undefined,\n      // The value is most likely not a valid HTML attribute.\n      'data-value': child.props.value // Instead, we provide it as a data attribute.\n    });\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!foundMatch && !multiple && value !== '') {\n        const values = childrenArray.map(child => child.props.value);\n        console.warn([`MUI: You have provided an out-of-range value \\`${value}\\` for the select ${name ? `(name=\"${name}\") ` : ''}component.`, \"Consider providing a value that matches one of the available options or ''.\", `The available values are ${values.filter(x => x != null).map(x => `\\`${x}\\``).join(', ') || '\"\"'}.`].join('\\n'));\n      }\n    }, [foundMatch, childrenArray, multiple, name, value]);\n  }\n  if (computeDisplay) {\n    if (multiple) {\n      if (displayMultiple.length === 0) {\n        display = null;\n      } else {\n        display = displayMultiple.reduce((output, child, index) => {\n          output.push(child);\n          if (index < displayMultiple.length - 1) {\n            output.push(', ');\n          }\n          return output;\n        }, []);\n      }\n    } else {\n      display = displaySingle;\n    }\n  }\n\n  // Avoid performing a layout computation in the render method.\n  let menuMinWidth = menuMinWidthState;\n  if (!autoWidth && isOpenControlled && displayNode) {\n    menuMinWidth = anchorElement.clientWidth;\n  }\n  let tabIndex;\n  if (typeof tabIndexProp !== 'undefined') {\n    tabIndex = tabIndexProp;\n  } else {\n    tabIndex = disabled ? null : 0;\n  }\n  const buttonId = SelectDisplayProps.id || (name ? `mui-component-select-${name}` : undefined);\n  const ownerState = _extends({}, props, {\n    variant,\n    value,\n    open,\n    error\n  });\n  const classes = useUtilityClasses(ownerState);\n  const paperProps = _extends({}, MenuProps.PaperProps, (_MenuProps$slotProps = MenuProps.slotProps) == null ? void 0 : _MenuProps$slotProps.paper);\n  const listboxId = useId();\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(SelectSelect, _extends({\n      ref: handleDisplayRef,\n      tabIndex: tabIndex,\n      role: \"combobox\",\n      \"aria-controls\": listboxId,\n      \"aria-disabled\": disabled ? 'true' : undefined,\n      \"aria-expanded\": open ? 'true' : 'false',\n      \"aria-haspopup\": \"listbox\",\n      \"aria-label\": ariaLabel,\n      \"aria-labelledby\": [labelId, buttonId].filter(Boolean).join(' ') || undefined,\n      \"aria-describedby\": ariaDescribedby,\n      onKeyDown: handleKeyDown,\n      onMouseDown: disabled || readOnly ? null : handleMouseDown,\n      onBlur: handleBlur,\n      onFocus: onFocus\n    }, SelectDisplayProps, {\n      ownerState: ownerState,\n      className: clsx(SelectDisplayProps.className, classes.select, className)\n      // The id is required for proper a11y\n      ,\n      id: buttonId,\n      children: isEmpty(display) ? // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        children: \"\\u200B\"\n      })) : display\n    })), /*#__PURE__*/_jsx(SelectNativeInput, _extends({\n      \"aria-invalid\": error,\n      value: Array.isArray(value) ? value.join(',') : value,\n      name: name,\n      ref: inputRef,\n      \"aria-hidden\": true,\n      onChange: handleChange,\n      tabIndex: -1,\n      disabled: disabled,\n      className: classes.nativeInput,\n      autoFocus: autoFocus,\n      ownerState: ownerState\n    }, other)), /*#__PURE__*/_jsx(SelectIcon, {\n      as: IconComponent,\n      className: classes.icon,\n      ownerState: ownerState\n    }), /*#__PURE__*/_jsx(Menu, _extends({\n      id: `menu-${name || ''}`,\n      anchorEl: anchorElement,\n      open: open,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      transformOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      }\n    }, MenuProps, {\n      MenuListProps: _extends({\n        'aria-labelledby': labelId,\n        role: 'listbox',\n        'aria-multiselectable': multiple ? 'true' : undefined,\n        disableListWrap: true,\n        id: listboxId\n      }, MenuProps.MenuListProps),\n      slotProps: _extends({}, MenuProps.slotProps, {\n        paper: _extends({}, paperProps, {\n          style: _extends({\n            minWidth: menuMinWidth\n          }, paperProps != null ? paperProps.style : null)\n        })\n      }),\n      children: items\n    }))]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SelectInput.propTypes = {\n  /**\n   * @ignore\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * @ignore\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * @ignore\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * If `true`, the width of the popover will automatically be set according to the items inside the\n   * menu, otherwise it will be at least the width of the select input.\n   */\n  autoWidth: PropTypes.bool,\n  /**\n   * The option elements to populate the select with.\n   * Can be some `<MenuItem>` elements.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The CSS class name of the select element.\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the component is toggled on mount. Use when the component open state is not controlled.\n   * You can only use it when the `native` prop is `false` (default).\n   */\n  defaultOpen: PropTypes.bool,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the select is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the selected item is displayed even if its value is empty.\n   */\n  displayEmpty: PropTypes.bool,\n  /**\n   * If `true`, the `select input` will indicate an error.\n   */\n  error: PropTypes.bool,\n  /**\n   * The icon that displays the arrow.\n   */\n  IconComponent: PropTypes.elementType.isRequired,\n  /**\n   * Imperative handle implementing `{ value: T, node: HTMLElement, focus(): void }`\n   * Equivalent to `ref`\n   */\n  inputRef: refType,\n  /**\n   * The ID of an element that acts as an additional label. The Select will\n   * be labelled by the additional label and the selected value.\n   */\n  labelId: PropTypes.string,\n  /**\n   * Props applied to the [`Menu`](/material-ui/api/menu/) element.\n   */\n  MenuProps: PropTypes.object,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Name attribute of the `select` or hidden `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when a menu item is selected.\n   *\n   * @param {object} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (any).\n   * @param {object} [child] The react element that was selected.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {object} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {object} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the selected value.\n   *\n   * @param {any} value The `value` provided to the component.\n   * @returns {ReactNode}\n   */\n  renderValue: PropTypes.func,\n  /**\n   * Props applied to the clickable div element.\n   */\n  SelectDisplayProps: PropTypes.object,\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  type: PropTypes.any,\n  /**\n   * The input value.\n   */\n  value: PropTypes.any,\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes.oneOf(['standard', 'outlined', 'filled'])\n} : void 0;\nexport default SelectInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,IAAIC,KAAK;AACT,MAAMC,SAAS,GAAG,CAAC,kBAAkB,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,oBAAoB,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;AAC3Y,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,IAAI,MAAM,cAAc;AAC/B,SAASC,wBAAwB,EAAEC,sBAAsB,QAAQ,mCAAmC;AACpG,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,MAAM,IAAIC,qBAAqB,QAAQ,kBAAkB;AAChE,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,aAAa,IAAIC,uBAAuB,QAAQ,iBAAiB;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,YAAY,GAAGV,MAAM,CAAC,KAAK,EAAE;EACjCW,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,OAAO;IACP;IACA;MACE,CAAC,KAAKV,aAAa,CAACa,MAAM,EAAE,GAAGF,MAAM,CAACE;IACxC,CAAC,EAAE;MACD,CAAC,KAAKb,aAAa,CAACa,MAAM,EAAE,GAAGF,MAAM,CAACC,UAAU,CAACE,OAAO;IAC1D,CAAC,EAAE;MACD,CAAC,KAAKd,aAAa,CAACe,KAAK,EAAE,GAAGJ,MAAM,CAACI;IACvC,CAAC,EAAE;MACD,CAAC,KAAKf,aAAa,CAACgB,QAAQ,EAAE,GAAGL,MAAM,CAACK;IAC1C,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,CAACvB,wBAAwB,EAAE;EAC3B;EACA,CAAC,KAAKO,aAAa,CAACa,MAAM,EAAE,GAAG;IAC7BI,MAAM,EAAE,MAAM;IACd;IACAC,SAAS,EAAE,UAAU;IACrB;IACAC,YAAY,EAAE,UAAU;IACxBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AACF,MAAMC,UAAU,GAAG1B,MAAM,CAAC,KAAK,EAAE;EAC/BW,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,OAAO,CAACC,MAAM,CAACY,IAAI,EAAEX,UAAU,CAACE,OAAO,IAAIH,MAAM,CAAC,OAAOpB,UAAU,CAACqB,UAAU,CAACE,OAAO,CAAC,EAAE,CAAC,EAAEF,UAAU,CAACY,IAAI,IAAIb,MAAM,CAACc,QAAQ,CAAC;EACjI;AACF,CAAC,CAAC,CAAC/B,sBAAsB,CAAC;AAC1B,MAAMgC,iBAAiB,GAAG9B,MAAM,CAAC,OAAO,EAAE;EACxC+B,iBAAiB,EAAEC,IAAI,IAAI/B,qBAAqB,CAAC+B,IAAI,CAAC,IAAIA,IAAI,KAAK,SAAS;EAC5ErB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,aAAa;EACnBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACkB;AAC/C,CAAC,CAAC,CAAC;EACDC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,CAAC;EACVC,aAAa,EAAE,MAAM;EACrBC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,SAASC,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,EAAE;IACvC,OAAOD,CAAC,KAAKC,CAAC;EAChB;;EAEA;EACA,OAAOC,MAAM,CAACF,CAAC,CAAC,KAAKE,MAAM,CAACD,CAAC,CAAC;AAChC;AACA,SAASE,OAAOA,CAACC,OAAO,EAAE;EACxB,OAAOA,OAAO,IAAI,IAAI,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACA,OAAO,CAACC,IAAI,CAAC,CAAC;AAC1E;AACA,MAAMC,iBAAiB,GAAGhC,UAAU,IAAI;EACtC,MAAM;IACJiC,OAAO;IACP/B,OAAO;IACPgC,QAAQ;IACR9B,QAAQ;IACRQ,IAAI;IACJT;EACF,CAAC,GAAGH,UAAU;EACd,MAAMmC,KAAK,GAAG;IACZlC,MAAM,EAAE,CAAC,QAAQ,EAAEC,OAAO,EAAEgC,QAAQ,IAAI,UAAU,EAAE9B,QAAQ,IAAI,UAAU,EAAED,KAAK,IAAI,OAAO,CAAC;IAC7FQ,IAAI,EAAE,CAAC,MAAM,EAAE,OAAOhC,UAAU,CAACuB,OAAO,CAAC,EAAE,EAAEU,IAAI,IAAI,UAAU,EAAEsB,QAAQ,IAAI,UAAU,CAAC;IACxFjB,WAAW,EAAE,CAAC,aAAa;EAC7B,CAAC;EACD,OAAO1C,cAAc,CAAC4D,KAAK,EAAE9C,uBAAuB,EAAE4C,OAAO,CAAC;AAChE,CAAC;;AAED;AACA;AACA;AACA,MAAMG,WAAW,GAAG,aAAajE,KAAK,CAACkE,UAAU,CAAC,SAASD,WAAWA,CAACtC,KAAK,EAAEwC,GAAG,EAAE;EACjF,IAAIC,oBAAoB;EACxB,MAAM;MACF,kBAAkB,EAAEC,eAAe;MACnC,YAAY,EAAEC,SAAS;MACvBC,SAAS;MACTC,SAAS;MACTC,QAAQ;MACRC,SAAS;MACTC,WAAW;MACXC,YAAY;MACZb,QAAQ;MACRc,YAAY;MACZ7C,KAAK,GAAG,KAAK;MACb8C,aAAa;MACbC,QAAQ,EAAEC,YAAY;MACtBC,OAAO;MACPC,SAAS,GAAG,CAAC,CAAC;MACdjD,QAAQ;MACRT,IAAI;MACJ2D,MAAM;MACNC,QAAQ;MACRC,OAAO;MACPC,OAAO;MACPC,MAAM;MACN9C,IAAI,EAAE+C,QAAQ;MACdC,QAAQ;MACRC,WAAW;MACXC,kBAAkB,GAAG,CAAC,CAAC;MACvBC,QAAQ,EAAEC;MACV;MAAA;;MAGAC,KAAK,EAAEC,SAAS;MAChBhE,OAAO,GAAG;IACZ,CAAC,GAAGJ,KAAK;IACTqE,KAAK,GAAGpG,6BAA6B,CAAC+B,KAAK,EAAE5B,SAAS,CAAC;EACzD,MAAM,CAAC+F,KAAK,EAAEG,aAAa,CAAC,GAAGjF,aAAa,CAAC;IAC3CkF,UAAU,EAAEH,SAAS;IACrBI,OAAO,EAAEvB,YAAY;IACrBpD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAAC4E,SAAS,EAAEC,YAAY,CAAC,GAAGrF,aAAa,CAAC;IAC9CkF,UAAU,EAAEV,QAAQ;IACpBW,OAAO,EAAExB,WAAW;IACpBnD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMuD,QAAQ,GAAG/E,KAAK,CAACsG,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,UAAU,GAAGvG,KAAK,CAACsG,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAGzG,KAAK,CAAC0G,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM;IACJC,OAAO,EAAEC;EACX,CAAC,GAAG5G,KAAK,CAACsG,MAAM,CAACd,QAAQ,IAAI,IAAI,CAAC;EAClC,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9G,KAAK,CAAC0G,QAAQ,CAAC,CAAC;EAClE,MAAMK,SAAS,GAAGhG,UAAU,CAACoD,GAAG,EAAEa,YAAY,CAAC;EAC/C,MAAMgC,gBAAgB,GAAGhH,KAAK,CAACiH,WAAW,CAACC,IAAI,IAAI;IACjDX,UAAU,CAACI,OAAO,GAAGO,IAAI;IACzB,IAAIA,IAAI,EAAE;MACRT,cAAc,CAACS,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,aAAa,GAAGX,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACY,UAAU;EAC3EpH,KAAK,CAACqH,mBAAmB,CAACN,SAAS,EAAE,OAAO;IAC1CO,KAAK,EAAEA,CAAA,KAAM;MACXf,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;IAC5B,CAAC;IACDJ,IAAI,EAAEnC,QAAQ,CAAC4B,OAAO;IACtBb;EACF,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEZ;EACA9F,KAAK,CAACuH,SAAS,CAAC,MAAM;IACpB,IAAI5C,WAAW,IAAIyB,SAAS,IAAII,WAAW,IAAI,CAACI,gBAAgB,EAAE;MAChEE,oBAAoB,CAACtC,SAAS,GAAG,IAAI,GAAG2C,aAAa,CAACK,WAAW,CAAC;MAClEjB,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;IAC5B;IACA;EACF,CAAC,EAAE,CAACd,WAAW,EAAEhC,SAAS,CAAC,CAAC;EAC5B;EACA;EACAxE,KAAK,CAACuH,SAAS,CAAC,MAAM;IACpB,IAAIhD,SAAS,EAAE;MACbgC,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAAC/C,SAAS,CAAC,CAAC;EACfvE,KAAK,CAACuH,SAAS,CAAC,MAAM;IACpB,IAAI,CAACtC,OAAO,EAAE;MACZ,OAAOwC,SAAS;IAClB;IACA,MAAMC,KAAK,GAAGnH,aAAa,CAACgG,UAAU,CAACI,OAAO,CAAC,CAACgB,cAAc,CAAC1C,OAAO,CAAC;IACvE,IAAIyC,KAAK,EAAE;MACT,MAAME,OAAO,GAAGA,CAAA,KAAM;QACpB,IAAIC,YAAY,CAAC,CAAC,CAACC,WAAW,EAAE;UAC9BvB,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;QAC5B;MACF,CAAC;MACDI,KAAK,CAACK,gBAAgB,CAAC,OAAO,EAAEH,OAAO,CAAC;MACxC,OAAO,MAAM;QACXF,KAAK,CAACM,mBAAmB,CAAC,OAAO,EAAEJ,OAAO,CAAC;MAC7C,CAAC;IACH;IACA,OAAOH,SAAS;EAClB,CAAC,EAAE,CAACxC,OAAO,CAAC,CAAC;EACb,MAAMgD,MAAM,GAAGA,CAACxF,IAAI,EAAEyF,KAAK,KAAK;IAC9B,IAAIzF,IAAI,EAAE;MACR,IAAI8C,MAAM,EAAE;QACVA,MAAM,CAAC2C,KAAK,CAAC;MACf;IACF,CAAC,MAAM,IAAI7C,OAAO,EAAE;MAClBA,OAAO,CAAC6C,KAAK,CAAC;IAChB;IACA,IAAI,CAACtB,gBAAgB,EAAE;MACrBE,oBAAoB,CAACtC,SAAS,GAAG,IAAI,GAAG2C,aAAa,CAACK,WAAW,CAAC;MAClEnB,YAAY,CAAC5D,IAAI,CAAC;IACpB;EACF,CAAC;EACD,MAAM0F,eAAe,GAAGD,KAAK,IAAI;IAC/B;IACA,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;IACA;IACAF,KAAK,CAACG,cAAc,CAAC,CAAC;IACtB9B,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;IAC1BW,MAAM,CAAC,IAAI,EAAEC,KAAK,CAAC;EACrB,CAAC;EACD,MAAMI,WAAW,GAAGJ,KAAK,IAAI;IAC3BD,MAAM,CAAC,KAAK,EAAEC,KAAK,CAAC;EACtB,CAAC;EACD,MAAMK,aAAa,GAAGvI,KAAK,CAACwI,QAAQ,CAACC,OAAO,CAAChE,QAAQ,CAAC;;EAEtD;EACA,MAAMiE,YAAY,GAAGR,KAAK,IAAI;IAC5B,MAAMS,KAAK,GAAGJ,aAAa,CAACK,IAAI,CAACC,SAAS,IAAIA,SAAS,CAAClH,KAAK,CAACmE,KAAK,KAAKoC,KAAK,CAACY,MAAM,CAAChD,KAAK,CAAC;IAC3F,IAAI6C,KAAK,KAAKlB,SAAS,EAAE;MACvB;IACF;IACAxB,aAAa,CAAC0C,KAAK,CAAChH,KAAK,CAACmE,KAAK,CAAC;IAChC,IAAIV,QAAQ,EAAE;MACZA,QAAQ,CAAC8C,KAAK,EAAES,KAAK,CAAC;IACxB;EACF,CAAC;EACD,MAAMI,eAAe,GAAGJ,KAAK,IAAIT,KAAK,IAAI;IACxC,IAAIc,QAAQ;;IAEZ;IACA,IAAI,CAACd,KAAK,CAACe,aAAa,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;MACjD;IACF;IACA,IAAIjH,QAAQ,EAAE;MACZ+G,QAAQ,GAAGG,KAAK,CAACC,OAAO,CAACtD,KAAK,CAAC,GAAGA,KAAK,CAACuD,KAAK,CAAC,CAAC,GAAG,EAAE;MACpD,MAAMC,SAAS,GAAGxD,KAAK,CAACyD,OAAO,CAACZ,KAAK,CAAChH,KAAK,CAACmE,KAAK,CAAC;MAClD,IAAIwD,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBN,QAAQ,CAACQ,IAAI,CAACb,KAAK,CAAChH,KAAK,CAACmE,KAAK,CAAC;MAClC,CAAC,MAAM;QACLkD,QAAQ,CAACS,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM;MACLN,QAAQ,GAAGL,KAAK,CAAChH,KAAK,CAACmE,KAAK;IAC9B;IACA,IAAI6C,KAAK,CAAChH,KAAK,CAAC+H,OAAO,EAAE;MACvBf,KAAK,CAAChH,KAAK,CAAC+H,OAAO,CAACxB,KAAK,CAAC;IAC5B;IACA,IAAIpC,KAAK,KAAKkD,QAAQ,EAAE;MACtB/C,aAAa,CAAC+C,QAAQ,CAAC;MACvB,IAAI5D,QAAQ,EAAE;QACZ;QACA;QACA;QACA;QACA,MAAMuE,WAAW,GAAGzB,KAAK,CAACyB,WAAW,IAAIzB,KAAK;QAC9C,MAAM0B,WAAW,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,IAAI,EAAEH,WAAW,CAAC;QAC9EI,MAAM,CAACC,cAAc,CAACJ,WAAW,EAAE,QAAQ,EAAE;UAC3CK,QAAQ,EAAE,IAAI;UACdnE,KAAK,EAAE;YACLA,KAAK,EAAEkD,QAAQ;YACfxH;UACF;QACF,CAAC,CAAC;QACF4D,QAAQ,CAACwE,WAAW,EAAEjB,KAAK,CAAC;MAC9B;IACF;IACA,IAAI,CAAC1G,QAAQ,EAAE;MACbgG,MAAM,CAAC,KAAK,EAAEC,KAAK,CAAC;IACtB;EACF,CAAC;EACD,MAAMgC,aAAa,GAAGhC,KAAK,IAAI;IAC7B,IAAI,CAACzC,QAAQ,EAAE;MACb,MAAM0E,SAAS,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,WAAW;MAC9C;MACA;MACA,OAAO,CAAC;MACR,IAAIA,SAAS,CAACZ,OAAO,CAACrB,KAAK,CAACkC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACvClC,KAAK,CAACG,cAAc,CAAC,CAAC;QACtBJ,MAAM,CAAC,IAAI,EAAEC,KAAK,CAAC;MACrB;IACF;EACF,CAAC;EACD,MAAMzF,IAAI,GAAG+D,WAAW,KAAK,IAAI,IAAIJ,SAAS;EAC9C,MAAMiE,UAAU,GAAGnC,KAAK,IAAI;IAC1B;IACA,IAAI,CAACzF,IAAI,IAAI0C,MAAM,EAAE;MACnB;MACA4E,MAAM,CAACC,cAAc,CAAC9B,KAAK,EAAE,QAAQ,EAAE;QACrC+B,QAAQ,EAAE,IAAI;QACdnE,KAAK,EAAE;UACLA,KAAK;UACLtE;QACF;MACF,CAAC,CAAC;MACF2D,MAAM,CAAC+C,KAAK,CAAC;IACf;EACF,CAAC;EACD,OAAOlC,KAAK,CAAC,cAAc,CAAC;EAC5B,IAAIrC,OAAO;EACX,IAAI2G,aAAa;EACjB,MAAMC,eAAe,GAAG,EAAE;EAC1B,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,UAAU,GAAG,KAAK;;EAEtB;EACA,IAAI7J,QAAQ,CAAC;IACXkF;EACF,CAAC,CAAC,IAAIjB,YAAY,EAAE;IAClB,IAAIa,WAAW,EAAE;MACf/B,OAAO,GAAG+B,WAAW,CAACI,KAAK,CAAC;IAC9B,CAAC,MAAM;MACL0E,cAAc,GAAG,IAAI;IACvB;EACF;EACA,MAAME,KAAK,GAAGnC,aAAa,CAACoC,GAAG,CAAChC,KAAK,IAAI;IACvC,IAAI,EAAE,aAAa3I,KAAK,CAAC4K,cAAc,CAACjC,KAAK,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;IACA,IAAIkC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI9K,UAAU,CAAC0I,KAAK,CAAC,EAAE;QACrBqC,OAAO,CAAChJ,KAAK,CAAC,CAAC,iEAAiE,EAAE,sCAAsC,CAAC,CAACiJ,IAAI,CAAC,IAAI,CAAC,CAAC;MACvI;IACF;IACA,IAAIC,QAAQ;IACZ,IAAIjJ,QAAQ,EAAE;MACZ,IAAI,CAACkH,KAAK,CAACC,OAAO,CAACtD,KAAK,CAAC,EAAE;QACzB,MAAM,IAAIqF,KAAK,CAACN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,iGAAiG,GAAGlL,sBAAsB,CAAC,CAAC,CAAC,CAAC;MACxL;MACAqL,QAAQ,GAAGpF,KAAK,CAACsF,IAAI,CAACC,CAAC,IAAI/H,cAAc,CAAC+H,CAAC,EAAE1C,KAAK,CAAChH,KAAK,CAACmE,KAAK,CAAC,CAAC;MAChE,IAAIoF,QAAQ,IAAIV,cAAc,EAAE;QAC9BD,eAAe,CAACf,IAAI,CAACb,KAAK,CAAChH,KAAK,CAAC8C,QAAQ,CAAC;MAC5C;IACF,CAAC,MAAM;MACLyG,QAAQ,GAAG5H,cAAc,CAACwC,KAAK,EAAE6C,KAAK,CAAChH,KAAK,CAACmE,KAAK,CAAC;MACnD,IAAIoF,QAAQ,IAAIV,cAAc,EAAE;QAC9BF,aAAa,GAAG3B,KAAK,CAAChH,KAAK,CAAC8C,QAAQ;MACtC;IACF;IACA,IAAIyG,QAAQ,EAAE;MACZT,UAAU,GAAG,IAAI;IACnB;IACA,OAAO,aAAazK,KAAK,CAACsL,YAAY,CAAC3C,KAAK,EAAE;MAC5C,eAAe,EAAEuC,QAAQ,GAAG,MAAM,GAAG,OAAO;MAC5CxB,OAAO,EAAEX,eAAe,CAACJ,KAAK,CAAC;MAC/B4C,OAAO,EAAErD,KAAK,IAAI;QAChB,IAAIA,KAAK,CAACkC,GAAG,KAAK,GAAG,EAAE;UACrB;UACA;UACA;UACAlC,KAAK,CAACG,cAAc,CAAC,CAAC;QACxB;QACA,IAAIM,KAAK,CAAChH,KAAK,CAAC4J,OAAO,EAAE;UACvB5C,KAAK,CAAChH,KAAK,CAAC4J,OAAO,CAACrD,KAAK,CAAC;QAC5B;MACF,CAAC;MACDsD,IAAI,EAAE,QAAQ;MACdN,QAAQ;MACRpF,KAAK,EAAE2B,SAAS;MAChB;MACA,YAAY,EAAEkB,KAAK,CAAChH,KAAK,CAACmE,KAAK,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI+E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA/K,KAAK,CAACuH,SAAS,CAAC,MAAM;MACpB,IAAI,CAACkD,UAAU,IAAI,CAACxI,QAAQ,IAAI6D,KAAK,KAAK,EAAE,EAAE;QAC5C,MAAM2F,MAAM,GAAGlD,aAAa,CAACoC,GAAG,CAAChC,KAAK,IAAIA,KAAK,CAAChH,KAAK,CAACmE,KAAK,CAAC;QAC5DkF,OAAO,CAACU,IAAI,CAAC,CAAC,kDAAkD5F,KAAK,qBAAqBtE,IAAI,GAAG,UAAUA,IAAI,KAAK,GAAG,EAAE,YAAY,EAAE,6EAA6E,EAAE,4BAA4BiK,MAAM,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC,CAACjB,GAAG,CAACiB,CAAC,IAAI,KAAKA,CAAC,IAAI,CAAC,CAACX,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC;MACzU;IACF,CAAC,EAAE,CAACR,UAAU,EAAElC,aAAa,EAAEtG,QAAQ,EAAET,IAAI,EAAEsE,KAAK,CAAC,CAAC;EACxD;EACA,IAAI0E,cAAc,EAAE;IAClB,IAAIvI,QAAQ,EAAE;MACZ,IAAIsI,eAAe,CAACsB,MAAM,KAAK,CAAC,EAAE;QAChClI,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM;QACLA,OAAO,GAAG4G,eAAe,CAACuB,MAAM,CAAC,CAACC,MAAM,EAAEpD,KAAK,EAAEqD,KAAK,KAAK;UACzDD,MAAM,CAACvC,IAAI,CAACb,KAAK,CAAC;UAClB,IAAIqD,KAAK,GAAGzB,eAAe,CAACsB,MAAM,GAAG,CAAC,EAAE;YACtCE,MAAM,CAACvC,IAAI,CAAC,IAAI,CAAC;UACnB;UACA,OAAOuC,MAAM;QACf,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC,MAAM;MACLpI,OAAO,GAAG2G,aAAa;IACzB;EACF;;EAEA;EACA,IAAI2B,YAAY,GAAGpF,iBAAiB;EACpC,IAAI,CAACrC,SAAS,IAAIoC,gBAAgB,IAAIJ,WAAW,EAAE;IACjDyF,YAAY,GAAG9E,aAAa,CAACK,WAAW;EAC1C;EACA,IAAI5B,QAAQ;EACZ,IAAI,OAAOC,YAAY,KAAK,WAAW,EAAE;IACvCD,QAAQ,GAAGC,YAAY;EACzB,CAAC,MAAM;IACLD,QAAQ,GAAG7B,QAAQ,GAAG,IAAI,GAAG,CAAC;EAChC;EACA,MAAMmI,QAAQ,GAAGvG,kBAAkB,CAACwG,EAAE,KAAK3K,IAAI,GAAG,wBAAwBA,IAAI,EAAE,GAAGiG,SAAS,CAAC;EAC7F,MAAM5F,UAAU,GAAGlC,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;IACrCI,OAAO;IACP+D,KAAK;IACLrD,IAAI;IACJT;EACF,CAAC,CAAC;EACF,MAAM8B,OAAO,GAAGD,iBAAiB,CAAChC,UAAU,CAAC;EAC7C,MAAMuK,UAAU,GAAGzM,QAAQ,CAAC,CAAC,CAAC,EAAEuF,SAAS,CAACmH,UAAU,EAAE,CAACjI,oBAAoB,GAAGc,SAAS,CAACoH,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGlI,oBAAoB,CAACmI,KAAK,CAAC;EACjJ,MAAMC,SAAS,GAAGnM,KAAK,CAAC,CAAC;EACzB,OAAO,aAAaiB,KAAK,CAACtB,KAAK,CAACyM,QAAQ,EAAE;IACxChI,QAAQ,EAAE,CAAC,aAAarD,IAAI,CAACG,YAAY,EAAE5B,QAAQ,CAAC;MAClDwE,GAAG,EAAE6C,gBAAgB;MACrBpB,QAAQ,EAAEA,QAAQ;MAClB4F,IAAI,EAAE,UAAU;MAChB,eAAe,EAAEgB,SAAS;MAC1B,eAAe,EAAEzI,QAAQ,GAAG,MAAM,GAAG0D,SAAS;MAC9C,eAAe,EAAEhF,IAAI,GAAG,MAAM,GAAG,OAAO;MACxC,eAAe,EAAE,SAAS;MAC1B,YAAY,EAAE6B,SAAS;MACvB,iBAAiB,EAAE,CAACW,OAAO,EAAEiH,QAAQ,CAAC,CAACP,MAAM,CAACe,OAAO,CAAC,CAACzB,IAAI,CAAC,GAAG,CAAC,IAAIxD,SAAS;MAC7E,kBAAkB,EAAEpD,eAAe;MACnCsI,SAAS,EAAEzC,aAAa;MACxB0C,WAAW,EAAE7I,QAAQ,IAAI0B,QAAQ,GAAG,IAAI,GAAG0C,eAAe;MAC1DhD,MAAM,EAAEkF,UAAU;MAClB/E,OAAO,EAAEA;IACX,CAAC,EAAEK,kBAAkB,EAAE;MACrB9D,UAAU,EAAEA,UAAU;MACtB6C,SAAS,EAAEvE,IAAI,CAACwF,kBAAkB,CAACjB,SAAS,EAAEZ,OAAO,CAAChC,MAAM,EAAE4C,SAAS;MACvE;MAAA;;MAEAyH,EAAE,EAAED,QAAQ;MACZzH,QAAQ,EAAEf,OAAO,CAACC,OAAO,CAAC;MAAG;MAC7B7D,KAAK,KAAKA,KAAK,GAAG,aAAasB,IAAI,CAAC,MAAM,EAAE;QAC1CsD,SAAS,EAAE,aAAa;QACxBD,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC,GAAGd;IACR,CAAC,CAAC,CAAC,EAAE,aAAavC,IAAI,CAACuB,iBAAiB,EAAEhD,QAAQ,CAAC;MACjD,cAAc,EAAEqC,KAAK;MACrB8D,KAAK,EAAEqD,KAAK,CAACC,OAAO,CAACtD,KAAK,CAAC,GAAGA,KAAK,CAACmF,IAAI,CAAC,GAAG,CAAC,GAAGnF,KAAK;MACrDtE,IAAI,EAAEA,IAAI;MACV2C,GAAG,EAAEY,QAAQ;MACb,aAAa,EAAE,IAAI;MACnBK,QAAQ,EAAEsD,YAAY;MACtB9C,QAAQ,EAAE,CAAC,CAAC;MACZ7B,QAAQ,EAAEA,QAAQ;MAClBW,SAAS,EAAEZ,OAAO,CAAChB,WAAW;MAC9ByB,SAAS,EAAEA,SAAS;MACpB1C,UAAU,EAAEA;IACd,CAAC,EAAEmE,KAAK,CAAC,CAAC,EAAE,aAAa5E,IAAI,CAACmB,UAAU,EAAE;MACxCsK,EAAE,EAAE/H,aAAa;MACjBJ,SAAS,EAAEZ,OAAO,CAACtB,IAAI;MACvBX,UAAU,EAAEA;IACd,CAAC,CAAC,EAAE,aAAaT,IAAI,CAACX,IAAI,EAAEd,QAAQ,CAAC;MACnCwM,EAAE,EAAE,QAAQ3K,IAAI,IAAI,EAAE,EAAE;MACxBsL,QAAQ,EAAE3F,aAAa;MACvB1E,IAAI,EAAEA,IAAI;MACV4C,OAAO,EAAEiD,WAAW;MACpByE,YAAY,EAAE;QACZC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACd,CAAC;MACDC,eAAe,EAAE;QACfF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd;IACF,CAAC,EAAE/H,SAAS,EAAE;MACZiI,aAAa,EAAExN,QAAQ,CAAC;QACtB,iBAAiB,EAAEsF,OAAO;QAC1BuG,IAAI,EAAE,SAAS;QACf,sBAAsB,EAAEvJ,QAAQ,GAAG,MAAM,GAAGwF,SAAS;QACrD2F,eAAe,EAAE,IAAI;QACrBjB,EAAE,EAAEK;MACN,CAAC,EAAEtH,SAAS,CAACiI,aAAa,CAAC;MAC3Bb,SAAS,EAAE3M,QAAQ,CAAC,CAAC,CAAC,EAAEuF,SAAS,CAACoH,SAAS,EAAE;QAC3CC,KAAK,EAAE5M,QAAQ,CAAC,CAAC,CAAC,EAAEyM,UAAU,EAAE;UAC9BiB,KAAK,EAAE1N,QAAQ,CAAC;YACd2N,QAAQ,EAAErB;UACZ,CAAC,EAAEG,UAAU,IAAI,IAAI,GAAGA,UAAU,CAACiB,KAAK,GAAG,IAAI;QACjD,CAAC;MACH,CAAC,CAAC;MACF5I,QAAQ,EAAEiG;IACZ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9G,WAAW,CAACsJ,SAAS,GAAG;EAC9D;AACF;AACA;EACE,kBAAkB,EAAErN,SAAS,CAACsN,MAAM;EACpC;AACF;AACA;EACE,YAAY,EAAEtN,SAAS,CAACsN,MAAM;EAC9B;AACF;AACA;EACEjJ,SAAS,EAAErE,SAAS,CAACuN,IAAI;EACzB;AACF;AACA;AACA;EACEjJ,SAAS,EAAEtE,SAAS,CAACuN,IAAI;EACzB;AACF;AACA;AACA;EACEhJ,QAAQ,EAAEvE,SAAS,CAACgH,IAAI;EACxB;AACF;AACA;EACEpD,OAAO,EAAE5D,SAAS,CAACwN,MAAM;EACzB;AACF;AACA;EACEhJ,SAAS,EAAExE,SAAS,CAACsN,MAAM;EAC3B;AACF;AACA;AACA;EACE7I,WAAW,EAAEzE,SAAS,CAACuN,IAAI;EAC3B;AACF;AACA;EACE7I,YAAY,EAAE1E,SAAS,CAACyN,GAAG;EAC3B;AACF;AACA;EACE5J,QAAQ,EAAE7D,SAAS,CAACuN,IAAI;EACxB;AACF;AACA;EACE5I,YAAY,EAAE3E,SAAS,CAACuN,IAAI;EAC5B;AACF;AACA;EACEzL,KAAK,EAAE9B,SAAS,CAACuN,IAAI;EACrB;AACF;AACA;EACE3I,aAAa,EAAE5E,SAAS,CAAC0N,WAAW,CAACC,UAAU;EAC/C;AACF;AACA;AACA;EACE9I,QAAQ,EAAEzE,OAAO;EACjB;AACF;AACA;AACA;EACE2E,OAAO,EAAE/E,SAAS,CAACsN,MAAM;EACzB;AACF;AACA;EACEtI,SAAS,EAAEhF,SAAS,CAACwN,MAAM;EAC3B;AACF;AACA;EACEzL,QAAQ,EAAE/B,SAAS,CAACuN,IAAI;EACxB;AACF;AACA;EACEjM,IAAI,EAAEtB,SAAS,CAACsN,MAAM;EACtB;AACF;AACA;EACErI,MAAM,EAAEjF,SAAS,CAAC4N,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;EACE1I,QAAQ,EAAElF,SAAS,CAAC4N,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACEzI,OAAO,EAAEnF,SAAS,CAAC4N,IAAI;EACvB;AACF;AACA;EACExI,OAAO,EAAEpF,SAAS,CAAC4N,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;EACEvI,MAAM,EAAErF,SAAS,CAAC4N,IAAI;EACtB;AACF;AACA;EACErL,IAAI,EAAEvC,SAAS,CAACuN,IAAI;EACpB;AACF;AACA;EACEhI,QAAQ,EAAEvF,SAAS,CAACuN,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACE/H,WAAW,EAAExF,SAAS,CAAC4N,IAAI;EAC3B;AACF;AACA;EACEnI,kBAAkB,EAAEzF,SAAS,CAACwN,MAAM;EACpC;AACF;AACA;EACE9H,QAAQ,EAAE1F,SAAS,CAAC6N,SAAS,CAAC,CAAC7N,SAAS,CAAC8N,MAAM,EAAE9N,SAAS,CAACsN,MAAM,CAAC,CAAC;EACnE;AACF;AACA;EACE1D,IAAI,EAAE5J,SAAS,CAACyN,GAAG;EACnB;AACF;AACA;EACE7H,KAAK,EAAE5F,SAAS,CAACyN,GAAG;EACpB;AACF;AACA;EACE5L,OAAO,EAAE7B,SAAS,CAAC+N,KAAK,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;AAC7D,CAAC,GAAG,KAAK,CAAC;AACV,eAAehK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}