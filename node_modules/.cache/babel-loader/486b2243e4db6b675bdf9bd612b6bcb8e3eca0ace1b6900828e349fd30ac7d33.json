{"ast": null, "code": "export { default } from './useTimeout';\nexport { Timeout } from './useTimeout';", "map": {"version": 3, "names": ["default", "Timeout"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mui/utils/esm/useTimeout/index.js"], "sourcesContent": ["export { default } from './useTimeout';\nexport { Timeout } from './useTimeout';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}