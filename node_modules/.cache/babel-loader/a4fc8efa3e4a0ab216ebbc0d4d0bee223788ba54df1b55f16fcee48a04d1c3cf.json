{"ast": null, "code": "// src/glossary.ts\nvar IS_PATCHED_MODULE = Symbol(\"isPatchedModule\");\n\n// src/utils/canParseUrl.ts\nfunction canParseUrl(url) {\n  try {\n    new URL(url);\n    return true;\n  } catch (_error) {\n    return false;\n  }\n}\n\n// src/utils/getValueBySymbol.ts\nfunction getValueBySymbol(symbolName, source) {\n  const ownSymbols = Object.getOwnPropertySymbols(source);\n  const symbol = ownSymbols.find(symbol2 => {\n    return symbol2.description === symbolName;\n  });\n  if (symbol) {\n    return Reflect.get(source, symbol);\n  }\n  return;\n}\n\n// src/utils/fetchUtils.ts\nvar _FetchResponse = class extends Response {\n  static isConfigurableStatusCode(status) {\n    return status >= 200 && status <= 599;\n  }\n  static isRedirectResponse(status) {\n    return _FetchResponse.STATUS_CODES_WITH_REDIRECT.includes(status);\n  }\n  /**\n   * Returns a boolean indicating whether the given response status\n   * code represents a response that can have a body.\n   */\n  static isResponseWithBody(status) {\n    return !_FetchResponse.STATUS_CODES_WITHOUT_BODY.includes(status);\n  }\n  static setUrl(url, response) {\n    if (!url || url === \"about:\" || !canParseUrl(url)) {\n      return;\n    }\n    const state = getValueBySymbol(\"state\", response);\n    if (state) {\n      state.urlList.push(new URL(url));\n    } else {\n      Object.defineProperty(response, \"url\", {\n        value: url,\n        enumerable: true,\n        configurable: true,\n        writable: false\n      });\n    }\n  }\n  /**\n   * Parses the given raw HTTP headers into a Fetch API `Headers` instance.\n   */\n  static parseRawHeaders(rawHeaders) {\n    const headers = new Headers();\n    for (let line = 0; line < rawHeaders.length; line += 2) {\n      headers.append(rawHeaders[line], rawHeaders[line + 1]);\n    }\n    return headers;\n  }\n  constructor(body, init = {}) {\n    var _a;\n    const status = (_a = init.status) != null ? _a : 200;\n    const safeStatus = _FetchResponse.isConfigurableStatusCode(status) ? status : 200;\n    const finalBody = _FetchResponse.isResponseWithBody(status) ? body : null;\n    super(finalBody, {\n      status: safeStatus,\n      statusText: init.statusText,\n      headers: init.headers\n    });\n    if (status !== safeStatus) {\n      const state = getValueBySymbol(\"state\", this);\n      if (state) {\n        state.status = status;\n      } else {\n        Object.defineProperty(this, \"status\", {\n          value: status,\n          enumerable: true,\n          configurable: true,\n          writable: false\n        });\n      }\n    }\n    _FetchResponse.setUrl(init.url, this);\n  }\n};\nvar FetchResponse = _FetchResponse;\n/**\n * Response status codes for responses that cannot have body.\n * @see https://fetch.spec.whatwg.org/#statuses\n */\nFetchResponse.STATUS_CODES_WITHOUT_BODY = [101, 103, 204, 205, 304];\nFetchResponse.STATUS_CODES_WITH_REDIRECT = [301, 302, 303, 307, 308];\n\n// src/getRawRequest.ts\nvar kRawRequest = Symbol(\"kRawRequest\");\nfunction getRawRequest(request) {\n  return Reflect.get(request, kRawRequest);\n}\nfunction setRawRequest(request, rawRequest) {\n  Reflect.set(request, kRawRequest, rawRequest);\n}\nexport { IS_PATCHED_MODULE, canParseUrl, FetchResponse, getRawRequest, setRawRequest };", "map": {"version": 3, "names": ["IS_PATCHED_MODULE", "Symbol", "canParseUrl", "url", "URL", "_error", "getValueBySymbol", "symbolName", "source", "ownSymbols", "Object", "getOwnPropertySymbols", "symbol", "find", "symbol2", "description", "Reflect", "get", "_FetchResponse", "Response", "isConfigurableStatusCode", "status", "isRedirectResponse", "STATUS_CODES_WITH_REDIRECT", "includes", "isResponseWithBody", "STATUS_CODES_WITHOUT_BODY", "setUrl", "response", "state", "urlList", "push", "defineProperty", "value", "enumerable", "configurable", "writable", "parseRawHeaders", "rawHeaders", "headers", "Headers", "line", "length", "append", "constructor", "body", "init", "_a", "safeStatus", "finalBody", "statusText", "FetchResponse", "kRawRequest", "getRawRequest", "request", "setRawRequest", "rawRequest", "set"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/glossary.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/utils/canParseUrl.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/utils/getValueBySymbol.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/utils/fetchUtils.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/getRawRequest.ts"], "sourcesContent": ["import type { RequestController } from './RequestController'\n\nexport const IS_PATCHED_MODULE: unique symbol = Symbol('isPatchedModule')\n\n/**\n * @note Export `RequestController` as a type only.\n * It's never meant to be created in the userland.\n */\nexport type { RequestController }\n\nexport type RequestCredentials = 'omit' | 'include' | 'same-origin'\n\nexport type HttpRequestEventMap = {\n  request: [\n    args: {\n      request: Request\n      requestId: string\n      controller: RequestController\n    }\n  ]\n  response: [\n    args: {\n      response: Response\n      isMockedResponse: boolean\n      request: Request\n      requestId: string\n    }\n  ]\n  unhandledException: [\n    args: {\n      error: unknown\n      request: Request\n      requestId: string\n      controller: RequestController\n    }\n  ]\n}\n", "/**\n * Returns a boolean indicating whether the given URL string\n * can be parsed into a `URL` instance.\n * A substitute for `URL.canParse()` for Node.js 18.\n */\nexport function canParseUrl(url: string): boolean {\n  try {\n    new URL(url)\n    return true\n  } catch (_error) {\n    return false\n  }\n}\n", "/**\n * Returns the value behind the symbol with the given name.\n */\nexport function getValueBySymbol<T>(\n  symbolName: string,\n  source: object\n): T | undefined {\n  const ownSymbols = Object.getOwnPropertySymbols(source)\n\n  const symbol = ownSymbols.find((symbol) => {\n    return symbol.description === symbolName\n  })\n\n  if (symbol) {\n    return Reflect.get(source, symbol)\n  }\n\n  return\n}\n", "import { canParseUrl } from './canParseUrl'\nimport { getValueBySymbol } from './getValueBySymbol'\n\nexport interface FetchResponseInit extends ResponseInit {\n  url?: string\n}\n\ninterface UndiciFetchInternalState {\n  aborted: boolean\n  rangeRequested: boolean\n  timingAllowPassed: boolean\n  requestIncludesCredentials: boolean\n  type: ResponseType\n  status: number\n  statusText: string\n  timingInfo: unknown\n  cacheState: unknown\n  headersList: Record<symbol, Map<string, unknown>>\n  urlList: Array<URL>\n  body?: {\n    stream: ReadableStream\n    source: unknown\n    length: number\n  }\n}\n\nexport class FetchResponse extends Response {\n  /**\n   * Response status codes for responses that cannot have body.\n   * @see https://fetch.spec.whatwg.org/#statuses\n   */\n  static readonly STATUS_CODES_WITHOUT_BODY = [101, 103, 204, 205, 304]\n\n  static readonly STATUS_CODES_WITH_REDIRECT = [301, 302, 303, 307, 308]\n\n  static isConfigurableStatusCode(status: number): boolean {\n    return status >= 200 && status <= 599\n  }\n\n  static isRedirectResponse(status: number): boolean {\n    return FetchResponse.STATUS_CODES_WITH_REDIRECT.includes(status)\n  }\n\n  /**\n   * Returns a boolean indicating whether the given response status\n   * code represents a response that can have a body.\n   */\n  static isResponseWithBody(status: number): boolean {\n    return !FetchResponse.STATUS_CODES_WITHOUT_BODY.includes(status)\n  }\n\n  static setUrl(url: string | undefined, response: Response): void {\n    if (!url || url === 'about:' || !canParseUrl(url)) {\n      return\n    }\n\n    const state = getValueBySymbol<UndiciFetchInternalState>('state', response)\n\n    if (state) {\n      // In Undici, push the URL to the internal list of URLs.\n      // This will respect the `response.url` getter logic correctly.\n      state.urlList.push(new URL(url))\n    } else {\n      // In other libraries, redefine the `url` property directly.\n      Object.defineProperty(response, 'url', {\n        value: url,\n        enumerable: true,\n        configurable: true,\n        writable: false,\n      })\n    }\n  }\n\n  /**\n   * Parses the given raw HTTP headers into a Fetch API `Headers` instance.\n   */\n  static parseRawHeaders(rawHeaders: Array<string>): Headers {\n    const headers = new Headers()\n    for (let line = 0; line < rawHeaders.length; line += 2) {\n      headers.append(rawHeaders[line], rawHeaders[line + 1])\n    }\n    return headers\n  }\n\n  constructor(body?: BodyInit | null, init: FetchResponseInit = {}) {\n    const status = init.status ?? 200\n    const safeStatus = FetchResponse.isConfigurableStatusCode(status)\n      ? status\n      : 200\n    const finalBody = FetchResponse.isResponseWithBody(status) ? body : null\n\n    super(finalBody, {\n      status: safeStatus,\n      statusText: init.statusText,\n      headers: init.headers,\n    })\n\n    if (status !== safeStatus) {\n      /**\n       * @note Undici keeps an internal \"Symbol(state)\" that holds\n       * the actual value of response status. Update that in Node.js.\n       */\n      const state = getValueBySymbol<UndiciFetchInternalState>('state', this)\n\n      if (state) {\n        state.status = status\n      } else {\n        Object.defineProperty(this, 'status', {\n          value: status,\n          enumerable: true,\n          configurable: true,\n          writable: false,\n        })\n      }\n    }\n\n    FetchResponse.setUrl(init.url, this)\n  }\n}\n", "const kRawRequest = Symbol('kRawRequest')\n\n/**\n * Returns a raw request instance associated with this request.\n *\n * @example\n * interceptor.on('request', ({ request }) => {\n *   const rawRequest = getRawRequest(request)\n *\n *   if (rawRequest instanceof http.ClientRequest) {\n *     console.log(rawRequest.rawHeaders)\n *   }\n * })\n */\nexport function getRawRequest(request: Request): unknown | undefined {\n  return Reflect.get(request, kRawRequest)\n}\n\nexport function setRawRequest(request: Request, rawRequest: unknown): void {\n  Reflect.set(request, kRawRequest, rawRequest)\n}\n"], "mappings": ";AAEO,IAAMA,iBAAA,GAAmCC,MAAA,CAAO,iBAAiB;;;ACGjE,SAASC,YAAYC,GAAA,EAAsB;EAChD,IAAI;IACF,IAAIC,GAAA,CAAID,GAAG;IACX,OAAO;EACT,SAASE,MAAA,EAAP;IACA,OAAO;EACT;AACF;;;ACTO,SAASC,iBACdC,UAAA,EACAC,MAAA,EACe;EACf,MAAMC,UAAA,GAAaC,MAAA,CAAOC,qBAAA,CAAsBH,MAAM;EAEtD,MAAMI,MAAA,GAASH,UAAA,CAAWI,IAAA,CAAMC,OAAA,IAAW;IACzC,OAAOA,OAAA,CAAOC,WAAA,KAAgBR,UAAA;EAChC,CAAC;EAED,IAAIK,MAAA,EAAQ;IACV,OAAOI,OAAA,CAAQC,GAAA,CAAIT,MAAA,EAAQI,MAAM;EACnC;EAEA;AACF;;;ACQO,IAAMM,cAAA,GAAN,cAA4BC,QAAA,CAAS;EAS1C,OAAOC,yBAAyBC,MAAA,EAAyB;IACvD,OAAOA,MAAA,IAAU,OAAOA,MAAA,IAAU;EACpC;EAEA,OAAOC,mBAAmBD,MAAA,EAAyB;IACjD,OAAOH,cAAA,CAAcK,0BAAA,CAA2BC,QAAA,CAASH,MAAM;EACjE;EAAA;AAAA;AAAA;AAAA;EAMA,OAAOI,mBAAmBJ,MAAA,EAAyB;IACjD,OAAO,CAACH,cAAA,CAAcQ,yBAAA,CAA0BF,QAAA,CAASH,MAAM;EACjE;EAEA,OAAOM,OAAOxB,GAAA,EAAyByB,QAAA,EAA0B;IAC/D,IAAI,CAACzB,GAAA,IAAOA,GAAA,KAAQ,YAAY,CAACD,WAAA,CAAYC,GAAG,GAAG;MACjD;IACF;IAEA,MAAM0B,KAAA,GAAQvB,gBAAA,CAA2C,SAASsB,QAAQ;IAE1E,IAAIC,KAAA,EAAO;MAGTA,KAAA,CAAMC,OAAA,CAAQC,IAAA,CAAK,IAAI3B,GAAA,CAAID,GAAG,CAAC;IACjC,OAAO;MAELO,MAAA,CAAOsB,cAAA,CAAeJ,QAAA,EAAU,OAAO;QACrCK,KAAA,EAAO9B,GAAA;QACP+B,UAAA,EAAY;QACZC,YAAA,EAAc;QACdC,QAAA,EAAU;MACZ,CAAC;IACH;EACF;EAAA;AAAA;AAAA;EAKA,OAAOC,gBAAgBC,UAAA,EAAoC;IACzD,MAAMC,OAAA,GAAU,IAAIC,OAAA,CAAQ;IAC5B,SAASC,IAAA,GAAO,GAAGA,IAAA,GAAOH,UAAA,CAAWI,MAAA,EAAQD,IAAA,IAAQ,GAAG;MACtDF,OAAA,CAAQI,MAAA,CAAOL,UAAA,CAAWG,IAAI,GAAGH,UAAA,CAAWG,IAAA,GAAO,CAAC,CAAC;IACvD;IACA,OAAOF,OAAA;EACT;EAEAK,YAAYC,IAAA,EAAwBC,IAAA,GAA0B,CAAC,GAAG;IApFpE,IAAAC,EAAA;IAqFI,MAAM1B,MAAA,IAAS0B,EAAA,GAAAD,IAAA,CAAKzB,MAAA,KAAL,OAAA0B,EAAA,GAAe;IAC9B,MAAMC,UAAA,GAAa9B,cAAA,CAAcE,wBAAA,CAAyBC,MAAM,IAC5DA,MAAA,GACA;IACJ,MAAM4B,SAAA,GAAY/B,cAAA,CAAcO,kBAAA,CAAmBJ,MAAM,IAAIwB,IAAA,GAAO;IAEpE,MAAMI,SAAA,EAAW;MACf5B,MAAA,EAAQ2B,UAAA;MACRE,UAAA,EAAYJ,IAAA,CAAKI,UAAA;MACjBX,OAAA,EAASO,IAAA,CAAKP;IAChB,CAAC;IAED,IAAIlB,MAAA,KAAW2B,UAAA,EAAY;MAKzB,MAAMnB,KAAA,GAAQvB,gBAAA,CAA2C,SAAS,IAAI;MAEtE,IAAIuB,KAAA,EAAO;QACTA,KAAA,CAAMR,MAAA,GAASA,MAAA;MACjB,OAAO;QACLX,MAAA,CAAOsB,cAAA,CAAe,MAAM,UAAU;UACpCC,KAAA,EAAOZ,MAAA;UACPa,UAAA,EAAY;UACZC,YAAA,EAAc;UACdC,QAAA,EAAU;QACZ,CAAC;MACH;IACF;IAEAlB,cAAA,CAAcS,MAAA,CAAOmB,IAAA,CAAK3C,GAAA,EAAK,IAAI;EACrC;AACF;AA5FO,IAAMgD,aAAA,GAANjC,cAAA;AAAA;AAAA;AAAA;AAAA;AAAMiC,aAAA,CAKKzB,yBAAA,GAA4B,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AALzDyB,aAAA,CAOK5B,0BAAA,GAA6B,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;;;ACjCvE,IAAM6B,WAAA,GAAcnD,MAAA,CAAO,aAAa;AAcjC,SAASoD,cAAcC,OAAA,EAAuC;EACnE,OAAOtC,OAAA,CAAQC,GAAA,CAAIqC,OAAA,EAASF,WAAW;AACzC;AAEO,SAASG,cAAcD,OAAA,EAAkBE,UAAA,EAA2B;EACzExC,OAAA,CAAQyC,GAAA,CAAIH,OAAA,EAASF,WAAA,EAAaI,UAAU;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}