{"ast": null, "code": "import { hasConfigurableGlobal } from \"../../chunk-TX5GBTFY.mjs\";\nimport { Interceptor, createRequestId } from \"../../chunk-QED3Q6Z2.mjs\";\n\n// src/interceptors/WebSocket/utils/bindEvent.ts\nfunction bindEvent(target, event) {\n  Object.defineProperties(event, {\n    target: {\n      value: target,\n      enumerable: true,\n      writable: true\n    },\n    currentTarget: {\n      value: target,\n      enumerable: true,\n      writable: true\n    }\n  });\n  return event;\n}\n\n// src/interceptors/WebSocket/utils/events.ts\nvar kCancelable = Symbol(\"kCancelable\");\nvar kDefaultPrevented = Symbol(\"kDefaultPrevented\");\nvar CancelableMessageEvent = class extends MessageEvent {\n  constructor(type, init) {\n    super(type, init);\n    this[kCancelable] = !!init.cancelable;\n    this[kDefaultPrevented] = false;\n  }\n  get cancelable() {\n    return this[kCancelable];\n  }\n  set cancelable(nextCancelable) {\n    this[kCancelable] = nextCancelable;\n  }\n  get defaultPrevented() {\n    return this[kDefaultPrevented];\n  }\n  set defaultPrevented(nextDefaultPrevented) {\n    this[kDefaultPrevented] = nextDefaultPrevented;\n  }\n  preventDefault() {\n    if (this.cancelable && !this[kDefaultPrevented]) {\n      this[kDefaultPrevented] = true;\n    }\n  }\n};\nkCancelable, kDefaultPrevented;\nvar CloseEvent = class extends Event {\n  constructor(type, init = {}) {\n    super(type, init);\n    this.code = init.code === void 0 ? 0 : init.code;\n    this.reason = init.reason === void 0 ? \"\" : init.reason;\n    this.wasClean = init.wasClean === void 0 ? false : init.wasClean;\n  }\n};\nvar CancelableCloseEvent = class extends CloseEvent {\n  constructor(type, init = {}) {\n    super(type, init);\n    this[kCancelable] = !!init.cancelable;\n    this[kDefaultPrevented] = false;\n  }\n  get cancelable() {\n    return this[kCancelable];\n  }\n  set cancelable(nextCancelable) {\n    this[kCancelable] = nextCancelable;\n  }\n  get defaultPrevented() {\n    return this[kDefaultPrevented];\n  }\n  set defaultPrevented(nextDefaultPrevented) {\n    this[kDefaultPrevented] = nextDefaultPrevented;\n  }\n  preventDefault() {\n    if (this.cancelable && !this[kDefaultPrevented]) {\n      this[kDefaultPrevented] = true;\n    }\n  }\n};\nkCancelable, kDefaultPrevented;\n\n// src/interceptors/WebSocket/WebSocketClientConnection.ts\nvar kEmitter = Symbol(\"kEmitter\");\nvar kBoundListener = Symbol(\"kBoundListener\");\nvar WebSocketClientConnectionProtocol = class {};\nvar WebSocketClientConnection = class {\n  constructor(socket, transport) {\n    this.socket = socket;\n    this.transport = transport;\n    this.id = createRequestId();\n    this.url = new URL(socket.url);\n    this[kEmitter] = new EventTarget();\n    this.transport.addEventListener(\"outgoing\", event => {\n      const message = bindEvent(this.socket, new CancelableMessageEvent(\"message\", {\n        data: event.data,\n        origin: event.origin,\n        cancelable: true\n      }));\n      this[kEmitter].dispatchEvent(message);\n      if (message.defaultPrevented) {\n        event.preventDefault();\n      }\n    });\n    this.transport.addEventListener(\"close\", event => {\n      this[kEmitter].dispatchEvent(bindEvent(this.socket, new CloseEvent(\"close\", event)));\n    });\n  }\n  /**\n   * Listen for the outgoing events from the connected WebSocket client.\n   */\n  addEventListener(type, listener, options) {\n    if (!Reflect.has(listener, kBoundListener)) {\n      const boundListener = listener.bind(this.socket);\n      Object.defineProperty(listener, kBoundListener, {\n        value: boundListener,\n        enumerable: false,\n        configurable: false\n      });\n    }\n    this[kEmitter].addEventListener(type, Reflect.get(listener, kBoundListener), options);\n  }\n  /**\n   * Removes the listener for the given event.\n   */\n  removeEventListener(event, listener, options) {\n    this[kEmitter].removeEventListener(event, Reflect.get(listener, kBoundListener), options);\n  }\n  /**\n   * Send data to the connected client.\n   */\n  send(data) {\n    this.transport.send(data);\n  }\n  /**\n   * Close the WebSocket connection.\n   * @param {number} code A status code (see https://www.rfc-editor.org/rfc/rfc6455#section-7.4.1).\n   * @param {string} reason A custom connection close reason.\n   */\n  close(code, reason) {\n    this.transport.close(code, reason);\n  }\n};\nkEmitter;\n\n// src/interceptors/WebSocket/WebSocketServerConnection.ts\nimport { invariant as invariant2 } from \"outvariant\";\n\n// src/interceptors/WebSocket/WebSocketOverride.ts\nimport { invariant } from \"outvariant\";\nimport { DeferredPromise } from \"@open-draft/deferred-promise\";\nvar WEBSOCKET_CLOSE_CODE_RANGE_ERROR = \"InvalidAccessError: close code out of user configurable range\";\nvar kPassthroughPromise = Symbol(\"kPassthroughPromise\");\nvar kOnSend = Symbol(\"kOnSend\");\nvar kClose = Symbol(\"kClose\");\nvar WebSocketOverride = class extends EventTarget {\n  constructor(url, protocols) {\n    super();\n    this.CONNECTING = 0;\n    this.OPEN = 1;\n    this.CLOSING = 2;\n    this.CLOSED = 3;\n    this._onopen = null;\n    this._onmessage = null;\n    this._onerror = null;\n    this._onclose = null;\n    this.url = url.toString();\n    this.protocol = \"\";\n    this.extensions = \"\";\n    this.binaryType = \"blob\";\n    this.readyState = this.CONNECTING;\n    this.bufferedAmount = 0;\n    this[kPassthroughPromise] = new DeferredPromise();\n    queueMicrotask(async () => {\n      if (await this[kPassthroughPromise]) {\n        return;\n      }\n      this.protocol = typeof protocols === \"string\" ? protocols : Array.isArray(protocols) && protocols.length > 0 ? protocols[0] : \"\";\n      if (this.readyState === this.CONNECTING) {\n        this.readyState = this.OPEN;\n        this.dispatchEvent(bindEvent(this, new Event(\"open\")));\n      }\n    });\n  }\n  set onopen(listener) {\n    this.removeEventListener(\"open\", this._onopen);\n    this._onopen = listener;\n    if (listener !== null) {\n      this.addEventListener(\"open\", listener);\n    }\n  }\n  get onopen() {\n    return this._onopen;\n  }\n  set onmessage(listener) {\n    this.removeEventListener(\"message\", this._onmessage);\n    this._onmessage = listener;\n    if (listener !== null) {\n      this.addEventListener(\"message\", listener);\n    }\n  }\n  get onmessage() {\n    return this._onmessage;\n  }\n  set onerror(listener) {\n    this.removeEventListener(\"error\", this._onerror);\n    this._onerror = listener;\n    if (listener !== null) {\n      this.addEventListener(\"error\", listener);\n    }\n  }\n  get onerror() {\n    return this._onerror;\n  }\n  set onclose(listener) {\n    this.removeEventListener(\"close\", this._onclose);\n    this._onclose = listener;\n    if (listener !== null) {\n      this.addEventListener(\"close\", listener);\n    }\n  }\n  get onclose() {\n    return this._onclose;\n  }\n  /**\n   * @see https://websockets.spec.whatwg.org/#ref-for-dom-websocket-send%E2%91%A0\n   */\n  send(data) {\n    if (this.readyState === this.CONNECTING) {\n      this.close();\n      throw new DOMException(\"InvalidStateError\");\n    }\n    if (this.readyState === this.CLOSING || this.readyState === this.CLOSED) {\n      return;\n    }\n    this.bufferedAmount += getDataSize(data);\n    queueMicrotask(() => {\n      var _a;\n      this.bufferedAmount = 0;\n      (_a = this[kOnSend]) == null ? void 0 : _a.call(this, data);\n    });\n  }\n  close(code = 1e3, reason) {\n    invariant(code, WEBSOCKET_CLOSE_CODE_RANGE_ERROR);\n    invariant(code === 1e3 || code >= 3e3 && code <= 4999, WEBSOCKET_CLOSE_CODE_RANGE_ERROR);\n    this[kClose](code, reason);\n  }\n  [(kPassthroughPromise, kOnSend, kClose)](code = 1e3, reason, wasClean = true) {\n    if (this.readyState === this.CLOSING || this.readyState === this.CLOSED) {\n      return;\n    }\n    this.readyState = this.CLOSING;\n    queueMicrotask(() => {\n      this.readyState = this.CLOSED;\n      this.dispatchEvent(bindEvent(this, new CloseEvent(\"close\", {\n        code,\n        reason,\n        wasClean\n      })));\n      this._onopen = null;\n      this._onmessage = null;\n      this._onerror = null;\n      this._onclose = null;\n    });\n  }\n  addEventListener(type, listener, options) {\n    return super.addEventListener(type, listener, options);\n  }\n  removeEventListener(type, callback, options) {\n    return super.removeEventListener(type, callback, options);\n  }\n};\nWebSocketOverride.CONNECTING = 0;\nWebSocketOverride.OPEN = 1;\nWebSocketOverride.CLOSING = 2;\nWebSocketOverride.CLOSED = 3;\nfunction getDataSize(data) {\n  if (typeof data === \"string\") {\n    return data.length;\n  }\n  if (data instanceof Blob) {\n    return data.size;\n  }\n  return data.byteLength;\n}\n\n// src/interceptors/WebSocket/WebSocketServerConnection.ts\nvar kEmitter2 = Symbol(\"kEmitter\");\nvar kBoundListener2 = Symbol(\"kBoundListener\");\nvar kSend = Symbol(\"kSend\");\nvar WebSocketServerConnectionProtocol = class {};\nvar WebSocketServerConnection = class {\n  constructor(client, transport, createConnection) {\n    this.client = client;\n    this.transport = transport;\n    this.createConnection = createConnection;\n    this[kEmitter2] = new EventTarget();\n    this.mockCloseController = new AbortController();\n    this.realCloseController = new AbortController();\n    this.transport.addEventListener(\"outgoing\", event => {\n      if (typeof this.realWebSocket === \"undefined\") {\n        return;\n      }\n      queueMicrotask(() => {\n        if (!event.defaultPrevented) {\n          this[kSend](event.data);\n        }\n      });\n    });\n    this.transport.addEventListener(\"incoming\", this.handleIncomingMessage.bind(this));\n  }\n  /**\n   * The `WebSocket` instance connected to the original server.\n   * Accessing this before calling `server.connect()` will throw.\n   */\n  get socket() {\n    invariant2(this.realWebSocket, 'Cannot access \"socket\" on the original WebSocket server object: the connection is not open. Did you forget to call `server.connect()`?');\n    return this.realWebSocket;\n  }\n  /**\n   * Open connection to the original WebSocket server.\n   */\n  connect() {\n    invariant2(!this.realWebSocket || this.realWebSocket.readyState !== WebSocket.OPEN, 'Failed to call \"connect()\" on the original WebSocket instance: the connection already open');\n    const realWebSocket = this.createConnection();\n    realWebSocket.binaryType = this.client.binaryType;\n    realWebSocket.addEventListener(\"open\", event => {\n      this[kEmitter2].dispatchEvent(bindEvent(this.realWebSocket, new Event(\"open\", event)));\n    }, {\n      once: true\n    });\n    realWebSocket.addEventListener(\"message\", event => {\n      this.transport.dispatchEvent(bindEvent(this.realWebSocket, new MessageEvent(\"incoming\", {\n        data: event.data,\n        origin: event.origin\n      })));\n    });\n    this.client.addEventListener(\"close\", event => {\n      this.handleMockClose(event);\n    }, {\n      signal: this.mockCloseController.signal\n    });\n    realWebSocket.addEventListener(\"close\", event => {\n      this.handleRealClose(event);\n    }, {\n      signal: this.realCloseController.signal\n    });\n    realWebSocket.addEventListener(\"error\", () => {\n      const errorEvent = bindEvent(realWebSocket, new Event(\"error\", {\n        cancelable: true\n      }));\n      this[kEmitter2].dispatchEvent(errorEvent);\n      if (!errorEvent.defaultPrevented) {\n        this.client.dispatchEvent(bindEvent(this.client, new Event(\"error\")));\n      }\n    });\n    this.realWebSocket = realWebSocket;\n  }\n  /**\n   * Listen for the incoming events from the original WebSocket server.\n   */\n  addEventListener(event, listener, options) {\n    if (!Reflect.has(listener, kBoundListener2)) {\n      const boundListener = listener.bind(this.client);\n      Object.defineProperty(listener, kBoundListener2, {\n        value: boundListener,\n        enumerable: false\n      });\n    }\n    this[kEmitter2].addEventListener(event, Reflect.get(listener, kBoundListener2), options);\n  }\n  /**\n   * Remove the listener for the given event.\n   */\n  removeEventListener(event, listener, options) {\n    this[kEmitter2].removeEventListener(event, Reflect.get(listener, kBoundListener2), options);\n  }\n  /**\n   * Send data to the original WebSocket server.\n   * @example\n   * server.send('hello')\n   * server.send(new Blob(['hello']))\n   * server.send(new TextEncoder().encode('hello'))\n   */\n  send(data) {\n    this[kSend](data);\n  }\n  [(kEmitter2, kSend)](data) {\n    const {\n      realWebSocket\n    } = this;\n    invariant2(realWebSocket, 'Failed to call \"server.send()\" for \"%s\": the connection is not open. Did you forget to call \"server.connect()\"?', this.client.url);\n    if (realWebSocket.readyState === WebSocket.CLOSING || realWebSocket.readyState === WebSocket.CLOSED) {\n      return;\n    }\n    if (realWebSocket.readyState === WebSocket.CONNECTING) {\n      realWebSocket.addEventListener(\"open\", () => {\n        realWebSocket.send(data);\n      }, {\n        once: true\n      });\n      return;\n    }\n    realWebSocket.send(data);\n  }\n  /**\n   * Close the actual server connection.\n   */\n  close() {\n    const {\n      realWebSocket\n    } = this;\n    invariant2(realWebSocket, 'Failed to close server connection for \"%s\": the connection is not open. Did you forget to call \"server.connect()\"?', this.client.url);\n    this.realCloseController.abort();\n    if (realWebSocket.readyState === WebSocket.CLOSING || realWebSocket.readyState === WebSocket.CLOSED) {\n      return;\n    }\n    realWebSocket.close();\n    queueMicrotask(() => {\n      this[kEmitter2].dispatchEvent(bindEvent(this.realWebSocket, new CancelableCloseEvent(\"close\", {\n        /**\n         * @note `server.close()` in the interceptor\n         * always results in clean closures.\n         */\n        code: 1e3,\n        cancelable: true\n      })));\n    });\n  }\n  handleIncomingMessage(event) {\n    const messageEvent = bindEvent(event.target, new CancelableMessageEvent(\"message\", {\n      data: event.data,\n      origin: event.origin,\n      cancelable: true\n    }));\n    this[kEmitter2].dispatchEvent(messageEvent);\n    if (!messageEvent.defaultPrevented) {\n      this.client.dispatchEvent(bindEvent(\n      /**\n       * @note Bind the forwarded original server events\n       * to the mock WebSocket instance so it would\n       * dispatch them straight away.\n       */\n      this.client,\n      // Clone the message event again to prevent\n      // the \"already being dispatched\" exception.\n      new MessageEvent(\"message\", {\n        data: event.data,\n        origin: event.origin\n      })));\n    }\n  }\n  handleMockClose(_event) {\n    if (this.realWebSocket) {\n      this.realWebSocket.close();\n    }\n  }\n  handleRealClose(event) {\n    this.mockCloseController.abort();\n    const closeEvent = bindEvent(this.realWebSocket, new CancelableCloseEvent(\"close\", {\n      code: event.code,\n      reason: event.reason,\n      wasClean: event.wasClean,\n      cancelable: true\n    }));\n    this[kEmitter2].dispatchEvent(closeEvent);\n    if (!closeEvent.defaultPrevented) {\n      this.client[kClose](event.code, event.reason);\n    }\n  }\n};\n\n// src/interceptors/WebSocket/WebSocketClassTransport.ts\nvar WebSocketClassTransport = class extends EventTarget {\n  constructor(socket) {\n    super();\n    this.socket = socket;\n    this.socket.addEventListener(\"close\", event => {\n      this.dispatchEvent(bindEvent(this.socket, new CloseEvent(\"close\", event)));\n    });\n    this.socket[kOnSend] = data => {\n      this.dispatchEvent(bindEvent(this.socket,\n      // Dispatch this as cancelable because \"client\" connection\n      // re-creates this message event (cannot dispatch the same event).\n      new CancelableMessageEvent(\"outgoing\", {\n        data,\n        origin: this.socket.url,\n        cancelable: true\n      })));\n    };\n  }\n  addEventListener(type, callback, options) {\n    return super.addEventListener(type, callback, options);\n  }\n  dispatchEvent(event) {\n    return super.dispatchEvent(event);\n  }\n  send(data) {\n    queueMicrotask(() => {\n      if (this.socket.readyState === this.socket.CLOSING || this.socket.readyState === this.socket.CLOSED) {\n        return;\n      }\n      const dispatchEvent = () => {\n        this.socket.dispatchEvent(bindEvent(\n        /**\n         * @note Setting this event's \"target\" to the\n         * WebSocket override instance is important.\n         * This way it can tell apart original incoming events\n         * (must be forwarded to the transport) from the\n         * mocked message events like the one below\n         * (must be dispatched on the client instance).\n         */\n        this.socket, new MessageEvent(\"message\", {\n          data,\n          origin: this.socket.url\n        })));\n      };\n      if (this.socket.readyState === this.socket.CONNECTING) {\n        this.socket.addEventListener(\"open\", () => {\n          dispatchEvent();\n        }, {\n          once: true\n        });\n      } else {\n        dispatchEvent();\n      }\n    });\n  }\n  close(code, reason) {\n    this.socket[kClose](code, reason);\n  }\n};\n\n// src/interceptors/WebSocket/index.ts\nvar _WebSocketInterceptor = class extends Interceptor {\n  constructor() {\n    super(_WebSocketInterceptor.symbol);\n  }\n  checkEnvironment() {\n    return hasConfigurableGlobal(\"WebSocket\");\n  }\n  setup() {\n    const originalWebSocketDescriptor = Object.getOwnPropertyDescriptor(globalThis, \"WebSocket\");\n    const WebSocketProxy = new Proxy(globalThis.WebSocket, {\n      construct: (target, args, newTarget) => {\n        const [url, protocols] = args;\n        const createConnection = () => {\n          return Reflect.construct(target, args, newTarget);\n        };\n        const socket = new WebSocketOverride(url, protocols);\n        const transport = new WebSocketClassTransport(socket);\n        queueMicrotask(() => {\n          try {\n            const server = new WebSocketServerConnection(socket, transport, createConnection);\n            const hasConnectionListeners = this.emitter.emit(\"connection\", {\n              client: new WebSocketClientConnection(socket, transport),\n              server,\n              info: {\n                protocols\n              }\n            });\n            if (hasConnectionListeners) {\n              socket[kPassthroughPromise].resolve(false);\n            } else {\n              socket[kPassthroughPromise].resolve(true);\n              server.connect();\n              server.addEventListener(\"open\", () => {\n                socket.dispatchEvent(bindEvent(socket, new Event(\"open\")));\n                if (server[\"realWebSocket\"]) {\n                  socket.protocol = server[\"realWebSocket\"].protocol;\n                }\n              });\n            }\n          } catch (error) {\n            if (error instanceof Error) {\n              socket.dispatchEvent(new Event(\"error\"));\n              if (socket.readyState !== WebSocket.CLOSING && socket.readyState !== WebSocket.CLOSED) {\n                socket[kClose](1011, error.message, false);\n              }\n              console.error(error);\n            }\n          }\n        });\n        return socket;\n      }\n    });\n    Object.defineProperty(globalThis, \"WebSocket\", {\n      value: WebSocketProxy,\n      configurable: true\n    });\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis, \"WebSocket\", originalWebSocketDescriptor);\n    });\n  }\n};\nvar WebSocketInterceptor = _WebSocketInterceptor;\nWebSocketInterceptor.symbol = Symbol(\"websocket\");\nexport { CancelableCloseEvent, CancelableMessageEvent, CloseEvent, WebSocketClientConnection, WebSocketClientConnectionProtocol, WebSocketInterceptor, WebSocketServerConnection, WebSocketServerConnectionProtocol };", "map": {"version": 3, "names": ["bindEvent", "target", "event", "Object", "defineProperties", "value", "enumerable", "writable", "currentTarget", "kCancelable", "Symbol", "kDefaultPrevented", "CancelableMessageEvent", "MessageEvent", "constructor", "type", "init", "cancelable", "nextCancelable", "defaultPrevented", "nextDefaultPrevented", "preventDefault", "CloseEvent", "Event", "code", "reason", "<PERSON><PERSON><PERSON>", "CancelableCloseEvent", "kEmitter", "kBoundListener", "WebSocketClientConnectionProtocol", "WebSocketClientConnection", "socket", "transport", "id", "createRequestId", "url", "URL", "EventTarget", "addEventListener", "message", "data", "origin", "dispatchEvent", "listener", "options", "Reflect", "has", "boundListener", "bind", "defineProperty", "configurable", "get", "removeEventListener", "send", "close", "invariant", "invariant2", "DeferredPromise", "WEBSOCKET_CLOSE_CODE_RANGE_ERROR", "kPassthroughPromise", "kOnSend", "kClose", "WebSocketOverride", "protocols", "CONNECTING", "OPEN", "CLOSING", "CLOSED", "_onopen", "_onmessage", "_onerror", "_onclose", "toString", "protocol", "extensions", "binaryType", "readyState", "bufferedAmount", "queueMicrotask", "Array", "isArray", "length", "onopen", "onmessage", "onerror", "onclose", "DOMException", "getDataSize", "_a", "call", "callback", "Blob", "size", "byteLength", "kEmitter2", "kBoundListener2", "kSend", "WebSocketServerConnectionProtocol", "WebSocketServerConnection", "client", "createConnection", "mockCloseController", "AbortController", "realCloseController", "realWebSocket", "handleIncomingMessage", "connect", "WebSocket", "once", "handleMockClose", "signal", "handleRealClose", "errorEvent", "abort", "messageEvent", "_event", "closeEvent", "WebSocketClassTransport", "_WebSocketInterceptor", "Interceptor", "symbol", "checkEnvironment", "hasConfigurableGlobal", "setup", "originalWebSocketDescriptor", "getOwnPropertyDescriptor", "globalThis", "WebSocketProxy", "Proxy", "construct", "args", "newTarget", "server", "hasConnectionListeners", "emitter", "emit", "info", "resolve", "error", "Error", "console", "subscriptions", "push", "WebSocketInterceptor"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/interceptors/WebSocket/utils/bindEvent.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/interceptors/WebSocket/utils/events.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/interceptors/WebSocket/WebSocketClientConnection.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/interceptors/WebSocket/WebSocketServerConnection.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/interceptors/WebSocket/WebSocketOverride.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/interceptors/WebSocket/WebSocketClassTransport.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/interceptors/WebSocket/index.ts"], "sourcesContent": ["type EventWithTarget<E extends Event, T> = E & { target: T }\n\nexport function bindEvent<E extends Event, T>(\n  target: T,\n  event: E\n): EventWithTarget<E, T> {\n  Object.defineProperties(event, {\n    target: {\n      value: target,\n      enumerable: true,\n      writable: true,\n    },\n    currentTarget: {\n      value: target,\n      enumerable: true,\n      writable: true,\n    },\n  })\n\n  return event as EventWithTarget<E, T>\n}\n", "const kCancelable = Symbol('kCancelable')\nconst kDefaultPrevented = Symbol('kDefaultPrevented')\n\n/**\n * A `MessageEvent` superset that supports event cancellation\n * in Node.js. It's rather non-intrusive so it can be safely\n * used in the browser as well.\n *\n * @see https://github.com/nodejs/node/issues/51767\n */\nexport class CancelableMessageEvent<T = any> extends MessageEvent<T> {\n  [kCancelable]: boolean;\n  [kDefaultPrevented]: boolean\n\n  constructor(type: string, init: MessageEventInit<T>) {\n    super(type, init)\n    this[kCancelable] = !!init.cancelable\n    this[kDefaultPrevented] = false\n  }\n\n  get cancelable() {\n    return this[kCancelable]\n  }\n\n  set cancelable(nextCancelable) {\n    this[kCancelable] = nextCancelable\n  }\n\n  get defaultPrevented() {\n    return this[kDefaultPrevented]\n  }\n\n  set defaultPrevented(nextDefaultPrevented) {\n    this[kDefaultPrevented] = nextDefaultPrevented\n  }\n\n  public preventDefault(): void {\n    if (this.cancelable && !this[kDefaultPrevented]) {\n      this[kDefaultPrevented] = true\n    }\n  }\n}\n\ninterface CloseEventInit extends EventInit {\n  code?: number\n  reason?: string\n  wasClean?: boolean\n}\n\nexport class CloseEvent extends Event {\n  public code: number\n  public reason: string\n  public wasClean: boolean\n\n  constructor(type: string, init: CloseEventInit = {}) {\n    super(type, init)\n    this.code = init.code === undefined ? 0 : init.code\n    this.reason = init.reason === undefined ? '' : init.reason\n    this.wasClean = init.wasClean === undefined ? false : init.wasClean\n  }\n}\n\nexport class CancelableCloseEvent extends CloseEvent {\n  [kCancelable]: boolean;\n  [kDefaultPrevented]: boolean\n\n  constructor(type: string, init: CloseEventInit = {}) {\n    super(type, init)\n    this[kCancelable] = !!init.cancelable\n    this[kDefaultPrevented] = false\n  }\n\n  get cancelable() {\n    return this[kCancelable]\n  }\n\n  set cancelable(nextCancelable) {\n    this[kCancelable] = nextCancelable\n  }\n\n  get defaultPrevented() {\n    return this[kDefaultPrevented]\n  }\n\n  set defaultPrevented(nextDefaultPrevented) {\n    this[kDefaultPrevented] = nextDefaultPrevented\n  }\n\n  public preventDefault(): void {\n    if (this.cancelable && !this[kDefaultPrevented]) {\n      this[kDefaultPrevented] = true\n    }\n  }\n}\n", "import type { WebSocketData, WebSocketTransport } from './WebSocketTransport'\nimport type { WebSocketEventListener } from './WebSocketOverride'\nimport { bindEvent } from './utils/bindEvent'\nimport { CancelableMessageEvent, CloseEvent } from './utils/events'\nimport { createRequestId } from '../../createRequestId'\n\nconst kEmitter = Symbol('kEmitter')\nconst kBoundListener = Symbol('kBoundListener')\n\nexport interface WebSocketClientEventMap {\n  message: MessageEvent<WebSocketData>\n  close: CloseEvent\n}\n\nexport abstract class WebSocketClientConnectionProtocol {\n  abstract id: string\n  abstract url: URL\n  public abstract send(data: WebSocketData): void\n  public abstract close(code?: number, reason?: string): void\n\n  public abstract addEventListener<\n    EventType extends keyof WebSocketClientEventMap\n  >(\n    type: EventType,\n    listener: WebSocketEventListener<WebSocketClientEventMap[EventType]>,\n    options?: AddEventListenerOptions | boolean\n  ): void\n\n  public abstract removeEventListener<\n    EventType extends keyof WebSocketClientEventMap\n  >(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketClientEventMap[EventType]>,\n    options?: EventListenerOptions | boolean\n  ): void\n}\n\n/**\n * The WebSocket client instance represents an incoming\n * client connection. The user can control the connection,\n * send and receive events.\n */\nexport class WebSocketClientConnection\n  implements WebSocketClientConnectionProtocol\n{\n  public readonly id: string\n  public readonly url: URL\n\n  private [kEmitter]: EventTarget\n\n  constructor(\n    public readonly socket: WebSocket,\n    private readonly transport: WebSocketTransport\n  ) {\n    this.id = createRequestId()\n    this.url = new URL(socket.url)\n    this[kEmitter] = new EventTarget()\n\n    // Emit outgoing client data (\"ws.send()\") as \"message\"\n    // events on the \"client\" connection.\n    this.transport.addEventListener('outgoing', (event) => {\n      const message = bindEvent(\n        this.socket,\n        new CancelableMessageEvent('message', {\n          data: event.data,\n          origin: event.origin,\n          cancelable: true,\n        })\n      )\n\n      this[kEmitter].dispatchEvent(message)\n\n      // This is a bit silly but forward the cancellation state\n      // of the \"client\" message event to the \"outgoing\" transport event.\n      // This way, other agens (like \"server\" connection) can know\n      // whether the client listener has pervented the default.\n      if (message.defaultPrevented) {\n        event.preventDefault()\n      }\n    })\n\n    /**\n     * Emit the \"close\" event on the \"client\" connection\n     * whenever the underlying transport is closed.\n     * @note \"client.close()\" does NOT dispatch the \"close\"\n     * event on the WebSocket because it uses non-configurable\n     * close status code. Thus, we listen to the transport\n     * instead of the WebSocket's \"close\" event.\n     */\n    this.transport.addEventListener('close', (event) => {\n      this[kEmitter].dispatchEvent(\n        bindEvent(this.socket, new CloseEvent('close', event))\n      )\n    })\n  }\n\n  /**\n   * Listen for the outgoing events from the connected WebSocket client.\n   */\n  public addEventListener<EventType extends keyof WebSocketClientEventMap>(\n    type: EventType,\n    listener: WebSocketEventListener<WebSocketClientEventMap[EventType]>,\n    options?: AddEventListenerOptions | boolean\n  ): void {\n    if (!Reflect.has(listener, kBoundListener)) {\n      const boundListener = listener.bind(this.socket)\n\n      // Store the bound listener on the original listener\n      // so the exact bound function can be accessed in \"removeEventListener()\".\n      Object.defineProperty(listener, kBoundListener, {\n        value: boundListener,\n        enumerable: false,\n        configurable: false,\n      })\n    }\n\n    this[kEmitter].addEventListener(\n      type,\n      Reflect.get(listener, kBoundListener) as EventListener,\n      options\n    )\n  }\n\n  /**\n   * Removes the listener for the given event.\n   */\n  public removeEventListener<EventType extends keyof WebSocketClientEventMap>(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketClientEventMap[EventType]>,\n    options?: EventListenerOptions | boolean\n  ): void {\n    this[kEmitter].removeEventListener(\n      event,\n      Reflect.get(listener, kBoundListener) as EventListener,\n      options\n    )\n  }\n\n  /**\n   * Send data to the connected client.\n   */\n  public send(data: WebSocketData): void {\n    this.transport.send(data)\n  }\n\n  /**\n   * Close the WebSocket connection.\n   * @param {number} code A status code (see https://www.rfc-editor.org/rfc/rfc6455#section-7.4.1).\n   * @param {string} reason A custom connection close reason.\n   */\n  public close(code?: number, reason?: string): void {\n    this.transport.close(code, reason)\n  }\n}\n", "import { invariant } from 'outvariant'\nimport {\n  kClose,\n  WebSocketEventListener,\n  WebSocketOverride,\n} from './WebSocketOverride'\nimport type { WebSocketData } from './WebSocketTransport'\nimport type { WebSocketClassTransport } from './WebSocketClassTransport'\nimport { bindEvent } from './utils/bindEvent'\nimport {\n  CancelableMessageEvent,\n  CancelableCloseEvent,\n  CloseEvent,\n} from './utils/events'\n\nconst kEmitter = Symbol('kEmitter')\nconst kBoundListener = Symbol('kBoundListener')\nconst kSend = Symbol('kSend')\n\nexport interface WebSocketServerEventMap {\n  open: Event\n  message: MessageEvent<WebSocketData>\n  error: Event\n  close: CloseEvent\n}\n\nexport abstract class WebSocketServerConnectionProtocol {\n  public abstract connect(): void\n  public abstract send(data: WebSocketData): void\n  public abstract close(): void\n\n  public abstract addEventListener<\n    EventType extends keyof WebSocketServerEventMap\n  >(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketServerEventMap[EventType]>,\n    options?: AddEventListenerOptions | boolean\n  ): void\n\n  public abstract removeEventListener<\n    EventType extends keyof WebSocketServerEventMap\n  >(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketServerEventMap[EventType]>,\n    options?: EventListenerOptions | boolean\n  ): void\n}\n\n/**\n * The WebSocket server instance represents the actual production\n * WebSocket server connection. It's idle by default but you can\n * establish it by calling `server.connect()`.\n */\nexport class WebSocketServerConnection\n  implements WebSocketServerConnectionProtocol\n{\n  /**\n   * A WebSocket instance connected to the original server.\n   */\n  private realWebSocket?: WebSocket\n  private mockCloseController: AbortController\n  private realCloseController: AbortController\n  private [kEmitter]: EventTarget\n\n  constructor(\n    private readonly client: WebSocketOverride,\n    private readonly transport: WebSocketClassTransport,\n    private readonly createConnection: () => WebSocket\n  ) {\n    this[kEmitter] = new EventTarget()\n    this.mockCloseController = new AbortController()\n    this.realCloseController = new AbortController()\n\n    // Automatically forward outgoing client events\n    // to the actual server unless the outgoing message event\n    // has been prevented. The \"outgoing\" transport event it\n    // dispatched by the \"client\" connection.\n    this.transport.addEventListener('outgoing', (event) => {\n      // Ignore client messages if the server connection\n      // hasn't been established yet. Nowhere to forward.\n      if (typeof this.realWebSocket === 'undefined') {\n        return\n      }\n\n      // Every outgoing client message can prevent this forwarding\n      // by preventing the default of the outgoing message event.\n      // This listener will be added before user-defined listeners,\n      // so execute the logic on the next tick.\n      queueMicrotask(() => {\n        if (!event.defaultPrevented) {\n          /**\n           * @note Use the internal send mechanism so consumers can tell\n           * apart direct user calls to `server.send()` and internal calls.\n           * E.g. MSW has to ignore this internal call to log out messages correctly.\n           */\n          this[kSend](event.data)\n        }\n      })\n    })\n\n    this.transport.addEventListener(\n      'incoming',\n      this.handleIncomingMessage.bind(this)\n    )\n  }\n\n  /**\n   * The `WebSocket` instance connected to the original server.\n   * Accessing this before calling `server.connect()` will throw.\n   */\n  public get socket(): WebSocket {\n    invariant(\n      this.realWebSocket,\n      'Cannot access \"socket\" on the original WebSocket server object: the connection is not open. Did you forget to call `server.connect()`?'\n    )\n\n    return this.realWebSocket\n  }\n\n  /**\n   * Open connection to the original WebSocket server.\n   */\n  public connect(): void {\n    invariant(\n      !this.realWebSocket || this.realWebSocket.readyState !== WebSocket.OPEN,\n      'Failed to call \"connect()\" on the original WebSocket instance: the connection already open'\n    )\n\n    const realWebSocket = this.createConnection()\n\n    // Inherit the binary type from the mock WebSocket client.\n    realWebSocket.binaryType = this.client.binaryType\n\n    // Allow the interceptor to listen to when the server connection\n    // has been established. This isn't necessary to operate with the connection\n    // but may be beneficial in some cases (like conditionally adding logging).\n    realWebSocket.addEventListener(\n      'open',\n      (event) => {\n        this[kEmitter].dispatchEvent(\n          bindEvent(this.realWebSocket!, new Event('open', event))\n        )\n      },\n      { once: true }\n    )\n\n    realWebSocket.addEventListener('message', (event) => {\n      // Dispatch the \"incoming\" transport event instead of\n      // invoking the internal handler directly. This way,\n      // anyone can listen to the \"incoming\" event but this\n      // class is the one resulting in it.\n      this.transport.dispatchEvent(\n        bindEvent(\n          this.realWebSocket!,\n          new MessageEvent('incoming', {\n            data: event.data,\n            origin: event.origin,\n          })\n        )\n      )\n    })\n\n    // Close the original connection when the mock client closes.\n    // E.g. \"client.close()\" was called. This is never forwarded anywhere.\n    this.client.addEventListener(\n      'close',\n      (event) => {\n        this.handleMockClose(event)\n      },\n      {\n        signal: this.mockCloseController.signal,\n      }\n    )\n\n    // Forward the \"close\" event to let the interceptor handle\n    // closures initiated by the original server.\n    realWebSocket.addEventListener(\n      'close',\n      (event) => {\n        this.handleRealClose(event)\n      },\n      {\n        signal: this.realCloseController.signal,\n      }\n    )\n\n    realWebSocket.addEventListener('error', () => {\n      const errorEvent = bindEvent(\n        realWebSocket,\n        new Event('error', { cancelable: true })\n      )\n\n      // Emit the \"error\" event on the `server` connection\n      // to let the interceptor react to original server errors.\n      this[kEmitter].dispatchEvent(errorEvent)\n\n      // If the error event from the original server hasn't been prevented,\n      // forward it to the underlying client.\n      if (!errorEvent.defaultPrevented) {\n        this.client.dispatchEvent(bindEvent(this.client, new Event('error')))\n      }\n    })\n\n    this.realWebSocket = realWebSocket\n  }\n\n  /**\n   * Listen for the incoming events from the original WebSocket server.\n   */\n  public addEventListener<EventType extends keyof WebSocketServerEventMap>(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketServerEventMap[EventType]>,\n    options?: AddEventListenerOptions | boolean\n  ): void {\n    if (!Reflect.has(listener, kBoundListener)) {\n      const boundListener = listener.bind(this.client)\n\n      // Store the bound listener on the original listener\n      // so the exact bound function can be accessed in \"removeEventListener()\".\n      Object.defineProperty(listener, kBoundListener, {\n        value: boundListener,\n        enumerable: false,\n      })\n    }\n\n    this[kEmitter].addEventListener(\n      event,\n      Reflect.get(listener, kBoundListener) as EventListener,\n      options\n    )\n  }\n\n  /**\n   * Remove the listener for the given event.\n   */\n  public removeEventListener<EventType extends keyof WebSocketServerEventMap>(\n    event: EventType,\n    listener: WebSocketEventListener<WebSocketServerEventMap[EventType]>,\n    options?: EventListenerOptions | boolean\n  ): void {\n    this[kEmitter].removeEventListener(\n      event,\n      Reflect.get(listener, kBoundListener) as EventListener,\n      options\n    )\n  }\n\n  /**\n   * Send data to the original WebSocket server.\n   * @example\n   * server.send('hello')\n   * server.send(new Blob(['hello']))\n   * server.send(new TextEncoder().encode('hello'))\n   */\n  public send(data: WebSocketData): void {\n    this[kSend](data)\n  }\n\n  private [kSend](data: WebSocketData): void {\n    const { realWebSocket } = this\n\n    invariant(\n      realWebSocket,\n      'Failed to call \"server.send()\" for \"%s\": the connection is not open. Did you forget to call \"server.connect()\"?',\n      this.client.url\n    )\n\n    // Silently ignore writes on the closed original WebSocket.\n    if (\n      realWebSocket.readyState === WebSocket.CLOSING ||\n      realWebSocket.readyState === WebSocket.CLOSED\n    ) {\n      return\n    }\n\n    // Delegate the send to when the original connection is open.\n    // Unlike the mock, connecting to the original server may take time\n    // so we cannot call this on the next tick.\n    if (realWebSocket.readyState === WebSocket.CONNECTING) {\n      realWebSocket.addEventListener(\n        'open',\n        () => {\n          realWebSocket.send(data)\n        },\n        { once: true }\n      )\n      return\n    }\n\n    // Send the data to the original WebSocket server.\n    realWebSocket.send(data)\n  }\n\n  /**\n   * Close the actual server connection.\n   */\n  public close(): void {\n    const { realWebSocket } = this\n\n    invariant(\n      realWebSocket,\n      'Failed to close server connection for \"%s\": the connection is not open. Did you forget to call \"server.connect()\"?',\n      this.client.url\n    )\n\n    // Remove the \"close\" event listener from the server\n    // so it doesn't close the underlying WebSocket client\n    // when you call \"server.close()\". This also prevents the\n    // `close` event on the `server` connection from being dispatched twice.\n    this.realCloseController.abort()\n\n    if (\n      realWebSocket.readyState === WebSocket.CLOSING ||\n      realWebSocket.readyState === WebSocket.CLOSED\n    ) {\n      return\n    }\n\n    // Close the actual client connection.\n    realWebSocket.close()\n\n    // Dispatch the \"close\" event on the `server` connection.\n    queueMicrotask(() => {\n      this[kEmitter].dispatchEvent(\n        bindEvent(\n          this.realWebSocket,\n          new CancelableCloseEvent('close', {\n            /**\n             * @note `server.close()` in the interceptor\n             * always results in clean closures.\n             */\n            code: 1000,\n            cancelable: true,\n          })\n        )\n      )\n    })\n  }\n\n  private handleIncomingMessage(event: MessageEvent<WebSocketData>): void {\n    // Clone the event to dispatch it on this class\n    // once again and prevent the \"already being dispatched\"\n    // exception. Clone it here so we can observe this event\n    // being prevented in the \"server.on()\" listeners.\n    const messageEvent = bindEvent(\n      event.target,\n      new CancelableMessageEvent('message', {\n        data: event.data,\n        origin: event.origin,\n        cancelable: true,\n      })\n    )\n\n    /**\n     * @note Emit \"message\" event on the server connection\n     * instance to let the interceptor know about these\n     * incoming events from the original server. In that listener,\n     * the interceptor can modify or skip the event forwarding\n     * to the mock WebSocket instance.\n     */\n    this[kEmitter].dispatchEvent(messageEvent)\n\n    /**\n     * @note Forward the incoming server events to the client.\n     * Preventing the default on the message event stops this.\n     */\n    if (!messageEvent.defaultPrevented) {\n      this.client.dispatchEvent(\n        bindEvent(\n          /**\n           * @note Bind the forwarded original server events\n           * to the mock WebSocket instance so it would\n           * dispatch them straight away.\n           */\n          this.client,\n          // Clone the message event again to prevent\n          // the \"already being dispatched\" exception.\n          new MessageEvent('message', {\n            data: event.data,\n            origin: event.origin,\n          })\n        )\n      )\n    }\n  }\n\n  private handleMockClose(_event: Event): void {\n    // Close the original connection if the mock client closes.\n    if (this.realWebSocket) {\n      this.realWebSocket.close()\n    }\n  }\n\n  private handleRealClose(event: CloseEvent): void {\n    // For closures originating from the original server,\n    // remove the \"close\" listener from the mock client.\n    // original close -> (?) client[kClose]() --X--> \"close\" (again).\n    this.mockCloseController.abort()\n\n    const closeEvent = bindEvent(\n      this.realWebSocket,\n      new CancelableCloseEvent('close', {\n        code: event.code,\n        reason: event.reason,\n        wasClean: event.wasClean,\n        cancelable: true,\n      })\n    )\n\n    this[kEmitter].dispatchEvent(closeEvent)\n\n    // If the close event from the server hasn't been prevented,\n    // forward the closure to the mock client.\n    if (!closeEvent.defaultPrevented) {\n      // Close the intercepted client forcefully to\n      // allow non-configurable status codes from the server.\n      // If the socket has been closed by now, no harm calling\n      // this again—it will have no effect.\n      this.client[kClose](event.code, event.reason)\n    }\n  }\n}\n", "import { invariant } from 'outvariant'\nimport type { WebSocketData } from './WebSocketTransport'\nimport { bindEvent } from './utils/bindEvent'\nimport { CloseEvent } from './utils/events'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\n\nexport type WebSocketEventListener<\n  EventType extends WebSocketEventMap[keyof WebSocketEventMap] = Event\n> = (this: WebSocket, event: EventType) => void\n\nconst WEBSOCKET_CLOSE_CODE_RANGE_ERROR =\n  'InvalidAccessError: close code out of user configurable range'\n\nexport const kPassthroughPromise = Symbol('kPassthroughPromise')\nexport const kOnSend = Symbol('kOnSend')\nexport const kClose = Symbol('kClose')\n\nexport class WebSocketOverride extends EventTarget implements WebSocket {\n  static readonly CONNECTING = 0\n  static readonly OPEN = 1\n  static readonly CLOSING = 2\n  static readonly CLOSED = 3\n  readonly CONNECTING = 0\n  readonly OPEN = 1\n  readonly CLOSING = 2\n  readonly CLOSED = 3\n\n  public url: string\n  public protocol: string\n  public extensions: string\n  public binaryType: BinaryType\n  public readyState: number\n  public bufferedAmount: number\n\n  private _onopen: WebSocketEventListener | null = null\n  private _onmessage: WebSocketEventListener<\n    MessageEvent<WebSocketData>\n  > | null = null\n  private _onerror: WebSocketEventListener | null = null\n  private _onclose: WebSocketEventListener<CloseEvent> | null = null\n\n  private [kPassthroughPromise]: DeferredPromise<boolean>\n  private [kOnSend]?: (data: WebSocketData) => void\n\n  constructor(url: string | URL, protocols?: string | Array<string>) {\n    super()\n    this.url = url.toString()\n    this.protocol = ''\n    this.extensions = ''\n    this.binaryType = 'blob'\n    this.readyState = this.CONNECTING\n    this.bufferedAmount = 0\n\n    this[kPassthroughPromise] = new DeferredPromise<boolean>()\n\n    queueMicrotask(async () => {\n      if (await this[kPassthroughPromise]) {\n        return\n      }\n\n      this.protocol =\n        typeof protocols === 'string'\n          ? protocols\n          : Array.isArray(protocols) && protocols.length > 0\n          ? protocols[0]\n          : ''\n\n      /**\n       * @note Check that nothing has prevented this connection\n       * (e.g. called `client.close()` in the connection listener).\n       * If the connection has been prevented, never dispatch the open event,.\n       */\n      if (this.readyState === this.CONNECTING) {\n        this.readyState = this.OPEN\n        this.dispatchEvent(bindEvent(this, new Event('open')))\n      }\n    })\n  }\n\n  set onopen(listener: WebSocketEventListener | null) {\n    this.removeEventListener('open', this._onopen)\n    this._onopen = listener\n    if (listener !== null) {\n      this.addEventListener('open', listener)\n    }\n  }\n  get onopen(): WebSocketEventListener | null {\n    return this._onopen\n  }\n\n  set onmessage(\n    listener: WebSocketEventListener<MessageEvent<WebSocketData>> | null\n  ) {\n    this.removeEventListener(\n      'message',\n      this._onmessage as WebSocketEventListener\n    )\n    this._onmessage = listener\n    if (listener !== null) {\n      this.addEventListener('message', listener)\n    }\n  }\n  get onmessage(): WebSocketEventListener<MessageEvent<WebSocketData>> | null {\n    return this._onmessage\n  }\n\n  set onerror(listener: WebSocketEventListener | null) {\n    this.removeEventListener('error', this._onerror)\n    this._onerror = listener\n    if (listener !== null) {\n      this.addEventListener('error', listener)\n    }\n  }\n  get onerror(): WebSocketEventListener | null {\n    return this._onerror\n  }\n\n  set onclose(listener: WebSocketEventListener<CloseEvent> | null) {\n    this.removeEventListener('close', this._onclose as WebSocketEventListener)\n    this._onclose = listener\n    if (listener !== null) {\n      this.addEventListener('close', listener)\n    }\n  }\n  get onclose(): WebSocketEventListener<CloseEvent> | null {\n    return this._onclose\n  }\n\n  /**\n   * @see https://websockets.spec.whatwg.org/#ref-for-dom-websocket-send%E2%91%A0\n   */\n  public send(data: WebSocketData): void {\n    if (this.readyState === this.CONNECTING) {\n      this.close()\n      throw new DOMException('InvalidStateError')\n    }\n\n    // Sending when the socket is about to close\n    // discards the sent data.\n    if (this.readyState === this.CLOSING || this.readyState === this.CLOSED) {\n      return\n    }\n\n    // Buffer the data to send in this even loop\n    // but send it in the next.\n    this.bufferedAmount += getDataSize(data)\n\n    queueMicrotask(() => {\n      // This is a bit optimistic but since no actual data transfer\n      // is involved, all the data will be \"sent\" on the next tick.\n      this.bufferedAmount = 0\n\n      /**\n       * @note Notify the parent about outgoing data.\n       * This notifies the transport and the connection\n       * listens to the outgoing data to emit the \"message\" event.\n       */\n      this[kOnSend]?.(data)\n    })\n  }\n\n  public close(code: number = 1000, reason?: string): void {\n    invariant(code, WEBSOCKET_CLOSE_CODE_RANGE_ERROR)\n    invariant(\n      code === 1000 || (code >= 3000 && code <= 4999),\n      WEBSOCKET_CLOSE_CODE_RANGE_ERROR\n    )\n\n    this[kClose](code, reason)\n  }\n\n  private [kClose](\n    code: number = 1000,\n    reason?: string,\n    wasClean = true\n  ): void {\n    /**\n     * @note Move this check here so that even internall closures,\n     * like those triggered by the `server` connection, are not\n     * performed twice.\n     */\n    if (this.readyState === this.CLOSING || this.readyState === this.CLOSED) {\n      return\n    }\n\n    this.readyState = this.CLOSING\n\n    queueMicrotask(() => {\n      this.readyState = this.CLOSED\n\n      this.dispatchEvent(\n        bindEvent(\n          this,\n          new CloseEvent('close', {\n            code,\n            reason,\n            wasClean,\n          })\n        )\n      )\n\n      // Remove all event listeners once the socket is closed.\n      this._onopen = null\n      this._onmessage = null\n      this._onerror = null\n      this._onclose = null\n    })\n  }\n\n  public addEventListener<K extends keyof WebSocketEventMap>(\n    type: K,\n    listener: (this: WebSocket, event: WebSocketEventMap[K]) => void,\n    options?: boolean | AddEventListenerOptions\n  ): void\n  public addEventListener(\n    type: string,\n    listener: EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions\n  ): void\n  public addEventListener(\n    type: unknown,\n    listener: unknown,\n    options?: unknown\n  ): void {\n    return super.addEventListener(\n      type as string,\n      listener as EventListener,\n      options as AddEventListenerOptions\n    )\n  }\n\n  removeEventListener<K extends keyof WebSocketEventMap>(\n    type: K,\n    callback: EventListenerOrEventListenerObject | null,\n    options?: boolean | EventListenerOptions\n  ): void {\n    return super.removeEventListener(type, callback, options)\n  }\n}\n\nfunction getDataSize(data: WebSocketData): number {\n  if (typeof data === 'string') {\n    return data.length\n  }\n\n  if (data instanceof Blob) {\n    return data.size\n  }\n\n  return data.byteLength\n}\n", "import { bindEvent } from './utils/bindEvent'\nimport {\n  StrictEventListenerOrEventListenerObject,\n  WebSocketData,\n  WebSocketTransport,\n  WebSocketTransportEventMap,\n} from './WebSocketTransport'\nimport { kOnSend, kClose, WebSocketOverride } from './WebSocketOverride'\nimport { CancelableMessageEvent, CloseEvent } from './utils/events'\n\n/**\n * Abstraction over the given mock `WebSocket` instance that allows\n * for controlling that instance (e.g. sending and receiving messages).\n */\nexport class WebSocketClassTransport\n  extends EventTarget\n  implements WebSocketTransport\n{\n  constructor(protected readonly socket: WebSocketOverride) {\n    super()\n\n    // Emit the \"close\" event on the transport if the close\n    // originates from the WebSocket client. E.g. the application\n    // calls \"ws.close()\", not the interceptor.\n    this.socket.addEventListener('close', (event) => {\n      this.dispatchEvent(bindEvent(this.socket, new CloseEvent('close', event)))\n    })\n\n    /**\n     * Emit the \"outgoing\" event on the transport\n     * whenever the WebSocket client sends data (\"ws.send()\").\n     */\n    this.socket[kOnSend] = (data) => {\n      this.dispatchEvent(\n        bindEvent(\n          this.socket,\n          // Dispatch this as cancelable because \"client\" connection\n          // re-creates this message event (cannot dispatch the same event).\n          new CancelableMessageEvent('outgoing', {\n            data,\n            origin: this.socket.url,\n            cancelable: true,\n          })\n        )\n      )\n    }\n  }\n\n  public addEventListener<EventType extends keyof WebSocketTransportEventMap>(\n    type: EventType,\n    callback: StrictEventListenerOrEventListenerObject<\n      WebSocketTransportEventMap[EventType]\n    > | null,\n    options?: boolean | AddEventListenerOptions\n  ): void {\n    return super.addEventListener(type, callback as EventListener, options)\n  }\n\n  public dispatchEvent<EventType extends keyof WebSocketTransportEventMap>(\n    event: WebSocketTransportEventMap[EventType]\n  ): boolean {\n    return super.dispatchEvent(event)\n  }\n\n  public send(data: WebSocketData): void {\n    queueMicrotask(() => {\n      if (\n        this.socket.readyState === this.socket.CLOSING ||\n        this.socket.readyState === this.socket.CLOSED\n      ) {\n        return\n      }\n\n      const dispatchEvent = () => {\n        this.socket.dispatchEvent(\n          bindEvent(\n            /**\n             * @note Setting this event's \"target\" to the\n             * WebSocket override instance is important.\n             * This way it can tell apart original incoming events\n             * (must be forwarded to the transport) from the\n             * mocked message events like the one below\n             * (must be dispatched on the client instance).\n             */\n            this.socket,\n            new MessageEvent('message', {\n              data,\n              origin: this.socket.url,\n            })\n          )\n        )\n      }\n\n      if (this.socket.readyState === this.socket.CONNECTING) {\n        this.socket.addEventListener(\n          'open',\n          () => {\n            dispatchEvent()\n          },\n          { once: true }\n        )\n      } else {\n        dispatchEvent()\n      }\n    })\n  }\n\n  public close(code: number, reason?: string): void {\n    /**\n     * @note Call the internal close method directly\n     * to allow closing the connection with the status codes\n     * that are non-configurable by the user (> 1000 <= 1015).\n     */\n    this.socket[kClose](code, reason)\n  }\n}\n", "import { Interceptor } from '../../Interceptor'\nimport {\n  WebSocketClientConnectionProtocol,\n  WebSocketClientConnection,\n  type WebSocketClientEventMap,\n} from './WebSocketClientConnection'\nimport {\n  WebSocketServerConnectionProtocol,\n  WebSocketServerConnection,\n  type WebSocketServerEventMap,\n} from './WebSocketServerConnection'\nimport { WebSocketClassTransport } from './WebSocketClassTransport'\nimport {\n  kClose,\n  kPassthroughPromise,\n  WebSocketOverride,\n} from './WebSocketOverride'\nimport { bindEvent } from './utils/bindEvent'\nimport { hasConfigurableGlobal } from '../../utils/hasConfigurableGlobal'\n\nexport { type WebSocketData, WebSocketTransport } from './WebSocketTransport'\nexport {\n  WebSocketClientEventMap,\n  WebSocketClientConnectionProtocol,\n  WebSocketClientConnection,\n  WebSocketServerEventMap,\n  WebSocketServerConnectionProtocol,\n  WebSocketServerConnection,\n}\n\nexport {\n  CloseEvent,\n  CancelableCloseEvent,\n  CancelableMessageEvent,\n} from './utils/events'\n\nexport type WebSocketEventMap = {\n  connection: [args: WebSocketConnectionData]\n}\n\nexport type WebSocketConnectionData = {\n  /**\n   * The incoming WebSocket client connection.\n   */\n  client: WebSocketClientConnection\n\n  /**\n   * The original WebSocket server connection.\n   */\n  server: WebSocketServerConnection\n\n  /**\n   * The connection information.\n   */\n  info: {\n    /**\n     * The protocols supported by the WebSocket client.\n     */\n    protocols: string | Array<string> | undefined\n  }\n}\n\n/**\n * Intercept the outgoing WebSocket connections created using\n * the global `WebSocket` class.\n */\nexport class WebSocketInterceptor extends Interceptor<WebSocketEventMap> {\n  static symbol = Symbol('websocket')\n\n  constructor() {\n    super(WebSocketInterceptor.symbol)\n  }\n\n  protected checkEnvironment(): boolean {\n    return hasConfigurableGlobal('WebSocket')\n  }\n\n  protected setup(): void {\n    const originalWebSocketDescriptor = Object.getOwnPropertyDescriptor(\n      globalThis,\n      'WebSocket'\n    )\n\n    const WebSocketProxy = new Proxy(globalThis.WebSocket, {\n      construct: (\n        target,\n        args: ConstructorParameters<typeof globalThis.WebSocket>,\n        newTarget\n      ) => {\n        const [url, protocols] = args\n\n        const createConnection = (): WebSocket => {\n          return Reflect.construct(target, args, newTarget)\n        }\n\n        // All WebSocket instances are mocked and don't forward\n        // any events to the original server (no connection established).\n        // To forward the events, the user must use the \"server.send()\" API.\n        const socket = new WebSocketOverride(url, protocols)\n        const transport = new WebSocketClassTransport(socket)\n\n        // Emit the \"connection\" event to the interceptor on the next tick\n        // so the client can modify WebSocket options, like \"binaryType\"\n        // while the connection is already pending.\n        queueMicrotask(() => {\n          try {\n            const server = new WebSocketServerConnection(\n              socket,\n              transport,\n              createConnection\n            )\n\n            // The \"globalThis.WebSocket\" class stands for\n            // the client-side connection. Assume it's established\n            // as soon as the WebSocket instance is constructed.\n            const hasConnectionListeners = this.emitter.emit('connection', {\n              client: new WebSocketClientConnection(socket, transport),\n              server,\n              info: {\n                protocols,\n              },\n            })\n\n            if (hasConnectionListeners) {\n              socket[kPassthroughPromise].resolve(false)\n            } else {\n              socket[kPassthroughPromise].resolve(true)\n\n              server.connect()\n\n              // Forward the \"open\" event from the original server\n              // to the mock WebSocket client in the case of a passthrough connection.\n              server.addEventListener('open', () => {\n                socket.dispatchEvent(bindEvent(socket, new Event('open')))\n\n                // Forward the original connection protocol to the\n                // mock WebSocket client.\n                if (server['realWebSocket']) {\n                  socket.protocol = server['realWebSocket'].protocol\n                }\n              })\n            }\n          } catch (error) {\n            /**\n             * @note Translate unhandled exceptions during the connection\n             * handling (i.e. interceptor exceptions) as WebSocket connection\n             * closures with error. This prevents from the exceptions occurring\n             * in `queueMicrotask` from being process-wide and uncatchable.\n             */\n            if (error instanceof Error) {\n              socket.dispatchEvent(new Event('error'))\n\n              // No need to close the connection if it's already being closed.\n              // E.g. the interceptor called `client.close()` and then threw an error.\n              if (\n                socket.readyState !== WebSocket.CLOSING &&\n                socket.readyState !== WebSocket.CLOSED\n              ) {\n                socket[kClose](1011, error.message, false)\n              }\n\n              console.error(error)\n            }\n          }\n        })\n\n        return socket\n      },\n    })\n\n    Object.defineProperty(globalThis, 'WebSocket', {\n      value: WebSocketProxy,\n      configurable: true,\n    })\n\n    this.subscriptions.push(() => {\n      Object.defineProperty(\n        globalThis,\n        'WebSocket',\n        originalWebSocketDescriptor!\n      )\n    })\n  }\n}\n"], "mappings": ";;;;AAEO,SAASA,UACdC,MAAA,EACAC,KAAA,EACuB;EACvBC,MAAA,CAAOC,gBAAA,CAAiBF,KAAA,EAAO;IAC7BD,MAAA,EAAQ;MACNI,KAAA,EAAOJ,MAAA;MACPK,UAAA,EAAY;MACZC,QAAA,EAAU;IACZ;IACAC,aAAA,EAAe;MACbH,KAAA,EAAOJ,MAAA;MACPK,UAAA,EAAY;MACZC,QAAA,EAAU;IACZ;EACF,CAAC;EAED,OAAOL,KAAA;AACT;;;ACpBA,IAAMO,WAAA,GAAcC,MAAA,CAAO,aAAa;AACxC,IAAMC,iBAAA,GAAoBD,MAAA,CAAO,mBAAmB;AAS7C,IAAME,sBAAA,GAAN,cAA8CC,YAAA,CAAgB;EAInEC,YAAYC,IAAA,EAAcC,IAAA,EAA2B;IACnD,MAAMD,IAAA,EAAMC,IAAI;IAChB,KAAKP,WAAW,IAAI,CAAC,CAACO,IAAA,CAAKC,UAAA;IAC3B,KAAKN,iBAAiB,IAAI;EAC5B;EAEA,IAAIM,WAAA,EAAa;IACf,OAAO,KAAKR,WAAW;EACzB;EAEA,IAAIQ,WAAWC,cAAA,EAAgB;IAC7B,KAAKT,WAAW,IAAIS,cAAA;EACtB;EAEA,IAAIC,iBAAA,EAAmB;IACrB,OAAO,KAAKR,iBAAiB;EAC/B;EAEA,IAAIQ,iBAAiBC,oBAAA,EAAsB;IACzC,KAAKT,iBAAiB,IAAIS,oBAAA;EAC5B;EAEOC,eAAA,EAAuB;IAC5B,IAAI,KAAKJ,UAAA,IAAc,CAAC,KAAKN,iBAAiB,GAAG;MAC/C,KAAKA,iBAAiB,IAAI;IAC5B;EACF;AACF;AA9BGF,WAAA,EACAE,iBAAA;AAqCI,IAAMW,UAAA,GAAN,cAAyBC,KAAA,CAAM;EAKpCT,YAAYC,IAAA,EAAcC,IAAA,GAAuB,CAAC,GAAG;IACnD,MAAMD,IAAA,EAAMC,IAAI;IAChB,KAAKQ,IAAA,GAAOR,IAAA,CAAKQ,IAAA,KAAS,SAAY,IAAIR,IAAA,CAAKQ,IAAA;IAC/C,KAAKC,MAAA,GAAST,IAAA,CAAKS,MAAA,KAAW,SAAY,KAAKT,IAAA,CAAKS,MAAA;IACpD,KAAKC,QAAA,GAAWV,IAAA,CAAKU,QAAA,KAAa,SAAY,QAAQV,IAAA,CAAKU,QAAA;EAC7D;AACF;AAEO,IAAMC,oBAAA,GAAN,cAAmCL,UAAA,CAAW;EAInDR,YAAYC,IAAA,EAAcC,IAAA,GAAuB,CAAC,GAAG;IACnD,MAAMD,IAAA,EAAMC,IAAI;IAChB,KAAKP,WAAW,IAAI,CAAC,CAACO,IAAA,CAAKC,UAAA;IAC3B,KAAKN,iBAAiB,IAAI;EAC5B;EAEA,IAAIM,WAAA,EAAa;IACf,OAAO,KAAKR,WAAW;EACzB;EAEA,IAAIQ,WAAWC,cAAA,EAAgB;IAC7B,KAAKT,WAAW,IAAIS,cAAA;EACtB;EAEA,IAAIC,iBAAA,EAAmB;IACrB,OAAO,KAAKR,iBAAiB;EAC/B;EAEA,IAAIQ,iBAAiBC,oBAAA,EAAsB;IACzC,KAAKT,iBAAiB,IAAIS,oBAAA;EAC5B;EAEOC,eAAA,EAAuB;IAC5B,IAAI,KAAKJ,UAAA,IAAc,CAAC,KAAKN,iBAAiB,GAAG;MAC/C,KAAKA,iBAAiB,IAAI;IAC5B;EACF;AACF;AA9BGF,WAAA,EACAE,iBAAA;;;AC1DH,IAAMiB,QAAA,GAAWlB,MAAA,CAAO,UAAU;AAClC,IAAMmB,cAAA,GAAiBnB,MAAA,CAAO,gBAAgB;AAOvC,IAAeoB,iCAAA,GAAf,MAAiD,EAqBxD;AAOO,IAAMC,yBAAA,GAAN,MAEP;EAMEjB,YACkBkB,MAAA,EACCC,SAAA,EACjB;IAFgB,KAAAD,MAAA,GAAAA,MAAA;IACC,KAAAC,SAAA,GAAAA,SAAA;IAEjB,KAAKC,EAAA,GAAKC,eAAA,CAAgB;IAC1B,KAAKC,GAAA,GAAM,IAAIC,GAAA,CAAIL,MAAA,CAAOI,GAAG;IAC7B,KAAKR,QAAQ,IAAI,IAAIU,WAAA,CAAY;IAIjC,KAAKL,SAAA,CAAUM,gBAAA,CAAiB,YAAarC,KAAA,IAAU;MACrD,MAAMsC,OAAA,GAAUxC,SAAA,CACd,KAAKgC,MAAA,EACL,IAAIpB,sBAAA,CAAuB,WAAW;QACpC6B,IAAA,EAAMvC,KAAA,CAAMuC,IAAA;QACZC,MAAA,EAAQxC,KAAA,CAAMwC,MAAA;QACdzB,UAAA,EAAY;MACd,CAAC,CACH;MAEA,KAAKW,QAAQ,EAAEe,aAAA,CAAcH,OAAO;MAMpC,IAAIA,OAAA,CAAQrB,gBAAA,EAAkB;QAC5BjB,KAAA,CAAMmB,cAAA,CAAe;MACvB;IACF,CAAC;IAUD,KAAKY,SAAA,CAAUM,gBAAA,CAAiB,SAAUrC,KAAA,IAAU;MAClD,KAAK0B,QAAQ,EAAEe,aAAA,CACb3C,SAAA,CAAU,KAAKgC,MAAA,EAAQ,IAAIV,UAAA,CAAW,SAASpB,KAAK,CAAC,CACvD;IACF,CAAC;EACH;EAAA;AAAA;AAAA;EAKOqC,iBACLxB,IAAA,EACA6B,QAAA,EACAC,OAAA,EACM;IACN,IAAI,CAACC,OAAA,CAAQC,GAAA,CAAIH,QAAA,EAAUf,cAAc,GAAG;MAC1C,MAAMmB,aAAA,GAAgBJ,QAAA,CAASK,IAAA,CAAK,KAAKjB,MAAM;MAI/C7B,MAAA,CAAO+C,cAAA,CAAeN,QAAA,EAAUf,cAAA,EAAgB;QAC9CxB,KAAA,EAAO2C,aAAA;QACP1C,UAAA,EAAY;QACZ6C,YAAA,EAAc;MAChB,CAAC;IACH;IAEA,KAAKvB,QAAQ,EAAEW,gBAAA,CACbxB,IAAA,EACA+B,OAAA,CAAQM,GAAA,CAAIR,QAAA,EAAUf,cAAc,GACpCgB,OACF;EACF;EAAA;AAAA;AAAA;EAKOQ,oBACLnD,KAAA,EACA0C,QAAA,EACAC,OAAA,EACM;IACN,KAAKjB,QAAQ,EAAEyB,mBAAA,CACbnD,KAAA,EACA4C,OAAA,CAAQM,GAAA,CAAIR,QAAA,EAAUf,cAAc,GACpCgB,OACF;EACF;EAAA;AAAA;AAAA;EAKOS,KAAKb,IAAA,EAA2B;IACrC,KAAKR,SAAA,CAAUqB,IAAA,CAAKb,IAAI;EAC1B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOc,MAAM/B,IAAA,EAAeC,MAAA,EAAuB;IACjD,KAAKQ,SAAA,CAAUsB,KAAA,CAAM/B,IAAA,EAAMC,MAAM;EACnC;AACF;AAzGWG,QAAA;;;AChDX,SAAS4B,SAAA,IAAAC,UAAA,QAAiB;;;ACA1B,SAASD,SAAA,QAAiB;AAI1B,SAASE,eAAA,QAAuB;AAMhC,IAAMC,gCAAA,GACJ;AAEK,IAAMC,mBAAA,GAAsBlD,MAAA,CAAO,qBAAqB;AACxD,IAAMmD,OAAA,GAAUnD,MAAA,CAAO,SAAS;AAChC,IAAMoD,MAAA,GAASpD,MAAA,CAAO,QAAQ;AAE9B,IAAMqD,iBAAA,GAAN,cAAgCzB,WAAA,CAAiC;EA2BtExB,YAAYsB,GAAA,EAAmB4B,SAAA,EAAoC;IACjE,MAAM;IAvBR,KAASC,UAAA,GAAa;IACtB,KAASC,IAAA,GAAO;IAChB,KAASC,OAAA,GAAU;IACnB,KAASC,MAAA,GAAS;IASlB,KAAQC,OAAA,GAAyC;IACjD,KAAQC,UAAA,GAEG;IACX,KAAQC,QAAA,GAA0C;IAClD,KAAQC,QAAA,GAAsD;IAO5D,KAAKpC,GAAA,GAAMA,GAAA,CAAIqC,QAAA,CAAS;IACxB,KAAKC,QAAA,GAAW;IAChB,KAAKC,UAAA,GAAa;IAClB,KAAKC,UAAA,GAAa;IAClB,KAAKC,UAAA,GAAa,KAAKZ,UAAA;IACvB,KAAKa,cAAA,GAAiB;IAEtB,KAAKlB,mBAAmB,IAAI,IAAIF,eAAA,CAAyB;IAEzDqB,cAAA,CAAe,YAAY;MACzB,IAAI,MAAM,KAAKnB,mBAAmB,GAAG;QACnC;MACF;MAEA,KAAKc,QAAA,GACH,OAAOV,SAAA,KAAc,WACjBA,SAAA,GACAgB,KAAA,CAAMC,OAAA,CAAQjB,SAAS,KAAKA,SAAA,CAAUkB,MAAA,GAAS,IAC/ClB,SAAA,CAAU,CAAC,IACX;MAON,IAAI,KAAKa,UAAA,KAAe,KAAKZ,UAAA,EAAY;QACvC,KAAKY,UAAA,GAAa,KAAKX,IAAA;QACvB,KAAKvB,aAAA,CAAc3C,SAAA,CAAU,MAAM,IAAIuB,KAAA,CAAM,MAAM,CAAC,CAAC;MACvD;IACF,CAAC;EACH;EAEA,IAAI4D,OAAOvC,QAAA,EAAyC;IAClD,KAAKS,mBAAA,CAAoB,QAAQ,KAAKgB,OAAO;IAC7C,KAAKA,OAAA,GAAUzB,QAAA;IACf,IAAIA,QAAA,KAAa,MAAM;MACrB,KAAKL,gBAAA,CAAiB,QAAQK,QAAQ;IACxC;EACF;EACA,IAAIuC,OAAA,EAAwC;IAC1C,OAAO,KAAKd,OAAA;EACd;EAEA,IAAIe,UACFxC,QAAA,EACA;IACA,KAAKS,mBAAA,CACH,WACA,KAAKiB,UACP;IACA,KAAKA,UAAA,GAAa1B,QAAA;IAClB,IAAIA,QAAA,KAAa,MAAM;MACrB,KAAKL,gBAAA,CAAiB,WAAWK,QAAQ;IAC3C;EACF;EACA,IAAIwC,UAAA,EAAwE;IAC1E,OAAO,KAAKd,UAAA;EACd;EAEA,IAAIe,QAAQzC,QAAA,EAAyC;IACnD,KAAKS,mBAAA,CAAoB,SAAS,KAAKkB,QAAQ;IAC/C,KAAKA,QAAA,GAAW3B,QAAA;IAChB,IAAIA,QAAA,KAAa,MAAM;MACrB,KAAKL,gBAAA,CAAiB,SAASK,QAAQ;IACzC;EACF;EACA,IAAIyC,QAAA,EAAyC;IAC3C,OAAO,KAAKd,QAAA;EACd;EAEA,IAAIe,QAAQ1C,QAAA,EAAqD;IAC/D,KAAKS,mBAAA,CAAoB,SAAS,KAAKmB,QAAkC;IACzE,KAAKA,QAAA,GAAW5B,QAAA;IAChB,IAAIA,QAAA,KAAa,MAAM;MACrB,KAAKL,gBAAA,CAAiB,SAASK,QAAQ;IACzC;EACF;EACA,IAAI0C,QAAA,EAAqD;IACvD,OAAO,KAAKd,QAAA;EACd;EAAA;AAAA;AAAA;EAKOlB,KAAKb,IAAA,EAA2B;IACrC,IAAI,KAAKoC,UAAA,KAAe,KAAKZ,UAAA,EAAY;MACvC,KAAKV,KAAA,CAAM;MACX,MAAM,IAAIgC,YAAA,CAAa,mBAAmB;IAC5C;IAIA,IAAI,KAAKV,UAAA,KAAe,KAAKV,OAAA,IAAW,KAAKU,UAAA,KAAe,KAAKT,MAAA,EAAQ;MACvE;IACF;IAIA,KAAKU,cAAA,IAAkBU,WAAA,CAAY/C,IAAI;IAEvCsC,cAAA,CAAe,MAAM;MAnJzB,IAAAU,EAAA;MAsJM,KAAKX,cAAA,GAAiB;MAOtB,CAAAW,EAAA,QAAK5B,OAAA,MAAL,gBAAA4B,EAAA,CAAAC,IAAA,OAAgBjD,IAAA;IAClB,CAAC;EACH;EAEOc,MAAM/B,IAAA,GAAe,KAAMC,MAAA,EAAuB;IACvD+B,SAAA,CAAUhC,IAAA,EAAMmC,gCAAgC;IAChDH,SAAA,CACEhC,IAAA,KAAS,OAASA,IAAA,IAAQ,OAAQA,IAAA,IAAQ,MAC1CmC,gCACF;IAEA,KAAKG,MAAM,EAAEtC,IAAA,EAAMC,MAAM;EAC3B;EAEA,EAlISmC,mBAAA,EACAC,OAAA,EAiIAC,MAAA,GACPtC,IAAA,GAAe,KACfC,MAAA,EACAC,QAAA,GAAW,MACL;IAMN,IAAI,KAAKmD,UAAA,KAAe,KAAKV,OAAA,IAAW,KAAKU,UAAA,KAAe,KAAKT,MAAA,EAAQ;MACvE;IACF;IAEA,KAAKS,UAAA,GAAa,KAAKV,OAAA;IAEvBY,cAAA,CAAe,MAAM;MACnB,KAAKF,UAAA,GAAa,KAAKT,MAAA;MAEvB,KAAKzB,aAAA,CACH3C,SAAA,CACE,MACA,IAAIsB,UAAA,CAAW,SAAS;QACtBE,IAAA;QACAC,MAAA;QACAC;MACF,CAAC,CACH,CACF;MAGA,KAAK2C,OAAA,GAAU;MACf,KAAKC,UAAA,GAAa;MAClB,KAAKC,QAAA,GAAW;MAChB,KAAKC,QAAA,GAAW;IAClB,CAAC;EACH;EAYOjC,iBACLxB,IAAA,EACA6B,QAAA,EACAC,OAAA,EACM;IACN,OAAO,MAAMN,gBAAA,CACXxB,IAAA,EACA6B,QAAA,EACAC,OACF;EACF;EAEAQ,oBACEtC,IAAA,EACA4E,QAAA,EACA9C,OAAA,EACM;IACN,OAAO,MAAMQ,mBAAA,CAAoBtC,IAAA,EAAM4E,QAAA,EAAU9C,OAAO;EAC1D;AACF;AA7NakB,iBAAA,CACKE,UAAA,GAAa;AADlBF,iBAAA,CAEKG,IAAA,GAAO;AAFZH,iBAAA,CAGKI,OAAA,GAAU;AAHfJ,iBAAA,CAIKK,MAAA,GAAS;AA2N3B,SAASoB,YAAY/C,IAAA,EAA6B;EAChD,IAAI,OAAOA,IAAA,KAAS,UAAU;IAC5B,OAAOA,IAAA,CAAKyC,MAAA;EACd;EAEA,IAAIzC,IAAA,YAAgBmD,IAAA,EAAM;IACxB,OAAOnD,IAAA,CAAKoD,IAAA;EACd;EAEA,OAAOpD,IAAA,CAAKqD,UAAA;AACd;;;AD3OA,IAAMC,SAAA,GAAWrF,MAAA,CAAO,UAAU;AAClC,IAAMsF,eAAA,GAAiBtF,MAAA,CAAO,gBAAgB;AAC9C,IAAMuF,KAAA,GAAQvF,MAAA,CAAO,OAAO;AASrB,IAAewF,iCAAA,GAAf,MAAiD,EAoBxD;AAOO,IAAMC,yBAAA,GAAN,MAEP;EASErF,YACmBsF,MAAA,EACAnE,SAAA,EACAoE,gBAAA,EACjB;IAHiB,KAAAD,MAAA,GAAAA,MAAA;IACA,KAAAnE,SAAA,GAAAA,SAAA;IACA,KAAAoE,gBAAA,GAAAA,gBAAA;IAEjB,KAAKN,SAAQ,IAAI,IAAIzD,WAAA,CAAY;IACjC,KAAKgE,mBAAA,GAAsB,IAAIC,eAAA,CAAgB;IAC/C,KAAKC,mBAAA,GAAsB,IAAID,eAAA,CAAgB;IAM/C,KAAKtE,SAAA,CAAUM,gBAAA,CAAiB,YAAarC,KAAA,IAAU;MAGrD,IAAI,OAAO,KAAKuG,aAAA,KAAkB,aAAa;QAC7C;MACF;MAMA1B,cAAA,CAAe,MAAM;QACnB,IAAI,CAAC7E,KAAA,CAAMiB,gBAAA,EAAkB;UAM3B,KAAK8E,KAAK,EAAE/F,KAAA,CAAMuC,IAAI;QACxB;MACF,CAAC;IACH,CAAC;IAED,KAAKR,SAAA,CAAUM,gBAAA,CACb,YACA,KAAKmE,qBAAA,CAAsBzD,IAAA,CAAK,IAAI,CACtC;EACF;EAAA;AAAA;AAAA;AAAA;EAMA,IAAWjB,OAAA,EAAoB;IAC7ByB,UAAA,CACE,KAAKgD,aAAA,EACL,wIACF;IAEA,OAAO,KAAKA,aAAA;EACd;EAAA;AAAA;AAAA;EAKOE,QAAA,EAAgB;IACrBlD,UAAA,CACE,CAAC,KAAKgD,aAAA,IAAiB,KAAKA,aAAA,CAAc5B,UAAA,KAAe+B,SAAA,CAAU1C,IAAA,EACnE,4FACF;IAEA,MAAMuC,aAAA,GAAgB,KAAKJ,gBAAA,CAAiB;IAG5CI,aAAA,CAAc7B,UAAA,GAAa,KAAKwB,MAAA,CAAOxB,UAAA;IAKvC6B,aAAA,CAAclE,gBAAA,CACZ,QACCrC,KAAA,IAAU;MACT,KAAK6F,SAAQ,EAAEpD,aAAA,CACb3C,SAAA,CAAU,KAAKyG,aAAA,EAAgB,IAAIlF,KAAA,CAAM,QAAQrB,KAAK,CAAC,CACzD;IACF,GACA;MAAE2G,IAAA,EAAM;IAAK,CACf;IAEAJ,aAAA,CAAclE,gBAAA,CAAiB,WAAYrC,KAAA,IAAU;MAKnD,KAAK+B,SAAA,CAAUU,aAAA,CACb3C,SAAA,CACE,KAAKyG,aAAA,EACL,IAAI5F,YAAA,CAAa,YAAY;QAC3B4B,IAAA,EAAMvC,KAAA,CAAMuC,IAAA;QACZC,MAAA,EAAQxC,KAAA,CAAMwC;MAChB,CAAC,CACH,CACF;IACF,CAAC;IAID,KAAK0D,MAAA,CAAO7D,gBAAA,CACV,SACCrC,KAAA,IAAU;MACT,KAAK4G,eAAA,CAAgB5G,KAAK;IAC5B,GACA;MACE6G,MAAA,EAAQ,KAAKT,mBAAA,CAAoBS;IACnC,CACF;IAIAN,aAAA,CAAclE,gBAAA,CACZ,SACCrC,KAAA,IAAU;MACT,KAAK8G,eAAA,CAAgB9G,KAAK;IAC5B,GACA;MACE6G,MAAA,EAAQ,KAAKP,mBAAA,CAAoBO;IACnC,CACF;IAEAN,aAAA,CAAclE,gBAAA,CAAiB,SAAS,MAAM;MAC5C,MAAM0E,UAAA,GAAajH,SAAA,CACjByG,aAAA,EACA,IAAIlF,KAAA,CAAM,SAAS;QAAEN,UAAA,EAAY;MAAK,CAAC,CACzC;MAIA,KAAK8E,SAAQ,EAAEpD,aAAA,CAAcsE,UAAU;MAIvC,IAAI,CAACA,UAAA,CAAW9F,gBAAA,EAAkB;QAChC,KAAKiF,MAAA,CAAOzD,aAAA,CAAc3C,SAAA,CAAU,KAAKoG,MAAA,EAAQ,IAAI7E,KAAA,CAAM,OAAO,CAAC,CAAC;MACtE;IACF,CAAC;IAED,KAAKkF,aAAA,GAAgBA,aAAA;EACvB;EAAA;AAAA;AAAA;EAKOlE,iBACLrC,KAAA,EACA0C,QAAA,EACAC,OAAA,EACM;IACN,IAAI,CAACC,OAAA,CAAQC,GAAA,CAAIH,QAAA,EAAUoD,eAAc,GAAG;MAC1C,MAAMhD,aAAA,GAAgBJ,QAAA,CAASK,IAAA,CAAK,KAAKmD,MAAM;MAI/CjG,MAAA,CAAO+C,cAAA,CAAeN,QAAA,EAAUoD,eAAA,EAAgB;QAC9C3F,KAAA,EAAO2C,aAAA;QACP1C,UAAA,EAAY;MACd,CAAC;IACH;IAEA,KAAKyF,SAAQ,EAAExD,gBAAA,CACbrC,KAAA,EACA4C,OAAA,CAAQM,GAAA,CAAIR,QAAA,EAAUoD,eAAc,GACpCnD,OACF;EACF;EAAA;AAAA;AAAA;EAKOQ,oBACLnD,KAAA,EACA0C,QAAA,EACAC,OAAA,EACM;IACN,KAAKkD,SAAQ,EAAE1C,mBAAA,CACbnD,KAAA,EACA4C,OAAA,CAAQM,GAAA,CAAIR,QAAA,EAAUoD,eAAc,GACpCnD,OACF;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASOS,KAAKb,IAAA,EAA2B;IACrC,KAAKwD,KAAK,EAAExD,IAAI;EAClB;EAEA,EApMSsD,SAAA,EAoMAE,KAAA,GAAOxD,IAAA,EAA2B;IACzC,MAAM;MAAEgE;IAAc,IAAI;IAE1BhD,UAAA,CACEgD,aAAA,EACA,mHACA,KAAKL,MAAA,CAAOhE,GACd;IAGA,IACEqE,aAAA,CAAc5B,UAAA,KAAe+B,SAAA,CAAUzC,OAAA,IACvCsC,aAAA,CAAc5B,UAAA,KAAe+B,SAAA,CAAUxC,MAAA,EACvC;MACA;IACF;IAKA,IAAIqC,aAAA,CAAc5B,UAAA,KAAe+B,SAAA,CAAU3C,UAAA,EAAY;MACrDwC,aAAA,CAAclE,gBAAA,CACZ,QACA,MAAM;QACJkE,aAAA,CAAcnD,IAAA,CAAKb,IAAI;MACzB,GACA;QAAEoE,IAAA,EAAM;MAAK,CACf;MACA;IACF;IAGAJ,aAAA,CAAcnD,IAAA,CAAKb,IAAI;EACzB;EAAA;AAAA;AAAA;EAKOc,MAAA,EAAc;IACnB,MAAM;MAAEkD;IAAc,IAAI;IAE1BhD,UAAA,CACEgD,aAAA,EACA,sHACA,KAAKL,MAAA,CAAOhE,GACd;IAMA,KAAKoE,mBAAA,CAAoBU,KAAA,CAAM;IAE/B,IACET,aAAA,CAAc5B,UAAA,KAAe+B,SAAA,CAAUzC,OAAA,IACvCsC,aAAA,CAAc5B,UAAA,KAAe+B,SAAA,CAAUxC,MAAA,EACvC;MACA;IACF;IAGAqC,aAAA,CAAclD,KAAA,CAAM;IAGpBwB,cAAA,CAAe,MAAM;MACnB,KAAKgB,SAAQ,EAAEpD,aAAA,CACb3C,SAAA,CACE,KAAKyG,aAAA,EACL,IAAI9E,oBAAA,CAAqB,SAAS;QAAA;AAAA;AAAA;AAAA;QAKhCH,IAAA,EAAM;QACNP,UAAA,EAAY;MACd,CAAC,CACH,CACF;IACF,CAAC;EACH;EAEQyF,sBAAsBxG,KAAA,EAA0C;IAKtE,MAAMiH,YAAA,GAAenH,SAAA,CACnBE,KAAA,CAAMD,MAAA,EACN,IAAIW,sBAAA,CAAuB,WAAW;MACpC6B,IAAA,EAAMvC,KAAA,CAAMuC,IAAA;MACZC,MAAA,EAAQxC,KAAA,CAAMwC,MAAA;MACdzB,UAAA,EAAY;IACd,CAAC,CACH;IASA,KAAK8E,SAAQ,EAAEpD,aAAA,CAAcwE,YAAY;IAMzC,IAAI,CAACA,YAAA,CAAahG,gBAAA,EAAkB;MAClC,KAAKiF,MAAA,CAAOzD,aAAA,CACV3C,SAAA;MAAA;AAAA;AAAA;AAAA;AAAA;MAME,KAAKoG,MAAA;MAAA;MAAA;MAGL,IAAIvF,YAAA,CAAa,WAAW;QAC1B4B,IAAA,EAAMvC,KAAA,CAAMuC,IAAA;QACZC,MAAA,EAAQxC,KAAA,CAAMwC;MAChB,CAAC,CACH,CACF;IACF;EACF;EAEQoE,gBAAgBM,MAAA,EAAqB;IAE3C,IAAI,KAAKX,aAAA,EAAe;MACtB,KAAKA,aAAA,CAAclD,KAAA,CAAM;IAC3B;EACF;EAEQyD,gBAAgB9G,KAAA,EAAyB;IAI/C,KAAKoG,mBAAA,CAAoBY,KAAA,CAAM;IAE/B,MAAMG,UAAA,GAAarH,SAAA,CACjB,KAAKyG,aAAA,EACL,IAAI9E,oBAAA,CAAqB,SAAS;MAChCH,IAAA,EAAMtB,KAAA,CAAMsB,IAAA;MACZC,MAAA,EAAQvB,KAAA,CAAMuB,MAAA;MACdC,QAAA,EAAUxB,KAAA,CAAMwB,QAAA;MAChBT,UAAA,EAAY;IACd,CAAC,CACH;IAEA,KAAK8E,SAAQ,EAAEpD,aAAA,CAAc0E,UAAU;IAIvC,IAAI,CAACA,UAAA,CAAWlG,gBAAA,EAAkB;MAKhC,KAAKiF,MAAA,CAAOtC,MAAM,EAAE5D,KAAA,CAAMsB,IAAA,EAAMtB,KAAA,CAAMuB,MAAM;IAC9C;EACF;AACF;;;AEvZO,IAAM6F,uBAAA,GAAN,cACGhF,WAAA,CAEV;EACExB,YAA+BkB,MAAA,EAA2B;IACxD,MAAM;IADuB,KAAAA,MAAA,GAAAA,MAAA;IAM7B,KAAKA,MAAA,CAAOO,gBAAA,CAAiB,SAAUrC,KAAA,IAAU;MAC/C,KAAKyC,aAAA,CAAc3C,SAAA,CAAU,KAAKgC,MAAA,EAAQ,IAAIV,UAAA,CAAW,SAASpB,KAAK,CAAC,CAAC;IAC3E,CAAC;IAMD,KAAK8B,MAAA,CAAO6B,OAAO,IAAKpB,IAAA,IAAS;MAC/B,KAAKE,aAAA,CACH3C,SAAA,CACE,KAAKgC,MAAA;MAAA;MAAA;MAGL,IAAIpB,sBAAA,CAAuB,YAAY;QACrC6B,IAAA;QACAC,MAAA,EAAQ,KAAKV,MAAA,CAAOI,GAAA;QACpBnB,UAAA,EAAY;MACd,CAAC,CACH,CACF;IACF;EACF;EAEOsB,iBACLxB,IAAA,EACA4E,QAAA,EAGA9C,OAAA,EACM;IACN,OAAO,MAAMN,gBAAA,CAAiBxB,IAAA,EAAM4E,QAAA,EAA2B9C,OAAO;EACxE;EAEOF,cACLzC,KAAA,EACS;IACT,OAAO,MAAMyC,aAAA,CAAczC,KAAK;EAClC;EAEOoD,KAAKb,IAAA,EAA2B;IACrCsC,cAAA,CAAe,MAAM;MACnB,IACE,KAAK/C,MAAA,CAAO6C,UAAA,KAAe,KAAK7C,MAAA,CAAOmC,OAAA,IACvC,KAAKnC,MAAA,CAAO6C,UAAA,KAAe,KAAK7C,MAAA,CAAOoC,MAAA,EACvC;QACA;MACF;MAEA,MAAMzB,aAAA,GAAgBA,CAAA,KAAM;QAC1B,KAAKX,MAAA,CAAOW,aAAA,CACV3C,SAAA;QAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;QASE,KAAKgC,MAAA,EACL,IAAInB,YAAA,CAAa,WAAW;UAC1B4B,IAAA;UACAC,MAAA,EAAQ,KAAKV,MAAA,CAAOI;QACtB,CAAC,CACH,CACF;MACF;MAEA,IAAI,KAAKJ,MAAA,CAAO6C,UAAA,KAAe,KAAK7C,MAAA,CAAOiC,UAAA,EAAY;QACrD,KAAKjC,MAAA,CAAOO,gBAAA,CACV,QACA,MAAM;UACJI,aAAA,CAAc;QAChB,GACA;UAAEkE,IAAA,EAAM;QAAK,CACf;MACF,OAAO;QACLlE,aAAA,CAAc;MAChB;IACF,CAAC;EACH;EAEOY,MAAM/B,IAAA,EAAcC,MAAA,EAAuB;IAMhD,KAAKO,MAAA,CAAO8B,MAAM,EAAEtC,IAAA,EAAMC,MAAM;EAClC;AACF;;;ACjDO,IAAM8F,qBAAA,GAAN,cAAmCC,WAAA,CAA+B;EAGvE1G,YAAA,EAAc;IACZ,MAAMyG,qBAAA,CAAqBE,MAAM;EACnC;EAEUC,iBAAA,EAA4B;IACpC,OAAOC,qBAAA,CAAsB,WAAW;EAC1C;EAEUC,MAAA,EAAc;IACtB,MAAMC,2BAAA,GAA8B1H,MAAA,CAAO2H,wBAAA,CACzCC,UAAA,EACA,WACF;IAEA,MAAMC,cAAA,GAAiB,IAAIC,KAAA,CAAMF,UAAA,CAAWnB,SAAA,EAAW;MACrDsB,SAAA,EAAWA,CACTjI,MAAA,EACAkI,IAAA,EACAC,SAAA,KACG;QACH,MAAM,CAAChG,GAAA,EAAK4B,SAAS,IAAImE,IAAA;QAEzB,MAAM9B,gBAAA,GAAmBA,CAAA,KAAiB;UACxC,OAAOvD,OAAA,CAAQoF,SAAA,CAAUjI,MAAA,EAAQkI,IAAA,EAAMC,SAAS;QAClD;QAKA,MAAMpG,MAAA,GAAS,IAAI+B,iBAAA,CAAkB3B,GAAA,EAAK4B,SAAS;QACnD,MAAM/B,SAAA,GAAY,IAAIqF,uBAAA,CAAwBtF,MAAM;QAKpD+C,cAAA,CAAe,MAAM;UACnB,IAAI;YACF,MAAMsD,MAAA,GAAS,IAAIlC,yBAAA,CACjBnE,MAAA,EACAC,SAAA,EACAoE,gBACF;YAKA,MAAMiC,sBAAA,GAAyB,KAAKC,OAAA,CAAQC,IAAA,CAAK,cAAc;cAC7DpC,MAAA,EAAQ,IAAIrE,yBAAA,CAA0BC,MAAA,EAAQC,SAAS;cACvDoG,MAAA;cACAI,IAAA,EAAM;gBACJzE;cACF;YACF,CAAC;YAED,IAAIsE,sBAAA,EAAwB;cAC1BtG,MAAA,CAAO4B,mBAAmB,EAAE8E,OAAA,CAAQ,KAAK;YAC3C,OAAO;cACL1G,MAAA,CAAO4B,mBAAmB,EAAE8E,OAAA,CAAQ,IAAI;cAExCL,MAAA,CAAO1B,OAAA,CAAQ;cAIf0B,MAAA,CAAO9F,gBAAA,CAAiB,QAAQ,MAAM;gBACpCP,MAAA,CAAOW,aAAA,CAAc3C,SAAA,CAAUgC,MAAA,EAAQ,IAAIT,KAAA,CAAM,MAAM,CAAC,CAAC;gBAIzD,IAAI8G,MAAA,CAAO,eAAe,GAAG;kBAC3BrG,MAAA,CAAO0C,QAAA,GAAW2D,MAAA,CAAO,eAAe,EAAE3D,QAAA;gBAC5C;cACF,CAAC;YACH;UACF,SAASiE,KAAA,EAAP;YAOA,IAAIA,KAAA,YAAiBC,KAAA,EAAO;cAC1B5G,MAAA,CAAOW,aAAA,CAAc,IAAIpB,KAAA,CAAM,OAAO,CAAC;cAIvC,IACES,MAAA,CAAO6C,UAAA,KAAe+B,SAAA,CAAUzC,OAAA,IAChCnC,MAAA,CAAO6C,UAAA,KAAe+B,SAAA,CAAUxC,MAAA,EAChC;gBACApC,MAAA,CAAO8B,MAAM,EAAE,MAAM6E,KAAA,CAAMnG,OAAA,EAAS,KAAK;cAC3C;cAEAqG,OAAA,CAAQF,KAAA,CAAMA,KAAK;YACrB;UACF;QACF,CAAC;QAED,OAAO3G,MAAA;MACT;IACF,CAAC;IAED7B,MAAA,CAAO+C,cAAA,CAAe6E,UAAA,EAAY,aAAa;MAC7C1H,KAAA,EAAO2H,cAAA;MACP7E,YAAA,EAAc;IAChB,CAAC;IAED,KAAK2F,aAAA,CAAcC,IAAA,CAAK,MAAM;MAC5B5I,MAAA,CAAO+C,cAAA,CACL6E,UAAA,EACA,aACAF,2BACF;IACF,CAAC;EACH;AACF;AArHO,IAAMmB,oBAAA,GAANzB,qBAAA;AAAMyB,oBAAA,CACJvB,MAAA,GAAS/G,MAAA,CAAO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}