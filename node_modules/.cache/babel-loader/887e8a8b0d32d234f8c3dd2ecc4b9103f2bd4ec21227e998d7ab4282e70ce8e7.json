{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No undefined variables\n *\n * A GraphQL operation is only valid if all variables encountered, both directly\n * and via fragment spreads, are defined by that operation.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variable-Uses-Defined\n */\nexport function NoUndefinedVariablesRule(context) {\n  let variableNameDefined = Object.create(null);\n  return {\n    OperationDefinition: {\n      enter() {\n        variableNameDefined = Object.create(null);\n      },\n      leave(operation) {\n        const usages = context.getRecursiveVariableUsages(operation);\n        for (const {\n          node\n        } of usages) {\n          const varName = node.name.value;\n          if (variableNameDefined[varName] !== true) {\n            context.reportError(new GraphQLError(operation.name ? `Variable \"$${varName}\" is not defined by operation \"${operation.name.value}\".` : `Variable \"$${varName}\" is not defined.`, {\n              nodes: [node, operation]\n            }));\n          }\n        }\n      }\n    },\n    VariableDefinition(node) {\n      variableNameDefined[node.variable.name.value] = true;\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "NoUndefinedVariablesRule", "context", "variableNameDefined", "Object", "create", "OperationDefinition", "enter", "leave", "operation", "usages", "getRecursiveVariableUsages", "node", "varName", "name", "value", "reportError", "nodes", "VariableDefinition", "variable"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/NoUndefinedVariablesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No undefined variables\n *\n * A GraphQL operation is only valid if all variables encountered, both directly\n * and via fragment spreads, are defined by that operation.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variable-Uses-Defined\n */\nexport function NoUndefinedVariablesRule(context) {\n  let variableNameDefined = Object.create(null);\n  return {\n    OperationDefinition: {\n      enter() {\n        variableNameDefined = Object.create(null);\n      },\n\n      leave(operation) {\n        const usages = context.getRecursiveVariableUsages(operation);\n\n        for (const { node } of usages) {\n          const varName = node.name.value;\n\n          if (variableNameDefined[varName] !== true) {\n            context.reportError(\n              new GraphQLError(\n                operation.name\n                  ? `Variable \"$${varName}\" is not defined by operation \"${operation.name.value}\".`\n                  : `Variable \"$${varName}\" is not defined.`,\n                {\n                  nodes: [node, operation],\n                },\n              ),\n            );\n          }\n        }\n      },\n    },\n\n    VariableDefinition(node) {\n      variableNameDefined[node.variable.name.value] = true;\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,OAAO,EAAE;EAChD,IAAIC,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC7C,OAAO;IACLC,mBAAmB,EAAE;MACnBC,KAAKA,CAAA,EAAG;QACNJ,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC3C,CAAC;MAEDG,KAAKA,CAACC,SAAS,EAAE;QACf,MAAMC,MAAM,GAAGR,OAAO,CAACS,0BAA0B,CAACF,SAAS,CAAC;QAE5D,KAAK,MAAM;UAAEG;QAAK,CAAC,IAAIF,MAAM,EAAE;UAC7B,MAAMG,OAAO,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK;UAE/B,IAAIZ,mBAAmB,CAACU,OAAO,CAAC,KAAK,IAAI,EAAE;YACzCX,OAAO,CAACc,WAAW,CACjB,IAAIhB,YAAY,CACdS,SAAS,CAACK,IAAI,GACV,cAAcD,OAAO,kCAAkCJ,SAAS,CAACK,IAAI,CAACC,KAAK,IAAI,GAC/E,cAAcF,OAAO,mBAAmB,EAC5C;cACEI,KAAK,EAAE,CAACL,IAAI,EAAEH,SAAS;YACzB,CACF,CACF,CAAC;UACH;QACF;MACF;IACF,CAAC;IAEDS,kBAAkBA,CAACN,IAAI,EAAE;MACvBT,mBAAmB,CAACS,IAAI,CAACO,QAAQ,CAACL,IAAI,CAACC,KAAK,CAAC,GAAG,IAAI;IACtD;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}