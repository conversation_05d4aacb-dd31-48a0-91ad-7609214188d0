{"ast": null, "code": "import { http, HttpResponse } from 'msw';\nimport { masterUsers, masterFunctions, masterProcesses, masterApplicationLibrary, mockBIAs } from '../mockData';\nexport const handlers = [http.get('/api/users', () => {\n  return HttpResponse.json(masterUsers);\n}), http.get('/api/functions', () => {\n  return HttpResponse.json(masterFunctions);\n}), http.get('/api/processes/:functionId', ({\n  params\n}) => {\n  const {\n    functionId\n  } = params;\n  return HttpResponse.json(masterProcesses[functionId] || []);\n}), http.get('/api/applications', () => {\n  return HttpResponse.json(masterApplicationLibrary);\n}), http.get('/api/bias', () => {\n  return HttpResponse.json(mockBIAs);\n}), http.post('/api/bias', async ({\n  request\n}) => {\n  const body = await request.json();\n  return HttpResponse.json({\n    id: 'bia-new',\n    ...body\n  });\n}), http.put('/api/bias/:id', async ({\n  params,\n  request\n}) => {\n  const body = await request.json();\n  return HttpResponse.json({\n    id: params.id,\n    ...body\n  });\n})];", "map": {"version": 3, "names": ["http", "HttpResponse", "masterUsers", "masterFunctions", "masterProcesses", "masterApplicationLibrary", "mockBIAs", "handlers", "get", "json", "params", "functionId", "post", "request", "body", "id", "put"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/mocks/handlers.js"], "sourcesContent": ["import { http, HttpResponse } from 'msw';\nimport { masterUsers, masterFunctions, masterProcesses, masterApplicationLibrary, mockBIAs } from '../mockData';\n\nexport const handlers = [\n  http.get('/api/users', () => {\n    return HttpResponse.json(masterUsers);\n  }),\n\n  http.get('/api/functions', () => {\n    return HttpResponse.json(masterFunctions);\n  }),\n\n  http.get('/api/processes/:functionId', ({ params }) => {\n    const { functionId } = params;\n    return HttpResponse.json(masterProcesses[functionId] || []);\n  }),\n\n  http.get('/api/applications', () => {\n    return HttpResponse.json(masterApplicationLibrary);\n  }),\n\n  http.get('/api/bias', () => {\n    return HttpResponse.json(mockBIAs);\n  }),\n\n  http.post('/api/bias', async ({ request }) => {\n    const body = await request.json();\n    return HttpResponse.json({ id: 'bia-new', ...body });\n  }),\n\n  http.put('/api/bias/:id', async ({ params, request }) => {\n    const body = await request.json();\n    return HttpResponse.json({ id: params.id, ...body });\n  })\n];"], "mappings": "AAAA,SAASA,IAAI,EAAEC,YAAY,QAAQ,KAAK;AACxC,SAASC,WAAW,EAAEC,eAAe,EAAEC,eAAe,EAAEC,wBAAwB,EAAEC,QAAQ,QAAQ,aAAa;AAE/G,OAAO,MAAMC,QAAQ,GAAG,CACtBP,IAAI,CAACQ,GAAG,CAAC,YAAY,EAAE,MAAM;EAC3B,OAAOP,YAAY,CAACQ,IAAI,CAACP,WAAW,CAAC;AACvC,CAAC,CAAC,EAEFF,IAAI,CAACQ,GAAG,CAAC,gBAAgB,EAAE,MAAM;EAC/B,OAAOP,YAAY,CAACQ,IAAI,CAACN,eAAe,CAAC;AAC3C,CAAC,CAAC,EAEFH,IAAI,CAACQ,GAAG,CAAC,4BAA4B,EAAE,CAAC;EAAEE;AAAO,CAAC,KAAK;EACrD,MAAM;IAAEC;EAAW,CAAC,GAAGD,MAAM;EAC7B,OAAOT,YAAY,CAACQ,IAAI,CAACL,eAAe,CAACO,UAAU,CAAC,IAAI,EAAE,CAAC;AAC7D,CAAC,CAAC,EAEFX,IAAI,CAACQ,GAAG,CAAC,mBAAmB,EAAE,MAAM;EAClC,OAAOP,YAAY,CAACQ,IAAI,CAACJ,wBAAwB,CAAC;AACpD,CAAC,CAAC,EAEFL,IAAI,CAACQ,GAAG,CAAC,WAAW,EAAE,MAAM;EAC1B,OAAOP,YAAY,CAACQ,IAAI,CAACH,QAAQ,CAAC;AACpC,CAAC,CAAC,EAEFN,IAAI,CAACY,IAAI,CAAC,WAAW,EAAE,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAC5C,MAAMC,IAAI,GAAG,MAAMD,OAAO,CAACJ,IAAI,CAAC,CAAC;EACjC,OAAOR,YAAY,CAACQ,IAAI,CAAC;IAAEM,EAAE,EAAE,SAAS;IAAE,GAAGD;EAAK,CAAC,CAAC;AACtD,CAAC,CAAC,EAEFd,IAAI,CAACgB,GAAG,CAAC,eAAe,EAAE,OAAO;EAAEN,MAAM;EAAEG;AAAQ,CAAC,KAAK;EACvD,MAAMC,IAAI,GAAG,MAAMD,OAAO,CAACJ,IAAI,CAAC,CAAC;EACjC,OAAOR,YAAY,CAACQ,IAAI,CAAC;IAAEM,EAAE,EAAEL,MAAM,CAACK,EAAE;IAAE,GAAGD;EAAK,CAAC,CAAC;AACtD,CAAC,CAAC,CACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}