{"ast": null, "code": "/**\n * Creates an object map with the same keys as `map` and values generated by\n * running each value of `map` thru `fn`.\n */\nexport function mapValue(map, fn) {\n  const result = Object.create(null);\n  for (const key of Object.keys(map)) {\n    result[key] = fn(map[key], key);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["mapValue", "map", "fn", "result", "Object", "create", "key", "keys"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/jsutils/mapValue.mjs"], "sourcesContent": ["/**\n * Creates an object map with the same keys as `map` and values generated by\n * running each value of `map` thru `fn`.\n */\nexport function mapValue(map, fn) {\n  const result = Object.create(null);\n\n  for (const key of Object.keys(map)) {\n    result[key] = fn(map[key], key);\n  }\n\n  return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,GAAG,EAAEC,EAAE,EAAE;EAChC,MAAMC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAElC,KAAK,MAAMC,GAAG,IAAIF,MAAM,CAACG,IAAI,CAACN,GAAG,CAAC,EAAE;IAClCE,MAAM,CAACG,GAAG,CAAC,GAAGJ,EAAE,CAACD,GAAG,CAACK,GAAG,CAAC,EAAEA,GAAG,CAAC;EACjC;EAEA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}