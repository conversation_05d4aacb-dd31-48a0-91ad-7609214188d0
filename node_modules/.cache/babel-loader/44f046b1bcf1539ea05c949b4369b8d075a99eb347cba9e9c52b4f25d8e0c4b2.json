{"ast": null, "code": "function toPublicUrl(url) {\n  if (typeof location === \"undefined\") {\n    return url.toString();\n  }\n  const urlInstance = url instanceof URL ? url : new URL(url);\n  return urlInstance.origin === location.origin ? urlInstance.pathname : urlInstance.origin + urlInstance.pathname;\n}\nexport { toPublicUrl };", "map": {"version": 3, "names": ["toPublicUrl", "url", "location", "toString", "urlInstance", "URL", "origin", "pathname"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/request/toPublicUrl.ts"], "sourcesContent": ["/**\n * Returns a relative URL if the given request URL is relative\n * to the current origin. Otherwise returns an absolute URL.\n */\nexport function toPublicUrl(url: string | URL): string {\n  if (typeof location === 'undefined') {\n    return url.toString()\n  }\n\n  const urlInstance = url instanceof URL ? url : new URL(url)\n\n  return urlInstance.origin === location.origin\n    ? urlInstance.pathname\n    : urlInstance.origin + urlInstance.pathname\n}\n"], "mappings": "AAIO,SAASA,YAAYC,GAAA,EAA2B;EACrD,IAAI,OAAOC,QAAA,KAAa,aAAa;IACnC,OAAOD,GAAA,CAAIE,QAAA,CAAS;EACtB;EAEA,MAAMC,WAAA,GAAcH,GAAA,YAAeI,GAAA,GAAMJ,GAAA,GAAM,IAAII,GAAA,CAAIJ,GAAG;EAE1D,OAAOG,WAAA,CAAYE,MAAA,KAAWJ,QAAA,CAASI,MAAA,GACnCF,WAAA,CAAYG,QAAA,GACZH,WAAA,CAAYE,MAAA,GAASF,WAAA,CAAYG,QAAA;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}