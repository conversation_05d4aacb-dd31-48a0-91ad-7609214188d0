{"ast": null, "code": "/**\n * The set of allowed kind values for AST nodes.\n */\nvar Kind;\n(function (Kind) {\n  Kind['NAME'] = 'Name';\n  Kind['DOCUMENT'] = 'Document';\n  Kind['OPERATION_DEFINITION'] = 'OperationDefinition';\n  Kind['VARIABLE_DEFINITION'] = 'VariableDefinition';\n  Kind['SELECTION_SET'] = 'SelectionSet';\n  Kind['FIELD'] = 'Field';\n  Kind['ARGUMENT'] = 'Argument';\n  Kind['FRAGMENT_SPREAD'] = 'FragmentSpread';\n  Kind['INLINE_FRAGMENT'] = 'InlineFragment';\n  Kind['FRAGMENT_DEFINITION'] = 'FragmentDefinition';\n  Kind['VARIABLE'] = 'Variable';\n  Kind['INT'] = 'IntValue';\n  Kind['FLOAT'] = 'FloatValue';\n  Kind['STRING'] = 'StringValue';\n  Kind['BOOLEAN'] = 'BooleanValue';\n  Kind['NULL'] = 'NullValue';\n  Kind['ENUM'] = 'EnumValue';\n  Kind['LIST'] = 'ListValue';\n  Kind['OBJECT'] = 'ObjectValue';\n  Kind['OBJECT_FIELD'] = 'ObjectField';\n  Kind['DIRECTIVE'] = 'Directive';\n  Kind['NAMED_TYPE'] = 'NamedType';\n  Kind['LIST_TYPE'] = 'ListType';\n  Kind['NON_NULL_TYPE'] = 'NonNullType';\n  Kind['SCHEMA_DEFINITION'] = 'SchemaDefinition';\n  Kind['OPERATION_TYPE_DEFINITION'] = 'OperationTypeDefinition';\n  Kind['SCALAR_TYPE_DEFINITION'] = 'ScalarTypeDefinition';\n  Kind['OBJECT_TYPE_DEFINITION'] = 'ObjectTypeDefinition';\n  Kind['FIELD_DEFINITION'] = 'FieldDefinition';\n  Kind['INPUT_VALUE_DEFINITION'] = 'InputValueDefinition';\n  Kind['INTERFACE_TYPE_DEFINITION'] = 'InterfaceTypeDefinition';\n  Kind['UNION_TYPE_DEFINITION'] = 'UnionTypeDefinition';\n  Kind['ENUM_TYPE_DEFINITION'] = 'EnumTypeDefinition';\n  Kind['ENUM_VALUE_DEFINITION'] = 'EnumValueDefinition';\n  Kind['INPUT_OBJECT_TYPE_DEFINITION'] = 'InputObjectTypeDefinition';\n  Kind['DIRECTIVE_DEFINITION'] = 'DirectiveDefinition';\n  Kind['SCHEMA_EXTENSION'] = 'SchemaExtension';\n  Kind['SCALAR_TYPE_EXTENSION'] = 'ScalarTypeExtension';\n  Kind['OBJECT_TYPE_EXTENSION'] = 'ObjectTypeExtension';\n  Kind['INTERFACE_TYPE_EXTENSION'] = 'InterfaceTypeExtension';\n  Kind['UNION_TYPE_EXTENSION'] = 'UnionTypeExtension';\n  Kind['ENUM_TYPE_EXTENSION'] = 'EnumTypeExtension';\n  Kind['INPUT_OBJECT_TYPE_EXTENSION'] = 'InputObjectTypeExtension';\n})(Kind || (Kind = {}));\nexport { Kind };\n/**\n * The enum type representing the possible kind values of AST nodes.\n *\n * @deprecated Please use `Kind`. Will be remove in v17.\n */", "map": {"version": 3, "names": ["Kind"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/language/kinds.mjs"], "sourcesContent": ["/**\n * The set of allowed kind values for AST nodes.\n */\nvar Kind;\n\n(function (Kind) {\n  Kind['NAME'] = 'Name';\n  Kind['DOCUMENT'] = 'Document';\n  Kind['OPERATION_DEFINITION'] = 'OperationDefinition';\n  Kind['VARIABLE_DEFINITION'] = 'VariableDefinition';\n  Kind['SELECTION_SET'] = 'SelectionSet';\n  Kind['FIELD'] = 'Field';\n  Kind['ARGUMENT'] = 'Argument';\n  Kind['FRAGMENT_SPREAD'] = 'FragmentSpread';\n  Kind['INLINE_FRAGMENT'] = 'InlineFragment';\n  Kind['FRAGMENT_DEFINITION'] = 'FragmentDefinition';\n  Kind['VARIABLE'] = 'Variable';\n  Kind['INT'] = 'IntValue';\n  Kind['FLOAT'] = 'FloatValue';\n  Kind['STRING'] = 'StringValue';\n  Kind['BOOLEAN'] = 'BooleanValue';\n  Kind['NULL'] = 'NullValue';\n  Kind['ENUM'] = 'EnumValue';\n  Kind['LIST'] = 'ListValue';\n  Kind['OBJECT'] = 'ObjectValue';\n  Kind['OBJECT_FIELD'] = 'ObjectField';\n  Kind['DIRECTIVE'] = 'Directive';\n  Kind['NAMED_TYPE'] = 'NamedType';\n  Kind['LIST_TYPE'] = 'ListType';\n  Kind['NON_NULL_TYPE'] = 'NonNullType';\n  Kind['SCHEMA_DEFINITION'] = 'SchemaDefinition';\n  Kind['OPERATION_TYPE_DEFINITION'] = 'OperationTypeDefinition';\n  Kind['SCALAR_TYPE_DEFINITION'] = 'ScalarTypeDefinition';\n  Kind['OBJECT_TYPE_DEFINITION'] = 'ObjectTypeDefinition';\n  Kind['FIELD_DEFINITION'] = 'FieldDefinition';\n  Kind['INPUT_VALUE_DEFINITION'] = 'InputValueDefinition';\n  Kind['INTERFACE_TYPE_DEFINITION'] = 'InterfaceTypeDefinition';\n  Kind['UNION_TYPE_DEFINITION'] = 'UnionTypeDefinition';\n  Kind['ENUM_TYPE_DEFINITION'] = 'EnumTypeDefinition';\n  Kind['ENUM_VALUE_DEFINITION'] = 'EnumValueDefinition';\n  Kind['INPUT_OBJECT_TYPE_DEFINITION'] = 'InputObjectTypeDefinition';\n  Kind['DIRECTIVE_DEFINITION'] = 'DirectiveDefinition';\n  Kind['SCHEMA_EXTENSION'] = 'SchemaExtension';\n  Kind['SCALAR_TYPE_EXTENSION'] = 'ScalarTypeExtension';\n  Kind['OBJECT_TYPE_EXTENSION'] = 'ObjectTypeExtension';\n  Kind['INTERFACE_TYPE_EXTENSION'] = 'InterfaceTypeExtension';\n  Kind['UNION_TYPE_EXTENSION'] = 'UnionTypeExtension';\n  Kind['ENUM_TYPE_EXTENSION'] = 'EnumTypeExtension';\n  Kind['INPUT_OBJECT_TYPE_EXTENSION'] = 'InputObjectTypeExtension';\n})(Kind || (Kind = {}));\n\nexport { Kind };\n/**\n * The enum type representing the possible kind values of AST nodes.\n *\n * @deprecated Please use `Kind`. Will be remove in v17.\n */\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,IAAI;AAER,CAAC,UAAUA,IAAI,EAAE;EACfA,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM;EACrBA,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU;EAC7BA,IAAI,CAAC,sBAAsB,CAAC,GAAG,qBAAqB;EACpDA,IAAI,CAAC,qBAAqB,CAAC,GAAG,oBAAoB;EAClDA,IAAI,CAAC,eAAe,CAAC,GAAG,cAAc;EACtCA,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO;EACvBA,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU;EAC7BA,IAAI,CAAC,iBAAiB,CAAC,GAAG,gBAAgB;EAC1CA,IAAI,CAAC,iBAAiB,CAAC,GAAG,gBAAgB;EAC1CA,IAAI,CAAC,qBAAqB,CAAC,GAAG,oBAAoB;EAClDA,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU;EAC7BA,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU;EACxBA,IAAI,CAAC,OAAO,CAAC,GAAG,YAAY;EAC5BA,IAAI,CAAC,QAAQ,CAAC,GAAG,aAAa;EAC9BA,IAAI,CAAC,SAAS,CAAC,GAAG,cAAc;EAChCA,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;EAC1BA,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;EAC1BA,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;EAC1BA,IAAI,CAAC,QAAQ,CAAC,GAAG,aAAa;EAC9BA,IAAI,CAAC,cAAc,CAAC,GAAG,aAAa;EACpCA,IAAI,CAAC,WAAW,CAAC,GAAG,WAAW;EAC/BA,IAAI,CAAC,YAAY,CAAC,GAAG,WAAW;EAChCA,IAAI,CAAC,WAAW,CAAC,GAAG,UAAU;EAC9BA,IAAI,CAAC,eAAe,CAAC,GAAG,aAAa;EACrCA,IAAI,CAAC,mBAAmB,CAAC,GAAG,kBAAkB;EAC9CA,IAAI,CAAC,2BAA2B,CAAC,GAAG,yBAAyB;EAC7DA,IAAI,CAAC,wBAAwB,CAAC,GAAG,sBAAsB;EACvDA,IAAI,CAAC,wBAAwB,CAAC,GAAG,sBAAsB;EACvDA,IAAI,CAAC,kBAAkB,CAAC,GAAG,iBAAiB;EAC5CA,IAAI,CAAC,wBAAwB,CAAC,GAAG,sBAAsB;EACvDA,IAAI,CAAC,2BAA2B,CAAC,GAAG,yBAAyB;EAC7DA,IAAI,CAAC,uBAAuB,CAAC,GAAG,qBAAqB;EACrDA,IAAI,CAAC,sBAAsB,CAAC,GAAG,oBAAoB;EACnDA,IAAI,CAAC,uBAAuB,CAAC,GAAG,qBAAqB;EACrDA,IAAI,CAAC,8BAA8B,CAAC,GAAG,2BAA2B;EAClEA,IAAI,CAAC,sBAAsB,CAAC,GAAG,qBAAqB;EACpDA,IAAI,CAAC,kBAAkB,CAAC,GAAG,iBAAiB;EAC5CA,IAAI,CAAC,uBAAuB,CAAC,GAAG,qBAAqB;EACrDA,IAAI,CAAC,uBAAuB,CAAC,GAAG,qBAAqB;EACrDA,IAAI,CAAC,0BAA0B,CAAC,GAAG,wBAAwB;EAC3DA,IAAI,CAAC,sBAAsB,CAAC,GAAG,oBAAoB;EACnDA,IAAI,CAAC,qBAAqB,CAAC,GAAG,mBAAmB;EACjDA,IAAI,CAAC,6BAA6B,CAAC,GAAG,0BAA0B;AAClE,CAAC,EAAEA,IAAI,KAAKA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AAEvB,SAASA,IAAI;AACb;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}