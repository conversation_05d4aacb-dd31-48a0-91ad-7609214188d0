{"ast": null, "code": "export { validate } from './validate.mjs';\nexport { ValidationContext } from './ValidationContext.mjs';\n// All validation rules in the GraphQL Specification.\nexport { specifiedRules, recommendedRules } from './specifiedRules.mjs'; // Spec Section: \"Executable Definitions\"\n\nexport { ExecutableDefinitionsRule } from './rules/ExecutableDefinitionsRule.mjs'; // Spec Section: \"Field Selections on Objects, Interfaces, and Unions Types\"\n\nexport { FieldsOnCorrectTypeRule } from './rules/FieldsOnCorrectTypeRule.mjs'; // Spec Section: \"Fragments on Composite Types\"\n\nexport { FragmentsOnCompositeTypesRule } from './rules/FragmentsOnCompositeTypesRule.mjs'; // Spec Section: \"Argument Names\"\n\nexport { KnownArgumentNamesRule } from './rules/KnownArgumentNamesRule.mjs'; // Spec Section: \"Directives Are Defined\"\n\nexport { KnownDirectivesRule } from './rules/KnownDirectivesRule.mjs'; // Spec Section: \"Fragment spread target defined\"\n\nexport { KnownFragmentNamesRule } from './rules/KnownFragmentNamesRule.mjs'; // Spec Section: \"Fragment Spread Type Existence\"\n\nexport { KnownTypeNamesRule } from './rules/KnownTypeNamesRule.mjs'; // Spec Section: \"Lone Anonymous Operation\"\n\nexport { LoneAnonymousOperationRule } from './rules/LoneAnonymousOperationRule.mjs'; // Spec Section: \"Fragments must not form cycles\"\n\nexport { NoFragmentCyclesRule } from './rules/NoFragmentCyclesRule.mjs'; // Spec Section: \"All Variable Used Defined\"\n\nexport { NoUndefinedVariablesRule } from './rules/NoUndefinedVariablesRule.mjs'; // Spec Section: \"Fragments must be used\"\n\nexport { NoUnusedFragmentsRule } from './rules/NoUnusedFragmentsRule.mjs'; // Spec Section: \"All Variables Used\"\n\nexport { NoUnusedVariablesRule } from './rules/NoUnusedVariablesRule.mjs'; // Spec Section: \"Field Selection Merging\"\n\nexport { OverlappingFieldsCanBeMergedRule } from './rules/OverlappingFieldsCanBeMergedRule.mjs'; // Spec Section: \"Fragment spread is possible\"\n\nexport { PossibleFragmentSpreadsRule } from './rules/PossibleFragmentSpreadsRule.mjs'; // Spec Section: \"Argument Optionality\"\n\nexport { ProvidedRequiredArgumentsRule } from './rules/ProvidedRequiredArgumentsRule.mjs'; // Spec Section: \"Leaf Field Selections\"\n\nexport { ScalarLeafsRule } from './rules/ScalarLeafsRule.mjs'; // Spec Section: \"Subscriptions with Single Root Field\"\n\nexport { SingleFieldSubscriptionsRule } from './rules/SingleFieldSubscriptionsRule.mjs'; // Spec Section: \"Argument Uniqueness\"\n\nexport { UniqueArgumentNamesRule } from './rules/UniqueArgumentNamesRule.mjs'; // Spec Section: \"Directives Are Unique Per Location\"\n\nexport { UniqueDirectivesPerLocationRule } from './rules/UniqueDirectivesPerLocationRule.mjs'; // Spec Section: \"Fragment Name Uniqueness\"\n\nexport { UniqueFragmentNamesRule } from './rules/UniqueFragmentNamesRule.mjs'; // Spec Section: \"Input Object Field Uniqueness\"\n\nexport { UniqueInputFieldNamesRule } from './rules/UniqueInputFieldNamesRule.mjs'; // Spec Section: \"Operation Name Uniqueness\"\n\nexport { UniqueOperationNamesRule } from './rules/UniqueOperationNamesRule.mjs'; // Spec Section: \"Variable Uniqueness\"\n\nexport { UniqueVariableNamesRule } from './rules/UniqueVariableNamesRule.mjs'; // Spec Section: \"Values Type Correctness\"\n\nexport { ValuesOfCorrectTypeRule } from './rules/ValuesOfCorrectTypeRule.mjs'; // Spec Section: \"Variables are Input Types\"\n\nexport { VariablesAreInputTypesRule } from './rules/VariablesAreInputTypesRule.mjs'; // Spec Section: \"All Variable Usages Are Allowed\"\n\nexport { VariablesInAllowedPositionRule } from './rules/VariablesInAllowedPositionRule.mjs';\nexport { MaxIntrospectionDepthRule } from './rules/MaxIntrospectionDepthRule.mjs'; // SDL-specific validation rules\n\nexport { LoneSchemaDefinitionRule } from './rules/LoneSchemaDefinitionRule.mjs';\nexport { UniqueOperationTypesRule } from './rules/UniqueOperationTypesRule.mjs';\nexport { UniqueTypeNamesRule } from './rules/UniqueTypeNamesRule.mjs';\nexport { UniqueEnumValueNamesRule } from './rules/UniqueEnumValueNamesRule.mjs';\nexport { UniqueFieldDefinitionNamesRule } from './rules/UniqueFieldDefinitionNamesRule.mjs';\nexport { UniqueArgumentDefinitionNamesRule } from './rules/UniqueArgumentDefinitionNamesRule.mjs';\nexport { UniqueDirectiveNamesRule } from './rules/UniqueDirectiveNamesRule.mjs';\nexport { PossibleTypeExtensionsRule } from './rules/PossibleTypeExtensionsRule.mjs'; // Optional rules not defined by the GraphQL Specification\n\nexport { NoDeprecatedCustomRule } from './rules/custom/NoDeprecatedCustomRule.mjs';\nexport { NoSchemaIntrospectionCustomRule } from './rules/custom/NoSchemaIntrospectionCustomRule.mjs';", "map": {"version": 3, "names": ["validate", "ValidationContext", "specifiedRules", "recommendedRules", "ExecutableDefinitionsRule", "FieldsOnCorrectTypeRule", "FragmentsOnCompositeTypesRule", "KnownArgumentNamesRule", "KnownDirectivesRule", "KnownFragmentNamesRule", "KnownTypeNamesRule", "LoneAnonymousOperationRule", "NoFragmentCyclesRule", "NoUndefinedVariablesRule", "NoUnusedFragmentsRule", "NoUnusedVariablesRule", "OverlappingFieldsCanBeMergedRule", "PossibleFragmentSpreadsRule", "ProvidedRequiredArgumentsRule", "ScalarLeafsRule", "SingleFieldSubscriptionsRule", "UniqueArgumentNamesRule", "UniqueDirectivesPerLocationRule", "UniqueFragmentNamesRule", "UniqueInputFieldNamesRule", "UniqueOperationNamesRule", "UniqueVariableNamesRule", "ValuesOfCorrectTypeRule", "VariablesAreInputTypesRule", "VariablesInAllowedPositionRule", "MaxIntrospectionDepthRule", "LoneSchemaDefinitionRule", "UniqueOperationTypesRule", "UniqueTypeNamesRule", "UniqueEnumValueNamesRule", "UniqueFieldDefinitionNamesRule", "UniqueArgumentDefinitionNamesRule", "UniqueDirectiveNamesRule", "PossibleTypeExtensionsRule", "NoDeprecatedCustomRule", "NoSchemaIntrospectionCustomRule"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/index.mjs"], "sourcesContent": ["export { validate } from './validate.mjs';\nexport { ValidationContext } from './ValidationContext.mjs';\n// All validation rules in the GraphQL Specification.\nexport { specifiedRules, recommendedRules } from './specifiedRules.mjs'; // Spec Section: \"Executable Definitions\"\n\nexport { ExecutableDefinitionsRule } from './rules/ExecutableDefinitionsRule.mjs'; // Spec Section: \"Field Selections on Objects, Interfaces, and Unions Types\"\n\nexport { FieldsOnCorrectTypeRule } from './rules/FieldsOnCorrectTypeRule.mjs'; // Spec Section: \"Fragments on Composite Types\"\n\nexport { FragmentsOnCompositeTypesRule } from './rules/FragmentsOnCompositeTypesRule.mjs'; // Spec Section: \"Argument Names\"\n\nexport { KnownArgumentNamesRule } from './rules/KnownArgumentNamesRule.mjs'; // Spec Section: \"Directives Are Defined\"\n\nexport { KnownDirectivesRule } from './rules/KnownDirectivesRule.mjs'; // Spec Section: \"Fragment spread target defined\"\n\nexport { KnownFragmentNamesRule } from './rules/KnownFragmentNamesRule.mjs'; // Spec Section: \"Fragment Spread Type Existence\"\n\nexport { KnownTypeNamesRule } from './rules/KnownTypeNamesRule.mjs'; // Spec Section: \"Lone Anonymous Operation\"\n\nexport { LoneAnonymousOperationRule } from './rules/LoneAnonymousOperationRule.mjs'; // Spec Section: \"Fragments must not form cycles\"\n\nexport { NoFragmentCyclesRule } from './rules/NoFragmentCyclesRule.mjs'; // Spec Section: \"All Variable Used Defined\"\n\nexport { NoUndefinedVariablesRule } from './rules/NoUndefinedVariablesRule.mjs'; // Spec Section: \"Fragments must be used\"\n\nexport { NoUnusedFragmentsRule } from './rules/NoUnusedFragmentsRule.mjs'; // Spec Section: \"All Variables Used\"\n\nexport { NoUnusedVariablesRule } from './rules/NoUnusedVariablesRule.mjs'; // Spec Section: \"Field Selection Merging\"\n\nexport { OverlappingFieldsCanBeMergedRule } from './rules/OverlappingFieldsCanBeMergedRule.mjs'; // Spec Section: \"Fragment spread is possible\"\n\nexport { PossibleFragmentSpreadsRule } from './rules/PossibleFragmentSpreadsRule.mjs'; // Spec Section: \"Argument Optionality\"\n\nexport { ProvidedRequiredArgumentsRule } from './rules/ProvidedRequiredArgumentsRule.mjs'; // Spec Section: \"Leaf Field Selections\"\n\nexport { ScalarLeafsRule } from './rules/ScalarLeafsRule.mjs'; // Spec Section: \"Subscriptions with Single Root Field\"\n\nexport { SingleFieldSubscriptionsRule } from './rules/SingleFieldSubscriptionsRule.mjs'; // Spec Section: \"Argument Uniqueness\"\n\nexport { UniqueArgumentNamesRule } from './rules/UniqueArgumentNamesRule.mjs'; // Spec Section: \"Directives Are Unique Per Location\"\n\nexport { UniqueDirectivesPerLocationRule } from './rules/UniqueDirectivesPerLocationRule.mjs'; // Spec Section: \"Fragment Name Uniqueness\"\n\nexport { UniqueFragmentNamesRule } from './rules/UniqueFragmentNamesRule.mjs'; // Spec Section: \"Input Object Field Uniqueness\"\n\nexport { UniqueInputFieldNamesRule } from './rules/UniqueInputFieldNamesRule.mjs'; // Spec Section: \"Operation Name Uniqueness\"\n\nexport { UniqueOperationNamesRule } from './rules/UniqueOperationNamesRule.mjs'; // Spec Section: \"Variable Uniqueness\"\n\nexport { UniqueVariableNamesRule } from './rules/UniqueVariableNamesRule.mjs'; // Spec Section: \"Values Type Correctness\"\n\nexport { ValuesOfCorrectTypeRule } from './rules/ValuesOfCorrectTypeRule.mjs'; // Spec Section: \"Variables are Input Types\"\n\nexport { VariablesAreInputTypesRule } from './rules/VariablesAreInputTypesRule.mjs'; // Spec Section: \"All Variable Usages Are Allowed\"\n\nexport { VariablesInAllowedPositionRule } from './rules/VariablesInAllowedPositionRule.mjs';\nexport { MaxIntrospectionDepthRule } from './rules/MaxIntrospectionDepthRule.mjs'; // SDL-specific validation rules\n\nexport { LoneSchemaDefinitionRule } from './rules/LoneSchemaDefinitionRule.mjs';\nexport { UniqueOperationTypesRule } from './rules/UniqueOperationTypesRule.mjs';\nexport { UniqueTypeNamesRule } from './rules/UniqueTypeNamesRule.mjs';\nexport { UniqueEnumValueNamesRule } from './rules/UniqueEnumValueNamesRule.mjs';\nexport { UniqueFieldDefinitionNamesRule } from './rules/UniqueFieldDefinitionNamesRule.mjs';\nexport { UniqueArgumentDefinitionNamesRule } from './rules/UniqueArgumentDefinitionNamesRule.mjs';\nexport { UniqueDirectiveNamesRule } from './rules/UniqueDirectiveNamesRule.mjs';\nexport { PossibleTypeExtensionsRule } from './rules/PossibleTypeExtensionsRule.mjs'; // Optional rules not defined by the GraphQL Specification\n\nexport { NoDeprecatedCustomRule } from './rules/custom/NoDeprecatedCustomRule.mjs';\nexport { NoSchemaIntrospectionCustomRule } from './rules/custom/NoSchemaIntrospectionCustomRule.mjs';\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D;AACA,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,sBAAsB,CAAC,CAAC;;AAEzE,SAASC,yBAAyB,QAAQ,uCAAuC,CAAC,CAAC;;AAEnF,SAASC,uBAAuB,QAAQ,qCAAqC,CAAC,CAAC;;AAE/E,SAASC,6BAA6B,QAAQ,2CAA2C,CAAC,CAAC;;AAE3F,SAASC,sBAAsB,QAAQ,oCAAoC,CAAC,CAAC;;AAE7E,SAASC,mBAAmB,QAAQ,iCAAiC,CAAC,CAAC;;AAEvE,SAASC,sBAAsB,QAAQ,oCAAoC,CAAC,CAAC;;AAE7E,SAASC,kBAAkB,QAAQ,gCAAgC,CAAC,CAAC;;AAErE,SAASC,0BAA0B,QAAQ,wCAAwC,CAAC,CAAC;;AAErF,SAASC,oBAAoB,QAAQ,kCAAkC,CAAC,CAAC;;AAEzE,SAASC,wBAAwB,QAAQ,sCAAsC,CAAC,CAAC;;AAEjF,SAASC,qBAAqB,QAAQ,mCAAmC,CAAC,CAAC;;AAE3E,SAASC,qBAAqB,QAAQ,mCAAmC,CAAC,CAAC;;AAE3E,SAASC,gCAAgC,QAAQ,8CAA8C,CAAC,CAAC;;AAEjG,SAASC,2BAA2B,QAAQ,yCAAyC,CAAC,CAAC;;AAEvF,SAASC,6BAA6B,QAAQ,2CAA2C,CAAC,CAAC;;AAE3F,SAASC,eAAe,QAAQ,6BAA6B,CAAC,CAAC;;AAE/D,SAASC,4BAA4B,QAAQ,0CAA0C,CAAC,CAAC;;AAEzF,SAASC,uBAAuB,QAAQ,qCAAqC,CAAC,CAAC;;AAE/E,SAASC,+BAA+B,QAAQ,6CAA6C,CAAC,CAAC;;AAE/F,SAASC,uBAAuB,QAAQ,qCAAqC,CAAC,CAAC;;AAE/E,SAASC,yBAAyB,QAAQ,uCAAuC,CAAC,CAAC;;AAEnF,SAASC,wBAAwB,QAAQ,sCAAsC,CAAC,CAAC;;AAEjF,SAASC,uBAAuB,QAAQ,qCAAqC,CAAC,CAAC;;AAE/E,SAASC,uBAAuB,QAAQ,qCAAqC,CAAC,CAAC;;AAE/E,SAASC,0BAA0B,QAAQ,wCAAwC,CAAC,CAAC;;AAErF,SAASC,8BAA8B,QAAQ,4CAA4C;AAC3F,SAASC,yBAAyB,QAAQ,uCAAuC,CAAC,CAAC;;AAEnF,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,8BAA8B,QAAQ,4CAA4C;AAC3F,SAASC,iCAAiC,QAAQ,+CAA+C;AACjG,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,SAASC,0BAA0B,QAAQ,wCAAwC,CAAC,CAAC;;AAErF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,+BAA+B,QAAQ,oDAAoD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}