{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/Scorecard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSelector } from 'react-redux';\nimport { Box, Typography, Divider, Chip, List, ListItem, ListItemText, Alert, LinearProgress } from '@mui/material';\nimport { Warning, CheckCircle, Error } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Scorecard = () => {\n  _s();\n  var _currentBIA$processes2, _currentBIA$processes3, _currentBIA$processes4, _currentBIA$processes5, _currentBIA$processes6;\n  const {\n    currentBIA,\n    selectedProcess\n  } = useSelector(state => state.bia);\n  const getOwnerName = ownerId => {\n    // Mock lookup - in real app, would use selector\n    const users = {\n      'user-01': '<PERSON>',\n      'user-02': '<PERSON>'\n    };\n    return users[ownerId] || ownerId;\n  };\n  const getFunctionName = functionId => {\n    const functions = {\n      'func-01': 'Finance',\n      'func-02': 'Technology'\n    };\n    return functions[functionId] || functionId;\n  };\n  const getProcessName = processId => {\n    // Mock lookup\n    const processes = {\n      'proc-f-01': 'Accounts Payable',\n      'proc-f-02': 'Payroll Processing',\n      'proc-t-01': 'Oracle Financials DB'\n    };\n    return processes[processId] || processId;\n  };\n  const calculateSuggestedRTO = processId => {\n    var _currentBIA$processDa;\n    const processData = currentBIA === null || currentBIA === void 0 ? void 0 : (_currentBIA$processDa = currentBIA.processData) === null || _currentBIA$processDa === void 0 ? void 0 : _currentBIA$processDa[processId];\n    if (!(processData !== null && processData !== void 0 && processData.impactMatrix)) return 'Not Set';\n\n    // Logic: Find highest impact timeframe\n    const matrix = processData.impactMatrix;\n    const timeframes = ['0-4h', '4-8h', '8-24h', '24-72h', '72h+'];\n    for (let timeframe of timeframes) {\n      const hasHighImpact = Object.values(matrix[timeframe] || {}).some(rating => rating === 'High');\n      if (hasHighImpact) {\n        return timeframe === '0-4h' ? '4 Hours' : timeframe === '4-8h' ? '8 Hours' : timeframe === '8-24h' ? '24 Hours' : timeframe === '24-72h' ? '72 Hours' : '72+ Hours';\n      }\n    }\n    return '72+ Hours';\n  };\n  const getDependencyConflicts = processId => {\n    var _currentBIA$processDa2;\n    const processData = currentBIA === null || currentBIA === void 0 ? void 0 : (_currentBIA$processDa2 = currentBIA.processData) === null || _currentBIA$processDa2 === void 0 ? void 0 : _currentBIA$processDa2[processId];\n    return (processData === null || processData === void 0 ? void 0 : processData.dependencyConflicts) || 0;\n  };\n  const getSPOFCount = processId => {\n    var _currentBIA$processDa3, _processData$spofs;\n    const processData = currentBIA === null || currentBIA === void 0 ? void 0 : (_currentBIA$processDa3 = currentBIA.processData) === null || _currentBIA$processDa3 === void 0 ? void 0 : _currentBIA$processDa3[processId];\n    return (processData === null || processData === void 0 ? void 0 : (_processData$spofs = processData.spofs) === null || _processData$spofs === void 0 ? void 0 : _processData$spofs.length) || 0;\n  };\n  const getCompletionPercentage = () => {\n    var _currentBIA$processes;\n    if (!(currentBIA !== null && currentBIA !== void 0 && (_currentBIA$processes = currentBIA.processes) !== null && _currentBIA$processes !== void 0 && _currentBIA$processes.length)) return 0;\n    let completed = 0;\n    const total = currentBIA.processes.length;\n    currentBIA.processes.forEach(processId => {\n      var _currentBIA$processDa4;\n      const processData = (_currentBIA$processDa4 = currentBIA.processData) === null || _currentBIA$processDa4 === void 0 ? void 0 : _currentBIA$processDa4[processId];\n      if (processData !== null && processData !== void 0 && processData.finalRTO && processData !== null && processData !== void 0 && processData.finalRPO) {\n        completed++;\n      }\n    });\n    return Math.round(completed / total * 100);\n  };\n  if (!currentBIA) return null;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"BIA Scorecard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        color: \"text.secondary\",\n        children: \"BIA Owner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        gutterBottom: true,\n        children: getOwnerName(currentBIA.owner)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        color: \"text.secondary\",\n        children: \"Function\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        gutterBottom: true,\n        children: getFunctionName(currentBIA.function)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        color: \"text.secondary\",\n        children: \"Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        label: currentBIA.status,\n        color: currentBIA.status === 'Draft' ? 'default' : 'primary',\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: \"Completion Progress\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n        variant: \"determinate\",\n        value: getCompletionPercentage(),\n        sx: {\n          mb: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: [getCompletionPercentage(), \"% Complete\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: [\"Processes Being Analyzed (\", ((_currentBIA$processes2 = currentBIA.processes) === null || _currentBIA$processes2 === void 0 ? void 0 : _currentBIA$processes2.length) || 0, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        dense: true,\n        children: (_currentBIA$processes3 = currentBIA.processes) === null || _currentBIA$processes3 === void 0 ? void 0 : _currentBIA$processes3.map(processId => {\n          var _currentBIA$processDa5, _currentBIA$processDa6, _currentBIA$processDa7, _currentBIA$processDa8;\n          return /*#__PURE__*/_jsxDEV(ListItem, {\n            sx: {\n              px: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: getProcessName(processId),\n              secondary: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: [\"RTO: \", ((_currentBIA$processDa5 = currentBIA.processData) === null || _currentBIA$processDa5 === void 0 ? void 0 : (_currentBIA$processDa6 = _currentBIA$processDa5[processId]) === null || _currentBIA$processDa6 === void 0 ? void 0 : _currentBIA$processDa6.finalRTO) || 'Not Set']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: [\"RPO: \", ((_currentBIA$processDa7 = currentBIA.processData) === null || _currentBIA$processDa7 === void 0 ? void 0 : (_currentBIA$processDa8 = _currentBIA$processDa7[processId]) === null || _currentBIA$processDa8 === void 0 ? void 0 : _currentBIA$processDa8.finalRPO) || 'Not Set']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)\n          }, processId, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), selectedProcess && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          children: [\"Current Process: \", getProcessName(selectedProcess)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Suggested RTO\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: calculateSuggestedRTO(selectedProcess)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Dependency Conflicts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: getDependencyConflicts(selectedProcess)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this), getDependencyConflicts(selectedProcess) > 0 && /*#__PURE__*/_jsxDEV(Warning, {\n              color: \"warning\",\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"SPOFs Identified\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: getSPOFCount(selectedProcess)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: \"Validation Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), getCompletionPercentage() === 100 ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        icon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 43\n        }, this),\n        sx: {\n          mb: 1\n        },\n        children: \"All processes analyzed\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 1\n        },\n        children: [((_currentBIA$processes4 = currentBIA.processes) === null || _currentBIA$processes4 === void 0 ? void 0 : _currentBIA$processes4.length) - Math.round(getCompletionPercentage() / 100 * ((_currentBIA$processes5 = currentBIA.processes) === null || _currentBIA$processes5 === void 0 ? void 0 : _currentBIA$processes5.length) || 0), \" processes need analysis\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this), ((_currentBIA$processes6 = currentBIA.processes) === null || _currentBIA$processes6 === void 0 ? void 0 : _currentBIA$processes6.some(processId => getDependencyConflicts(processId) > 0)) && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        icon: /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 43\n        }, this),\n        children: \"Dependency conflicts detected\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(Scorecard, \"jcvdMqeqGZNYv/qswMpH+TWFXqs=\", false, function () {\n  return [useSelector];\n});\n_c = Scorecard;\nexport default Scorecard;\nvar _c;\n$RefreshReg$(_c, \"Scorecard\");", "map": {"version": 3, "names": ["React", "useSelector", "Box", "Typography", "Divider", "Chip", "List", "ListItem", "ListItemText", "<PERSON><PERSON>", "LinearProgress", "Warning", "CheckCircle", "Error", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Scorecard", "_s", "_currentBIA$processes2", "_currentBIA$processes3", "_currentBIA$processes4", "_currentBIA$processes5", "_currentBIA$processes6", "currentBIA", "selectedProcess", "state", "bia", "getOwnerName", "ownerId", "users", "getFunctionName", "functionId", "functions", "getProcessName", "processId", "processes", "calculateSuggestedRTO", "_currentBIA$processDa", "processData", "impactMatrix", "matrix", "timeframes", "timeframe", "hasHighImpact", "Object", "values", "some", "rating", "getDependencyConflicts", "_currentBIA$processDa2", "dependencyConflicts", "getSPOFCount", "_currentBIA$processDa3", "_processData$spofs", "spofs", "length", "getCompletionPercentage", "_currentBIA$processes", "completed", "total", "for<PERSON>ach", "_currentBIA$processDa4", "finalRTO", "finalRPO", "Math", "round", "sx", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "color", "owner", "function", "label", "status", "size", "value", "dense", "map", "_currentBIA$processDa5", "_currentBIA$processDa6", "_currentBIA$processDa7", "_currentBIA$processDa8", "px", "primary", "secondary", "display", "alignItems", "gap", "fontSize", "severity", "icon", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/Scorecard.js"], "sourcesContent": ["import React from 'react';\nimport { useSelector } from 'react-redux';\nimport {\n  <PERSON>, Typography, Divider, Chip, List, ListItem, ListItemText,\n  Alert, LinearProgress\n} from '@mui/material';\nimport { Warning, CheckCircle, Error } from '@mui/icons-material';\n\nconst Scorecard = () => {\n  const { currentBIA, selectedProcess } = useSelector(state => state.bia);\n\n  const getOwnerName = (ownerId) => {\n    // Mock lookup - in real app, would use selector\n    const users = { 'user-01': '<PERSON> Johnson', 'user-02': '<PERSON>' };\n    return users[ownerId] || ownerId;\n  };\n\n  const getFunctionName = (functionId) => {\n    const functions = { 'func-01': 'Finance', 'func-02': 'Technology' };\n    return functions[functionId] || functionId;\n  };\n\n  const getProcessName = (processId) => {\n    // Mock lookup\n    const processes = {\n      'proc-f-01': 'Accounts Payable',\n      'proc-f-02': 'Payroll Processing',\n      'proc-t-01': 'Oracle Financials DB'\n    };\n    return processes[processId] || processId;\n  };\n\n  const calculateSuggestedRTO = (processId) => {\n    const processData = currentBIA?.processData?.[processId];\n    if (!processData?.impactMatrix) return 'Not Set';\n    \n    // Logic: Find highest impact timeframe\n    const matrix = processData.impactMatrix;\n    const timeframes = ['0-4h', '4-8h', '8-24h', '24-72h', '72h+'];\n    \n    for (let timeframe of timeframes) {\n      const hasHighImpact = Object.values(matrix[timeframe] || {}).some(rating => rating === 'High');\n      if (hasHighImpact) {\n        return timeframe === '0-4h' ? '4 Hours' : \n               timeframe === '4-8h' ? '8 Hours' :\n               timeframe === '8-24h' ? '24 Hours' :\n               timeframe === '24-72h' ? '72 Hours' : '72+ Hours';\n      }\n    }\n    return '72+ Hours';\n  };\n\n  const getDependencyConflicts = (processId) => {\n    const processData = currentBIA?.processData?.[processId];\n    return processData?.dependencyConflicts || 0;\n  };\n\n  const getSPOFCount = (processId) => {\n    const processData = currentBIA?.processData?.[processId];\n    return processData?.spofs?.length || 0;\n  };\n\n  const getCompletionPercentage = () => {\n    if (!currentBIA?.processes?.length) return 0;\n    \n    let completed = 0;\n    const total = currentBIA.processes.length;\n    \n    currentBIA.processes.forEach(processId => {\n      const processData = currentBIA.processData?.[processId];\n      if (processData?.finalRTO && processData?.finalRPO) {\n        completed++;\n      }\n    });\n    \n    return Math.round((completed / total) * 100);\n  };\n\n  if (!currentBIA) return null;\n\n  return (\n    <Box sx={{ p: 2 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        BIA Scorecard\n      </Typography>\n      \n      <Divider sx={{ mb: 2 }} />\n\n      {/* Basic Info */}\n      <Box sx={{ mb: 3 }}>\n        <Typography variant=\"subtitle2\" color=\"text.secondary\">\n          BIA Owner\n        </Typography>\n        <Typography variant=\"body2\" gutterBottom>\n          {getOwnerName(currentBIA.owner)}\n        </Typography>\n\n        <Typography variant=\"subtitle2\" color=\"text.secondary\">\n          Function\n        </Typography>\n        <Typography variant=\"body2\" gutterBottom>\n          {getFunctionName(currentBIA.function)}\n        </Typography>\n\n        <Typography variant=\"subtitle2\" color=\"text.secondary\">\n          Status\n        </Typography>\n        <Chip \n          label={currentBIA.status} \n          color={currentBIA.status === 'Draft' ? 'default' : 'primary'}\n          size=\"small\"\n        />\n      </Box>\n\n      <Divider sx={{ mb: 2 }} />\n\n      {/* Progress */}\n      <Box sx={{ mb: 3 }}>\n        <Typography variant=\"subtitle2\" gutterBottom>\n          Completion Progress\n        </Typography>\n        <LinearProgress \n          variant=\"determinate\" \n          value={getCompletionPercentage()} \n          sx={{ mb: 1 }}\n        />\n        <Typography variant=\"caption\" color=\"text.secondary\">\n          {getCompletionPercentage()}% Complete\n        </Typography>\n      </Box>\n\n      <Divider sx={{ mb: 2 }} />\n\n      {/* Processes */}\n      <Box sx={{ mb: 3 }}>\n        <Typography variant=\"subtitle2\" gutterBottom>\n          Processes Being Analyzed ({currentBIA.processes?.length || 0})\n        </Typography>\n        <List dense>\n          {currentBIA.processes?.map(processId => (\n            <ListItem key={processId} sx={{ px: 0 }}>\n              <ListItemText \n                primary={getProcessName(processId)}\n                secondary={\n                  <Box>\n                    <Typography variant=\"caption\" display=\"block\">\n                      RTO: {currentBIA.processData?.[processId]?.finalRTO || 'Not Set'}\n                    </Typography>\n                    <Typography variant=\"caption\" display=\"block\">\n                      RPO: {currentBIA.processData?.[processId]?.finalRPO || 'Not Set'}\n                    </Typography>\n                  </Box>\n                }\n              />\n            </ListItem>\n          ))}\n        </List>\n      </Box>\n\n      {/* Current Process Details */}\n      {selectedProcess && (\n        <>\n          <Divider sx={{ mb: 2 }} />\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              Current Process: {getProcessName(selectedProcess)}\n            </Typography>\n            \n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Suggested RTO\n              </Typography>\n              <Typography variant=\"body2\">\n                {calculateSuggestedRTO(selectedProcess)}\n              </Typography>\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Dependency Conflicts\n              </Typography>\n              <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                <Typography variant=\"body2\">\n                  {getDependencyConflicts(selectedProcess)}\n                </Typography>\n                {getDependencyConflicts(selectedProcess) > 0 && (\n                  <Warning color=\"warning\" fontSize=\"small\" />\n                )}\n              </Box>\n            </Box>\n\n            <Box>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                SPOFs Identified\n              </Typography>\n              <Typography variant=\"body2\">\n                {getSPOFCount(selectedProcess)}\n              </Typography>\n            </Box>\n          </Box>\n        </>\n      )}\n\n      {/* Validation Alerts */}\n      <Divider sx={{ mb: 2 }} />\n      <Box>\n        <Typography variant=\"subtitle2\" gutterBottom>\n          Validation Status\n        </Typography>\n        \n        {getCompletionPercentage() === 100 ? (\n          <Alert severity=\"success\" icon={<CheckCircle />} sx={{ mb: 1 }}>\n            All processes analyzed\n          </Alert>\n        ) : (\n          <Alert severity=\"info\" sx={{ mb: 1 }}>\n            {currentBIA.processes?.length - Math.round((getCompletionPercentage() / 100) * currentBIA.processes?.length || 0)} processes need analysis\n          </Alert>\n        )}\n\n        {currentBIA.processes?.some(processId => getDependencyConflicts(processId) > 0) && (\n          <Alert severity=\"warning\" icon={<Warning />}>\n            Dependency conflicts detected\n          </Alert>\n        )}\n      </Box>\n    </Box>\n  );\n};\n\nexport default Scorecard;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAC5DC,KAAK,EAAEC,cAAc,QAChB,eAAe;AACtB,SAASC,OAAO,EAAEC,WAAW,EAAEC,KAAK,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACtB,MAAM;IAAEC,UAAU;IAAEC;EAAgB,CAAC,GAAGzB,WAAW,CAAC0B,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EAEvE,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC;IACA,MAAMC,KAAK,GAAG;MAAE,SAAS,EAAE,eAAe;MAAE,SAAS,EAAE;IAAe,CAAC;IACvE,OAAOA,KAAK,CAACD,OAAO,CAAC,IAAIA,OAAO;EAClC,CAAC;EAED,MAAME,eAAe,GAAIC,UAAU,IAAK;IACtC,MAAMC,SAAS,GAAG;MAAE,SAAS,EAAE,SAAS;MAAE,SAAS,EAAE;IAAa,CAAC;IACnE,OAAOA,SAAS,CAACD,UAAU,CAAC,IAAIA,UAAU;EAC5C,CAAC;EAED,MAAME,cAAc,GAAIC,SAAS,IAAK;IACpC;IACA,MAAMC,SAAS,GAAG;MAChB,WAAW,EAAE,kBAAkB;MAC/B,WAAW,EAAE,oBAAoB;MACjC,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,SAAS,CAACD,SAAS,CAAC,IAAIA,SAAS;EAC1C,CAAC;EAED,MAAME,qBAAqB,GAAIF,SAAS,IAAK;IAAA,IAAAG,qBAAA;IAC3C,MAAMC,WAAW,GAAGf,UAAU,aAAVA,UAAU,wBAAAc,qBAAA,GAAVd,UAAU,CAAEe,WAAW,cAAAD,qBAAA,uBAAvBA,qBAAA,CAA0BH,SAAS,CAAC;IACxD,IAAI,EAACI,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEC,YAAY,GAAE,OAAO,SAAS;;IAEhD;IACA,MAAMC,MAAM,GAAGF,WAAW,CAACC,YAAY;IACvC,MAAME,UAAU,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;IAE9D,KAAK,IAAIC,SAAS,IAAID,UAAU,EAAE;MAChC,MAAME,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACL,MAAM,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,MAAM,CAAC;MAC9F,IAAIJ,aAAa,EAAE;QACjB,OAAOD,SAAS,KAAK,MAAM,GAAG,SAAS,GAChCA,SAAS,KAAK,MAAM,GAAG,SAAS,GAChCA,SAAS,KAAK,OAAO,GAAG,UAAU,GAClCA,SAAS,KAAK,QAAQ,GAAG,UAAU,GAAG,WAAW;MAC1D;IACF;IACA,OAAO,WAAW;EACpB,CAAC;EAED,MAAMM,sBAAsB,GAAId,SAAS,IAAK;IAAA,IAAAe,sBAAA;IAC5C,MAAMX,WAAW,GAAGf,UAAU,aAAVA,UAAU,wBAAA0B,sBAAA,GAAV1B,UAAU,CAAEe,WAAW,cAAAW,sBAAA,uBAAvBA,sBAAA,CAA0Bf,SAAS,CAAC;IACxD,OAAO,CAAAI,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,mBAAmB,KAAI,CAAC;EAC9C,CAAC;EAED,MAAMC,YAAY,GAAIjB,SAAS,IAAK;IAAA,IAAAkB,sBAAA,EAAAC,kBAAA;IAClC,MAAMf,WAAW,GAAGf,UAAU,aAAVA,UAAU,wBAAA6B,sBAAA,GAAV7B,UAAU,CAAEe,WAAW,cAAAc,sBAAA,uBAAvBA,sBAAA,CAA0BlB,SAAS,CAAC;IACxD,OAAO,CAAAI,WAAW,aAAXA,WAAW,wBAAAe,kBAAA,GAAXf,WAAW,CAAEgB,KAAK,cAAAD,kBAAA,uBAAlBA,kBAAA,CAAoBE,MAAM,KAAI,CAAC;EACxC,CAAC;EAED,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IACpC,IAAI,EAAClC,UAAU,aAAVA,UAAU,gBAAAkC,qBAAA,GAAVlC,UAAU,CAAEY,SAAS,cAAAsB,qBAAA,eAArBA,qBAAA,CAAuBF,MAAM,GAAE,OAAO,CAAC;IAE5C,IAAIG,SAAS,GAAG,CAAC;IACjB,MAAMC,KAAK,GAAGpC,UAAU,CAACY,SAAS,CAACoB,MAAM;IAEzChC,UAAU,CAACY,SAAS,CAACyB,OAAO,CAAC1B,SAAS,IAAI;MAAA,IAAA2B,sBAAA;MACxC,MAAMvB,WAAW,IAAAuB,sBAAA,GAAGtC,UAAU,CAACe,WAAW,cAAAuB,sBAAA,uBAAtBA,sBAAA,CAAyB3B,SAAS,CAAC;MACvD,IAAII,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEwB,QAAQ,IAAIxB,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEyB,QAAQ,EAAE;QAClDL,SAAS,EAAE;MACb;IACF,CAAC,CAAC;IAEF,OAAOM,IAAI,CAACC,KAAK,CAAEP,SAAS,GAAGC,KAAK,GAAI,GAAG,CAAC;EAC9C,CAAC;EAED,IAAI,CAACpC,UAAU,EAAE,OAAO,IAAI;EAE5B,oBACEV,OAAA,CAACb,GAAG;IAACkE,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChBvD,OAAA,CAACZ,UAAU;MAACoE,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb7D,OAAA,CAACX,OAAO;MAACgE,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE;IAAE;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG1B7D,OAAA,CAACb,GAAG;MAACkE,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,gBACjBvD,OAAA,CAACZ,UAAU;QAACoE,OAAO,EAAC,WAAW;QAACO,KAAK,EAAC,gBAAgB;QAAAR,QAAA,EAAC;MAEvD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7D,OAAA,CAACZ,UAAU;QAACoE,OAAO,EAAC,OAAO;QAACC,YAAY;QAAAF,QAAA,EACrCzC,YAAY,CAACJ,UAAU,CAACsD,KAAK;MAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eAEb7D,OAAA,CAACZ,UAAU;QAACoE,OAAO,EAAC,WAAW;QAACO,KAAK,EAAC,gBAAgB;QAAAR,QAAA,EAAC;MAEvD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7D,OAAA,CAACZ,UAAU;QAACoE,OAAO,EAAC,OAAO;QAACC,YAAY;QAAAF,QAAA,EACrCtC,eAAe,CAACP,UAAU,CAACuD,QAAQ;MAAC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAEb7D,OAAA,CAACZ,UAAU;QAACoE,OAAO,EAAC,WAAW;QAACO,KAAK,EAAC,gBAAgB;QAAAR,QAAA,EAAC;MAEvD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7D,OAAA,CAACV,IAAI;QACH4E,KAAK,EAAExD,UAAU,CAACyD,MAAO;QACzBJ,KAAK,EAAErD,UAAU,CAACyD,MAAM,KAAK,OAAO,GAAG,SAAS,GAAG,SAAU;QAC7DC,IAAI,EAAC;MAAO;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN7D,OAAA,CAACX,OAAO;MAACgE,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE;IAAE;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG1B7D,OAAA,CAACb,GAAG;MAACkE,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,gBACjBvD,OAAA,CAACZ,UAAU;QAACoE,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAF,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7D,OAAA,CAACL,cAAc;QACb6D,OAAO,EAAC,aAAa;QACrBa,KAAK,EAAE1B,uBAAuB,CAAC,CAAE;QACjCU,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACF7D,OAAA,CAACZ,UAAU;QAACoE,OAAO,EAAC,SAAS;QAACO,KAAK,EAAC,gBAAgB;QAAAR,QAAA,GACjDZ,uBAAuB,CAAC,CAAC,EAAC,YAC7B;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN7D,OAAA,CAACX,OAAO;MAACgE,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE;IAAE;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG1B7D,OAAA,CAACb,GAAG;MAACkE,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,gBACjBvD,OAAA,CAACZ,UAAU;QAACoE,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAF,QAAA,GAAC,4BACjB,EAAC,EAAAlD,sBAAA,GAAAK,UAAU,CAACY,SAAS,cAAAjB,sBAAA,uBAApBA,sBAAA,CAAsBqC,MAAM,KAAI,CAAC,EAAC,GAC/D;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7D,OAAA,CAACT,IAAI;QAAC+E,KAAK;QAAAf,QAAA,GAAAjD,sBAAA,GACRI,UAAU,CAACY,SAAS,cAAAhB,sBAAA,uBAApBA,sBAAA,CAAsBiE,GAAG,CAAClD,SAAS;UAAA,IAAAmD,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAAA,oBAClC3E,OAAA,CAACR,QAAQ;YAAiB6D,EAAE,EAAE;cAAEuB,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,eACtCvD,OAAA,CAACP,YAAY;cACXoF,OAAO,EAAEzD,cAAc,CAACC,SAAS,CAAE;cACnCyD,SAAS,eACP9E,OAAA,CAACb,GAAG;gBAAAoE,QAAA,gBACFvD,OAAA,CAACZ,UAAU;kBAACoE,OAAO,EAAC,SAAS;kBAACuB,OAAO,EAAC,OAAO;kBAAAxB,QAAA,GAAC,OACvC,EAAC,EAAAiB,sBAAA,GAAA9D,UAAU,CAACe,WAAW,cAAA+C,sBAAA,wBAAAC,sBAAA,GAAtBD,sBAAA,CAAyBnD,SAAS,CAAC,cAAAoD,sBAAA,uBAAnCA,sBAAA,CAAqCxB,QAAQ,KAAI,SAAS;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACb7D,OAAA,CAACZ,UAAU;kBAACoE,OAAO,EAAC,SAAS;kBAACuB,OAAO,EAAC,OAAO;kBAAAxB,QAAA,GAAC,OACvC,EAAC,EAAAmB,sBAAA,GAAAhE,UAAU,CAACe,WAAW,cAAAiD,sBAAA,wBAAAC,sBAAA,GAAtBD,sBAAA,CAAyBrD,SAAS,CAAC,cAAAsD,sBAAA,uBAAnCA,sBAAA,CAAqCzB,QAAQ,KAAI,SAAS;gBAAA;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GAbWxC,SAAS;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcd,CAAC;QAAA,CACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLlD,eAAe,iBACdX,OAAA,CAAAE,SAAA;MAAAqD,QAAA,gBACEvD,OAAA,CAACX,OAAO;QAACgE,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1B7D,OAAA,CAACb,GAAG;QAACkE,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACjBvD,OAAA,CAACZ,UAAU;UAACoE,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,GAAC,mBAC1B,EAACnC,cAAc,CAACT,eAAe,CAAC;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eAEb7D,OAAA,CAACb,GAAG;UAACkE,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,gBACjBvD,OAAA,CAACZ,UAAU;YAACoE,OAAO,EAAC,SAAS;YAACO,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAErD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7D,OAAA,CAACZ,UAAU;YAACoE,OAAO,EAAC,OAAO;YAAAD,QAAA,EACxBhC,qBAAqB,CAACZ,eAAe;UAAC;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN7D,OAAA,CAACb,GAAG;UAACkE,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,gBACjBvD,OAAA,CAACZ,UAAU;YAACoE,OAAO,EAAC,SAAS;YAACO,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAErD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7D,OAAA,CAACb,GAAG;YAAC4F,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAAA1B,QAAA,gBAC7CvD,OAAA,CAACZ,UAAU;cAACoE,OAAO,EAAC,OAAO;cAAAD,QAAA,EACxBpB,sBAAsB,CAACxB,eAAe;YAAC;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACZ1B,sBAAsB,CAACxB,eAAe,CAAC,GAAG,CAAC,iBAC1CX,OAAA,CAACJ,OAAO;cAACmE,KAAK,EAAC,SAAS;cAACmB,QAAQ,EAAC;YAAO;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAC5C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7D,OAAA,CAACb,GAAG;UAAAoE,QAAA,gBACFvD,OAAA,CAACZ,UAAU;YAACoE,OAAO,EAAC,SAAS;YAACO,KAAK,EAAC,gBAAgB;YAAAR,QAAA,EAAC;UAErD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7D,OAAA,CAACZ,UAAU;YAACoE,OAAO,EAAC,OAAO;YAAAD,QAAA,EACxBjB,YAAY,CAAC3B,eAAe;UAAC;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CACH,eAGD7D,OAAA,CAACX,OAAO;MAACgE,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE;IAAE;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1B7D,OAAA,CAACb,GAAG;MAAAoE,QAAA,gBACFvD,OAAA,CAACZ,UAAU;QAACoE,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAF,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZlB,uBAAuB,CAAC,CAAC,KAAK,GAAG,gBAChC3C,OAAA,CAACN,KAAK;QAACyF,QAAQ,EAAC,SAAS;QAACC,IAAI,eAAEpF,OAAA,CAACH,WAAW;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACR,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,EAAC;MAEhE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,gBAER7D,OAAA,CAACN,KAAK;QAACyF,QAAQ,EAAC,MAAM;QAAC9B,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,GAClC,EAAAhD,sBAAA,GAAAG,UAAU,CAACY,SAAS,cAAAf,sBAAA,uBAApBA,sBAAA,CAAsBmC,MAAM,IAAGS,IAAI,CAACC,KAAK,CAAET,uBAAuB,CAAC,CAAC,GAAG,GAAG,KAAAnC,sBAAA,GAAIE,UAAU,CAACY,SAAS,cAAAd,sBAAA,uBAApBA,sBAAA,CAAsBkC,MAAM,KAAI,CAAC,CAAC,EAAC,0BACpH;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAEA,EAAApD,sBAAA,GAAAC,UAAU,CAACY,SAAS,cAAAb,sBAAA,uBAApBA,sBAAA,CAAsBwB,IAAI,CAACZ,SAAS,IAAIc,sBAAsB,CAACd,SAAS,CAAC,GAAG,CAAC,CAAC,kBAC7ErB,OAAA,CAACN,KAAK;QAACyF,QAAQ,EAAC,SAAS;QAACC,IAAI,eAAEpF,OAAA,CAACJ,OAAO;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAN,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzD,EAAA,CA5NID,SAAS;EAAA,QAC2BjB,WAAW;AAAA;AAAAmG,EAAA,GAD/ClF,SAAS;AA8Nf,eAAeA,SAAS;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}