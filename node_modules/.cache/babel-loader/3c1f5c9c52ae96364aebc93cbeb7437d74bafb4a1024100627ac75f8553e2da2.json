{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { getNamedType, isInterfaceType, isLeafType, isListType, isNonNullType, isObjectType } from '../../type/definition.mjs';\nimport { sortValueNode } from '../../utilities/sortValueNode.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\nfunction reasonMessage(reason) {\n  if (Array.isArray(reason)) {\n    return reason.map(([responseName, subReason]) => `subfields \"${responseName}\" conflict because ` + reasonMessage(subReason)).join(' and ');\n  }\n  return reason;\n}\n/**\n * Overlapping fields can be merged\n *\n * A selection set is only valid if all fields (including spreading any\n * fragments) either correspond to distinct response names or can be merged\n * without ambiguity.\n *\n * See https://spec.graphql.org/draft/#sec-Field-Selection-Merging\n */\n\nexport function OverlappingFieldsCanBeMergedRule(context) {\n  // A memoization for when fields and a fragment or two fragments are compared\n  // \"between\" each other for conflicts. Comparisons made be made many times,\n  // so memoizing this can dramatically improve the performance of this validator.\n  const comparedFieldsAndFragmentPairs = new OrderedPairSet();\n  const comparedFragmentPairs = new PairSet(); // A cache for the \"field map\" and list of fragment names found in any given\n  // selection set. Selection sets may be asked for this information multiple\n  // times, so this improves the performance of this validator.\n\n  const cachedFieldsAndFragmentNames = new Map();\n  return {\n    SelectionSet(selectionSet) {\n      const conflicts = findConflictsWithinSelectionSet(context, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, context.getParentType(), selectionSet);\n      for (const [[responseName, reason], fields1, fields2] of conflicts) {\n        const reasonMsg = reasonMessage(reason);\n        context.reportError(new GraphQLError(`Fields \"${responseName}\" conflict because ${reasonMsg}. Use different aliases on the fields to fetch both if this was intentional.`, {\n          nodes: fields1.concat(fields2)\n        }));\n      }\n    }\n  };\n}\n\n/**\n * Algorithm:\n *\n * Conflicts occur when two fields exist in a query which will produce the same\n * response name, but represent differing values, thus creating a conflict.\n * The algorithm below finds all conflicts via making a series of comparisons\n * between fields. In order to compare as few fields as possible, this makes\n * a series of comparisons \"within\" sets of fields and \"between\" sets of fields.\n *\n * Given any selection set, a collection produces both a set of fields by\n * also including all inline fragments, as well as a list of fragments\n * referenced by fragment spreads.\n *\n * A) Each selection set represented in the document first compares \"within\" its\n * collected set of fields, finding any conflicts between every pair of\n * overlapping fields.\n * Note: This is the *only time* that a the fields \"within\" a set are compared\n * to each other. After this only fields \"between\" sets are compared.\n *\n * B) Also, if any fragment is referenced in a selection set, then a\n * comparison is made \"between\" the original set of fields and the\n * referenced fragment.\n *\n * C) Also, if multiple fragments are referenced, then comparisons\n * are made \"between\" each referenced fragment.\n *\n * D) When comparing \"between\" a set of fields and a referenced fragment, first\n * a comparison is made between each field in the original set of fields and\n * each field in the the referenced set of fields.\n *\n * E) Also, if any fragment is referenced in the referenced selection set,\n * then a comparison is made \"between\" the original set of fields and the\n * referenced fragment (recursively referring to step D).\n *\n * F) When comparing \"between\" two fragments, first a comparison is made between\n * each field in the first referenced set of fields and each field in the the\n * second referenced set of fields.\n *\n * G) Also, any fragments referenced by the first must be compared to the\n * second, and any fragments referenced by the second must be compared to the\n * first (recursively referring to step F).\n *\n * H) When comparing two fields, if both have selection sets, then a comparison\n * is made \"between\" both selection sets, first comparing the set of fields in\n * the first selection set with the set of fields in the second.\n *\n * I) Also, if any fragment is referenced in either selection set, then a\n * comparison is made \"between\" the other set of fields and the\n * referenced fragment.\n *\n * J) Also, if two fragments are referenced in both selection sets, then a\n * comparison is made \"between\" the two fragments.\n *\n */\n// Find all conflicts found \"within\" a selection set, including those found\n// via spreading in fragments. Called when visiting each SelectionSet in the\n// GraphQL Document.\nfunction findConflictsWithinSelectionSet(context, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, parentType, selectionSet) {\n  const conflicts = [];\n  const [fieldMap, fragmentNames] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType, selectionSet); // (A) Find find all conflicts \"within\" the fields of this selection set.\n  // Note: this is the *only place* `collectConflictsWithin` is called.\n\n  collectConflictsWithin(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, fieldMap);\n  if (fragmentNames.length !== 0) {\n    // (B) Then collect conflicts between these fields and those represented by\n    // each spread fragment name found.\n    for (let i = 0; i < fragmentNames.length; i++) {\n      collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, false, fieldMap, fragmentNames[i]); // (C) Then compare this fragment with all other fragments found in this\n      // selection set to collect conflicts between fragments spread together.\n      // This compares each item in the list of fragment names to every other\n      // item in that same list (except for itself).\n\n      for (let j = i + 1; j < fragmentNames.length; j++) {\n        collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, false, fragmentNames[i], fragmentNames[j]);\n      }\n    }\n  }\n  return conflicts;\n} // Collect all conflicts found between a set of fields and a fragment reference\n// including via spreading in any nested fragments.\n\nfunction collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fieldMap, fragmentName) {\n  // Memoize so the fields and fragments are not compared for conflicts more\n  // than once.\n  if (comparedFieldsAndFragmentPairs.has(fieldMap, fragmentName, areMutuallyExclusive)) {\n    return;\n  }\n  comparedFieldsAndFragmentPairs.add(fieldMap, fragmentName, areMutuallyExclusive);\n  const fragment = context.getFragment(fragmentName);\n  if (!fragment) {\n    return;\n  }\n  const [fieldMap2, referencedFragmentNames] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment); // Do not compare a fragment's fieldMap to itself.\n\n  if (fieldMap === fieldMap2) {\n    return;\n  } // (D) First collect any conflicts between the provided collection of fields\n  // and the collection of fields represented by the given fragment.\n\n  collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fieldMap, fieldMap2); // (E) Then collect any conflicts between the provided collection of fields\n  // and any fragment names found in the given fragment.\n\n  for (const referencedFragmentName of referencedFragmentNames) {\n    collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fieldMap, referencedFragmentName);\n  }\n} // Collect all conflicts found between two fragments, including via spreading in\n// any nested fragments.\n\nfunction collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, fragmentName2) {\n  // No need to compare a fragment to itself.\n  if (fragmentName1 === fragmentName2) {\n    return;\n  } // Memoize so two fragments are not compared for conflicts more than once.\n\n  if (comparedFragmentPairs.has(fragmentName1, fragmentName2, areMutuallyExclusive)) {\n    return;\n  }\n  comparedFragmentPairs.add(fragmentName1, fragmentName2, areMutuallyExclusive);\n  const fragment1 = context.getFragment(fragmentName1);\n  const fragment2 = context.getFragment(fragmentName2);\n  if (!fragment1 || !fragment2) {\n    return;\n  }\n  const [fieldMap1, referencedFragmentNames1] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment1);\n  const [fieldMap2, referencedFragmentNames2] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment2); // (F) First, collect all conflicts between these two collections of fields\n  // (not including any nested fragments).\n\n  collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fieldMap2); // (G) Then collect conflicts between the first fragment and any nested\n  // fragments spread in the second fragment.\n\n  for (const referencedFragmentName2 of referencedFragmentNames2) {\n    collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, referencedFragmentName2);\n  } // (G) Then collect conflicts between the second fragment and any nested\n  // fragments spread in the first fragment.\n\n  for (const referencedFragmentName1 of referencedFragmentNames1) {\n    collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, referencedFragmentName1, fragmentName2);\n  }\n} // Find all conflicts found between two selection sets, including those found\n// via spreading in fragments. Called when determining if conflicts exist\n// between the sub-fields of two overlapping fields.\n\nfunction findConflictsBetweenSubSelectionSets(context, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, parentType1, selectionSet1, parentType2, selectionSet2) {\n  const conflicts = [];\n  const [fieldMap1, fragmentNames1] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType1, selectionSet1);\n  const [fieldMap2, fragmentNames2] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType2, selectionSet2); // (H) First, collect all conflicts between these two collections of field.\n\n  collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fieldMap2); // (I) Then collect conflicts between the first collection of fields and\n  // those referenced by each fragment name associated with the second.\n\n  for (const fragmentName2 of fragmentNames2) {\n    collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fragmentName2);\n  } // (I) Then collect conflicts between the second collection of fields and\n  // those referenced by each fragment name associated with the first.\n\n  for (const fragmentName1 of fragmentNames1) {\n    collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fieldMap2, fragmentName1);\n  } // (J) Also collect conflicts between any fragment names by the first and\n  // fragment names by the second. This compares each item in the first set of\n  // names to each item in the second set of names.\n\n  for (const fragmentName1 of fragmentNames1) {\n    for (const fragmentName2 of fragmentNames2) {\n      collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, fragmentName2);\n    }\n  }\n  return conflicts;\n} // Collect all Conflicts \"within\" one collection of fields.\n\nfunction collectConflictsWithin(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, fieldMap) {\n  // A field map is a keyed collection, where each key represents a response\n  // name and the value at that key is a list of all fields which provide that\n  // response name. For every response name, if there are multiple fields, they\n  // must be compared to find a potential conflict.\n  for (const [responseName, fields] of Object.entries(fieldMap)) {\n    // This compares every field in the list to every other field in this list\n    // (except to itself). If the list only has one item, nothing needs to\n    // be compared.\n    if (fields.length > 1) {\n      for (let i = 0; i < fields.length; i++) {\n        for (let j = i + 1; j < fields.length; j++) {\n          const conflict = findConflict(context, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, false,\n          // within one collection is never mutually exclusive\n          responseName, fields[i], fields[j]);\n          if (conflict) {\n            conflicts.push(conflict);\n          }\n        }\n      }\n    }\n  }\n} // Collect all Conflicts between two collections of fields. This is similar to,\n// but different from the `collectConflictsWithin` function above. This check\n// assumes that `collectConflictsWithin` has already been called on each\n// provided collection of fields. This is true because this validator traverses\n// each individual selection set.\n\nfunction collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, fieldMap1, fieldMap2) {\n  // A field map is a keyed collection, where each key represents a response\n  // name and the value at that key is a list of all fields which provide that\n  // response name. For any response name which appears in both provided field\n  // maps, each field from the first field map must be compared to every field\n  // in the second field map to find potential conflicts.\n  for (const [responseName, fields1] of Object.entries(fieldMap1)) {\n    const fields2 = fieldMap2[responseName];\n    if (fields2) {\n      for (const field1 of fields1) {\n        for (const field2 of fields2) {\n          const conflict = findConflict(context, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, responseName, field1, field2);\n          if (conflict) {\n            conflicts.push(conflict);\n          }\n        }\n      }\n    }\n  }\n} // Determines if there is a conflict between two particular fields, including\n// comparing their sub-fields.\n\nfunction findConflict(context, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, responseName, field1, field2) {\n  const [parentType1, node1, def1] = field1;\n  const [parentType2, node2, def2] = field2; // If it is known that two fields could not possibly apply at the same\n  // time, due to the parent types, then it is safe to permit them to diverge\n  // in aliased field or arguments used as they will not present any ambiguity\n  // by differing.\n  // It is known that two parent types could never overlap if they are\n  // different Object types. Interface or Union types might overlap - if not\n  // in the current state of the schema, then perhaps in some future version,\n  // thus may not safely diverge.\n\n  const areMutuallyExclusive = parentFieldsAreMutuallyExclusive || parentType1 !== parentType2 && isObjectType(parentType1) && isObjectType(parentType2);\n  if (!areMutuallyExclusive) {\n    // Two aliases must refer to the same field.\n    const name1 = node1.name.value;\n    const name2 = node2.name.value;\n    if (name1 !== name2) {\n      return [[responseName, `\"${name1}\" and \"${name2}\" are different fields`], [node1], [node2]];\n    } // Two field calls must have the same arguments.\n\n    if (!sameArguments(node1, node2)) {\n      return [[responseName, 'they have differing arguments'], [node1], [node2]];\n    }\n  } // The return type for each field.\n\n  const type1 = def1 === null || def1 === void 0 ? void 0 : def1.type;\n  const type2 = def2 === null || def2 === void 0 ? void 0 : def2.type;\n  if (type1 && type2 && doTypesConflict(type1, type2)) {\n    return [[responseName, `they return conflicting types \"${inspect(type1)}\" and \"${inspect(type2)}\"`], [node1], [node2]];\n  } // Collect and compare sub-fields. Use the same \"visited fragment names\" list\n  // for both collections so fields in a fragment reference are never\n  // compared to themselves.\n\n  const selectionSet1 = node1.selectionSet;\n  const selectionSet2 = node2.selectionSet;\n  if (selectionSet1 && selectionSet2) {\n    const conflicts = findConflictsBetweenSubSelectionSets(context, cachedFieldsAndFragmentNames, comparedFieldsAndFragmentPairs, comparedFragmentPairs, areMutuallyExclusive, getNamedType(type1), selectionSet1, getNamedType(type2), selectionSet2);\n    return subfieldConflicts(conflicts, responseName, node1, node2);\n  }\n}\nfunction sameArguments(node1, node2) {\n  const args1 = node1.arguments;\n  const args2 = node2.arguments;\n  if (args1 === undefined || args1.length === 0) {\n    return args2 === undefined || args2.length === 0;\n  }\n  if (args2 === undefined || args2.length === 0) {\n    return false;\n  }\n  /* c8 ignore next */\n\n  if (args1.length !== args2.length) {\n    /* c8 ignore next */\n    return false;\n    /* c8 ignore next */\n  }\n  const values2 = new Map(args2.map(({\n    name,\n    value\n  }) => [name.value, value]));\n  return args1.every(arg1 => {\n    const value1 = arg1.value;\n    const value2 = values2.get(arg1.name.value);\n    if (value2 === undefined) {\n      return false;\n    }\n    return stringifyValue(value1) === stringifyValue(value2);\n  });\n}\nfunction stringifyValue(value) {\n  return print(sortValueNode(value));\n} // Two types conflict if both types could not apply to a value simultaneously.\n// Composite types are ignored as their individual field types will be compared\n// later recursively. However List and Non-Null types must match.\n\nfunction doTypesConflict(type1, type2) {\n  if (isListType(type1)) {\n    return isListType(type2) ? doTypesConflict(type1.ofType, type2.ofType) : true;\n  }\n  if (isListType(type2)) {\n    return true;\n  }\n  if (isNonNullType(type1)) {\n    return isNonNullType(type2) ? doTypesConflict(type1.ofType, type2.ofType) : true;\n  }\n  if (isNonNullType(type2)) {\n    return true;\n  }\n  if (isLeafType(type1) || isLeafType(type2)) {\n    return type1 !== type2;\n  }\n  return false;\n} // Given a selection set, return the collection of fields (a mapping of response\n// name to field nodes and definitions) as well as a list of fragment names\n// referenced via fragment spreads.\n\nfunction getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType, selectionSet) {\n  const cached = cachedFieldsAndFragmentNames.get(selectionSet);\n  if (cached) {\n    return cached;\n  }\n  const nodeAndDefs = Object.create(null);\n  const fragmentNames = Object.create(null);\n  _collectFieldsAndFragmentNames(context, parentType, selectionSet, nodeAndDefs, fragmentNames);\n  const result = [nodeAndDefs, Object.keys(fragmentNames)];\n  cachedFieldsAndFragmentNames.set(selectionSet, result);\n  return result;\n} // Given a reference to a fragment, return the represented collection of fields\n// as well as a list of nested fragment names referenced via fragment spreads.\n\nfunction getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment) {\n  // Short-circuit building a type from the node if possible.\n  const cached = cachedFieldsAndFragmentNames.get(fragment.selectionSet);\n  if (cached) {\n    return cached;\n  }\n  const fragmentType = typeFromAST(context.getSchema(), fragment.typeCondition);\n  return getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragmentType, fragment.selectionSet);\n}\nfunction _collectFieldsAndFragmentNames(context, parentType, selectionSet, nodeAndDefs, fragmentNames) {\n  for (const selection of selectionSet.selections) {\n    switch (selection.kind) {\n      case Kind.FIELD:\n        {\n          const fieldName = selection.name.value;\n          let fieldDef;\n          if (isObjectType(parentType) || isInterfaceType(parentType)) {\n            fieldDef = parentType.getFields()[fieldName];\n          }\n          const responseName = selection.alias ? selection.alias.value : fieldName;\n          if (!nodeAndDefs[responseName]) {\n            nodeAndDefs[responseName] = [];\n          }\n          nodeAndDefs[responseName].push([parentType, selection, fieldDef]);\n          break;\n        }\n      case Kind.FRAGMENT_SPREAD:\n        fragmentNames[selection.name.value] = true;\n        break;\n      case Kind.INLINE_FRAGMENT:\n        {\n          const typeCondition = selection.typeCondition;\n          const inlineFragmentType = typeCondition ? typeFromAST(context.getSchema(), typeCondition) : parentType;\n          _collectFieldsAndFragmentNames(context, inlineFragmentType, selection.selectionSet, nodeAndDefs, fragmentNames);\n          break;\n        }\n    }\n  }\n} // Given a series of Conflicts which occurred between two sub-fields, generate\n// a single Conflict.\n\nfunction subfieldConflicts(conflicts, responseName, node1, node2) {\n  if (conflicts.length > 0) {\n    return [[responseName, conflicts.map(([reason]) => reason)], [node1, ...conflicts.map(([, fields1]) => fields1).flat()], [node2, ...conflicts.map(([,, fields2]) => fields2).flat()]];\n  }\n}\n/**\n * A way to keep track of pairs of things where the ordering of the pair\n * matters.\n *\n * Provides a third argument for has/set to allow flagging the pair as\n * weakly or strongly present within the collection.\n */\n\nclass OrderedPairSet {\n  constructor() {\n    this._data = new Map();\n  }\n  has(a, b, weaklyPresent) {\n    var _this$_data$get;\n    const result = (_this$_data$get = this._data.get(a)) === null || _this$_data$get === void 0 ? void 0 : _this$_data$get.get(b);\n    if (result === undefined) {\n      return false;\n    }\n    return weaklyPresent ? true : weaklyPresent === result;\n  }\n  add(a, b, weaklyPresent) {\n    const map = this._data.get(a);\n    if (map === undefined) {\n      this._data.set(a, new Map([[b, weaklyPresent]]));\n    } else {\n      map.set(b, weaklyPresent);\n    }\n  }\n}\n/**\n * A way to keep track of pairs of similar things when the ordering of the pair\n * does not matter.\n */\n\nclass PairSet {\n  constructor() {\n    this._orderedPairSet = new OrderedPairSet();\n  }\n  has(a, b, weaklyPresent) {\n    return a < b ? this._orderedPairSet.has(a, b, weaklyPresent) : this._orderedPairSet.has(b, a, weaklyPresent);\n  }\n  add(a, b, weaklyPresent) {\n    if (a < b) {\n      this._orderedPairSet.add(a, b, weaklyPresent);\n    } else {\n      this._orderedPairSet.add(b, a, weaklyPresent);\n    }\n  }\n}", "map": {"version": 3, "names": ["inspect", "GraphQLError", "Kind", "print", "getNamedType", "isInterfaceType", "isLeafType", "isListType", "isNonNullType", "isObjectType", "sortValueNode", "typeFromAST", "reasonMessage", "reason", "Array", "isArray", "map", "responseName", "subReason", "join", "OverlappingFieldsCanBeMergedRule", "context", "comparedFieldsAndFragmentPairs", "OrderedPairSet", "comparedFragmentPairs", "PairSet", "cachedFieldsAndFragmentNames", "Map", "SelectionSet", "selectionSet", "conflicts", "findConflictsWithinSelectionSet", "getParentType", "fields1", "fields2", "reasonMsg", "reportError", "nodes", "concat", "parentType", "fieldMap", "fragmentNames", "getFieldsAndFragmentNames", "collectConflictsWithin", "length", "i", "collectConflictsBetweenFieldsAndFragment", "j", "collectConflictsBetweenFragments", "areMutuallyExclusive", "fragmentName", "has", "add", "fragment", "getFragment", "fieldMap2", "referencedFragmentNames", "getReferencedFieldsAndFragmentNames", "collectConflictsBetween", "referencedFragmentName", "fragmentName1", "fragmentName2", "fragment1", "fragment2", "fieldMap1", "referencedFragmentNames1", "referencedFragmentNames2", "referencedFragmentName2", "referencedFragmentName1", "findConflictsBetweenSubSelectionSets", "parentType1", "selectionSet1", "parentType2", "selectionSet2", "fragmentNames1", "fragmentNames2", "fields", "Object", "entries", "conflict", "findConflict", "push", "parentFieldsAreMutuallyExclusive", "field1", "field2", "node1", "def1", "node2", "def2", "name1", "name", "value", "name2", "sameArguments", "type1", "type", "type2", "doTypesConflict", "subfieldConflicts", "args1", "arguments", "args2", "undefined", "values2", "every", "arg1", "value1", "value2", "get", "stringifyValue", "ofType", "cached", "nodeAndDefs", "create", "_collectFieldsAndFragmentNames", "result", "keys", "set", "fragmentType", "getSchema", "typeCondition", "selection", "selections", "kind", "FIELD", "fieldName", "fieldDef", "getFields", "alias", "FRAGMENT_SPREAD", "INLINE_FRAGMENT", "inlineFragmentType", "flat", "constructor", "_data", "a", "b", "weaklyPresent", "_this$_data$get", "_orderedPairSet"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/OverlappingFieldsCanBeMergedRule.mjs"], "sourcesContent": ["import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { print } from '../../language/printer.mjs';\nimport {\n  getNamedType,\n  isInterfaceType,\n  isLeafType,\n  isListType,\n  isNonNullType,\n  isObjectType,\n} from '../../type/definition.mjs';\nimport { sortValueNode } from '../../utilities/sortValueNode.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\nfunction reasonMessage(reason) {\n  if (Array.isArray(reason)) {\n    return reason\n      .map(\n        ([responseName, subReason]) =>\n          `subfields \"${responseName}\" conflict because ` +\n          reasonMessage(subReason),\n      )\n      .join(' and ');\n  }\n\n  return reason;\n}\n/**\n * Overlapping fields can be merged\n *\n * A selection set is only valid if all fields (including spreading any\n * fragments) either correspond to distinct response names or can be merged\n * without ambiguity.\n *\n * See https://spec.graphql.org/draft/#sec-Field-Selection-Merging\n */\n\nexport function OverlappingFieldsCanBeMergedRule(context) {\n  // A memoization for when fields and a fragment or two fragments are compared\n  // \"between\" each other for conflicts. Comparisons made be made many times,\n  // so memoizing this can dramatically improve the performance of this validator.\n  const comparedFieldsAndFragmentPairs = new OrderedPairSet();\n  const comparedFragmentPairs = new PairSet(); // A cache for the \"field map\" and list of fragment names found in any given\n  // selection set. Selection sets may be asked for this information multiple\n  // times, so this improves the performance of this validator.\n\n  const cachedFieldsAndFragmentNames = new Map();\n  return {\n    SelectionSet(selectionSet) {\n      const conflicts = findConflictsWithinSelectionSet(\n        context,\n        cachedFieldsAndFragmentNames,\n        comparedFieldsAndFragmentPairs,\n        comparedFragmentPairs,\n        context.getParentType(),\n        selectionSet,\n      );\n\n      for (const [[responseName, reason], fields1, fields2] of conflicts) {\n        const reasonMsg = reasonMessage(reason);\n        context.reportError(\n          new GraphQLError(\n            `Fields \"${responseName}\" conflict because ${reasonMsg}. Use different aliases on the fields to fetch both if this was intentional.`,\n            {\n              nodes: fields1.concat(fields2),\n            },\n          ),\n        );\n      }\n    },\n  };\n}\n\n/**\n * Algorithm:\n *\n * Conflicts occur when two fields exist in a query which will produce the same\n * response name, but represent differing values, thus creating a conflict.\n * The algorithm below finds all conflicts via making a series of comparisons\n * between fields. In order to compare as few fields as possible, this makes\n * a series of comparisons \"within\" sets of fields and \"between\" sets of fields.\n *\n * Given any selection set, a collection produces both a set of fields by\n * also including all inline fragments, as well as a list of fragments\n * referenced by fragment spreads.\n *\n * A) Each selection set represented in the document first compares \"within\" its\n * collected set of fields, finding any conflicts between every pair of\n * overlapping fields.\n * Note: This is the *only time* that a the fields \"within\" a set are compared\n * to each other. After this only fields \"between\" sets are compared.\n *\n * B) Also, if any fragment is referenced in a selection set, then a\n * comparison is made \"between\" the original set of fields and the\n * referenced fragment.\n *\n * C) Also, if multiple fragments are referenced, then comparisons\n * are made \"between\" each referenced fragment.\n *\n * D) When comparing \"between\" a set of fields and a referenced fragment, first\n * a comparison is made between each field in the original set of fields and\n * each field in the the referenced set of fields.\n *\n * E) Also, if any fragment is referenced in the referenced selection set,\n * then a comparison is made \"between\" the original set of fields and the\n * referenced fragment (recursively referring to step D).\n *\n * F) When comparing \"between\" two fragments, first a comparison is made between\n * each field in the first referenced set of fields and each field in the the\n * second referenced set of fields.\n *\n * G) Also, any fragments referenced by the first must be compared to the\n * second, and any fragments referenced by the second must be compared to the\n * first (recursively referring to step F).\n *\n * H) When comparing two fields, if both have selection sets, then a comparison\n * is made \"between\" both selection sets, first comparing the set of fields in\n * the first selection set with the set of fields in the second.\n *\n * I) Also, if any fragment is referenced in either selection set, then a\n * comparison is made \"between\" the other set of fields and the\n * referenced fragment.\n *\n * J) Also, if two fragments are referenced in both selection sets, then a\n * comparison is made \"between\" the two fragments.\n *\n */\n// Find all conflicts found \"within\" a selection set, including those found\n// via spreading in fragments. Called when visiting each SelectionSet in the\n// GraphQL Document.\nfunction findConflictsWithinSelectionSet(\n  context,\n  cachedFieldsAndFragmentNames,\n  comparedFieldsAndFragmentPairs,\n  comparedFragmentPairs,\n  parentType,\n  selectionSet,\n) {\n  const conflicts = [];\n  const [fieldMap, fragmentNames] = getFieldsAndFragmentNames(\n    context,\n    cachedFieldsAndFragmentNames,\n    parentType,\n    selectionSet,\n  ); // (A) Find find all conflicts \"within\" the fields of this selection set.\n  // Note: this is the *only place* `collectConflictsWithin` is called.\n\n  collectConflictsWithin(\n    context,\n    conflicts,\n    cachedFieldsAndFragmentNames,\n    comparedFieldsAndFragmentPairs,\n    comparedFragmentPairs,\n    fieldMap,\n  );\n\n  if (fragmentNames.length !== 0) {\n    // (B) Then collect conflicts between these fields and those represented by\n    // each spread fragment name found.\n    for (let i = 0; i < fragmentNames.length; i++) {\n      collectConflictsBetweenFieldsAndFragment(\n        context,\n        conflicts,\n        cachedFieldsAndFragmentNames,\n        comparedFieldsAndFragmentPairs,\n        comparedFragmentPairs,\n        false,\n        fieldMap,\n        fragmentNames[i],\n      ); // (C) Then compare this fragment with all other fragments found in this\n      // selection set to collect conflicts between fragments spread together.\n      // This compares each item in the list of fragment names to every other\n      // item in that same list (except for itself).\n\n      for (let j = i + 1; j < fragmentNames.length; j++) {\n        collectConflictsBetweenFragments(\n          context,\n          conflicts,\n          cachedFieldsAndFragmentNames,\n          comparedFieldsAndFragmentPairs,\n          comparedFragmentPairs,\n          false,\n          fragmentNames[i],\n          fragmentNames[j],\n        );\n      }\n    }\n  }\n\n  return conflicts;\n} // Collect all conflicts found between a set of fields and a fragment reference\n// including via spreading in any nested fragments.\n\nfunction collectConflictsBetweenFieldsAndFragment(\n  context,\n  conflicts,\n  cachedFieldsAndFragmentNames,\n  comparedFieldsAndFragmentPairs,\n  comparedFragmentPairs,\n  areMutuallyExclusive,\n  fieldMap,\n  fragmentName,\n) {\n  // Memoize so the fields and fragments are not compared for conflicts more\n  // than once.\n  if (\n    comparedFieldsAndFragmentPairs.has(\n      fieldMap,\n      fragmentName,\n      areMutuallyExclusive,\n    )\n  ) {\n    return;\n  }\n\n  comparedFieldsAndFragmentPairs.add(\n    fieldMap,\n    fragmentName,\n    areMutuallyExclusive,\n  );\n  const fragment = context.getFragment(fragmentName);\n\n  if (!fragment) {\n    return;\n  }\n\n  const [fieldMap2, referencedFragmentNames] =\n    getReferencedFieldsAndFragmentNames(\n      context,\n      cachedFieldsAndFragmentNames,\n      fragment,\n    ); // Do not compare a fragment's fieldMap to itself.\n\n  if (fieldMap === fieldMap2) {\n    return;\n  } // (D) First collect any conflicts between the provided collection of fields\n  // and the collection of fields represented by the given fragment.\n\n  collectConflictsBetween(\n    context,\n    conflicts,\n    cachedFieldsAndFragmentNames,\n    comparedFieldsAndFragmentPairs,\n    comparedFragmentPairs,\n    areMutuallyExclusive,\n    fieldMap,\n    fieldMap2,\n  ); // (E) Then collect any conflicts between the provided collection of fields\n  // and any fragment names found in the given fragment.\n\n  for (const referencedFragmentName of referencedFragmentNames) {\n    collectConflictsBetweenFieldsAndFragment(\n      context,\n      conflicts,\n      cachedFieldsAndFragmentNames,\n      comparedFieldsAndFragmentPairs,\n      comparedFragmentPairs,\n      areMutuallyExclusive,\n      fieldMap,\n      referencedFragmentName,\n    );\n  }\n} // Collect all conflicts found between two fragments, including via spreading in\n// any nested fragments.\n\nfunction collectConflictsBetweenFragments(\n  context,\n  conflicts,\n  cachedFieldsAndFragmentNames,\n  comparedFieldsAndFragmentPairs,\n  comparedFragmentPairs,\n  areMutuallyExclusive,\n  fragmentName1,\n  fragmentName2,\n) {\n  // No need to compare a fragment to itself.\n  if (fragmentName1 === fragmentName2) {\n    return;\n  } // Memoize so two fragments are not compared for conflicts more than once.\n\n  if (\n    comparedFragmentPairs.has(\n      fragmentName1,\n      fragmentName2,\n      areMutuallyExclusive,\n    )\n  ) {\n    return;\n  }\n\n  comparedFragmentPairs.add(fragmentName1, fragmentName2, areMutuallyExclusive);\n  const fragment1 = context.getFragment(fragmentName1);\n  const fragment2 = context.getFragment(fragmentName2);\n\n  if (!fragment1 || !fragment2) {\n    return;\n  }\n\n  const [fieldMap1, referencedFragmentNames1] =\n    getReferencedFieldsAndFragmentNames(\n      context,\n      cachedFieldsAndFragmentNames,\n      fragment1,\n    );\n  const [fieldMap2, referencedFragmentNames2] =\n    getReferencedFieldsAndFragmentNames(\n      context,\n      cachedFieldsAndFragmentNames,\n      fragment2,\n    ); // (F) First, collect all conflicts between these two collections of fields\n  // (not including any nested fragments).\n\n  collectConflictsBetween(\n    context,\n    conflicts,\n    cachedFieldsAndFragmentNames,\n    comparedFieldsAndFragmentPairs,\n    comparedFragmentPairs,\n    areMutuallyExclusive,\n    fieldMap1,\n    fieldMap2,\n  ); // (G) Then collect conflicts between the first fragment and any nested\n  // fragments spread in the second fragment.\n\n  for (const referencedFragmentName2 of referencedFragmentNames2) {\n    collectConflictsBetweenFragments(\n      context,\n      conflicts,\n      cachedFieldsAndFragmentNames,\n      comparedFieldsAndFragmentPairs,\n      comparedFragmentPairs,\n      areMutuallyExclusive,\n      fragmentName1,\n      referencedFragmentName2,\n    );\n  } // (G) Then collect conflicts between the second fragment and any nested\n  // fragments spread in the first fragment.\n\n  for (const referencedFragmentName1 of referencedFragmentNames1) {\n    collectConflictsBetweenFragments(\n      context,\n      conflicts,\n      cachedFieldsAndFragmentNames,\n      comparedFieldsAndFragmentPairs,\n      comparedFragmentPairs,\n      areMutuallyExclusive,\n      referencedFragmentName1,\n      fragmentName2,\n    );\n  }\n} // Find all conflicts found between two selection sets, including those found\n// via spreading in fragments. Called when determining if conflicts exist\n// between the sub-fields of two overlapping fields.\n\nfunction findConflictsBetweenSubSelectionSets(\n  context,\n  cachedFieldsAndFragmentNames,\n  comparedFieldsAndFragmentPairs,\n  comparedFragmentPairs,\n  areMutuallyExclusive,\n  parentType1,\n  selectionSet1,\n  parentType2,\n  selectionSet2,\n) {\n  const conflicts = [];\n  const [fieldMap1, fragmentNames1] = getFieldsAndFragmentNames(\n    context,\n    cachedFieldsAndFragmentNames,\n    parentType1,\n    selectionSet1,\n  );\n  const [fieldMap2, fragmentNames2] = getFieldsAndFragmentNames(\n    context,\n    cachedFieldsAndFragmentNames,\n    parentType2,\n    selectionSet2,\n  ); // (H) First, collect all conflicts between these two collections of field.\n\n  collectConflictsBetween(\n    context,\n    conflicts,\n    cachedFieldsAndFragmentNames,\n    comparedFieldsAndFragmentPairs,\n    comparedFragmentPairs,\n    areMutuallyExclusive,\n    fieldMap1,\n    fieldMap2,\n  ); // (I) Then collect conflicts between the first collection of fields and\n  // those referenced by each fragment name associated with the second.\n\n  for (const fragmentName2 of fragmentNames2) {\n    collectConflictsBetweenFieldsAndFragment(\n      context,\n      conflicts,\n      cachedFieldsAndFragmentNames,\n      comparedFieldsAndFragmentPairs,\n      comparedFragmentPairs,\n      areMutuallyExclusive,\n      fieldMap1,\n      fragmentName2,\n    );\n  } // (I) Then collect conflicts between the second collection of fields and\n  // those referenced by each fragment name associated with the first.\n\n  for (const fragmentName1 of fragmentNames1) {\n    collectConflictsBetweenFieldsAndFragment(\n      context,\n      conflicts,\n      cachedFieldsAndFragmentNames,\n      comparedFieldsAndFragmentPairs,\n      comparedFragmentPairs,\n      areMutuallyExclusive,\n      fieldMap2,\n      fragmentName1,\n    );\n  } // (J) Also collect conflicts between any fragment names by the first and\n  // fragment names by the second. This compares each item in the first set of\n  // names to each item in the second set of names.\n\n  for (const fragmentName1 of fragmentNames1) {\n    for (const fragmentName2 of fragmentNames2) {\n      collectConflictsBetweenFragments(\n        context,\n        conflicts,\n        cachedFieldsAndFragmentNames,\n        comparedFieldsAndFragmentPairs,\n        comparedFragmentPairs,\n        areMutuallyExclusive,\n        fragmentName1,\n        fragmentName2,\n      );\n    }\n  }\n\n  return conflicts;\n} // Collect all Conflicts \"within\" one collection of fields.\n\nfunction collectConflictsWithin(\n  context,\n  conflicts,\n  cachedFieldsAndFragmentNames,\n  comparedFieldsAndFragmentPairs,\n  comparedFragmentPairs,\n  fieldMap,\n) {\n  // A field map is a keyed collection, where each key represents a response\n  // name and the value at that key is a list of all fields which provide that\n  // response name. For every response name, if there are multiple fields, they\n  // must be compared to find a potential conflict.\n  for (const [responseName, fields] of Object.entries(fieldMap)) {\n    // This compares every field in the list to every other field in this list\n    // (except to itself). If the list only has one item, nothing needs to\n    // be compared.\n    if (fields.length > 1) {\n      for (let i = 0; i < fields.length; i++) {\n        for (let j = i + 1; j < fields.length; j++) {\n          const conflict = findConflict(\n            context,\n            cachedFieldsAndFragmentNames,\n            comparedFieldsAndFragmentPairs,\n            comparedFragmentPairs,\n            false, // within one collection is never mutually exclusive\n            responseName,\n            fields[i],\n            fields[j],\n          );\n\n          if (conflict) {\n            conflicts.push(conflict);\n          }\n        }\n      }\n    }\n  }\n} // Collect all Conflicts between two collections of fields. This is similar to,\n// but different from the `collectConflictsWithin` function above. This check\n// assumes that `collectConflictsWithin` has already been called on each\n// provided collection of fields. This is true because this validator traverses\n// each individual selection set.\n\nfunction collectConflictsBetween(\n  context,\n  conflicts,\n  cachedFieldsAndFragmentNames,\n  comparedFieldsAndFragmentPairs,\n  comparedFragmentPairs,\n  parentFieldsAreMutuallyExclusive,\n  fieldMap1,\n  fieldMap2,\n) {\n  // A field map is a keyed collection, where each key represents a response\n  // name and the value at that key is a list of all fields which provide that\n  // response name. For any response name which appears in both provided field\n  // maps, each field from the first field map must be compared to every field\n  // in the second field map to find potential conflicts.\n  for (const [responseName, fields1] of Object.entries(fieldMap1)) {\n    const fields2 = fieldMap2[responseName];\n\n    if (fields2) {\n      for (const field1 of fields1) {\n        for (const field2 of fields2) {\n          const conflict = findConflict(\n            context,\n            cachedFieldsAndFragmentNames,\n            comparedFieldsAndFragmentPairs,\n            comparedFragmentPairs,\n            parentFieldsAreMutuallyExclusive,\n            responseName,\n            field1,\n            field2,\n          );\n\n          if (conflict) {\n            conflicts.push(conflict);\n          }\n        }\n      }\n    }\n  }\n} // Determines if there is a conflict between two particular fields, including\n// comparing their sub-fields.\n\nfunction findConflict(\n  context,\n  cachedFieldsAndFragmentNames,\n  comparedFieldsAndFragmentPairs,\n  comparedFragmentPairs,\n  parentFieldsAreMutuallyExclusive,\n  responseName,\n  field1,\n  field2,\n) {\n  const [parentType1, node1, def1] = field1;\n  const [parentType2, node2, def2] = field2; // If it is known that two fields could not possibly apply at the same\n  // time, due to the parent types, then it is safe to permit them to diverge\n  // in aliased field or arguments used as they will not present any ambiguity\n  // by differing.\n  // It is known that two parent types could never overlap if they are\n  // different Object types. Interface or Union types might overlap - if not\n  // in the current state of the schema, then perhaps in some future version,\n  // thus may not safely diverge.\n\n  const areMutuallyExclusive =\n    parentFieldsAreMutuallyExclusive ||\n    (parentType1 !== parentType2 &&\n      isObjectType(parentType1) &&\n      isObjectType(parentType2));\n\n  if (!areMutuallyExclusive) {\n    // Two aliases must refer to the same field.\n    const name1 = node1.name.value;\n    const name2 = node2.name.value;\n\n    if (name1 !== name2) {\n      return [\n        [responseName, `\"${name1}\" and \"${name2}\" are different fields`],\n        [node1],\n        [node2],\n      ];\n    } // Two field calls must have the same arguments.\n\n    if (!sameArguments(node1, node2)) {\n      return [\n        [responseName, 'they have differing arguments'],\n        [node1],\n        [node2],\n      ];\n    }\n  } // The return type for each field.\n\n  const type1 = def1 === null || def1 === void 0 ? void 0 : def1.type;\n  const type2 = def2 === null || def2 === void 0 ? void 0 : def2.type;\n\n  if (type1 && type2 && doTypesConflict(type1, type2)) {\n    return [\n      [\n        responseName,\n        `they return conflicting types \"${inspect(type1)}\" and \"${inspect(\n          type2,\n        )}\"`,\n      ],\n      [node1],\n      [node2],\n    ];\n  } // Collect and compare sub-fields. Use the same \"visited fragment names\" list\n  // for both collections so fields in a fragment reference are never\n  // compared to themselves.\n\n  const selectionSet1 = node1.selectionSet;\n  const selectionSet2 = node2.selectionSet;\n\n  if (selectionSet1 && selectionSet2) {\n    const conflicts = findConflictsBetweenSubSelectionSets(\n      context,\n      cachedFieldsAndFragmentNames,\n      comparedFieldsAndFragmentPairs,\n      comparedFragmentPairs,\n      areMutuallyExclusive,\n      getNamedType(type1),\n      selectionSet1,\n      getNamedType(type2),\n      selectionSet2,\n    );\n    return subfieldConflicts(conflicts, responseName, node1, node2);\n  }\n}\n\nfunction sameArguments(node1, node2) {\n  const args1 = node1.arguments;\n  const args2 = node2.arguments;\n\n  if (args1 === undefined || args1.length === 0) {\n    return args2 === undefined || args2.length === 0;\n  }\n\n  if (args2 === undefined || args2.length === 0) {\n    return false;\n  }\n  /* c8 ignore next */\n\n  if (args1.length !== args2.length) {\n    /* c8 ignore next */\n    return false;\n    /* c8 ignore next */\n  }\n\n  const values2 = new Map(args2.map(({ name, value }) => [name.value, value]));\n  return args1.every((arg1) => {\n    const value1 = arg1.value;\n    const value2 = values2.get(arg1.name.value);\n\n    if (value2 === undefined) {\n      return false;\n    }\n\n    return stringifyValue(value1) === stringifyValue(value2);\n  });\n}\n\nfunction stringifyValue(value) {\n  return print(sortValueNode(value));\n} // Two types conflict if both types could not apply to a value simultaneously.\n// Composite types are ignored as their individual field types will be compared\n// later recursively. However List and Non-Null types must match.\n\nfunction doTypesConflict(type1, type2) {\n  if (isListType(type1)) {\n    return isListType(type2)\n      ? doTypesConflict(type1.ofType, type2.ofType)\n      : true;\n  }\n\n  if (isListType(type2)) {\n    return true;\n  }\n\n  if (isNonNullType(type1)) {\n    return isNonNullType(type2)\n      ? doTypesConflict(type1.ofType, type2.ofType)\n      : true;\n  }\n\n  if (isNonNullType(type2)) {\n    return true;\n  }\n\n  if (isLeafType(type1) || isLeafType(type2)) {\n    return type1 !== type2;\n  }\n\n  return false;\n} // Given a selection set, return the collection of fields (a mapping of response\n// name to field nodes and definitions) as well as a list of fragment names\n// referenced via fragment spreads.\n\nfunction getFieldsAndFragmentNames(\n  context,\n  cachedFieldsAndFragmentNames,\n  parentType,\n  selectionSet,\n) {\n  const cached = cachedFieldsAndFragmentNames.get(selectionSet);\n\n  if (cached) {\n    return cached;\n  }\n\n  const nodeAndDefs = Object.create(null);\n  const fragmentNames = Object.create(null);\n\n  _collectFieldsAndFragmentNames(\n    context,\n    parentType,\n    selectionSet,\n    nodeAndDefs,\n    fragmentNames,\n  );\n\n  const result = [nodeAndDefs, Object.keys(fragmentNames)];\n  cachedFieldsAndFragmentNames.set(selectionSet, result);\n  return result;\n} // Given a reference to a fragment, return the represented collection of fields\n// as well as a list of nested fragment names referenced via fragment spreads.\n\nfunction getReferencedFieldsAndFragmentNames(\n  context,\n  cachedFieldsAndFragmentNames,\n  fragment,\n) {\n  // Short-circuit building a type from the node if possible.\n  const cached = cachedFieldsAndFragmentNames.get(fragment.selectionSet);\n\n  if (cached) {\n    return cached;\n  }\n\n  const fragmentType = typeFromAST(context.getSchema(), fragment.typeCondition);\n  return getFieldsAndFragmentNames(\n    context,\n    cachedFieldsAndFragmentNames,\n    fragmentType,\n    fragment.selectionSet,\n  );\n}\n\nfunction _collectFieldsAndFragmentNames(\n  context,\n  parentType,\n  selectionSet,\n  nodeAndDefs,\n  fragmentNames,\n) {\n  for (const selection of selectionSet.selections) {\n    switch (selection.kind) {\n      case Kind.FIELD: {\n        const fieldName = selection.name.value;\n        let fieldDef;\n\n        if (isObjectType(parentType) || isInterfaceType(parentType)) {\n          fieldDef = parentType.getFields()[fieldName];\n        }\n\n        const responseName = selection.alias\n          ? selection.alias.value\n          : fieldName;\n\n        if (!nodeAndDefs[responseName]) {\n          nodeAndDefs[responseName] = [];\n        }\n\n        nodeAndDefs[responseName].push([parentType, selection, fieldDef]);\n        break;\n      }\n\n      case Kind.FRAGMENT_SPREAD:\n        fragmentNames[selection.name.value] = true;\n        break;\n\n      case Kind.INLINE_FRAGMENT: {\n        const typeCondition = selection.typeCondition;\n        const inlineFragmentType = typeCondition\n          ? typeFromAST(context.getSchema(), typeCondition)\n          : parentType;\n\n        _collectFieldsAndFragmentNames(\n          context,\n          inlineFragmentType,\n          selection.selectionSet,\n          nodeAndDefs,\n          fragmentNames,\n        );\n\n        break;\n      }\n    }\n  }\n} // Given a series of Conflicts which occurred between two sub-fields, generate\n// a single Conflict.\n\nfunction subfieldConflicts(conflicts, responseName, node1, node2) {\n  if (conflicts.length > 0) {\n    return [\n      [responseName, conflicts.map(([reason]) => reason)],\n      [node1, ...conflicts.map(([, fields1]) => fields1).flat()],\n      [node2, ...conflicts.map(([, , fields2]) => fields2).flat()],\n    ];\n  }\n}\n/**\n * A way to keep track of pairs of things where the ordering of the pair\n * matters.\n *\n * Provides a third argument for has/set to allow flagging the pair as\n * weakly or strongly present within the collection.\n */\n\nclass OrderedPairSet {\n  constructor() {\n    this._data = new Map();\n  }\n\n  has(a, b, weaklyPresent) {\n    var _this$_data$get;\n\n    const result =\n      (_this$_data$get = this._data.get(a)) === null ||\n      _this$_data$get === void 0\n        ? void 0\n        : _this$_data$get.get(b);\n\n    if (result === undefined) {\n      return false;\n    }\n\n    return weaklyPresent ? true : weaklyPresent === result;\n  }\n\n  add(a, b, weaklyPresent) {\n    const map = this._data.get(a);\n\n    if (map === undefined) {\n      this._data.set(a, new Map([[b, weaklyPresent]]));\n    } else {\n      map.set(b, weaklyPresent);\n    }\n  }\n}\n/**\n * A way to keep track of pairs of similar things when the ordering of the pair\n * does not matter.\n */\n\nclass PairSet {\n  constructor() {\n    this._orderedPairSet = new OrderedPairSet();\n  }\n\n  has(a, b, weaklyPresent) {\n    return a < b\n      ? this._orderedPairSet.has(a, b, weaklyPresent)\n      : this._orderedPairSet.has(b, a, weaklyPresent);\n  }\n\n  add(a, b, weaklyPresent) {\n    if (a < b) {\n      this._orderedPairSet.add(a, b, weaklyPresent);\n    } else {\n      this._orderedPairSet.add(b, a, weaklyPresent);\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,KAAK,QAAQ,4BAA4B;AAClD,SACEC,YAAY,EACZC,eAAe,EACfC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,2BAA2B;AAClC,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,WAAW,QAAQ,iCAAiC;AAE7D,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACzB,OAAOA,MAAM,CACVG,GAAG,CACF,CAAC,CAACC,YAAY,EAAEC,SAAS,CAAC,KACxB,cAAcD,YAAY,qBAAqB,GAC/CL,aAAa,CAACM,SAAS,CAC3B,CAAC,CACAC,IAAI,CAAC,OAAO,CAAC;EAClB;EAEA,OAAON,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASO,gCAAgCA,CAACC,OAAO,EAAE;EACxD;EACA;EACA;EACA,MAAMC,8BAA8B,GAAG,IAAIC,cAAc,CAAC,CAAC;EAC3D,MAAMC,qBAAqB,GAAG,IAAIC,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7C;EACA;;EAEA,MAAMC,4BAA4B,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9C,OAAO;IACLC,YAAYA,CAACC,YAAY,EAAE;MACzB,MAAMC,SAAS,GAAGC,+BAA+B,CAC/CV,OAAO,EACPK,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrBH,OAAO,CAACW,aAAa,CAAC,CAAC,EACvBH,YACF,CAAC;MAED,KAAK,MAAM,CAAC,CAACZ,YAAY,EAAEJ,MAAM,CAAC,EAAEoB,OAAO,EAAEC,OAAO,CAAC,IAAIJ,SAAS,EAAE;QAClE,MAAMK,SAAS,GAAGvB,aAAa,CAACC,MAAM,CAAC;QACvCQ,OAAO,CAACe,WAAW,CACjB,IAAInC,YAAY,CACd,WAAWgB,YAAY,sBAAsBkB,SAAS,8EAA8E,EACpI;UACEE,KAAK,EAAEJ,OAAO,CAACK,MAAM,CAACJ,OAAO;QAC/B,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,+BAA+BA,CACtCV,OAAO,EACPK,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrBe,UAAU,EACVV,YAAY,EACZ;EACA,MAAMC,SAAS,GAAG,EAAE;EACpB,MAAM,CAACU,QAAQ,EAAEC,aAAa,CAAC,GAAGC,yBAAyB,CACzDrB,OAAO,EACPK,4BAA4B,EAC5Ba,UAAU,EACVV,YACF,CAAC,CAAC,CAAC;EACH;;EAEAc,sBAAsB,CACpBtB,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrBgB,QACF,CAAC;EAED,IAAIC,aAAa,CAACG,MAAM,KAAK,CAAC,EAAE;IAC9B;IACA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,aAAa,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC7CC,wCAAwC,CACtCzB,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrB,KAAK,EACLgB,QAAQ,EACRC,aAAa,CAACI,CAAC,CACjB,CAAC,CAAC,CAAC;MACH;MACA;MACA;;MAEA,KAAK,IAAIE,CAAC,GAAGF,CAAC,GAAG,CAAC,EAAEE,CAAC,GAAGN,aAAa,CAACG,MAAM,EAAEG,CAAC,EAAE,EAAE;QACjDC,gCAAgC,CAC9B3B,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrB,KAAK,EACLiB,aAAa,CAACI,CAAC,CAAC,EAChBJ,aAAa,CAACM,CAAC,CACjB,CAAC;MACH;IACF;EACF;EAEA,OAAOjB,SAAS;AAClB,CAAC,CAAC;AACF;;AAEA,SAASgB,wCAAwCA,CAC/CzB,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBT,QAAQ,EACRU,YAAY,EACZ;EACA;EACA;EACA,IACE5B,8BAA8B,CAAC6B,GAAG,CAChCX,QAAQ,EACRU,YAAY,EACZD,oBACF,CAAC,EACD;IACA;EACF;EAEA3B,8BAA8B,CAAC8B,GAAG,CAChCZ,QAAQ,EACRU,YAAY,EACZD,oBACF,CAAC;EACD,MAAMI,QAAQ,GAAGhC,OAAO,CAACiC,WAAW,CAACJ,YAAY,CAAC;EAElD,IAAI,CAACG,QAAQ,EAAE;IACb;EACF;EAEA,MAAM,CAACE,SAAS,EAAEC,uBAAuB,CAAC,GACxCC,mCAAmC,CACjCpC,OAAO,EACPK,4BAA4B,EAC5B2B,QACF,CAAC,CAAC,CAAC;;EAEL,IAAIb,QAAQ,KAAKe,SAAS,EAAE;IAC1B;EACF,CAAC,CAAC;EACF;;EAEAG,uBAAuB,CACrBrC,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBT,QAAQ,EACRe,SACF,CAAC,CAAC,CAAC;EACH;;EAEA,KAAK,MAAMI,sBAAsB,IAAIH,uBAAuB,EAAE;IAC5DV,wCAAwC,CACtCzB,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBT,QAAQ,EACRmB,sBACF,CAAC;EACH;AACF,CAAC,CAAC;AACF;;AAEA,SAASX,gCAAgCA,CACvC3B,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBW,aAAa,EACbC,aAAa,EACb;EACA;EACA,IAAID,aAAa,KAAKC,aAAa,EAAE;IACnC;EACF,CAAC,CAAC;;EAEF,IACErC,qBAAqB,CAAC2B,GAAG,CACvBS,aAAa,EACbC,aAAa,EACbZ,oBACF,CAAC,EACD;IACA;EACF;EAEAzB,qBAAqB,CAAC4B,GAAG,CAACQ,aAAa,EAAEC,aAAa,EAAEZ,oBAAoB,CAAC;EAC7E,MAAMa,SAAS,GAAGzC,OAAO,CAACiC,WAAW,CAACM,aAAa,CAAC;EACpD,MAAMG,SAAS,GAAG1C,OAAO,CAACiC,WAAW,CAACO,aAAa,CAAC;EAEpD,IAAI,CAACC,SAAS,IAAI,CAACC,SAAS,EAAE;IAC5B;EACF;EAEA,MAAM,CAACC,SAAS,EAAEC,wBAAwB,CAAC,GACzCR,mCAAmC,CACjCpC,OAAO,EACPK,4BAA4B,EAC5BoC,SACF,CAAC;EACH,MAAM,CAACP,SAAS,EAAEW,wBAAwB,CAAC,GACzCT,mCAAmC,CACjCpC,OAAO,EACPK,4BAA4B,EAC5BqC,SACF,CAAC,CAAC,CAAC;EACL;;EAEAL,uBAAuB,CACrBrC,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBe,SAAS,EACTT,SACF,CAAC,CAAC,CAAC;EACH;;EAEA,KAAK,MAAMY,uBAAuB,IAAID,wBAAwB,EAAE;IAC9DlB,gCAAgC,CAC9B3B,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBW,aAAa,EACbO,uBACF,CAAC;EACH,CAAC,CAAC;EACF;;EAEA,KAAK,MAAMC,uBAAuB,IAAIH,wBAAwB,EAAE;IAC9DjB,gCAAgC,CAC9B3B,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBmB,uBAAuB,EACvBP,aACF,CAAC;EACH;AACF,CAAC,CAAC;AACF;AACA;;AAEA,SAASQ,oCAAoCA,CAC3ChD,OAAO,EACPK,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBqB,WAAW,EACXC,aAAa,EACbC,WAAW,EACXC,aAAa,EACb;EACA,MAAM3C,SAAS,GAAG,EAAE;EACpB,MAAM,CAACkC,SAAS,EAAEU,cAAc,CAAC,GAAGhC,yBAAyB,CAC3DrB,OAAO,EACPK,4BAA4B,EAC5B4C,WAAW,EACXC,aACF,CAAC;EACD,MAAM,CAAChB,SAAS,EAAEoB,cAAc,CAAC,GAAGjC,yBAAyB,CAC3DrB,OAAO,EACPK,4BAA4B,EAC5B8C,WAAW,EACXC,aACF,CAAC,CAAC,CAAC;;EAEHf,uBAAuB,CACrBrC,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBe,SAAS,EACTT,SACF,CAAC,CAAC,CAAC;EACH;;EAEA,KAAK,MAAMM,aAAa,IAAIc,cAAc,EAAE;IAC1C7B,wCAAwC,CACtCzB,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBe,SAAS,EACTH,aACF,CAAC;EACH,CAAC,CAAC;EACF;;EAEA,KAAK,MAAMD,aAAa,IAAIc,cAAc,EAAE;IAC1C5B,wCAAwC,CACtCzB,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBM,SAAS,EACTK,aACF,CAAC;EACH,CAAC,CAAC;EACF;EACA;;EAEA,KAAK,MAAMA,aAAa,IAAIc,cAAc,EAAE;IAC1C,KAAK,MAAMb,aAAa,IAAIc,cAAc,EAAE;MAC1C3B,gCAAgC,CAC9B3B,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpBW,aAAa,EACbC,aACF,CAAC;IACH;EACF;EAEA,OAAO/B,SAAS;AAClB,CAAC,CAAC;;AAEF,SAASa,sBAAsBA,CAC7BtB,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrBgB,QAAQ,EACR;EACA;EACA;EACA;EACA;EACA,KAAK,MAAM,CAACvB,YAAY,EAAE2D,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACtC,QAAQ,CAAC,EAAE;IAC7D;IACA;IACA;IACA,IAAIoC,MAAM,CAAChC,MAAM,GAAG,CAAC,EAAE;MACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,MAAM,CAAChC,MAAM,EAAEC,CAAC,EAAE,EAAE;QACtC,KAAK,IAAIE,CAAC,GAAGF,CAAC,GAAG,CAAC,EAAEE,CAAC,GAAG6B,MAAM,CAAChC,MAAM,EAAEG,CAAC,EAAE,EAAE;UAC1C,MAAMgC,QAAQ,GAAGC,YAAY,CAC3B3D,OAAO,EACPK,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrB,KAAK;UAAE;UACPP,YAAY,EACZ2D,MAAM,CAAC/B,CAAC,CAAC,EACT+B,MAAM,CAAC7B,CAAC,CACV,CAAC;UAED,IAAIgC,QAAQ,EAAE;YACZjD,SAAS,CAACmD,IAAI,CAACF,QAAQ,CAAC;UAC1B;QACF;MACF;IACF;EACF;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;;AAEA,SAASrB,uBAAuBA,CAC9BrC,OAAO,EACPS,SAAS,EACTJ,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrB0D,gCAAgC,EAChClB,SAAS,EACTT,SAAS,EACT;EACA;EACA;EACA;EACA;EACA;EACA,KAAK,MAAM,CAACtC,YAAY,EAAEgB,OAAO,CAAC,IAAI4C,MAAM,CAACC,OAAO,CAACd,SAAS,CAAC,EAAE;IAC/D,MAAM9B,OAAO,GAAGqB,SAAS,CAACtC,YAAY,CAAC;IAEvC,IAAIiB,OAAO,EAAE;MACX,KAAK,MAAMiD,MAAM,IAAIlD,OAAO,EAAE;QAC5B,KAAK,MAAMmD,MAAM,IAAIlD,OAAO,EAAE;UAC5B,MAAM6C,QAAQ,GAAGC,YAAY,CAC3B3D,OAAO,EACPK,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrB0D,gCAAgC,EAChCjE,YAAY,EACZkE,MAAM,EACNC,MACF,CAAC;UAED,IAAIL,QAAQ,EAAE;YACZjD,SAAS,CAACmD,IAAI,CAACF,QAAQ,CAAC;UAC1B;QACF;MACF;IACF;EACF;AACF,CAAC,CAAC;AACF;;AAEA,SAASC,YAAYA,CACnB3D,OAAO,EACPK,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrB0D,gCAAgC,EAChCjE,YAAY,EACZkE,MAAM,EACNC,MAAM,EACN;EACA,MAAM,CAACd,WAAW,EAAEe,KAAK,EAAEC,IAAI,CAAC,GAAGH,MAAM;EACzC,MAAM,CAACX,WAAW,EAAEe,KAAK,EAAEC,IAAI,CAAC,GAAGJ,MAAM,CAAC,CAAC;EAC3C;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMnC,oBAAoB,GACxBiC,gCAAgC,IAC/BZ,WAAW,KAAKE,WAAW,IAC1B/D,YAAY,CAAC6D,WAAW,CAAC,IACzB7D,YAAY,CAAC+D,WAAW,CAAE;EAE9B,IAAI,CAACvB,oBAAoB,EAAE;IACzB;IACA,MAAMwC,KAAK,GAAGJ,KAAK,CAACK,IAAI,CAACC,KAAK;IAC9B,MAAMC,KAAK,GAAGL,KAAK,CAACG,IAAI,CAACC,KAAK;IAE9B,IAAIF,KAAK,KAAKG,KAAK,EAAE;MACnB,OAAO,CACL,CAAC3E,YAAY,EAAE,IAAIwE,KAAK,UAAUG,KAAK,wBAAwB,CAAC,EAChE,CAACP,KAAK,CAAC,EACP,CAACE,KAAK,CAAC,CACR;IACH,CAAC,CAAC;;IAEF,IAAI,CAACM,aAAa,CAACR,KAAK,EAAEE,KAAK,CAAC,EAAE;MAChC,OAAO,CACL,CAACtE,YAAY,EAAE,+BAA+B,CAAC,EAC/C,CAACoE,KAAK,CAAC,EACP,CAACE,KAAK,CAAC,CACR;IACH;EACF,CAAC,CAAC;;EAEF,MAAMO,KAAK,GAAGR,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACS,IAAI;EACnE,MAAMC,KAAK,GAAGR,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACO,IAAI;EAEnE,IAAID,KAAK,IAAIE,KAAK,IAAIC,eAAe,CAACH,KAAK,EAAEE,KAAK,CAAC,EAAE;IACnD,OAAO,CACL,CACE/E,YAAY,EACZ,kCAAkCjB,OAAO,CAAC8F,KAAK,CAAC,UAAU9F,OAAO,CAC/DgG,KACF,CAAC,GAAG,CACL,EACD,CAACX,KAAK,CAAC,EACP,CAACE,KAAK,CAAC,CACR;EACH,CAAC,CAAC;EACF;EACA;;EAEA,MAAMhB,aAAa,GAAGc,KAAK,CAACxD,YAAY;EACxC,MAAM4C,aAAa,GAAGc,KAAK,CAAC1D,YAAY;EAExC,IAAI0C,aAAa,IAAIE,aAAa,EAAE;IAClC,MAAM3C,SAAS,GAAGuC,oCAAoC,CACpDhD,OAAO,EACPK,4BAA4B,EAC5BJ,8BAA8B,EAC9BE,qBAAqB,EACrByB,oBAAoB,EACpB7C,YAAY,CAAC0F,KAAK,CAAC,EACnBvB,aAAa,EACbnE,YAAY,CAAC4F,KAAK,CAAC,EACnBvB,aACF,CAAC;IACD,OAAOyB,iBAAiB,CAACpE,SAAS,EAAEb,YAAY,EAAEoE,KAAK,EAAEE,KAAK,CAAC;EACjE;AACF;AAEA,SAASM,aAAaA,CAACR,KAAK,EAAEE,KAAK,EAAE;EACnC,MAAMY,KAAK,GAAGd,KAAK,CAACe,SAAS;EAC7B,MAAMC,KAAK,GAAGd,KAAK,CAACa,SAAS;EAE7B,IAAID,KAAK,KAAKG,SAAS,IAAIH,KAAK,CAACvD,MAAM,KAAK,CAAC,EAAE;IAC7C,OAAOyD,KAAK,KAAKC,SAAS,IAAID,KAAK,CAACzD,MAAM,KAAK,CAAC;EAClD;EAEA,IAAIyD,KAAK,KAAKC,SAAS,IAAID,KAAK,CAACzD,MAAM,KAAK,CAAC,EAAE;IAC7C,OAAO,KAAK;EACd;EACA;;EAEA,IAAIuD,KAAK,CAACvD,MAAM,KAAKyD,KAAK,CAACzD,MAAM,EAAE;IACjC;IACA,OAAO,KAAK;IACZ;EACF;EAEA,MAAM2D,OAAO,GAAG,IAAI5E,GAAG,CAAC0E,KAAK,CAACrF,GAAG,CAAC,CAAC;IAAE0E,IAAI;IAAEC;EAAM,CAAC,KAAK,CAACD,IAAI,CAACC,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC;EAC5E,OAAOQ,KAAK,CAACK,KAAK,CAAEC,IAAI,IAAK;IAC3B,MAAMC,MAAM,GAAGD,IAAI,CAACd,KAAK;IACzB,MAAMgB,MAAM,GAAGJ,OAAO,CAACK,GAAG,CAACH,IAAI,CAACf,IAAI,CAACC,KAAK,CAAC;IAE3C,IAAIgB,MAAM,KAAKL,SAAS,EAAE;MACxB,OAAO,KAAK;IACd;IAEA,OAAOO,cAAc,CAACH,MAAM,CAAC,KAAKG,cAAc,CAACF,MAAM,CAAC;EAC1D,CAAC,CAAC;AACJ;AAEA,SAASE,cAAcA,CAAClB,KAAK,EAAE;EAC7B,OAAOxF,KAAK,CAACO,aAAa,CAACiF,KAAK,CAAC,CAAC;AACpC,CAAC,CAAC;AACF;AACA;;AAEA,SAASM,eAAeA,CAACH,KAAK,EAAEE,KAAK,EAAE;EACrC,IAAIzF,UAAU,CAACuF,KAAK,CAAC,EAAE;IACrB,OAAOvF,UAAU,CAACyF,KAAK,CAAC,GACpBC,eAAe,CAACH,KAAK,CAACgB,MAAM,EAAEd,KAAK,CAACc,MAAM,CAAC,GAC3C,IAAI;EACV;EAEA,IAAIvG,UAAU,CAACyF,KAAK,CAAC,EAAE;IACrB,OAAO,IAAI;EACb;EAEA,IAAIxF,aAAa,CAACsF,KAAK,CAAC,EAAE;IACxB,OAAOtF,aAAa,CAACwF,KAAK,CAAC,GACvBC,eAAe,CAACH,KAAK,CAACgB,MAAM,EAAEd,KAAK,CAACc,MAAM,CAAC,GAC3C,IAAI;EACV;EAEA,IAAItG,aAAa,CAACwF,KAAK,CAAC,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,IAAI1F,UAAU,CAACwF,KAAK,CAAC,IAAIxF,UAAU,CAAC0F,KAAK,CAAC,EAAE;IAC1C,OAAOF,KAAK,KAAKE,KAAK;EACxB;EAEA,OAAO,KAAK;AACd,CAAC,CAAC;AACF;AACA;;AAEA,SAAStD,yBAAyBA,CAChCrB,OAAO,EACPK,4BAA4B,EAC5Ba,UAAU,EACVV,YAAY,EACZ;EACA,MAAMkF,MAAM,GAAGrF,4BAA4B,CAACkF,GAAG,CAAC/E,YAAY,CAAC;EAE7D,IAAIkF,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EAEA,MAAMC,WAAW,GAAGnC,MAAM,CAACoC,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMxE,aAAa,GAAGoC,MAAM,CAACoC,MAAM,CAAC,IAAI,CAAC;EAEzCC,8BAA8B,CAC5B7F,OAAO,EACPkB,UAAU,EACVV,YAAY,EACZmF,WAAW,EACXvE,aACF,CAAC;EAED,MAAM0E,MAAM,GAAG,CAACH,WAAW,EAAEnC,MAAM,CAACuC,IAAI,CAAC3E,aAAa,CAAC,CAAC;EACxDf,4BAA4B,CAAC2F,GAAG,CAACxF,YAAY,EAAEsF,MAAM,CAAC;EACtD,OAAOA,MAAM;AACf,CAAC,CAAC;AACF;;AAEA,SAAS1D,mCAAmCA,CAC1CpC,OAAO,EACPK,4BAA4B,EAC5B2B,QAAQ,EACR;EACA;EACA,MAAM0D,MAAM,GAAGrF,4BAA4B,CAACkF,GAAG,CAACvD,QAAQ,CAACxB,YAAY,CAAC;EAEtE,IAAIkF,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EAEA,MAAMO,YAAY,GAAG3G,WAAW,CAACU,OAAO,CAACkG,SAAS,CAAC,CAAC,EAAElE,QAAQ,CAACmE,aAAa,CAAC;EAC7E,OAAO9E,yBAAyB,CAC9BrB,OAAO,EACPK,4BAA4B,EAC5B4F,YAAY,EACZjE,QAAQ,CAACxB,YACX,CAAC;AACH;AAEA,SAASqF,8BAA8BA,CACrC7F,OAAO,EACPkB,UAAU,EACVV,YAAY,EACZmF,WAAW,EACXvE,aAAa,EACb;EACA,KAAK,MAAMgF,SAAS,IAAI5F,YAAY,CAAC6F,UAAU,EAAE;IAC/C,QAAQD,SAAS,CAACE,IAAI;MACpB,KAAKzH,IAAI,CAAC0H,KAAK;QAAE;UACf,MAAMC,SAAS,GAAGJ,SAAS,CAAC/B,IAAI,CAACC,KAAK;UACtC,IAAImC,QAAQ;UAEZ,IAAIrH,YAAY,CAAC8B,UAAU,CAAC,IAAIlC,eAAe,CAACkC,UAAU,CAAC,EAAE;YAC3DuF,QAAQ,GAAGvF,UAAU,CAACwF,SAAS,CAAC,CAAC,CAACF,SAAS,CAAC;UAC9C;UAEA,MAAM5G,YAAY,GAAGwG,SAAS,CAACO,KAAK,GAChCP,SAAS,CAACO,KAAK,CAACrC,KAAK,GACrBkC,SAAS;UAEb,IAAI,CAACb,WAAW,CAAC/F,YAAY,CAAC,EAAE;YAC9B+F,WAAW,CAAC/F,YAAY,CAAC,GAAG,EAAE;UAChC;UAEA+F,WAAW,CAAC/F,YAAY,CAAC,CAACgE,IAAI,CAAC,CAAC1C,UAAU,EAAEkF,SAAS,EAAEK,QAAQ,CAAC,CAAC;UACjE;QACF;MAEA,KAAK5H,IAAI,CAAC+H,eAAe;QACvBxF,aAAa,CAACgF,SAAS,CAAC/B,IAAI,CAACC,KAAK,CAAC,GAAG,IAAI;QAC1C;MAEF,KAAKzF,IAAI,CAACgI,eAAe;QAAE;UACzB,MAAMV,aAAa,GAAGC,SAAS,CAACD,aAAa;UAC7C,MAAMW,kBAAkB,GAAGX,aAAa,GACpC7G,WAAW,CAACU,OAAO,CAACkG,SAAS,CAAC,CAAC,EAAEC,aAAa,CAAC,GAC/CjF,UAAU;UAEd2E,8BAA8B,CAC5B7F,OAAO,EACP8G,kBAAkB,EAClBV,SAAS,CAAC5F,YAAY,EACtBmF,WAAW,EACXvE,aACF,CAAC;UAED;QACF;IACF;EACF;AACF,CAAC,CAAC;AACF;;AAEA,SAASyD,iBAAiBA,CAACpE,SAAS,EAAEb,YAAY,EAAEoE,KAAK,EAAEE,KAAK,EAAE;EAChE,IAAIzD,SAAS,CAACc,MAAM,GAAG,CAAC,EAAE;IACxB,OAAO,CACL,CAAC3B,YAAY,EAAEa,SAAS,CAACd,GAAG,CAAC,CAAC,CAACH,MAAM,CAAC,KAAKA,MAAM,CAAC,CAAC,EACnD,CAACwE,KAAK,EAAE,GAAGvD,SAAS,CAACd,GAAG,CAAC,CAAC,GAAGiB,OAAO,CAAC,KAAKA,OAAO,CAAC,CAACmG,IAAI,CAAC,CAAC,CAAC,EAC1D,CAAC7C,KAAK,EAAE,GAAGzD,SAAS,CAACd,GAAG,CAAC,CAAC,IAAKkB,OAAO,CAAC,KAAKA,OAAO,CAAC,CAACkG,IAAI,CAAC,CAAC,CAAC,CAC7D;EACH;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM7G,cAAc,CAAC;EACnB8G,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAG,IAAI3G,GAAG,CAAC,CAAC;EACxB;EAEAwB,GAAGA,CAACoF,CAAC,EAAEC,CAAC,EAAEC,aAAa,EAAE;IACvB,IAAIC,eAAe;IAEnB,MAAMvB,MAAM,GACV,CAACuB,eAAe,GAAG,IAAI,CAACJ,KAAK,CAAC1B,GAAG,CAAC2B,CAAC,CAAC,MAAM,IAAI,IAC9CG,eAAe,KAAK,KAAK,CAAC,GACtB,KAAK,CAAC,GACNA,eAAe,CAAC9B,GAAG,CAAC4B,CAAC,CAAC;IAE5B,IAAIrB,MAAM,KAAKb,SAAS,EAAE;MACxB,OAAO,KAAK;IACd;IAEA,OAAOmC,aAAa,GAAG,IAAI,GAAGA,aAAa,KAAKtB,MAAM;EACxD;EAEA/D,GAAGA,CAACmF,CAAC,EAAEC,CAAC,EAAEC,aAAa,EAAE;IACvB,MAAMzH,GAAG,GAAG,IAAI,CAACsH,KAAK,CAAC1B,GAAG,CAAC2B,CAAC,CAAC;IAE7B,IAAIvH,GAAG,KAAKsF,SAAS,EAAE;MACrB,IAAI,CAACgC,KAAK,CAACjB,GAAG,CAACkB,CAAC,EAAE,IAAI5G,GAAG,CAAC,CAAC,CAAC6G,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,MAAM;MACLzH,GAAG,CAACqG,GAAG,CAACmB,CAAC,EAAEC,aAAa,CAAC;IAC3B;EACF;AACF;AACA;AACA;AACA;AACA;;AAEA,MAAMhH,OAAO,CAAC;EACZ4G,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACM,eAAe,GAAG,IAAIpH,cAAc,CAAC,CAAC;EAC7C;EAEA4B,GAAGA,CAACoF,CAAC,EAAEC,CAAC,EAAEC,aAAa,EAAE;IACvB,OAAOF,CAAC,GAAGC,CAAC,GACR,IAAI,CAACG,eAAe,CAACxF,GAAG,CAACoF,CAAC,EAAEC,CAAC,EAAEC,aAAa,CAAC,GAC7C,IAAI,CAACE,eAAe,CAACxF,GAAG,CAACqF,CAAC,EAAED,CAAC,EAAEE,aAAa,CAAC;EACnD;EAEArF,GAAGA,CAACmF,CAAC,EAAEC,CAAC,EAAEC,aAAa,EAAE;IACvB,IAAIF,CAAC,GAAGC,CAAC,EAAE;MACT,IAAI,CAACG,eAAe,CAACvF,GAAG,CAACmF,CAAC,EAAEC,CAAC,EAAEC,aAAa,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAACE,eAAe,CAACvF,GAAG,CAACoF,CAAC,EAAED,CAAC,EAAEE,aAAa,CAAC;IAC/C;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}