{"ast": null, "code": "// src/Interceptor.ts\nimport { Logger } from \"@open-draft/logger\";\nimport { Emitter } from \"strict-event-emitter\";\nvar INTERNAL_REQUEST_ID_HEADER_NAME = \"x-interceptors-internal-request-id\";\nfunction getGlobalSymbol(symbol) {\n  return (\n    // @ts-ignore https://github.com/Microsoft/TypeScript/issues/24587\n    globalThis[symbol] || void 0\n  );\n}\nfunction setGlobalSymbol(symbol, value) {\n  globalThis[symbol] = value;\n}\nfunction deleteGlobalSymbol(symbol) {\n  delete globalThis[symbol];\n}\nvar InterceptorReadyState = /* @__PURE__ */(InterceptorReadyState2 => {\n  InterceptorReadyState2[\"INACTIVE\"] = \"INACTIVE\";\n  InterceptorReadyState2[\"APPLYING\"] = \"APPLYING\";\n  InterceptorReadyState2[\"APPLIED\"] = \"APPLIED\";\n  InterceptorReadyState2[\"DISPOSING\"] = \"DISPOSING\";\n  InterceptorReadyState2[\"DISPOSED\"] = \"DISPOSED\";\n  return InterceptorReadyState2;\n})(InterceptorReadyState || {});\nvar Interceptor = class {\n  constructor(symbol) {\n    this.symbol = symbol;\n    this.readyState = \"INACTIVE\" /* INACTIVE */;\n    this.emitter = new Emitter();\n    this.subscriptions = [];\n    this.logger = new Logger(symbol.description);\n    this.emitter.setMaxListeners(0);\n    this.logger.info(\"constructing the interceptor...\");\n  }\n  /**\n   * Determine if this interceptor can be applied\n   * in the current environment.\n   */\n  checkEnvironment() {\n    return true;\n  }\n  /**\n   * Apply this interceptor to the current process.\n   * Returns an already running interceptor instance if it's present.\n   */\n  apply() {\n    const logger = this.logger.extend(\"apply\");\n    logger.info(\"applying the interceptor...\");\n    if (this.readyState === \"APPLIED\" /* APPLIED */) {\n      logger.info(\"intercepted already applied!\");\n      return;\n    }\n    const shouldApply = this.checkEnvironment();\n    if (!shouldApply) {\n      logger.info(\"the interceptor cannot be applied in this environment!\");\n      return;\n    }\n    this.readyState = \"APPLYING\" /* APPLYING */;\n    const runningInstance = this.getInstance();\n    if (runningInstance) {\n      logger.info(\"found a running instance, reusing...\");\n      this.on = (event, listener) => {\n        logger.info('proxying the \"%s\" listener', event);\n        runningInstance.emitter.addListener(event, listener);\n        this.subscriptions.push(() => {\n          runningInstance.emitter.removeListener(event, listener);\n          logger.info('removed proxied \"%s\" listener!', event);\n        });\n        return this;\n      };\n      this.readyState = \"APPLIED\" /* APPLIED */;\n      return;\n    }\n    logger.info(\"no running instance found, setting up a new instance...\");\n    this.setup();\n    this.setInstance();\n    this.readyState = \"APPLIED\" /* APPLIED */;\n  }\n  /**\n   * Setup the module augments and stubs necessary for this interceptor.\n   * This method is not run if there's a running interceptor instance\n   * to prevent instantiating an interceptor multiple times.\n   */\n  setup() {}\n  /**\n   * Listen to the interceptor's public events.\n   */\n  on(event, listener) {\n    const logger = this.logger.extend(\"on\");\n    if (this.readyState === \"DISPOSING\" /* DISPOSING */ || this.readyState === \"DISPOSED\" /* DISPOSED */) {\n      logger.info(\"cannot listen to events, already disposed!\");\n      return this;\n    }\n    logger.info('adding \"%s\" event listener:', event, listener);\n    this.emitter.on(event, listener);\n    return this;\n  }\n  once(event, listener) {\n    this.emitter.once(event, listener);\n    return this;\n  }\n  off(event, listener) {\n    this.emitter.off(event, listener);\n    return this;\n  }\n  removeAllListeners(event) {\n    this.emitter.removeAllListeners(event);\n    return this;\n  }\n  /**\n   * Disposes of any side-effects this interceptor has introduced.\n   */\n  dispose() {\n    const logger = this.logger.extend(\"dispose\");\n    if (this.readyState === \"DISPOSED\" /* DISPOSED */) {\n      logger.info(\"cannot dispose, already disposed!\");\n      return;\n    }\n    logger.info(\"disposing the interceptor...\");\n    this.readyState = \"DISPOSING\" /* DISPOSING */;\n    if (!this.getInstance()) {\n      logger.info(\"no interceptors running, skipping dispose...\");\n      return;\n    }\n    this.clearInstance();\n    logger.info(\"global symbol deleted:\", getGlobalSymbol(this.symbol));\n    if (this.subscriptions.length > 0) {\n      logger.info(\"disposing of %d subscriptions...\", this.subscriptions.length);\n      for (const dispose of this.subscriptions) {\n        dispose();\n      }\n      this.subscriptions = [];\n      logger.info(\"disposed of all subscriptions!\", this.subscriptions.length);\n    }\n    this.emitter.removeAllListeners();\n    logger.info(\"destroyed the listener!\");\n    this.readyState = \"DISPOSED\" /* DISPOSED */;\n  }\n  getInstance() {\n    var _a;\n    const instance = getGlobalSymbol(this.symbol);\n    this.logger.info(\"retrieved global instance:\", (_a = instance == null ? void 0 : instance.constructor) == null ? void 0 : _a.name);\n    return instance;\n  }\n  setInstance() {\n    setGlobalSymbol(this.symbol, this);\n    this.logger.info(\"set global instance!\", this.symbol.description);\n  }\n  clearInstance() {\n    deleteGlobalSymbol(this.symbol);\n    this.logger.info(\"cleared global instance!\", this.symbol.description);\n  }\n};\n\n// src/createRequestId.ts\nfunction createRequestId() {\n  return Math.random().toString(16).slice(2);\n}\nexport { INTERNAL_REQUEST_ID_HEADER_NAME, getGlobalSymbol, deleteGlobalSymbol, InterceptorReadyState, Interceptor, createRequestId };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Emitter", "INTERNAL_REQUEST_ID_HEADER_NAME", "getGlobalSymbol", "symbol", "globalThis", "setGlobalSymbol", "value", "deleteGlobalSymbol", "InterceptorReadyState", "InterceptorReadyState2", "Interceptor", "constructor", "readyState", "emitter", "subscriptions", "logger", "description", "setMaxListeners", "info", "checkEnvironment", "apply", "extend", "shouldApply", "runningInstance", "getInstance", "on", "event", "listener", "addListener", "push", "removeListener", "setup", "setInstance", "once", "off", "removeAllListeners", "dispose", "clearInstance", "length", "_a", "instance", "name", "createRequestId", "Math", "random", "toString", "slice"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/Interceptor.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mswjs/interceptors/src/createRequestId.ts"], "sourcesContent": ["import { Logger } from '@open-draft/logger'\nimport { Emitter, Listener } from 'strict-event-emitter'\n\nexport type InterceptorEventMap = Record<string, any>\nexport type InterceptorSubscription = () => void\n\n/**\n * Request header name to detect when a single request\n * is being handled by nested interceptors (XHR -> ClientRequest).\n * Obscure by design to prevent collisions with user-defined headers.\n * Ideally, come up with the Interceptor-level mechanism for this.\n * @see https://github.com/mswjs/interceptors/issues/378\n */\nexport const INTERNAL_REQUEST_ID_HEADER_NAME =\n  'x-interceptors-internal-request-id'\n\nexport function getGlobalSymbol<V>(symbol: Symbol): V | undefined {\n  return (\n    // @ts-ignore https://github.com/Microsoft/TypeScript/issues/24587\n    globalThis[symbol] || undefined\n  )\n}\n\nfunction setGlobalSymbol(symbol: Symbol, value: any): void {\n  // @ts-ignore\n  globalThis[symbol] = value\n}\n\nexport function deleteGlobalSymbol(symbol: Symbol): void {\n  // @ts-ignore\n  delete globalThis[symbol]\n}\n\nexport enum InterceptorReadyState {\n  INACTIVE = 'INACTIVE',\n  APPLYING = 'APPLYING',\n  APPLIED = 'APPLIED',\n  DISPOSING = 'DISPOSING',\n  DISPOSED = 'DISPOSED',\n}\n\nexport type ExtractEventNames<Events extends Record<string, any>> =\n  Events extends Record<infer EventName, any> ? EventName : never\n\nexport class Interceptor<Events extends InterceptorEventMap> {\n  protected emitter: Emitter<Events>\n  protected subscriptions: Array<InterceptorSubscription>\n  protected logger: Logger\n\n  public readyState: InterceptorReadyState\n\n  constructor(private readonly symbol: symbol) {\n    this.readyState = InterceptorReadyState.INACTIVE\n\n    this.emitter = new Emitter()\n    this.subscriptions = []\n    this.logger = new Logger(symbol.description!)\n\n    // Do not limit the maximum number of listeners\n    // so not to limit the maximum amount of parallel events emitted.\n    this.emitter.setMaxListeners(0)\n\n    this.logger.info('constructing the interceptor...')\n  }\n\n  /**\n   * Determine if this interceptor can be applied\n   * in the current environment.\n   */\n  protected checkEnvironment(): boolean {\n    return true\n  }\n\n  /**\n   * Apply this interceptor to the current process.\n   * Returns an already running interceptor instance if it's present.\n   */\n  public apply(): void {\n    const logger = this.logger.extend('apply')\n    logger.info('applying the interceptor...')\n\n    if (this.readyState === InterceptorReadyState.APPLIED) {\n      logger.info('intercepted already applied!')\n      return\n    }\n\n    const shouldApply = this.checkEnvironment()\n\n    if (!shouldApply) {\n      logger.info('the interceptor cannot be applied in this environment!')\n      return\n    }\n\n    this.readyState = InterceptorReadyState.APPLYING\n\n    // Whenever applying a new interceptor, check if it hasn't been applied already.\n    // This enables to apply the same interceptor multiple times, for example from a different\n    // interceptor, only proxying events but keeping the stubs in a single place.\n    const runningInstance = this.getInstance()\n\n    if (runningInstance) {\n      logger.info('found a running instance, reusing...')\n\n      // Proxy any listeners you set on this instance to the running instance.\n      this.on = (event, listener) => {\n        logger.info('proxying the \"%s\" listener', event)\n\n        // Add listeners to the running instance so they appear\n        // at the top of the event listeners list and are executed first.\n        runningInstance.emitter.addListener(event, listener)\n\n        // Ensure that once this interceptor instance is disposed,\n        // it removes all listeners it has appended to the running interceptor instance.\n        this.subscriptions.push(() => {\n          runningInstance.emitter.removeListener(event, listener)\n          logger.info('removed proxied \"%s\" listener!', event)\n        })\n\n        return this\n      }\n\n      this.readyState = InterceptorReadyState.APPLIED\n\n      return\n    }\n\n    logger.info('no running instance found, setting up a new instance...')\n\n    // Setup the interceptor.\n    this.setup()\n\n    // Store the newly applied interceptor instance globally.\n    this.setInstance()\n\n    this.readyState = InterceptorReadyState.APPLIED\n  }\n\n  /**\n   * Setup the module augments and stubs necessary for this interceptor.\n   * This method is not run if there's a running interceptor instance\n   * to prevent instantiating an interceptor multiple times.\n   */\n  protected setup(): void {}\n\n  /**\n   * Listen to the interceptor's public events.\n   */\n  public on<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    const logger = this.logger.extend('on')\n\n    if (\n      this.readyState === InterceptorReadyState.DISPOSING ||\n      this.readyState === InterceptorReadyState.DISPOSED\n    ) {\n      logger.info('cannot listen to events, already disposed!')\n      return this\n    }\n\n    logger.info('adding \"%s\" event listener:', event, listener)\n\n    this.emitter.on(event, listener)\n    return this\n  }\n\n  public once<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    this.emitter.once(event, listener)\n    return this\n  }\n\n  public off<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    this.emitter.off(event, listener)\n    return this\n  }\n\n  public removeAllListeners<EventName extends ExtractEventNames<Events>>(\n    event?: EventName\n  ): this {\n    this.emitter.removeAllListeners(event)\n    return this\n  }\n\n  /**\n   * Disposes of any side-effects this interceptor has introduced.\n   */\n  public dispose(): void {\n    const logger = this.logger.extend('dispose')\n\n    if (this.readyState === InterceptorReadyState.DISPOSED) {\n      logger.info('cannot dispose, already disposed!')\n      return\n    }\n\n    logger.info('disposing the interceptor...')\n    this.readyState = InterceptorReadyState.DISPOSING\n\n    if (!this.getInstance()) {\n      logger.info('no interceptors running, skipping dispose...')\n      return\n    }\n\n    // Delete the global symbol as soon as possible,\n    // indicating that the interceptor is no longer running.\n    this.clearInstance()\n\n    logger.info('global symbol deleted:', getGlobalSymbol(this.symbol))\n\n    if (this.subscriptions.length > 0) {\n      logger.info('disposing of %d subscriptions...', this.subscriptions.length)\n\n      for (const dispose of this.subscriptions) {\n        dispose()\n      }\n\n      this.subscriptions = []\n\n      logger.info('disposed of all subscriptions!', this.subscriptions.length)\n    }\n\n    this.emitter.removeAllListeners()\n    logger.info('destroyed the listener!')\n\n    this.readyState = InterceptorReadyState.DISPOSED\n  }\n\n  private getInstance(): this | undefined {\n    const instance = getGlobalSymbol<this>(this.symbol)\n    this.logger.info('retrieved global instance:', instance?.constructor?.name)\n    return instance\n  }\n\n  private setInstance(): void {\n    setGlobalSymbol(this.symbol, this)\n    this.logger.info('set global instance!', this.symbol.description)\n  }\n\n  private clearInstance(): void {\n    deleteGlobalSymbol(this.symbol)\n    this.logger.info('cleared global instance!', this.symbol.description)\n  }\n}\n", "/**\n * Generate a random ID string to represent a request.\n * @example\n * createRequestId()\n * // \"f774b6c9c600f\"\n */\nexport function createRequestId(): string {\n  return Math.random().toString(16).slice(2)\n}\n"], "mappings": ";AAAA,SAASA,MAAA,QAAc;AACvB,SAASC,OAAA,QAAyB;AAY3B,IAAMC,+BAAA,GACX;AAEK,SAASC,gBAAmBC,MAAA,EAA+B;EAChE;IAAA;IAEEC,UAAA,CAAWD,MAAM,KAAK;EAAA;AAE1B;AAEA,SAASE,gBAAgBF,MAAA,EAAgBG,KAAA,EAAkB;EAEzDF,UAAA,CAAWD,MAAM,IAAIG,KAAA;AACvB;AAEO,SAASC,mBAAmBJ,MAAA,EAAsB;EAEvD,OAAOC,UAAA,CAAWD,MAAM;AAC1B;AAEO,IAAKK,qBAAA,GAAL,gBAAKC,sBAAA,IAAL;EACLA,sBAAA,eAAW;EACXA,sBAAA,eAAW;EACXA,sBAAA,cAAU;EACVA,sBAAA,gBAAY;EACZA,sBAAA,eAAW;EALD,OAAAA,sBAAA;AAAA,GAAAD,qBAAA;AAWL,IAAME,WAAA,GAAN,MAAsD;EAO3DC,YAA6BR,MAAA,EAAgB;IAAhB,KAAAA,MAAA,GAAAA,MAAA;IAC3B,KAAKS,UAAA,GAAa;IAElB,KAAKC,OAAA,GAAU,IAAIb,OAAA,CAAQ;IAC3B,KAAKc,aAAA,GAAgB,EAAC;IACtB,KAAKC,MAAA,GAAS,IAAIhB,MAAA,CAAOI,MAAA,CAAOa,WAAY;IAI5C,KAAKH,OAAA,CAAQI,eAAA,CAAgB,CAAC;IAE9B,KAAKF,MAAA,CAAOG,IAAA,CAAK,iCAAiC;EACpD;EAAA;AAAA;AAAA;AAAA;EAMUC,iBAAA,EAA4B;IACpC,OAAO;EACT;EAAA;AAAA;AAAA;AAAA;EAMOC,MAAA,EAAc;IACnB,MAAML,MAAA,GAAS,KAAKA,MAAA,CAAOM,MAAA,CAAO,OAAO;IACzCN,MAAA,CAAOG,IAAA,CAAK,6BAA6B;IAEzC,IAAI,KAAKN,UAAA,KAAe,yBAA+B;MACrDG,MAAA,CAAOG,IAAA,CAAK,8BAA8B;MAC1C;IACF;IAEA,MAAMI,WAAA,GAAc,KAAKH,gBAAA,CAAiB;IAE1C,IAAI,CAACG,WAAA,EAAa;MAChBP,MAAA,CAAOG,IAAA,CAAK,wDAAwD;MACpE;IACF;IAEA,KAAKN,UAAA,GAAa;IAKlB,MAAMW,eAAA,GAAkB,KAAKC,WAAA,CAAY;IAEzC,IAAID,eAAA,EAAiB;MACnBR,MAAA,CAAOG,IAAA,CAAK,sCAAsC;MAGlD,KAAKO,EAAA,GAAK,CAACC,KAAA,EAAOC,QAAA,KAAa;QAC7BZ,MAAA,CAAOG,IAAA,CAAK,8BAA8BQ,KAAK;QAI/CH,eAAA,CAAgBV,OAAA,CAAQe,WAAA,CAAYF,KAAA,EAAOC,QAAQ;QAInD,KAAKb,aAAA,CAAce,IAAA,CAAK,MAAM;UAC5BN,eAAA,CAAgBV,OAAA,CAAQiB,cAAA,CAAeJ,KAAA,EAAOC,QAAQ;UACtDZ,MAAA,CAAOG,IAAA,CAAK,kCAAkCQ,KAAK;QACrD,CAAC;QAED,OAAO;MACT;MAEA,KAAKd,UAAA,GAAa;MAElB;IACF;IAEAG,MAAA,CAAOG,IAAA,CAAK,yDAAyD;IAGrE,KAAKa,KAAA,CAAM;IAGX,KAAKC,WAAA,CAAY;IAEjB,KAAKpB,UAAA,GAAa;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOUmB,MAAA,EAAc,CAAC;EAAA;AAAA;AAAA;EAKlBN,GACLC,KAAA,EACAC,QAAA,EACM;IACN,MAAMZ,MAAA,GAAS,KAAKA,MAAA,CAAOM,MAAA,CAAO,IAAI;IAEtC,IACE,KAAKT,UAAA,KAAe,+BACpB,KAAKA,UAAA,KAAe,2BACpB;MACAG,MAAA,CAAOG,IAAA,CAAK,4CAA4C;MACxD,OAAO;IACT;IAEAH,MAAA,CAAOG,IAAA,CAAK,+BAA+BQ,KAAA,EAAOC,QAAQ;IAE1D,KAAKd,OAAA,CAAQY,EAAA,CAAGC,KAAA,EAAOC,QAAQ;IAC/B,OAAO;EACT;EAEOM,KACLP,KAAA,EACAC,QAAA,EACM;IACN,KAAKd,OAAA,CAAQoB,IAAA,CAAKP,KAAA,EAAOC,QAAQ;IACjC,OAAO;EACT;EAEOO,IACLR,KAAA,EACAC,QAAA,EACM;IACN,KAAKd,OAAA,CAAQqB,GAAA,CAAIR,KAAA,EAAOC,QAAQ;IAChC,OAAO;EACT;EAEOQ,mBACLT,KAAA,EACM;IACN,KAAKb,OAAA,CAAQsB,kBAAA,CAAmBT,KAAK;IACrC,OAAO;EACT;EAAA;AAAA;AAAA;EAKOU,QAAA,EAAgB;IACrB,MAAMrB,MAAA,GAAS,KAAKA,MAAA,CAAOM,MAAA,CAAO,SAAS;IAE3C,IAAI,KAAKT,UAAA,KAAe,2BAAgC;MACtDG,MAAA,CAAOG,IAAA,CAAK,mCAAmC;MAC/C;IACF;IAEAH,MAAA,CAAOG,IAAA,CAAK,8BAA8B;IAC1C,KAAKN,UAAA,GAAa;IAElB,IAAI,CAAC,KAAKY,WAAA,CAAY,GAAG;MACvBT,MAAA,CAAOG,IAAA,CAAK,8CAA8C;MAC1D;IACF;IAIA,KAAKmB,aAAA,CAAc;IAEnBtB,MAAA,CAAOG,IAAA,CAAK,0BAA0BhB,eAAA,CAAgB,KAAKC,MAAM,CAAC;IAElE,IAAI,KAAKW,aAAA,CAAcwB,MAAA,GAAS,GAAG;MACjCvB,MAAA,CAAOG,IAAA,CAAK,oCAAoC,KAAKJ,aAAA,CAAcwB,MAAM;MAEzE,WAAWF,OAAA,IAAW,KAAKtB,aAAA,EAAe;QACxCsB,OAAA,CAAQ;MACV;MAEA,KAAKtB,aAAA,GAAgB,EAAC;MAEtBC,MAAA,CAAOG,IAAA,CAAK,kCAAkC,KAAKJ,aAAA,CAAcwB,MAAM;IACzE;IAEA,KAAKzB,OAAA,CAAQsB,kBAAA,CAAmB;IAChCpB,MAAA,CAAOG,IAAA,CAAK,yBAAyB;IAErC,KAAKN,UAAA,GAAa;EACpB;EAEQY,YAAA,EAAgC;IAzO1C,IAAAe,EAAA;IA0OI,MAAMC,QAAA,GAAWtC,eAAA,CAAsB,KAAKC,MAAM;IAClD,KAAKY,MAAA,CAAOG,IAAA,CAAK,+BAA8BqB,EAAA,GAAAC,QAAA,oBAAAA,QAAA,CAAU7B,WAAA,KAAV,gBAAA4B,EAAA,CAAuBE,IAAI;IAC1E,OAAOD,QAAA;EACT;EAEQR,YAAA,EAAoB;IAC1B3B,eAAA,CAAgB,KAAKF,MAAA,EAAQ,IAAI;IACjC,KAAKY,MAAA,CAAOG,IAAA,CAAK,wBAAwB,KAAKf,MAAA,CAAOa,WAAW;EAClE;EAEQqB,cAAA,EAAsB;IAC5B9B,kBAAA,CAAmB,KAAKJ,MAAM;IAC9B,KAAKY,MAAA,CAAOG,IAAA,CAAK,4BAA4B,KAAKf,MAAA,CAAOa,WAAW;EACtE;AACF;;;AClPO,SAAS0B,gBAAA,EAA0B;EACxC,OAAOC,IAAA,CAAKC,MAAA,CAAO,EAAEC,QAAA,CAAS,EAAE,EAAEC,KAAA,CAAM,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}