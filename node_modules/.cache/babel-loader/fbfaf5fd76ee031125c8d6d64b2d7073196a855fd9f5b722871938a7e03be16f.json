{"ast": null, "code": "import { setupWorker } from 'msw/browser';\nimport { handlers } from './handlers';\nexport const worker = setupWorker(...handlers);", "map": {"version": 3, "names": ["setupWorker", "handlers", "worker"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/mocks/browser.js"], "sourcesContent": ["import { setupWorker } from 'msw/browser';\nimport { handlers } from './handlers';\n\nexport const worker = setupWorker(...handlers);"], "mappings": "AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,QAAQ,YAAY;AAErC,OAAO,MAAMC,MAAM,GAAGF,WAAW,CAAC,GAAGC,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}