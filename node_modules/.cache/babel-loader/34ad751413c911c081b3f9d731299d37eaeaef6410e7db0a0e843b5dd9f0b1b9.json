{"ast": null, "code": "function isAbsoluteUrl(url) {\n  return /^([a-z][a-z\\d\\+\\-\\.]*:)?\\/\\//i.test(url);\n}\nexport { isAbsoluteUrl };", "map": {"version": 3, "names": ["isAbsoluteUrl", "url", "test"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/url/isAbsoluteUrl.ts"], "sourcesContent": ["/**\n * Determines if the given URL string is an absolute URL.\n */\nexport function isAbsoluteUrl(url: string): boolean {\n  return /^([a-z][a-z\\d\\+\\-\\.]*:)?\\/\\//i.test(url)\n}\n"], "mappings": "AAGO,SAASA,cAAcC,GAAA,EAAsB;EAClD,OAAO,gCAAgCC,IAAA,CAAKD,GAAG;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}