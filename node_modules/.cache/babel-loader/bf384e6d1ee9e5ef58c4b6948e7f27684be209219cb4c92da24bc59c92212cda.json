{"ast": null, "code": "// node_modules/.pnpm/outvariant@1.4.3/node_modules/outvariant/lib/index.mjs\nvar POSITIONALS_EXP = /(%?)(%([sdijo]))/g;\nfunction serializePositional(positional, flag) {\n  switch (flag) {\n    case \"s\":\n      return positional;\n    case \"d\":\n    case \"i\":\n      return Number(positional);\n    case \"j\":\n      return JSON.stringify(positional);\n    case \"o\":\n      {\n        if (typeof positional === \"string\") {\n          return positional;\n        }\n        const json = JSON.stringify(positional);\n        if (json === \"{}\" || json === \"[]\" || /^\\[object .+?\\]$/.test(json)) {\n          return positional;\n        }\n        return json;\n      }\n  }\n}\nfunction format(message, ...positionals) {\n  if (positionals.length === 0) {\n    return message;\n  }\n  let positionalIndex = 0;\n  let formattedMessage = message.replace(POSITIONALS_EXP, (match, isEscaped, _, flag) => {\n    const positional = positionals[positionalIndex];\n    const value = serializePositional(positional, flag);\n    if (!isEscaped) {\n      positionalIndex++;\n      return value;\n    }\n    return match;\n  });\n  if (positionalIndex < positionals.length) {\n    formattedMessage += ` ${positionals.slice(positionalIndex).join(\" \")}`;\n  }\n  formattedMessage = formattedMessage.replace(/%{2,2}/g, \"%\");\n  return formattedMessage;\n}\nvar STACK_FRAMES_TO_IGNORE = 2;\nfunction cleanErrorStack(error2) {\n  if (!error2.stack) {\n    return;\n  }\n  const nextStack = error2.stack.split(\"\\n\");\n  nextStack.splice(1, STACK_FRAMES_TO_IGNORE);\n  error2.stack = nextStack.join(\"\\n\");\n}\nvar InvariantError = class extends Error {\n  constructor(message, ...positionals) {\n    super(message);\n    this.message = message;\n    this.name = \"Invariant Violation\";\n    this.message = format(message, ...positionals);\n    cleanErrorStack(this);\n  }\n};\nvar invariant = (predicate, message, ...positionals) => {\n  if (!predicate) {\n    throw new InvariantError(message, ...positionals);\n  }\n};\ninvariant.as = (ErrorConstructor, predicate, message, ...positionals) => {\n  if (!predicate) {\n    const formatMessage = positionals.length === 0 ? message : format(message, ...positionals);\n    let error2;\n    try {\n      error2 = Reflect.construct(ErrorConstructor, [formatMessage]);\n    } catch (err) {\n      error2 = ErrorConstructor(formatMessage);\n    }\n    throw error2;\n  }\n};\n\n// node_modules/.pnpm/is-node-process@1.2.0/node_modules/is-node-process/lib/index.mjs\nfunction isNodeProcess() {\n  if (typeof navigator !== \"undefined\" && navigator.product === \"ReactNative\") {\n    return true;\n  }\n  if (typeof process !== \"undefined\") {\n    const type = process.type;\n    if (type === \"renderer\" || type === \"worker\") {\n      return false;\n    }\n    return !!(process.versions && process.versions.node);\n  }\n  return false;\n}\n\n// src/browser/setupWorker/start/createStartHandler.ts\nimport { devUtils as devUtils7 } from '../core/utils/internal/devUtils.mjs';\n\n// node_modules/.pnpm/@open-draft+until@2.1.0/node_modules/@open-draft/until/lib/index.mjs\nvar until = async promise => {\n  try {\n    const data = await promise().catch(error2 => {\n      throw error2;\n    });\n    return {\n      error: null,\n      data\n    };\n  } catch (error2) {\n    return {\n      error: error2,\n      data: null\n    };\n  }\n};\n\n// src/browser/setupWorker/start/utils/getWorkerInstance.ts\nimport { devUtils } from '../core/utils/internal/devUtils.mjs';\n\n// src/browser/utils/getAbsoluteWorkerUrl.ts\nfunction getAbsoluteWorkerUrl(workerUrl) {\n  return new URL(workerUrl, location.href).href;\n}\n\n// src/browser/setupWorker/start/utils/getWorkerByRegistration.ts\nfunction getWorkerByRegistration(registration, absoluteWorkerUrl, findWorker) {\n  const allStates = [registration.active, registration.installing, registration.waiting];\n  const relevantStates = allStates.filter(state => {\n    return state != null;\n  });\n  const worker = relevantStates.find(worker2 => {\n    return findWorker(worker2.scriptURL, absoluteWorkerUrl);\n  });\n  return worker || null;\n}\n\n// src/browser/setupWorker/start/utils/getWorkerInstance.ts\nvar getWorkerInstance = async (url, options = {}, findWorker) => {\n  const absoluteWorkerUrl = getAbsoluteWorkerUrl(url);\n  const mockRegistrations = await navigator.serviceWorker.getRegistrations().then(registrations => registrations.filter(registration => getWorkerByRegistration(registration, absoluteWorkerUrl, findWorker)));\n  if (!navigator.serviceWorker.controller && mockRegistrations.length > 0) {\n    location.reload();\n  }\n  const [existingRegistration] = mockRegistrations;\n  if (existingRegistration) {\n    existingRegistration.update();\n    return [getWorkerByRegistration(existingRegistration, absoluteWorkerUrl, findWorker), existingRegistration];\n  }\n  const registrationResult = await until(async () => {\n    const registration = await navigator.serviceWorker.register(url, options);\n    return [\n    // Compare existing worker registration by its worker URL,\n    // to prevent irrelevant workers to resolve here (such as Codesandbox worker).\n    getWorkerByRegistration(registration, absoluteWorkerUrl, findWorker), registration];\n  });\n  if (registrationResult.error) {\n    const isWorkerMissing = registrationResult.error.message.includes(\"(404)\");\n    if (isWorkerMissing) {\n      const scopeUrl = new URL(options?.scope || \"/\", location.href);\n      throw new Error(devUtils.formatMessage(`Failed to register a Service Worker for scope ('${scopeUrl.href}') with script ('${absoluteWorkerUrl}'): Service Worker script does not exist at the given path.\n\nDid you forget to run \"npx msw init <PUBLIC_DIR>\"?\n\nLearn more about creating the Service Worker script: https://mswjs.io/docs/cli/init`));\n    }\n    throw new Error(devUtils.formatMessage(\"Failed to register the Service Worker:\\n\\n%s\", registrationResult.error.message));\n  }\n  return registrationResult.data;\n};\n\n// src/browser/setupWorker/start/utils/enableMocking.ts\nimport { devUtils as devUtils3 } from '../core/utils/internal/devUtils.mjs';\n\n// src/browser/setupWorker/start/utils/printStartMessage.ts\nimport { devUtils as devUtils2 } from '../core/utils/internal/devUtils.mjs';\nfunction printStartMessage(args = {}) {\n  if (args.quiet) {\n    return;\n  }\n  const message = args.message || \"Mocking enabled.\";\n  console.groupCollapsed(`%c${devUtils2.formatMessage(message)}`, \"color:orangered;font-weight:bold;\");\n  console.log(\"%cDocumentation: %chttps://mswjs.io/docs\", \"font-weight:bold\", \"font-weight:normal\");\n  console.log(\"Found an issue? https://github.com/mswjs/msw/issues\");\n  if (args.workerUrl) {\n    console.log(\"Worker script URL:\", args.workerUrl);\n  }\n  if (args.workerScope) {\n    console.log(\"Worker scope:\", args.workerScope);\n  }\n  if (args.client) {\n    console.log(\"Client ID: %s (%s)\", args.client.id, args.client.frameType);\n  }\n  console.groupEnd();\n}\n\n// src/browser/setupWorker/start/utils/enableMocking.ts\nasync function enableMocking(context, options) {\n  context.workerChannel.send(\"MOCK_ACTIVATE\");\n  const {\n    payload\n  } = await context.events.once(\"MOCKING_ENABLED\");\n  if (context.isMockingEnabled) {\n    devUtils3.warn(`Found a redundant \"worker.start()\" call. Note that starting the worker while mocking is already enabled will have no effect. Consider removing this \"worker.start()\" call.`);\n    return;\n  }\n  context.isMockingEnabled = true;\n  printStartMessage({\n    quiet: options.quiet,\n    workerScope: context.registration?.scope,\n    workerUrl: context.worker?.scriptURL,\n    client: payload.client\n  });\n}\n\n// src/browser/setupWorker/start/utils/createMessageChannel.ts\nvar WorkerChannel = class {\n  constructor(port) {\n    this.port = port;\n  }\n  postMessage(event, ...rest) {\n    const [data, transfer] = rest;\n    this.port.postMessage({\n      type: event,\n      data\n    }, {\n      transfer\n    });\n  }\n};\n\n// src/browser/utils/pruneGetRequestBody.ts\nfunction pruneGetRequestBody(request) {\n  if ([\"HEAD\", \"GET\"].includes(request.method)) {\n    return void 0;\n  }\n  return request.body;\n}\n\n// src/browser/utils/deserializeRequest.ts\nfunction deserializeRequest(serializedRequest) {\n  return new Request(serializedRequest.url, {\n    ...serializedRequest,\n    body: pruneGetRequestBody(serializedRequest)\n  });\n}\n\n// src/browser/setupWorker/start/createRequestListener.ts\nimport { RequestHandler } from '../core/handlers/RequestHandler.mjs';\nimport { handleRequest } from '../core/utils/handleRequest.mjs';\nimport { devUtils as devUtils4 } from '../core/utils/internal/devUtils.mjs';\nimport { toResponseInit } from '../core/utils/toResponseInit.mjs';\nimport { isHandlerKind } from '../core/utils/internal/isHandlerKind.mjs';\nvar createRequestListener = (context, options) => {\n  return async (event, message) => {\n    const messageChannel = new WorkerChannel(event.ports[0]);\n    const requestId = message.payload.id;\n    const request = deserializeRequest(message.payload);\n    const requestCloneForLogs = request.clone();\n    const requestClone = request.clone();\n    RequestHandler.cache.set(request, requestClone);\n    try {\n      await handleRequest(request, requestId, context.getRequestHandlers().filter(isHandlerKind(\"RequestHandler\")), options, context.emitter, {\n        onPassthroughResponse() {\n          messageChannel.postMessage(\"PASSTHROUGH\");\n        },\n        async onMockedResponse(response, {\n          handler,\n          parsedResult\n        }) {\n          const responseClone = response.clone();\n          const responseCloneForLogs = response.clone();\n          const responseInit = toResponseInit(response);\n          if (context.supports.readableStreamTransfer) {\n            const responseStreamOrNull = response.body;\n            messageChannel.postMessage(\"MOCK_RESPONSE\", {\n              ...responseInit,\n              body: responseStreamOrNull\n            }, responseStreamOrNull ? [responseStreamOrNull] : void 0);\n          } else {\n            const responseBufferOrNull = response.body === null ? null : await responseClone.arrayBuffer();\n            messageChannel.postMessage(\"MOCK_RESPONSE\", {\n              ...responseInit,\n              body: responseBufferOrNull\n            });\n          }\n          if (!options.quiet) {\n            context.emitter.once(\"response:mocked\", () => {\n              handler.log({\n                request: requestCloneForLogs,\n                response: responseCloneForLogs,\n                parsedResult\n              });\n            });\n          }\n        }\n      });\n    } catch (error2) {\n      if (error2 instanceof Error) {\n        devUtils4.error(`Uncaught exception in the request handler for \"%s %s\":\n\n%s\n\nThis exception has been gracefully handled as a 500 response, however, it's strongly recommended to resolve this error, as it indicates a mistake in your code. If you wish to mock an error response, please see this guide: https://mswjs.io/docs/http/mocking-responses/error-responses`, request.method, request.url, error2.stack ?? error2);\n        messageChannel.postMessage(\"MOCK_RESPONSE\", {\n          status: 500,\n          statusText: \"Request Handler Error\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            name: error2.name,\n            message: error2.message,\n            stack: error2.stack\n          })\n        });\n      }\n    }\n  };\n};\n\n// src/browser/utils/checkWorkerIntegrity.ts\nimport { devUtils as devUtils5 } from '../core/utils/internal/devUtils.mjs';\nasync function checkWorkerIntegrity(context) {\n  context.workerChannel.send(\"INTEGRITY_CHECK_REQUEST\");\n  const {\n    payload\n  } = await context.events.once(\"INTEGRITY_CHECK_RESPONSE\");\n  if (payload.checksum !== \"f5825c521429caf22a4dd13b66e243af\") {\n    devUtils5.warn(`The currently registered Service Worker has been generated by a different version of MSW (${payload.packageVersion}) and may not be fully compatible with the installed version.\n\nIt's recommended you update your worker script by running this command:\n\n  \\u2022 npx msw init <PUBLIC_DIR>\n\nYou can also automate this process and make the worker script update automatically upon the library installations. Read more: https://mswjs.io/docs/cli/init.`);\n  }\n}\n\n// node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/lib/browser/chunk-6HYIRFX2.mjs\nvar encoder = new TextEncoder();\nfunction encodeBuffer(text) {\n  return encoder.encode(text);\n}\nfunction decodeBuffer(buffer, encoding) {\n  const decoder = new TextDecoder(encoding);\n  return decoder.decode(buffer);\n}\nfunction toArrayBuffer(array) {\n  return array.buffer.slice(array.byteOffset, array.byteOffset + array.byteLength);\n}\n\n// node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/lib/browser/chunk-3RXCRGL2.mjs\nvar IS_PATCHED_MODULE = Symbol(\"isPatchedModule\");\nfunction canParseUrl(url) {\n  try {\n    new URL(url);\n    return true;\n  } catch (_error) {\n    return false;\n  }\n}\nfunction getValueBySymbol(symbolName, source) {\n  const ownSymbols = Object.getOwnPropertySymbols(source);\n  const symbol = ownSymbols.find(symbol2 => {\n    return symbol2.description === symbolName;\n  });\n  if (symbol) {\n    return Reflect.get(source, symbol);\n  }\n  return;\n}\nvar _FetchResponse = class extends Response {\n  static isConfigurableStatusCode(status) {\n    return status >= 200 && status <= 599;\n  }\n  static isRedirectResponse(status) {\n    return _FetchResponse.STATUS_CODES_WITH_REDIRECT.includes(status);\n  }\n  /**\n   * Returns a boolean indicating whether the given response status\n   * code represents a response that can have a body.\n   */\n  static isResponseWithBody(status) {\n    return !_FetchResponse.STATUS_CODES_WITHOUT_BODY.includes(status);\n  }\n  static setUrl(url, response) {\n    if (!url || url === \"about:\" || !canParseUrl(url)) {\n      return;\n    }\n    const state = getValueBySymbol(\"state\", response);\n    if (state) {\n      state.urlList.push(new URL(url));\n    } else {\n      Object.defineProperty(response, \"url\", {\n        value: url,\n        enumerable: true,\n        configurable: true,\n        writable: false\n      });\n    }\n  }\n  /**\n   * Parses the given raw HTTP headers into a Fetch API `Headers` instance.\n   */\n  static parseRawHeaders(rawHeaders) {\n    const headers = new Headers();\n    for (let line = 0; line < rawHeaders.length; line += 2) {\n      headers.append(rawHeaders[line], rawHeaders[line + 1]);\n    }\n    return headers;\n  }\n  constructor(body, init = {}) {\n    var _a;\n    const status = (_a = init.status) != null ? _a : 200;\n    const safeStatus = _FetchResponse.isConfigurableStatusCode(status) ? status : 200;\n    const finalBody = _FetchResponse.isResponseWithBody(status) ? body : null;\n    super(finalBody, {\n      status: safeStatus,\n      statusText: init.statusText,\n      headers: init.headers\n    });\n    if (status !== safeStatus) {\n      const state = getValueBySymbol(\"state\", this);\n      if (state) {\n        state.status = status;\n      } else {\n        Object.defineProperty(this, \"status\", {\n          value: status,\n          enumerable: true,\n          configurable: true,\n          writable: false\n        });\n      }\n    }\n    _FetchResponse.setUrl(init.url, this);\n  }\n};\nvar FetchResponse = _FetchResponse;\nFetchResponse.STATUS_CODES_WITHOUT_BODY = [101, 103, 204, 205, 304];\nFetchResponse.STATUS_CODES_WITH_REDIRECT = [301, 302, 303, 307, 308];\nvar kRawRequest = Symbol(\"kRawRequest\");\nfunction setRawRequest(request, rawRequest) {\n  Reflect.set(request, kRawRequest, rawRequest);\n}\n\n// node_modules/.pnpm/@open-draft+logger@0.3.0/node_modules/@open-draft/logger/lib/index.mjs\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar colors_exports = {};\n__export(colors_exports, {\n  blue: () => blue,\n  gray: () => gray,\n  green: () => green,\n  red: () => red,\n  yellow: () => yellow\n});\nfunction yellow(text) {\n  return `\\x1B[33m${text}\\x1B[0m`;\n}\nfunction blue(text) {\n  return `\\x1B[34m${text}\\x1B[0m`;\n}\nfunction gray(text) {\n  return `\\x1B[90m${text}\\x1B[0m`;\n}\nfunction red(text) {\n  return `\\x1B[31m${text}\\x1B[0m`;\n}\nfunction green(text) {\n  return `\\x1B[32m${text}\\x1B[0m`;\n}\nvar IS_NODE = isNodeProcess();\nvar Logger = class {\n  constructor(name) {\n    this.name = name;\n    this.prefix = `[${this.name}]`;\n    const LOGGER_NAME = getVariable(\"DEBUG\");\n    const LOGGER_LEVEL = getVariable(\"LOG_LEVEL\");\n    const isLoggingEnabled = LOGGER_NAME === \"1\" || LOGGER_NAME === \"true\" || typeof LOGGER_NAME !== \"undefined\" && this.name.startsWith(LOGGER_NAME);\n    if (isLoggingEnabled) {\n      this.debug = isDefinedAndNotEquals(LOGGER_LEVEL, \"debug\") ? noop : this.debug;\n      this.info = isDefinedAndNotEquals(LOGGER_LEVEL, \"info\") ? noop : this.info;\n      this.success = isDefinedAndNotEquals(LOGGER_LEVEL, \"success\") ? noop : this.success;\n      this.warning = isDefinedAndNotEquals(LOGGER_LEVEL, \"warning\") ? noop : this.warning;\n      this.error = isDefinedAndNotEquals(LOGGER_LEVEL, \"error\") ? noop : this.error;\n    } else {\n      this.info = noop;\n      this.success = noop;\n      this.warning = noop;\n      this.error = noop;\n      this.only = noop;\n    }\n  }\n  prefix;\n  extend(domain) {\n    return new Logger(`${this.name}:${domain}`);\n  }\n  /**\n   * Print a debug message.\n   * @example\n   * logger.debug('no duplicates found, creating a document...')\n   */\n  debug(message, ...positionals) {\n    this.logEntry({\n      level: \"debug\",\n      message: gray(message),\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"gray\"\n      }\n    });\n  }\n  /**\n   * Print an info message.\n   * @example\n   * logger.info('start parsing...')\n   */\n  info(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"blue\"\n      }\n    });\n    const performance2 = new PerformanceEntry();\n    return (message2, ...positionals2) => {\n      performance2.measure();\n      this.logEntry({\n        level: \"info\",\n        message: `${message2} ${gray(`${performance2.deltaTime}ms`)}`,\n        positionals: positionals2,\n        prefix: this.prefix,\n        colors: {\n          prefix: \"blue\"\n        }\n      });\n    };\n  }\n  /**\n   * Print a success message.\n   * @example\n   * logger.success('successfully created document')\n   */\n  success(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: `\\u2714 ${this.prefix}`,\n      colors: {\n        timestamp: \"green\",\n        prefix: \"green\"\n      }\n    });\n  }\n  /**\n   * Print a warning.\n   * @example\n   * logger.warning('found legacy document format')\n   */\n  warning(message, ...positionals) {\n    this.logEntry({\n      level: \"warning\",\n      message,\n      positionals,\n      prefix: `\\u26A0 ${this.prefix}`,\n      colors: {\n        timestamp: \"yellow\",\n        prefix: \"yellow\"\n      }\n    });\n  }\n  /**\n   * Print an error message.\n   * @example\n   * logger.error('something went wrong')\n   */\n  error(message, ...positionals) {\n    this.logEntry({\n      level: \"error\",\n      message,\n      positionals,\n      prefix: `\\u2716 ${this.prefix}`,\n      colors: {\n        timestamp: \"red\",\n        prefix: \"red\"\n      }\n    });\n  }\n  /**\n   * Execute the given callback only when the logging is enabled.\n   * This is skipped in its entirety and has no runtime cost otherwise.\n   * This executes regardless of the log level.\n   * @example\n   * logger.only(() => {\n   *   logger.info('additional info')\n   * })\n   */\n  only(callback) {\n    callback();\n  }\n  createEntry(level, message) {\n    return {\n      timestamp: /* @__PURE__ */new Date(),\n      level,\n      message\n    };\n  }\n  logEntry(args) {\n    const {\n      level,\n      message,\n      prefix,\n      colors: customColors,\n      positionals = []\n    } = args;\n    const entry = this.createEntry(level, message);\n    const timestampColor = customColors?.timestamp || \"gray\";\n    const prefixColor = customColors?.prefix || \"gray\";\n    const colorize = {\n      timestamp: colors_exports[timestampColor],\n      prefix: colors_exports[prefixColor]\n    };\n    const write = this.getWriter(level);\n    write([colorize.timestamp(this.formatTimestamp(entry.timestamp))].concat(prefix != null ? colorize.prefix(prefix) : []).concat(serializeInput(message)).join(\" \"), ...positionals.map(serializeInput));\n  }\n  formatTimestamp(timestamp) {\n    return `${timestamp.toLocaleTimeString(\"en-GB\")}:${timestamp.getMilliseconds()}`;\n  }\n  getWriter(level) {\n    switch (level) {\n      case \"debug\":\n      case \"success\":\n      case \"info\":\n        {\n          return log;\n        }\n      case \"warning\":\n        {\n          return warn;\n        }\n      case \"error\":\n        {\n          return error;\n        }\n    }\n  }\n};\nvar PerformanceEntry = class {\n  startTime;\n  endTime;\n  deltaTime;\n  constructor() {\n    this.startTime = performance.now();\n  }\n  measure() {\n    this.endTime = performance.now();\n    const deltaTime = this.endTime - this.startTime;\n    this.deltaTime = deltaTime.toFixed(2);\n  }\n};\nvar noop = () => void 0;\nfunction log(message, ...positionals) {\n  if (IS_NODE) {\n    process.stdout.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.log(message, ...positionals);\n}\nfunction warn(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.warn(message, ...positionals);\n}\nfunction error(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.error(message, ...positionals);\n}\nfunction getVariable(variableName) {\n  if (IS_NODE) {\n    return process.env[variableName];\n  }\n  return globalThis[variableName]?.toString();\n}\nfunction isDefinedAndNotEquals(value, expected) {\n  return value !== void 0 && value !== expected;\n}\nfunction serializeInput(message) {\n  if (typeof message === \"undefined\") {\n    return \"undefined\";\n  }\n  if (message === null) {\n    return \"null\";\n  }\n  if (typeof message === \"string\") {\n    return message;\n  }\n  if (typeof message === \"object\") {\n    return JSON.stringify(message);\n  }\n  return message.toString();\n}\n\n// node_modules/.pnpm/strict-event-emitter@0.5.1/node_modules/strict-event-emitter/lib/index.mjs\nvar MemoryLeakError = class extends Error {\n  constructor(emitter, type, count) {\n    super(`Possible EventEmitter memory leak detected. ${count} ${type.toString()} listeners added. Use emitter.setMaxListeners() to increase limit`);\n    this.emitter = emitter;\n    this.type = type;\n    this.count = count;\n    this.name = \"MaxListenersExceededWarning\";\n  }\n};\nvar _Emitter = class {\n  static listenerCount(emitter, eventName) {\n    return emitter.listenerCount(eventName);\n  }\n  constructor() {\n    this.events = /* @__PURE__ */new Map();\n    this.maxListeners = _Emitter.defaultMaxListeners;\n    this.hasWarnedAboutPotentialMemoryLeak = false;\n  }\n  _emitInternalEvent(internalEventName, eventName, listener) {\n    this.emit(internalEventName, ...[eventName, listener]);\n  }\n  _getListeners(eventName) {\n    return Array.prototype.concat.apply([], this.events.get(eventName)) || [];\n  }\n  _removeListener(listeners, listener) {\n    const index = listeners.indexOf(listener);\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n    return [];\n  }\n  _wrapOnceListener(eventName, listener) {\n    const onceListener = (...data) => {\n      this.removeListener(eventName, onceListener);\n      return listener.apply(this, data);\n    };\n    Object.defineProperty(onceListener, \"name\", {\n      value: listener.name\n    });\n    return onceListener;\n  }\n  setMaxListeners(maxListeners) {\n    this.maxListeners = maxListeners;\n    return this;\n  }\n  /**\n   * Returns the current max listener value for the `Emitter` which is\n   * either set by `emitter.setMaxListeners(n)` or defaults to\n   * `Emitter.defaultMaxListeners`.\n   */\n  getMaxListeners() {\n    return this.maxListeners;\n  }\n  /**\n   * Returns an array listing the events for which the emitter has registered listeners.\n   * The values in the array will be strings or Symbols.\n   */\n  eventNames() {\n    return Array.from(this.events.keys());\n  }\n  /**\n   * Synchronously calls each of the listeners registered for the event named `eventName`,\n   * in the order they were registered, passing the supplied arguments to each.\n   * Returns `true` if the event has listeners, `false` otherwise.\n   *\n   * @example\n   * const emitter = new Emitter<{ hello: [string] }>()\n   * emitter.emit('hello', 'John')\n   */\n  emit(eventName, ...data) {\n    const listeners = this._getListeners(eventName);\n    listeners.forEach(listener => {\n      listener.apply(this, data);\n    });\n    return listeners.length > 0;\n  }\n  addListener(eventName, listener) {\n    this._emitInternalEvent(\"newListener\", eventName, listener);\n    const nextListeners = this._getListeners(eventName).concat(listener);\n    this.events.set(eventName, nextListeners);\n    if (this.maxListeners > 0 && this.listenerCount(eventName) > this.maxListeners && !this.hasWarnedAboutPotentialMemoryLeak) {\n      this.hasWarnedAboutPotentialMemoryLeak = true;\n      const memoryLeakWarning = new MemoryLeakError(this, eventName, this.listenerCount(eventName));\n      console.warn(memoryLeakWarning);\n    }\n    return this;\n  }\n  on(eventName, listener) {\n    return this.addListener(eventName, listener);\n  }\n  once(eventName, listener) {\n    return this.addListener(eventName, this._wrapOnceListener(eventName, listener));\n  }\n  prependListener(eventName, listener) {\n    const listeners = this._getListeners(eventName);\n    if (listeners.length > 0) {\n      const nextListeners = [listener].concat(listeners);\n      this.events.set(eventName, nextListeners);\n    } else {\n      this.events.set(eventName, listeners.concat(listener));\n    }\n    return this;\n  }\n  prependOnceListener(eventName, listener) {\n    return this.prependListener(eventName, this._wrapOnceListener(eventName, listener));\n  }\n  removeListener(eventName, listener) {\n    const listeners = this._getListeners(eventName);\n    if (listeners.length > 0) {\n      this._removeListener(listeners, listener);\n      this.events.set(eventName, listeners);\n      this._emitInternalEvent(\"removeListener\", eventName, listener);\n    }\n    return this;\n  }\n  /**\n   * Alias for `emitter.removeListener()`.\n   *\n   * @example\n   * emitter.off('hello', listener)\n   */\n  off(eventName, listener) {\n    return this.removeListener(eventName, listener);\n  }\n  removeAllListeners(eventName) {\n    if (eventName) {\n      this.events.delete(eventName);\n    } else {\n      this.events.clear();\n    }\n    return this;\n  }\n  /**\n   * Returns a copy of the array of listeners for the event named `eventName`.\n   */\n  listeners(eventName) {\n    return Array.from(this._getListeners(eventName));\n  }\n  /**\n   * Returns the number of listeners listening to the event named `eventName`.\n   */\n  listenerCount(eventName) {\n    return this._getListeners(eventName).length;\n  }\n  rawListeners(eventName) {\n    return this.listeners(eventName);\n  }\n};\nvar Emitter = _Emitter;\nEmitter.defaultMaxListeners = 10;\n\n// node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/lib/browser/chunk-QED3Q6Z2.mjs\nvar INTERNAL_REQUEST_ID_HEADER_NAME = \"x-interceptors-internal-request-id\";\nfunction getGlobalSymbol(symbol) {\n  return (\n    // @ts-ignore https://github.com/Microsoft/TypeScript/issues/24587\n    globalThis[symbol] || void 0\n  );\n}\nfunction setGlobalSymbol(symbol, value) {\n  globalThis[symbol] = value;\n}\nfunction deleteGlobalSymbol(symbol) {\n  delete globalThis[symbol];\n}\nvar Interceptor = class {\n  constructor(symbol) {\n    this.symbol = symbol;\n    this.readyState = \"INACTIVE\";\n    this.emitter = new Emitter();\n    this.subscriptions = [];\n    this.logger = new Logger(symbol.description);\n    this.emitter.setMaxListeners(0);\n    this.logger.info(\"constructing the interceptor...\");\n  }\n  /**\n   * Determine if this interceptor can be applied\n   * in the current environment.\n   */\n  checkEnvironment() {\n    return true;\n  }\n  /**\n   * Apply this interceptor to the current process.\n   * Returns an already running interceptor instance if it's present.\n   */\n  apply() {\n    const logger = this.logger.extend(\"apply\");\n    logger.info(\"applying the interceptor...\");\n    if (this.readyState === \"APPLIED\") {\n      logger.info(\"intercepted already applied!\");\n      return;\n    }\n    const shouldApply = this.checkEnvironment();\n    if (!shouldApply) {\n      logger.info(\"the interceptor cannot be applied in this environment!\");\n      return;\n    }\n    this.readyState = \"APPLYING\";\n    const runningInstance = this.getInstance();\n    if (runningInstance) {\n      logger.info(\"found a running instance, reusing...\");\n      this.on = (event, listener) => {\n        logger.info('proxying the \"%s\" listener', event);\n        runningInstance.emitter.addListener(event, listener);\n        this.subscriptions.push(() => {\n          runningInstance.emitter.removeListener(event, listener);\n          logger.info('removed proxied \"%s\" listener!', event);\n        });\n        return this;\n      };\n      this.readyState = \"APPLIED\";\n      return;\n    }\n    logger.info(\"no running instance found, setting up a new instance...\");\n    this.setup();\n    this.setInstance();\n    this.readyState = \"APPLIED\";\n  }\n  /**\n   * Setup the module augments and stubs necessary for this interceptor.\n   * This method is not run if there's a running interceptor instance\n   * to prevent instantiating an interceptor multiple times.\n   */\n  setup() {}\n  /**\n   * Listen to the interceptor's public events.\n   */\n  on(event, listener) {\n    const logger = this.logger.extend(\"on\");\n    if (this.readyState === \"DISPOSING\" || this.readyState === \"DISPOSED\") {\n      logger.info(\"cannot listen to events, already disposed!\");\n      return this;\n    }\n    logger.info('adding \"%s\" event listener:', event, listener);\n    this.emitter.on(event, listener);\n    return this;\n  }\n  once(event, listener) {\n    this.emitter.once(event, listener);\n    return this;\n  }\n  off(event, listener) {\n    this.emitter.off(event, listener);\n    return this;\n  }\n  removeAllListeners(event) {\n    this.emitter.removeAllListeners(event);\n    return this;\n  }\n  /**\n   * Disposes of any side-effects this interceptor has introduced.\n   */\n  dispose() {\n    const logger = this.logger.extend(\"dispose\");\n    if (this.readyState === \"DISPOSED\") {\n      logger.info(\"cannot dispose, already disposed!\");\n      return;\n    }\n    logger.info(\"disposing the interceptor...\");\n    this.readyState = \"DISPOSING\";\n    if (!this.getInstance()) {\n      logger.info(\"no interceptors running, skipping dispose...\");\n      return;\n    }\n    this.clearInstance();\n    logger.info(\"global symbol deleted:\", getGlobalSymbol(this.symbol));\n    if (this.subscriptions.length > 0) {\n      logger.info(\"disposing of %d subscriptions...\", this.subscriptions.length);\n      for (const dispose of this.subscriptions) {\n        dispose();\n      }\n      this.subscriptions = [];\n      logger.info(\"disposed of all subscriptions!\", this.subscriptions.length);\n    }\n    this.emitter.removeAllListeners();\n    logger.info(\"destroyed the listener!\");\n    this.readyState = \"DISPOSED\";\n  }\n  getInstance() {\n    var _a;\n    const instance = getGlobalSymbol(this.symbol);\n    this.logger.info(\"retrieved global instance:\", (_a = instance == null ? void 0 : instance.constructor) == null ? void 0 : _a.name);\n    return instance;\n  }\n  setInstance() {\n    setGlobalSymbol(this.symbol, this);\n    this.logger.info(\"set global instance!\", this.symbol.description);\n  }\n  clearInstance() {\n    deleteGlobalSymbol(this.symbol);\n    this.logger.info(\"cleared global instance!\", this.symbol.description);\n  }\n};\nfunction createRequestId() {\n  return Math.random().toString(16).slice(2);\n}\n\n// node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/lib/browser/index.mjs\nvar BatchInterceptor = class extends Interceptor {\n  constructor(options) {\n    BatchInterceptor.symbol = Symbol(options.name);\n    super(BatchInterceptor.symbol);\n    this.interceptors = options.interceptors;\n  }\n  setup() {\n    const logger = this.logger.extend(\"setup\");\n    logger.info(\"applying all %d interceptors...\", this.interceptors.length);\n    for (const interceptor of this.interceptors) {\n      logger.info('applying \"%s\" interceptor...', interceptor.constructor.name);\n      interceptor.apply();\n      logger.info(\"adding interceptor dispose subscription\");\n      this.subscriptions.push(() => interceptor.dispose());\n    }\n  }\n  on(event, listener) {\n    for (const interceptor of this.interceptors) {\n      interceptor.on(event, listener);\n    }\n    return this;\n  }\n  once(event, listener) {\n    for (const interceptor of this.interceptors) {\n      interceptor.once(event, listener);\n    }\n    return this;\n  }\n  off(event, listener) {\n    for (const interceptor of this.interceptors) {\n      interceptor.off(event, listener);\n    }\n    return this;\n  }\n  removeAllListeners(event) {\n    for (const interceptors of this.interceptors) {\n      interceptors.removeAllListeners(event);\n    }\n    return this;\n  }\n};\n\n// src/browser/setupWorker/start/createResponseListener.ts\nfunction createResponseListener(context) {\n  return (_, message) => {\n    const {\n      payload: responseJson\n    } = message;\n    const request = deserializeRequest(responseJson.request);\n    if (responseJson.response.type?.includes(\"opaque\")) {\n      return;\n    }\n    const response = responseJson.response.status === 0 ? Response.error() : new FetchResponse(\n    /**\n     * Responses may be streams here, but when we create a response object\n     * with null-body status codes, like 204, 205, 304 Response will\n     * throw when passed a non-null body, so ensure it's null here\n     * for those codes\n     */\n    FetchResponse.isResponseWithBody(responseJson.response.status) ? responseJson.response.body : null, {\n      ...responseJson,\n      /**\n       * Set response URL if it's not set already.\n       * @see https://github.com/mswjs/msw/issues/2030\n       * @see https://developer.mozilla.org/en-US/docs/Web/API/Response/url\n       */\n      url: request.url\n    });\n    context.emitter.emit(responseJson.isMockedResponse ? \"response:mocked\" : \"response:bypass\", {\n      requestId: responseJson.request.id,\n      request,\n      response\n    });\n  };\n}\n\n// src/browser/setupWorker/start/utils/validateWorkerScope.ts\nimport { devUtils as devUtils6 } from '../core/utils/internal/devUtils.mjs';\nfunction validateWorkerScope(registration, options) {\n  if (!options?.quiet && !location.href.startsWith(registration.scope)) {\n    devUtils6.warn(`Cannot intercept requests on this page because it's outside of the worker's scope (\"${registration.scope}\"). If you wish to mock API requests on this page, you must resolve this scope issue.\n\n- (Recommended) Register the worker at the root level (\"/\") of your application.\n- Set the \"Service-Worker-Allowed\" response header to allow out-of-scope workers.`);\n  }\n}\n\n// src/browser/setupWorker/start/createStartHandler.ts\nvar createStartHandler = context => {\n  return function start(options, customOptions) {\n    const startWorkerInstance = async () => {\n      context.events.removeAllListeners();\n      context.workerChannel.on(\"REQUEST\", createRequestListener(context, options));\n      context.workerChannel.on(\"RESPONSE\", createResponseListener(context));\n      const instance = await getWorkerInstance(options.serviceWorker.url, options.serviceWorker.options, options.findWorker);\n      const [worker, registration] = instance;\n      if (!worker) {\n        const missingWorkerMessage = customOptions?.findWorker ? devUtils7.formatMessage(`Failed to locate the Service Worker registration using a custom \"findWorker\" predicate.\n\nPlease ensure that the custom predicate properly locates the Service Worker registration at \"%s\".\nMore details: https://mswjs.io/docs/api/setup-worker/start#findworker\n`, options.serviceWorker.url) : devUtils7.formatMessage(`Failed to locate the Service Worker registration.\n\nThis most likely means that the worker script URL \"%s\" cannot resolve against the actual public hostname (%s). This may happen if your application runs behind a proxy, or has a dynamic hostname.\n\nPlease consider using a custom \"serviceWorker.url\" option to point to the actual worker script location, or a custom \"findWorker\" option to resolve the Service Worker registration manually. More details: https://mswjs.io/docs/api/setup-worker/start`, options.serviceWorker.url, location.host);\n        throw new Error(missingWorkerMessage);\n      }\n      context.worker = worker;\n      context.registration = registration;\n      context.events.addListener(window, \"beforeunload\", () => {\n        if (worker.state !== \"redundant\") {\n          context.workerChannel.send(\"CLIENT_CLOSED\");\n        }\n        window.clearInterval(context.keepAliveInterval);\n        window.postMessage({\n          type: \"msw/worker:stop\"\n        });\n      });\n      await checkWorkerIntegrity(context).catch(error2 => {\n        devUtils7.error(\"Error while checking the worker script integrity. Please report this on GitHub (https://github.com/mswjs/msw/issues), including the original error below.\");\n        console.error(error2);\n      });\n      context.keepAliveInterval = window.setInterval(() => context.workerChannel.send(\"KEEPALIVE_REQUEST\"), 5e3);\n      validateWorkerScope(registration, context.startOptions);\n      return registration;\n    };\n    const workerRegistration = startWorkerInstance().then(async registration => {\n      const pendingInstance = registration.installing || registration.waiting;\n      if (pendingInstance) {\n        await new Promise(resolve => {\n          pendingInstance.addEventListener(\"statechange\", () => {\n            if (pendingInstance.state === \"activated\") {\n              return resolve();\n            }\n          });\n        });\n      }\n      await enableMocking(context, options).catch(error2 => {\n        throw new Error(`Failed to enable mocking: ${error2?.message}`);\n      });\n      return registration;\n    });\n    return workerRegistration;\n  };\n};\n\n// src/browser/setupWorker/stop/createStop.ts\nimport { devUtils as devUtils9 } from '../core/utils/internal/devUtils.mjs';\n\n// src/browser/setupWorker/stop/utils/printStopMessage.ts\nimport { devUtils as devUtils8 } from '../core/utils/internal/devUtils.mjs';\nfunction printStopMessage(args = {}) {\n  if (args.quiet) {\n    return;\n  }\n  console.log(`%c${devUtils8.formatMessage(\"Mocking disabled.\")}`, \"color:orangered;font-weight:bold;\");\n}\n\n// src/browser/setupWorker/stop/createStop.ts\nvar createStop = context => {\n  return function stop() {\n    if (!context.isMockingEnabled) {\n      devUtils9.warn('Found a redundant \"worker.stop()\" call. Note that stopping the worker while mocking already stopped has no effect. Consider removing this \"worker.stop()\" call.');\n      return;\n    }\n    context.workerChannel.send(\"MOCK_DEACTIVATE\");\n    context.isMockingEnabled = false;\n    window.clearInterval(context.keepAliveInterval);\n    window.postMessage({\n      type: \"msw/worker:stop\"\n    });\n    printStopMessage({\n      quiet: context.startOptions?.quiet\n    });\n  };\n};\n\n// src/browser/setupWorker/start/utils/prepareStartHandler.ts\nimport { mergeRight } from '../core/utils/internal/mergeRight.mjs';\nvar DEFAULT_START_OPTIONS = {\n  serviceWorker: {\n    url: \"/mockServiceWorker.js\",\n    options: null\n  },\n  quiet: false,\n  waitUntilReady: true,\n  onUnhandledRequest: \"warn\",\n  findWorker(scriptURL, mockServiceWorkerUrl) {\n    return scriptURL === mockServiceWorkerUrl;\n  }\n};\n\n// node_modules/.pnpm/@open-draft+deferred-promise@2.2.0/node_modules/@open-draft/deferred-promise/build/index.mjs\nfunction createDeferredExecutor() {\n  const executor = (resolve, reject) => {\n    executor.state = \"pending\";\n    executor.resolve = data => {\n      if (executor.state !== \"pending\") {\n        return;\n      }\n      executor.result = data;\n      const onFulfilled = value => {\n        executor.state = \"fulfilled\";\n        return value;\n      };\n      return resolve(data instanceof Promise ? data : Promise.resolve(data).then(onFulfilled));\n    };\n    executor.reject = reason => {\n      if (executor.state !== \"pending\") {\n        return;\n      }\n      queueMicrotask(() => {\n        executor.state = \"rejected\";\n      });\n      return reject(executor.rejectionReason = reason);\n    };\n  };\n  return executor;\n}\nvar DeferredPromise = class extends Promise {\n  #executor;\n  resolve;\n  reject;\n  constructor(executor = null) {\n    const deferredExecutor = createDeferredExecutor();\n    super((originalResolve, originalReject) => {\n      deferredExecutor(originalResolve, originalReject);\n      executor?.(deferredExecutor.resolve, deferredExecutor.reject);\n    });\n    this.#executor = deferredExecutor;\n    this.resolve = this.#executor.resolve;\n    this.reject = this.#executor.reject;\n  }\n  get state() {\n    return this.#executor.state;\n  }\n  get rejectionReason() {\n    return this.#executor.rejectionReason;\n  }\n  then(onFulfilled, onRejected) {\n    return this.#decorate(super.then(onFulfilled, onRejected));\n  }\n  catch(onRejected) {\n    return this.#decorate(super.catch(onRejected));\n  }\n  finally(onfinally) {\n    return this.#decorate(super.finally(onfinally));\n  }\n  #decorate(promise) {\n    return Object.defineProperties(promise, {\n      resolve: {\n        configurable: true,\n        value: this.resolve\n      },\n      reject: {\n        configurable: true,\n        value: this.reject\n      }\n    });\n  }\n};\n\n// node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/lib/browser/chunk-L37TY7LC.mjs\nvar InterceptorError = class extends Error {\n  constructor(message) {\n    super(message);\n    this.name = \"InterceptorError\";\n    Object.setPrototypeOf(this, InterceptorError.prototype);\n  }\n};\nvar kRequestHandled = Symbol(\"kRequestHandled\");\nvar kResponsePromise = Symbol(\"kResponsePromise\");\nvar RequestController = class {\n  constructor(request) {\n    this.request = request;\n    this[kRequestHandled] = false;\n    this[kResponsePromise] = new DeferredPromise();\n  }\n  /**\n   * Respond to this request with the given `Response` instance.\n   * @example\n   * controller.respondWith(new Response())\n   * controller.respondWith(Response.json({ id }))\n   * controller.respondWith(Response.error())\n   */\n  respondWith(response) {\n    invariant.as(InterceptorError, !this[kRequestHandled], 'Failed to respond to the \"%s %s\" request: the \"request\" event has already been handled.', this.request.method, this.request.url);\n    this[kRequestHandled] = true;\n    this[kResponsePromise].resolve(response);\n  }\n  /**\n   * Error this request with the given reason.\n   *\n   * @example\n   * controller.errorWith()\n   * controller.errorWith(new Error('Oops!'))\n   * controller.errorWith({ message: 'Oops!'})\n   */\n  errorWith(reason) {\n    invariant.as(InterceptorError, !this[kRequestHandled], 'Failed to error the \"%s %s\" request: the \"request\" event has already been handled.', this.request.method, this.request.url);\n    this[kRequestHandled] = true;\n    this[kResponsePromise].resolve(reason);\n  }\n};\nasync function emitAsync(emitter, eventName, ...data) {\n  const listners = emitter.listeners(eventName);\n  if (listners.length === 0) {\n    return;\n  }\n  for (const listener of listners) {\n    await listener.apply(emitter, data);\n  }\n}\nfunction isObject(value, loose = false) {\n  return loose ? Object.prototype.toString.call(value).startsWith(\"[object \") : Object.prototype.toString.call(value) === \"[object Object]\";\n}\nfunction isPropertyAccessible(obj, key) {\n  try {\n    obj[key];\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction createServerErrorResponse(body) {\n  return new Response(JSON.stringify(body instanceof Error ? {\n    name: body.name,\n    message: body.message,\n    stack: body.stack\n  } : body), {\n    status: 500,\n    statusText: \"Unhandled Exception\",\n    headers: {\n      \"Content-Type\": \"application/json\"\n    }\n  });\n}\nfunction isResponseError(response) {\n  return response != null && response instanceof Response && isPropertyAccessible(response, \"type\") && response.type === \"error\";\n}\nfunction isResponseLike(value) {\n  return isObject(value, true) && isPropertyAccessible(value, \"status\") && isPropertyAccessible(value, \"statusText\") && isPropertyAccessible(value, \"bodyUsed\");\n}\nfunction isNodeLikeError(error2) {\n  if (error2 == null) {\n    return false;\n  }\n  if (!(error2 instanceof Error)) {\n    return false;\n  }\n  return \"code\" in error2 && \"errno\" in error2;\n}\nasync function handleRequest2(options) {\n  const handleResponse = async response => {\n    if (response instanceof Error) {\n      options.onError(response);\n      return true;\n    }\n    if (isResponseError(response)) {\n      options.onRequestError(response);\n      return true;\n    }\n    if (isResponseLike(response)) {\n      await options.onResponse(response);\n      return true;\n    }\n    if (isObject(response)) {\n      options.onError(response);\n      return true;\n    }\n    return false;\n  };\n  const handleResponseError = async error2 => {\n    if (error2 instanceof InterceptorError) {\n      throw result.error;\n    }\n    if (isNodeLikeError(error2)) {\n      options.onError(error2);\n      return true;\n    }\n    if (error2 instanceof Response) {\n      return await handleResponse(error2);\n    }\n    return false;\n  };\n  options.emitter.once(\"request\", ({\n    requestId: pendingRequestId\n  }) => {\n    if (pendingRequestId !== options.requestId) {\n      return;\n    }\n    if (options.controller[kResponsePromise].state === \"pending\") {\n      options.controller[kResponsePromise].resolve(void 0);\n    }\n  });\n  const requestAbortPromise = new DeferredPromise();\n  if (options.request.signal) {\n    if (options.request.signal.aborted) {\n      requestAbortPromise.reject(options.request.signal.reason);\n    } else {\n      options.request.signal.addEventListener(\"abort\", () => {\n        requestAbortPromise.reject(options.request.signal.reason);\n      }, {\n        once: true\n      });\n    }\n  }\n  const result = await until(async () => {\n    const requestListenersPromise = emitAsync(options.emitter, \"request\", {\n      requestId: options.requestId,\n      request: options.request,\n      controller: options.controller\n    });\n    await Promise.race([\n    // Short-circuit the request handling promise if the request gets aborted.\n    requestAbortPromise, requestListenersPromise, options.controller[kResponsePromise]]);\n    return await options.controller[kResponsePromise];\n  });\n  if (requestAbortPromise.state === \"rejected\") {\n    options.onError(requestAbortPromise.rejectionReason);\n    return true;\n  }\n  if (result.error) {\n    if (await handleResponseError(result.error)) {\n      return true;\n    }\n    if (options.emitter.listenerCount(\"unhandledException\") > 0) {\n      const unhandledExceptionController = new RequestController(options.request);\n      await emitAsync(options.emitter, \"unhandledException\", {\n        error: result.error,\n        request: options.request,\n        requestId: options.requestId,\n        controller: unhandledExceptionController\n      }).then(() => {\n        if (unhandledExceptionController[kResponsePromise].state === \"pending\") {\n          unhandledExceptionController[kResponsePromise].resolve(void 0);\n        }\n      });\n      const nextResult = await until(() => unhandledExceptionController[kResponsePromise]);\n      if (nextResult.error) {\n        return handleResponseError(nextResult.error);\n      }\n      if (nextResult.data) {\n        return handleResponse(nextResult.data);\n      }\n    }\n    options.onResponse(createServerErrorResponse(result.error));\n    return true;\n  }\n  if (result.data) {\n    return handleResponse(result.data);\n  }\n  return false;\n}\n\n// node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/lib/browser/chunk-TX5GBTFY.mjs\nfunction hasConfigurableGlobal(propertyName) {\n  const descriptor = Object.getOwnPropertyDescriptor(globalThis, propertyName);\n  if (typeof descriptor === \"undefined\") {\n    return false;\n  }\n  if (typeof descriptor.get === \"function\" && typeof descriptor.get() === \"undefined\") {\n    return false;\n  }\n  if (typeof descriptor.get === \"undefined\" && descriptor.value == null) {\n    return false;\n  }\n  if (typeof descriptor.set === \"undefined\" && !descriptor.configurable) {\n    console.error(`[MSW] Failed to apply interceptor: the global \\`${propertyName}\\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`);\n    return false;\n  }\n  return true;\n}\n\n// node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/lib/browser/chunk-ARPHZXGT.mjs\nfunction createNetworkError(cause) {\n  return Object.assign(new TypeError(\"Failed to fetch\"), {\n    cause\n  });\n}\nvar REQUEST_BODY_HEADERS = [\"content-encoding\", \"content-language\", \"content-location\", \"content-type\", \"content-length\"];\nvar kRedirectCount = Symbol(\"kRedirectCount\");\nasync function followFetchRedirect(request, response) {\n  if (response.status !== 303 && request.body != null) {\n    return Promise.reject(createNetworkError());\n  }\n  const requestUrl = new URL(request.url);\n  let locationUrl;\n  try {\n    locationUrl = new URL(response.headers.get(\"location\"), request.url);\n  } catch (error2) {\n    return Promise.reject(createNetworkError(error2));\n  }\n  if (!(locationUrl.protocol === \"http:\" || locationUrl.protocol === \"https:\")) {\n    return Promise.reject(createNetworkError(\"URL scheme must be a HTTP(S) scheme\"));\n  }\n  if (Reflect.get(request, kRedirectCount) > 20) {\n    return Promise.reject(createNetworkError(\"redirect count exceeded\"));\n  }\n  Object.defineProperty(request, kRedirectCount, {\n    value: (Reflect.get(request, kRedirectCount) || 0) + 1\n  });\n  if (request.mode === \"cors\" && (locationUrl.username || locationUrl.password) && !sameOrigin(requestUrl, locationUrl)) {\n    return Promise.reject(createNetworkError('cross origin not allowed for request mode \"cors\"'));\n  }\n  const requestInit = {};\n  if ([301, 302].includes(response.status) && request.method === \"POST\" || response.status === 303 && ![\"HEAD\", \"GET\"].includes(request.method)) {\n    requestInit.method = \"GET\";\n    requestInit.body = null;\n    REQUEST_BODY_HEADERS.forEach(headerName => {\n      request.headers.delete(headerName);\n    });\n  }\n  if (!sameOrigin(requestUrl, locationUrl)) {\n    request.headers.delete(\"authorization\");\n    request.headers.delete(\"proxy-authorization\");\n    request.headers.delete(\"cookie\");\n    request.headers.delete(\"host\");\n  }\n  requestInit.headers = request.headers;\n  return fetch(new Request(locationUrl, requestInit));\n}\nfunction sameOrigin(left, right) {\n  if (left.origin === right.origin && left.origin === \"null\") {\n    return true;\n  }\n  if (left.protocol === right.protocol && left.hostname === right.hostname && left.port === right.port) {\n    return true;\n  }\n  return false;\n}\nvar BrotliDecompressionStream = class extends TransformStream {\n  constructor() {\n    console.warn(\"[Interceptors]: Brotli decompression of response streams is not supported in the browser\");\n    super({\n      transform(chunk, controller) {\n        controller.enqueue(chunk);\n      }\n    });\n  }\n};\nvar PipelineStream = class extends TransformStream {\n  constructor(transformStreams, ...strategies) {\n    super({}, ...strategies);\n    const readable = [super.readable, ...transformStreams].reduce((readable2, transform) => readable2.pipeThrough(transform));\n    Object.defineProperty(this, \"readable\", {\n      get() {\n        return readable;\n      }\n    });\n  }\n};\nfunction parseContentEncoding(contentEncoding) {\n  return contentEncoding.toLowerCase().split(\",\").map(coding => coding.trim());\n}\nfunction createDecompressionStream(contentEncoding) {\n  if (contentEncoding === \"\") {\n    return null;\n  }\n  const codings = parseContentEncoding(contentEncoding);\n  if (codings.length === 0) {\n    return null;\n  }\n  const transformers = codings.reduceRight((transformers2, coding) => {\n    if (coding === \"gzip\" || coding === \"x-gzip\") {\n      return transformers2.concat(new DecompressionStream(\"gzip\"));\n    } else if (coding === \"deflate\") {\n      return transformers2.concat(new DecompressionStream(\"deflate\"));\n    } else if (coding === \"br\") {\n      return transformers2.concat(new BrotliDecompressionStream());\n    } else {\n      transformers2.length = 0;\n    }\n    return transformers2;\n  }, []);\n  return new PipelineStream(transformers);\n}\nfunction decompressResponse(response) {\n  if (response.body === null) {\n    return null;\n  }\n  const decompressionStream = createDecompressionStream(response.headers.get(\"content-encoding\") || \"\");\n  if (!decompressionStream) {\n    return null;\n  }\n  response.body.pipeTo(decompressionStream.writable);\n  return decompressionStream.readable;\n}\nvar _FetchInterceptor = class extends Interceptor {\n  constructor() {\n    super(_FetchInterceptor.symbol);\n  }\n  checkEnvironment() {\n    return hasConfigurableGlobal(\"fetch\");\n  }\n  async setup() {\n    const pureFetch = globalThis.fetch;\n    invariant(!pureFetch[IS_PATCHED_MODULE], 'Failed to patch the \"fetch\" module: already patched.');\n    globalThis.fetch = async (input, init) => {\n      const requestId = createRequestId();\n      const resolvedInput = typeof input === \"string\" && typeof location !== \"undefined\" && !canParseUrl(input) ? new URL(input, location.href) : input;\n      const request = new Request(resolvedInput, init);\n      if (input instanceof Request) {\n        setRawRequest(request, input);\n      }\n      const responsePromise = new DeferredPromise();\n      const controller = new RequestController(request);\n      this.logger.info(\"[%s] %s\", request.method, request.url);\n      this.logger.info(\"awaiting for the mocked response...\");\n      this.logger.info('emitting the \"request\" event for %s listener(s)...', this.emitter.listenerCount(\"request\"));\n      const isRequestHandled = await handleRequest2({\n        request,\n        requestId,\n        emitter: this.emitter,\n        controller,\n        onResponse: async rawResponse => {\n          this.logger.info(\"received mocked response!\", {\n            rawResponse\n          });\n          const decompressedStream = decompressResponse(rawResponse);\n          const response = decompressedStream === null ? rawResponse : new FetchResponse(decompressedStream, rawResponse);\n          FetchResponse.setUrl(request.url, response);\n          if (FetchResponse.isRedirectResponse(response.status)) {\n            if (request.redirect === \"error\") {\n              responsePromise.reject(createNetworkError(\"unexpected redirect\"));\n              return;\n            }\n            if (request.redirect === \"follow\") {\n              followFetchRedirect(request, response).then(response2 => {\n                responsePromise.resolve(response2);\n              }, reason => {\n                responsePromise.reject(reason);\n              });\n              return;\n            }\n          }\n          if (this.emitter.listenerCount(\"response\") > 0) {\n            this.logger.info('emitting the \"response\" event...');\n            await emitAsync(this.emitter, \"response\", {\n              // Clone the mocked response for the \"response\" event listener.\n              // This way, the listener can read the response and not lock its body\n              // for the actual fetch consumer.\n              response: response.clone(),\n              isMockedResponse: true,\n              request,\n              requestId\n            });\n          }\n          responsePromise.resolve(response);\n        },\n        onRequestError: response => {\n          this.logger.info(\"request has errored!\", {\n            response\n          });\n          responsePromise.reject(createNetworkError(response));\n        },\n        onError: error2 => {\n          this.logger.info(\"request has been aborted!\", {\n            error: error2\n          });\n          responsePromise.reject(error2);\n        }\n      });\n      if (isRequestHandled) {\n        this.logger.info(\"request has been handled, returning mock promise...\");\n        return responsePromise;\n      }\n      this.logger.info(\"no mocked response received, performing request as-is...\");\n      const requestCloneForResponseEvent = request.clone();\n      return pureFetch(request).then(async response => {\n        this.logger.info(\"original fetch performed\", response);\n        if (this.emitter.listenerCount(\"response\") > 0) {\n          this.logger.info('emitting the \"response\" event...');\n          const responseClone = response.clone();\n          await emitAsync(this.emitter, \"response\", {\n            response: responseClone,\n            isMockedResponse: false,\n            request: requestCloneForResponseEvent,\n            requestId\n          });\n        }\n        return response;\n      });\n    };\n    Object.defineProperty(globalThis.fetch, IS_PATCHED_MODULE, {\n      enumerable: true,\n      configurable: true,\n      value: true\n    });\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis.fetch, IS_PATCHED_MODULE, {\n        value: void 0\n      });\n      globalThis.fetch = pureFetch;\n      this.logger.info('restored native \"globalThis.fetch\"!', globalThis.fetch.name);\n    });\n  }\n};\nvar FetchInterceptor = _FetchInterceptor;\nFetchInterceptor.symbol = Symbol(\"fetch\");\n\n// node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/lib/browser/chunk-QKSBFQDK.mjs\nfunction concatArrayBuffer(left, right) {\n  const result = new Uint8Array(left.byteLength + right.byteLength);\n  result.set(left, 0);\n  result.set(right, left.byteLength);\n  return result;\n}\nvar EventPolyfill = class {\n  constructor(type, options) {\n    this.NONE = 0;\n    this.CAPTURING_PHASE = 1;\n    this.AT_TARGET = 2;\n    this.BUBBLING_PHASE = 3;\n    this.type = \"\";\n    this.srcElement = null;\n    this.currentTarget = null;\n    this.eventPhase = 0;\n    this.isTrusted = true;\n    this.composed = false;\n    this.cancelable = true;\n    this.defaultPrevented = false;\n    this.bubbles = true;\n    this.lengthComputable = true;\n    this.loaded = 0;\n    this.total = 0;\n    this.cancelBubble = false;\n    this.returnValue = true;\n    this.type = type;\n    this.target = (options == null ? void 0 : options.target) || null;\n    this.currentTarget = (options == null ? void 0 : options.currentTarget) || null;\n    this.timeStamp = Date.now();\n  }\n  composedPath() {\n    return [];\n  }\n  initEvent(type, bubbles, cancelable) {\n    this.type = type;\n    this.bubbles = !!bubbles;\n    this.cancelable = !!cancelable;\n  }\n  preventDefault() {\n    this.defaultPrevented = true;\n  }\n  stopPropagation() {}\n  stopImmediatePropagation() {}\n};\nvar ProgressEventPolyfill = class extends EventPolyfill {\n  constructor(type, init) {\n    super(type);\n    this.lengthComputable = (init == null ? void 0 : init.lengthComputable) || false;\n    this.composed = (init == null ? void 0 : init.composed) || false;\n    this.loaded = (init == null ? void 0 : init.loaded) || 0;\n    this.total = (init == null ? void 0 : init.total) || 0;\n  }\n};\nvar SUPPORTS_PROGRESS_EVENT = typeof ProgressEvent !== \"undefined\";\nfunction createEvent(target, type, init) {\n  const progressEvents = [\"error\", \"progress\", \"loadstart\", \"loadend\", \"load\", \"timeout\", \"abort\"];\n  const ProgressEventClass = SUPPORTS_PROGRESS_EVENT ? ProgressEvent : ProgressEventPolyfill;\n  const event = progressEvents.includes(type) ? new ProgressEventClass(type, {\n    lengthComputable: true,\n    loaded: (init == null ? void 0 : init.loaded) || 0,\n    total: (init == null ? void 0 : init.total) || 0\n  }) : new EventPolyfill(type, {\n    target,\n    currentTarget: target\n  });\n  return event;\n}\nfunction findPropertySource(target, propertyName) {\n  if (!(propertyName in target)) {\n    return null;\n  }\n  const hasProperty = Object.prototype.hasOwnProperty.call(target, propertyName);\n  if (hasProperty) {\n    return target;\n  }\n  const prototype = Reflect.getPrototypeOf(target);\n  return prototype ? findPropertySource(prototype, propertyName) : null;\n}\nfunction createProxy(target, options) {\n  const proxy = new Proxy(target, optionsToProxyHandler(options));\n  return proxy;\n}\nfunction optionsToProxyHandler(options) {\n  const {\n    constructorCall,\n    methodCall,\n    getProperty,\n    setProperty\n  } = options;\n  const handler = {};\n  if (typeof constructorCall !== \"undefined\") {\n    handler.construct = function (target, args, newTarget) {\n      const next = Reflect.construct.bind(null, target, args, newTarget);\n      return constructorCall.call(newTarget, args, next);\n    };\n  }\n  handler.set = function (target, propertyName, nextValue) {\n    const next = () => {\n      const propertySource = findPropertySource(target, propertyName) || target;\n      const ownDescriptors = Reflect.getOwnPropertyDescriptor(propertySource, propertyName);\n      if (typeof (ownDescriptors == null ? void 0 : ownDescriptors.set) !== \"undefined\") {\n        ownDescriptors.set.apply(target, [nextValue]);\n        return true;\n      }\n      return Reflect.defineProperty(propertySource, propertyName, {\n        writable: true,\n        enumerable: true,\n        configurable: true,\n        value: nextValue\n      });\n    };\n    if (typeof setProperty !== \"undefined\") {\n      return setProperty.call(target, [propertyName, nextValue], next);\n    }\n    return next();\n  };\n  handler.get = function (target, propertyName, receiver) {\n    const next = () => target[propertyName];\n    const value = typeof getProperty !== \"undefined\" ? getProperty.call(target, [propertyName, receiver], next) : next();\n    if (typeof value === \"function\") {\n      return (...args) => {\n        const next2 = value.bind(target, ...args);\n        if (typeof methodCall !== \"undefined\") {\n          return methodCall.call(target, [propertyName, args], next2);\n        }\n        return next2();\n      };\n    }\n    return value;\n  };\n  return handler;\n}\nfunction isDomParserSupportedType(type) {\n  const supportedTypes = [\"application/xhtml+xml\", \"application/xml\", \"image/svg+xml\", \"text/html\", \"text/xml\"];\n  return supportedTypes.some(supportedType => {\n    return type.startsWith(supportedType);\n  });\n}\nfunction parseJson(data) {\n  try {\n    const json = JSON.parse(data);\n    return json;\n  } catch (_) {\n    return null;\n  }\n}\nfunction createResponse(request, body) {\n  const responseBodyOrNull = FetchResponse.isResponseWithBody(request.status) ? body : null;\n  return new FetchResponse(responseBodyOrNull, {\n    url: request.responseURL,\n    status: request.status,\n    statusText: request.statusText,\n    headers: createHeadersFromXMLHttpReqestHeaders(request.getAllResponseHeaders())\n  });\n}\nfunction createHeadersFromXMLHttpReqestHeaders(headersString) {\n  const headers = new Headers();\n  const lines = headersString.split(/[\\r\\n]+/);\n  for (const line of lines) {\n    if (line.trim() === \"\") {\n      continue;\n    }\n    const [name, ...parts] = line.split(\": \");\n    const value = parts.join(\": \");\n    headers.append(name, value);\n  }\n  return headers;\n}\nasync function getBodyByteLength(input) {\n  const explicitContentLength = input.headers.get(\"content-length\");\n  if (explicitContentLength != null && explicitContentLength !== \"\") {\n    return Number(explicitContentLength);\n  }\n  const buffer = await input.arrayBuffer();\n  return buffer.byteLength;\n}\nvar kIsRequestHandled = Symbol(\"kIsRequestHandled\");\nvar IS_NODE2 = isNodeProcess();\nvar kFetchRequest = Symbol(\"kFetchRequest\");\nvar XMLHttpRequestController = class {\n  constructor(initialRequest, logger) {\n    this.initialRequest = initialRequest;\n    this.logger = logger;\n    this.method = \"GET\";\n    this.url = null;\n    this[kIsRequestHandled] = false;\n    this.events = /* @__PURE__ */new Map();\n    this.uploadEvents = /* @__PURE__ */new Map();\n    this.requestId = createRequestId();\n    this.requestHeaders = new Headers();\n    this.responseBuffer = new Uint8Array();\n    this.request = createProxy(initialRequest, {\n      setProperty: ([propertyName, nextValue], invoke) => {\n        switch (propertyName) {\n          case \"ontimeout\":\n            {\n              const eventName = propertyName.slice(2);\n              this.request.addEventListener(eventName, nextValue);\n              return invoke();\n            }\n          default:\n            {\n              return invoke();\n            }\n        }\n      },\n      methodCall: ([methodName, args], invoke) => {\n        var _a;\n        switch (methodName) {\n          case \"open\":\n            {\n              const [method, url] = args;\n              if (typeof url === \"undefined\") {\n                this.method = \"GET\";\n                this.url = toAbsoluteUrl(method);\n              } else {\n                this.method = method;\n                this.url = toAbsoluteUrl(url);\n              }\n              this.logger = this.logger.extend(`${this.method} ${this.url.href}`);\n              this.logger.info(\"open\", this.method, this.url.href);\n              return invoke();\n            }\n          case \"addEventListener\":\n            {\n              const [eventName, listener] = args;\n              this.registerEvent(eventName, listener);\n              this.logger.info(\"addEventListener\", eventName, listener);\n              return invoke();\n            }\n          case \"setRequestHeader\":\n            {\n              const [name, value] = args;\n              this.requestHeaders.set(name, value);\n              this.logger.info(\"setRequestHeader\", name, value);\n              return invoke();\n            }\n          case \"send\":\n            {\n              const [body] = args;\n              this.request.addEventListener(\"load\", () => {\n                if (typeof this.onResponse !== \"undefined\") {\n                  const fetchResponse = createResponse(this.request,\n                  /**\n                   * The `response` property is the right way to read\n                   * the ambiguous response body, as the request's \"responseType\" may differ.\n                   * @see https://xhr.spec.whatwg.org/#the-response-attribute\n                   */\n                  this.request.response);\n                  this.onResponse.call(this, {\n                    response: fetchResponse,\n                    isMockedResponse: this[kIsRequestHandled],\n                    request: fetchRequest,\n                    requestId: this.requestId\n                  });\n                }\n              });\n              const requestBody = typeof body === \"string\" ? encodeBuffer(body) : body;\n              const fetchRequest = this.toFetchApiRequest(requestBody);\n              this[kFetchRequest] = fetchRequest.clone();\n              const onceRequestSettled = ((_a = this.onRequest) == null ? void 0 : _a.call(this, {\n                request: fetchRequest,\n                requestId: this.requestId\n              })) || Promise.resolve();\n              onceRequestSettled.finally(() => {\n                if (!this[kIsRequestHandled]) {\n                  this.logger.info(\"request callback settled but request has not been handled (readystate %d), performing as-is...\", this.request.readyState);\n                  if (IS_NODE2) {\n                    this.request.setRequestHeader(INTERNAL_REQUEST_ID_HEADER_NAME, this.requestId);\n                  }\n                  return invoke();\n                }\n              });\n              break;\n            }\n          default:\n            {\n              return invoke();\n            }\n        }\n      }\n    });\n    define(this.request, \"upload\", createProxy(this.request.upload, {\n      setProperty: ([propertyName, nextValue], invoke) => {\n        switch (propertyName) {\n          case \"onloadstart\":\n          case \"onprogress\":\n          case \"onaboart\":\n          case \"onerror\":\n          case \"onload\":\n          case \"ontimeout\":\n          case \"onloadend\":\n            {\n              const eventName = propertyName.slice(2);\n              this.registerUploadEvent(eventName, nextValue);\n            }\n        }\n        return invoke();\n      },\n      methodCall: ([methodName, args], invoke) => {\n        switch (methodName) {\n          case \"addEventListener\":\n            {\n              const [eventName, listener] = args;\n              this.registerUploadEvent(eventName, listener);\n              this.logger.info(\"upload.addEventListener\", eventName, listener);\n              return invoke();\n            }\n        }\n      }\n    }));\n  }\n  registerEvent(eventName, listener) {\n    const prevEvents = this.events.get(eventName) || [];\n    const nextEvents = prevEvents.concat(listener);\n    this.events.set(eventName, nextEvents);\n    this.logger.info('registered event \"%s\"', eventName, listener);\n  }\n  registerUploadEvent(eventName, listener) {\n    const prevEvents = this.uploadEvents.get(eventName) || [];\n    const nextEvents = prevEvents.concat(listener);\n    this.uploadEvents.set(eventName, nextEvents);\n    this.logger.info('registered upload event \"%s\"', eventName, listener);\n  }\n  /**\n   * Responds to the current request with the given\n   * Fetch API `Response` instance.\n   */\n  async respondWith(response) {\n    this[kIsRequestHandled] = true;\n    if (this[kFetchRequest]) {\n      const totalRequestBodyLength = await getBodyByteLength(this[kFetchRequest]);\n      this.trigger(\"loadstart\", this.request.upload, {\n        loaded: 0,\n        total: totalRequestBodyLength\n      });\n      this.trigger(\"progress\", this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength\n      });\n      this.trigger(\"load\", this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength\n      });\n      this.trigger(\"loadend\", this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength\n      });\n    }\n    this.logger.info(\"responding with a mocked response: %d %s\", response.status, response.statusText);\n    define(this.request, \"status\", response.status);\n    define(this.request, \"statusText\", response.statusText);\n    define(this.request, \"responseURL\", this.url.href);\n    this.request.getResponseHeader = new Proxy(this.request.getResponseHeader, {\n      apply: (_, __, args) => {\n        this.logger.info(\"getResponseHeader\", args[0]);\n        if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n          this.logger.info(\"headers not received yet, returning null\");\n          return null;\n        }\n        const headerValue = response.headers.get(args[0]);\n        this.logger.info('resolved response header \"%s\" to', args[0], headerValue);\n        return headerValue;\n      }\n    });\n    this.request.getAllResponseHeaders = new Proxy(this.request.getAllResponseHeaders, {\n      apply: () => {\n        this.logger.info(\"getAllResponseHeaders\");\n        if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n          this.logger.info(\"headers not received yet, returning empty string\");\n          return \"\";\n        }\n        const headersList = Array.from(response.headers.entries());\n        const allHeaders = headersList.map(([headerName, headerValue]) => {\n          return `${headerName}: ${headerValue}`;\n        }).join(\"\\r\\n\");\n        this.logger.info(\"resolved all response headers to\", allHeaders);\n        return allHeaders;\n      }\n    });\n    Object.defineProperties(this.request, {\n      response: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.response\n      },\n      responseText: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseText\n      },\n      responseXML: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseXML\n      }\n    });\n    const totalResponseBodyLength = await getBodyByteLength(response.clone());\n    this.logger.info(\"calculated response body length\", totalResponseBodyLength);\n    this.trigger(\"loadstart\", this.request, {\n      loaded: 0,\n      total: totalResponseBodyLength\n    });\n    this.setReadyState(this.request.HEADERS_RECEIVED);\n    this.setReadyState(this.request.LOADING);\n    const finalizeResponse = () => {\n      this.logger.info(\"finalizing the mocked response...\");\n      this.setReadyState(this.request.DONE);\n      this.trigger(\"load\", this.request, {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength\n      });\n      this.trigger(\"loadend\", this.request, {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength\n      });\n    };\n    if (response.body) {\n      this.logger.info(\"mocked response has body, streaming...\");\n      const reader = response.body.getReader();\n      const readNextResponseBodyChunk = async () => {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) {\n          this.logger.info(\"response body stream done!\");\n          finalizeResponse();\n          return;\n        }\n        if (value) {\n          this.logger.info(\"read response body chunk:\", value);\n          this.responseBuffer = concatArrayBuffer(this.responseBuffer, value);\n          this.trigger(\"progress\", this.request, {\n            loaded: this.responseBuffer.byteLength,\n            total: totalResponseBodyLength\n          });\n        }\n        readNextResponseBodyChunk();\n      };\n      readNextResponseBodyChunk();\n    } else {\n      finalizeResponse();\n    }\n  }\n  responseBufferToText() {\n    return decodeBuffer(this.responseBuffer);\n  }\n  get response() {\n    this.logger.info(\"getResponse (responseType: %s)\", this.request.responseType);\n    if (this.request.readyState !== this.request.DONE) {\n      return null;\n    }\n    switch (this.request.responseType) {\n      case \"json\":\n        {\n          const responseJson = parseJson(this.responseBufferToText());\n          this.logger.info(\"resolved response JSON\", responseJson);\n          return responseJson;\n        }\n      case \"arraybuffer\":\n        {\n          const arrayBuffer = toArrayBuffer(this.responseBuffer);\n          this.logger.info(\"resolved response ArrayBuffer\", arrayBuffer);\n          return arrayBuffer;\n        }\n      case \"blob\":\n        {\n          const mimeType = this.request.getResponseHeader(\"Content-Type\") || \"text/plain\";\n          const responseBlob = new Blob([this.responseBufferToText()], {\n            type: mimeType\n          });\n          this.logger.info(\"resolved response Blob (mime type: %s)\", responseBlob, mimeType);\n          return responseBlob;\n        }\n      default:\n        {\n          const responseText = this.responseBufferToText();\n          this.logger.info('resolving \"%s\" response type as text', this.request.responseType, responseText);\n          return responseText;\n        }\n    }\n  }\n  get responseText() {\n    invariant(this.request.responseType === \"\" || this.request.responseType === \"text\", \"InvalidStateError: The object is in invalid state.\");\n    if (this.request.readyState !== this.request.LOADING && this.request.readyState !== this.request.DONE) {\n      return \"\";\n    }\n    const responseText = this.responseBufferToText();\n    this.logger.info('getResponseText: \"%s\"', responseText);\n    return responseText;\n  }\n  get responseXML() {\n    invariant(this.request.responseType === \"\" || this.request.responseType === \"document\", \"InvalidStateError: The object is in invalid state.\");\n    if (this.request.readyState !== this.request.DONE) {\n      return null;\n    }\n    const contentType = this.request.getResponseHeader(\"Content-Type\") || \"\";\n    if (typeof DOMParser === \"undefined\") {\n      console.warn(\"Cannot retrieve XMLHttpRequest response body as XML: DOMParser is not defined. You are likely using an environment that is not browser or does not polyfill browser globals correctly.\");\n      return null;\n    }\n    if (isDomParserSupportedType(contentType)) {\n      return new DOMParser().parseFromString(this.responseBufferToText(), contentType);\n    }\n    return null;\n  }\n  errorWith(error2) {\n    this[kIsRequestHandled] = true;\n    this.logger.info(\"responding with an error\");\n    this.setReadyState(this.request.DONE);\n    this.trigger(\"error\", this.request);\n    this.trigger(\"loadend\", this.request);\n  }\n  /**\n   * Transitions this request's `readyState` to the given one.\n   */\n  setReadyState(nextReadyState) {\n    this.logger.info(\"setReadyState: %d -> %d\", this.request.readyState, nextReadyState);\n    if (this.request.readyState === nextReadyState) {\n      this.logger.info(\"ready state identical, skipping transition...\");\n      return;\n    }\n    define(this.request, \"readyState\", nextReadyState);\n    this.logger.info(\"set readyState to: %d\", nextReadyState);\n    if (nextReadyState !== this.request.UNSENT) {\n      this.logger.info('triggerring \"readystatechange\" event...');\n      this.trigger(\"readystatechange\", this.request);\n    }\n  }\n  /**\n   * Triggers given event on the `XMLHttpRequest` instance.\n   */\n  trigger(eventName, target, options) {\n    const callback = target[`on${eventName}`];\n    const event = createEvent(target, eventName, options);\n    this.logger.info('trigger \"%s\"', eventName, options || \"\");\n    if (typeof callback === \"function\") {\n      this.logger.info('found a direct \"%s\" callback, calling...', eventName);\n      callback.call(target, event);\n    }\n    const events = target instanceof XMLHttpRequestUpload ? this.uploadEvents : this.events;\n    for (const [registeredEventName, listeners] of events) {\n      if (registeredEventName === eventName) {\n        this.logger.info('found %d listener(s) for \"%s\" event, calling...', listeners.length, eventName);\n        listeners.forEach(listener => listener.call(target, event));\n      }\n    }\n  }\n  /**\n   * Converts this `XMLHttpRequest` instance into a Fetch API `Request` instance.\n   */\n  toFetchApiRequest(body) {\n    this.logger.info(\"converting request to a Fetch API Request...\");\n    const resolvedBody = body instanceof Document ? body.documentElement.innerText : body;\n    const fetchRequest = new Request(this.url.href, {\n      method: this.method,\n      headers: this.requestHeaders,\n      /**\n       * @see https://xhr.spec.whatwg.org/#cross-origin-credentials\n       */\n      credentials: this.request.withCredentials ? \"include\" : \"same-origin\",\n      body: [\"GET\", \"HEAD\"].includes(this.method.toUpperCase()) ? null : resolvedBody\n    });\n    const proxyHeaders = createProxy(fetchRequest.headers, {\n      methodCall: ([methodName, args], invoke) => {\n        switch (methodName) {\n          case \"append\":\n          case \"set\":\n            {\n              const [headerName, headerValue] = args;\n              this.request.setRequestHeader(headerName, headerValue);\n              break;\n            }\n          case \"delete\":\n            {\n              const [headerName] = args;\n              console.warn(`XMLHttpRequest: Cannot remove a \"${headerName}\" header from the Fetch API representation of the \"${fetchRequest.method} ${fetchRequest.url}\" request. XMLHttpRequest headers cannot be removed.`);\n              break;\n            }\n        }\n        return invoke();\n      }\n    });\n    define(fetchRequest, \"headers\", proxyHeaders);\n    setRawRequest(fetchRequest, this.request);\n    this.logger.info(\"converted request to a Fetch API Request!\", fetchRequest);\n    return fetchRequest;\n  }\n};\nfunction toAbsoluteUrl(url) {\n  if (typeof location === \"undefined\") {\n    return new URL(url);\n  }\n  return new URL(url.toString(), location.href);\n}\nfunction define(target, property, value) {\n  Reflect.defineProperty(target, property, {\n    // Ensure writable properties to allow redefining readonly properties.\n    writable: true,\n    enumerable: true,\n    value\n  });\n}\nfunction createXMLHttpRequestProxy({\n  emitter,\n  logger\n}) {\n  const XMLHttpRequestProxy = new Proxy(globalThis.XMLHttpRequest, {\n    construct(target, args, newTarget) {\n      logger.info(\"constructed new XMLHttpRequest\");\n      const originalRequest = Reflect.construct(target, args, newTarget);\n      const prototypeDescriptors = Object.getOwnPropertyDescriptors(target.prototype);\n      for (const propertyName in prototypeDescriptors) {\n        Reflect.defineProperty(originalRequest, propertyName, prototypeDescriptors[propertyName]);\n      }\n      const xhrRequestController = new XMLHttpRequestController(originalRequest, logger);\n      xhrRequestController.onRequest = async function ({\n        request,\n        requestId\n      }) {\n        const controller = new RequestController(request);\n        this.logger.info(\"awaiting mocked response...\");\n        this.logger.info('emitting the \"request\" event for %s listener(s)...', emitter.listenerCount(\"request\"));\n        const isRequestHandled = await handleRequest2({\n          request,\n          requestId,\n          controller,\n          emitter,\n          onResponse: async response => {\n            await this.respondWith(response);\n          },\n          onRequestError: () => {\n            this.errorWith(new TypeError(\"Network error\"));\n          },\n          onError: error2 => {\n            this.logger.info(\"request errored!\", {\n              error: error2\n            });\n            if (error2 instanceof Error) {\n              this.errorWith(error2);\n            }\n          }\n        });\n        if (!isRequestHandled) {\n          this.logger.info(\"no mocked response received, performing request as-is...\");\n        }\n      };\n      xhrRequestController.onResponse = async function ({\n        response,\n        isMockedResponse,\n        request,\n        requestId\n      }) {\n        this.logger.info('emitting the \"response\" event for %s listener(s)...', emitter.listenerCount(\"response\"));\n        emitter.emit(\"response\", {\n          response,\n          isMockedResponse,\n          request,\n          requestId\n        });\n      };\n      return xhrRequestController.request;\n    }\n  });\n  return XMLHttpRequestProxy;\n}\nvar _XMLHttpRequestInterceptor = class extends Interceptor {\n  constructor() {\n    super(_XMLHttpRequestInterceptor.interceptorSymbol);\n  }\n  checkEnvironment() {\n    return hasConfigurableGlobal(\"XMLHttpRequest\");\n  }\n  setup() {\n    const logger = this.logger.extend(\"setup\");\n    logger.info('patching \"XMLHttpRequest\" module...');\n    const PureXMLHttpRequest = globalThis.XMLHttpRequest;\n    invariant(!PureXMLHttpRequest[IS_PATCHED_MODULE], 'Failed to patch the \"XMLHttpRequest\" module: already patched.');\n    globalThis.XMLHttpRequest = createXMLHttpRequestProxy({\n      emitter: this.emitter,\n      logger: this.logger\n    });\n    logger.info('native \"XMLHttpRequest\" module patched!', globalThis.XMLHttpRequest.name);\n    Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n      enumerable: true,\n      configurable: true,\n      value: true\n    });\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n        value: void 0\n      });\n      globalThis.XMLHttpRequest = PureXMLHttpRequest;\n      logger.info('native \"XMLHttpRequest\" module restored!', globalThis.XMLHttpRequest.name);\n    });\n  }\n};\nvar XMLHttpRequestInterceptor = _XMLHttpRequestInterceptor;\nXMLHttpRequestInterceptor.interceptorSymbol = Symbol(\"xhr\");\n\n// src/browser/setupWorker/start/createFallbackRequestListener.ts\nimport { handleRequest as handleRequest3 } from '../core/utils/handleRequest.mjs';\nimport { isHandlerKind as isHandlerKind2 } from '../core/utils/internal/isHandlerKind.mjs';\nfunction createFallbackRequestListener(context, options) {\n  const interceptor = new BatchInterceptor({\n    name: \"fallback\",\n    interceptors: [new FetchInterceptor(), new XMLHttpRequestInterceptor()]\n  });\n  interceptor.on(\"request\", async ({\n    request,\n    requestId,\n    controller\n  }) => {\n    const requestCloneForLogs = request.clone();\n    const response = await handleRequest3(request, requestId, context.getRequestHandlers().filter(isHandlerKind2(\"RequestHandler\")), options, context.emitter, {\n      onMockedResponse(_, {\n        handler,\n        parsedResult\n      }) {\n        if (!options.quiet) {\n          context.emitter.once(\"response:mocked\", ({\n            response: response2\n          }) => {\n            handler.log({\n              request: requestCloneForLogs,\n              response: response2,\n              parsedResult\n            });\n          });\n        }\n      }\n    });\n    if (response) {\n      controller.respondWith(response);\n    }\n  });\n  interceptor.on(\"response\", ({\n    response,\n    isMockedResponse,\n    request,\n    requestId\n  }) => {\n    context.emitter.emit(isMockedResponse ? \"response:mocked\" : \"response:bypass\", {\n      response,\n      request,\n      requestId\n    });\n  });\n  interceptor.apply();\n  return interceptor;\n}\n\n// src/browser/setupWorker/start/createFallbackStart.ts\nfunction createFallbackStart(context) {\n  return async function start(options) {\n    context.fallbackInterceptor = createFallbackRequestListener(context, options);\n    printStartMessage({\n      message: \"Mocking enabled (fallback mode).\",\n      quiet: options.quiet\n    });\n    return void 0;\n  };\n}\n\n// src/browser/setupWorker/stop/createFallbackStop.ts\nfunction createFallbackStop(context) {\n  return function stop() {\n    context.fallbackInterceptor?.dispose();\n    printStopMessage({\n      quiet: context.startOptions?.quiet\n    });\n  };\n}\n\n// src/browser/setupWorker/setupWorker.ts\nimport { devUtils as devUtils10 } from '../core/utils/internal/devUtils.mjs';\nimport { SetupApi } from '../core/SetupApi.mjs';\nimport { mergeRight as mergeRight2 } from '../core/utils/internal/mergeRight.mjs';\n\n// src/browser/utils/supportsReadableStreamTransfer.ts\nfunction supportsReadableStreamTransfer() {\n  try {\n    const stream = new ReadableStream({\n      start: controller => controller.close()\n    });\n    const message = new MessageChannel();\n    message.port1.postMessage(stream, [stream]);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n// src/browser/setupWorker/setupWorker.ts\nimport { webSocketInterceptor } from '../core/ws/webSocketInterceptor.mjs';\nimport { handleWebSocketEvent } from '../core/ws/handleWebSocketEvent.mjs';\nimport { attachWebSocketLogger } from '../core/ws/utils/attachWebSocketLogger.mjs';\nvar SetupWorkerApi = class extends SetupApi {\n  context;\n  startHandler = null;\n  stopHandler = null;\n  listeners;\n  constructor(...handlers) {\n    super(...handlers);\n    invariant(!isNodeProcess(), devUtils10.formatMessage(\"Failed to execute `setupWorker` in a non-browser environment. Consider using `setupServer` for Node.js environment instead.\"));\n    this.listeners = [];\n    this.context = this.createWorkerContext();\n  }\n  createWorkerContext() {\n    const context = {\n      // Mocking is not considered enabled until the worker\n      // signals back the successful activation event.\n      isMockingEnabled: false,\n      startOptions: null,\n      worker: null,\n      getRequestHandlers: () => {\n        return this.handlersController.currentHandlers();\n      },\n      registration: null,\n      emitter: this.emitter,\n      workerChannel: {\n        on: (eventType, callback) => {\n          this.context.events.addListener(navigator.serviceWorker, \"message\", event => {\n            if (event.source !== this.context.worker) {\n              return;\n            }\n            const message = event.data;\n            if (!message) {\n              return;\n            }\n            if (message.type === eventType) {\n              callback(event, message);\n            }\n          });\n        },\n        send: type => {\n          this.context.worker?.postMessage(type);\n        }\n      },\n      events: {\n        addListener: (target, eventType, callback) => {\n          target.addEventListener(eventType, callback);\n          this.listeners.push({\n            eventType,\n            target,\n            callback\n          });\n          return () => {\n            target.removeEventListener(eventType, callback);\n          };\n        },\n        removeAllListeners: () => {\n          for (const {\n            target,\n            eventType,\n            callback\n          } of this.listeners) {\n            target.removeEventListener(eventType, callback);\n          }\n          this.listeners = [];\n        },\n        once: eventType => {\n          const bindings = [];\n          return new Promise((resolve, reject) => {\n            const handleIncomingMessage = event => {\n              try {\n                const message = event.data;\n                if (message.type === eventType) {\n                  resolve(message);\n                }\n              } catch (error2) {\n                reject(error2);\n              }\n            };\n            bindings.push(this.context.events.addListener(navigator.serviceWorker, \"message\", handleIncomingMessage), this.context.events.addListener(navigator.serviceWorker, \"messageerror\", reject));\n          }).finally(() => {\n            bindings.forEach(unbind => unbind());\n          });\n        }\n      },\n      supports: {\n        serviceWorkerApi: !(\"serviceWorker\" in navigator) || location.protocol === \"file:\",\n        readableStreamTransfer: supportsReadableStreamTransfer()\n      }\n    };\n    this.startHandler = context.supports.serviceWorkerApi ? createFallbackStart(context) : createStartHandler(context);\n    this.stopHandler = context.supports.serviceWorkerApi ? createFallbackStop(context) : createStop(context);\n    return context;\n  }\n  async start(options = {}) {\n    if (options.waitUntilReady === true) {\n      devUtils10.warn('The \"waitUntilReady\" option has been deprecated. Please remove it from this \"worker.start()\" call. Follow the recommended Browser integration (https://mswjs.io/docs/integrations/browser) to eliminate any race conditions between the Service Worker registration and any requests made by your application on initial render.');\n    }\n    this.context.startOptions = mergeRight2(DEFAULT_START_OPTIONS, options);\n    handleWebSocketEvent({\n      getUnhandledRequestStrategy: () => {\n        return this.context.startOptions.onUnhandledRequest;\n      },\n      getHandlers: () => {\n        return this.handlersController.currentHandlers();\n      },\n      onMockedConnection: connection => {\n        if (!this.context.startOptions.quiet) {\n          attachWebSocketLogger(connection);\n        }\n      },\n      onPassthroughConnection() {}\n    });\n    webSocketInterceptor.apply();\n    this.subscriptions.push(() => {\n      webSocketInterceptor.dispose();\n    });\n    return await this.startHandler(this.context.startOptions, options);\n  }\n  stop() {\n    super.dispose();\n    this.context.events.removeAllListeners();\n    this.context.emitter.removeAllListeners();\n    this.stopHandler();\n  }\n};\nfunction setupWorker(...handlers) {\n  return new SetupWorkerApi(...handlers);\n}\nexport { SetupWorkerApi, setupWorker };", "map": {"version": 3, "names": ["POSITIONALS_EXP", "serializePositional", "positional", "flag", "Number", "JSON", "stringify", "json", "test", "format", "message", "positionals", "length", "positionalIndex", "formattedMessage", "replace", "match", "isEscaped", "_", "value", "slice", "join", "STACK_FRAMES_TO_IGNORE", "cleanErrorStack", "error2", "stack", "nextStack", "split", "splice", "InvariantError", "Error", "constructor", "name", "invariant", "predicate", "as", "ErrorConstructor", "formatMessage", "Reflect", "construct", "err", "isNodeProcess", "navigator", "product", "process", "type", "versions", "node", "devU<PERSON>s", "devUtils7", "until", "promise", "data", "catch", "error", "getAbsoluteWorkerUrl", "workerUrl", "URL", "location", "href", "getWorkerByRegistration", "registration", "absoluteWorkerUrl", "findWorker", "allStates", "active", "installing", "waiting", "relevantStates", "filter", "state", "worker", "find", "worker2", "scriptURL", "getWorkerInstance", "url", "options", "mockRegistrations", "serviceWorker", "getRegistrations", "then", "registrations", "controller", "reload", "existingRegistration", "update", "registrationResult", "register", "isWorkerMissing", "includes", "scopeUrl", "scope", "devUtils3", "devUtils2", "printStartMessage", "args", "quiet", "console", "groupCollapsed", "log", "workerScope", "client", "id", "frameType", "groupEnd", "enableMocking", "context", "workerChannel", "send", "payload", "events", "once", "isMockingEnabled", "warn", "WorkerChannel", "port", "postMessage", "event", "rest", "transfer", "pruneGetRequestBody", "request", "method", "body", "deserializeRequest", "serializedRequest", "Request", "RequestHandler", "handleRequest", "devUtils4", "toResponseInit", "isHandlerKind", "createRequestListener", "messageChannel", "ports", "requestId", "requestCloneForLogs", "clone", "requestClone", "cache", "set", "getRequestHandlers", "emitter", "onPassthroughResponse", "onMockedResponse", "response", "handler", "parsedResult", "responseClone", "responseCloneForLogs", "responseInit", "supports", "readableStreamTransfer", "responseStreamOrNull", "responseBufferOrNull", "arrayBuffer", "status", "statusText", "headers", "devUtils5", "checkWorkerIntegrity", "checksum", "packageVersion", "encoder", "TextEncoder", "encodeBuffer", "text", "encode", "decodeBuffer", "buffer", "encoding", "decoder", "TextDecoder", "decode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "byteOffset", "byteLength", "IS_PATCHED_MODULE", "Symbol", "canParseUrl", "_error", "getValueBySymbol", "symbolName", "source", "ownSymbols", "Object", "getOwnPropertySymbols", "symbol", "symbol2", "description", "get", "_FetchResponse", "Response", "isConfigurableStatusCode", "isRedirectResponse", "STATUS_CODES_WITH_REDIRECT", "isResponseWithBody", "STATUS_CODES_WITHOUT_BODY", "setUrl", "urlList", "push", "defineProperty", "enumerable", "configurable", "writable", "parseRawHeaders", "rawHeaders", "Headers", "line", "append", "init", "_a", "safeStatus", "finalBody", "FetchResponse", "kRawRequest", "setRawRequest", "rawRequest", "__defProp", "__export", "target", "all", "colors_exports", "blue", "gray", "green", "red", "yellow", "IS_NODE", "<PERSON><PERSON>", "prefix", "LOGGER_NAME", "getVariable", "LOGGER_LEVEL", "isLoggingEnabled", "startsWith", "debug", "isDefinedAndNotEquals", "noop", "info", "success", "warning", "only", "extend", "domain", "logEntry", "level", "colors", "performance2", "PerformanceEntry", "message2", "positionals2", "measure", "deltaTime", "timestamp", "callback", "createEntry", "Date", "customColors", "entry", "timestampColor", "prefixColor", "colorize", "write", "getWriter", "formatTimestamp", "concat", "serializeInput", "map", "toLocaleTimeString", "getMilliseconds", "startTime", "endTime", "performance", "now", "toFixed", "stdout", "stderr", "variableName", "env", "globalThis", "toString", "expected", "MemoryLeakError", "count", "_Emitter", "listenerCount", "eventName", "Map", "maxListeners", "defaultMaxListeners", "hasWarnedAboutPotentialMemoryLeak", "_emitInternalEvent", "internalEventName", "listener", "emit", "_getListeners", "Array", "prototype", "apply", "_removeListener", "listeners", "index", "indexOf", "_wrapOnceListener", "onceListener", "removeListener", "setMaxListeners", "getMaxListeners", "eventNames", "from", "keys", "for<PERSON>ach", "addListener", "nextListeners", "memoryLeakWarning", "on", "prependListener", "prependOnceListener", "off", "removeAllListeners", "delete", "clear", "rawListeners", "Emitter", "INTERNAL_REQUEST_ID_HEADER_NAME", "getGlobalSymbol", "setGlobalSymbol", "deleteGlobalSymbol", "Interceptor", "readyState", "subscriptions", "logger", "checkEnvironment", "shouldApply", "runningInstance", "getInstance", "setup", "setInstance", "dispose", "clearInstance", "instance", "createRequestId", "Math", "random", "BatchInterceptor", "interceptors", "interceptor", "createResponseListener", "responseJson", "isMockedResponse", "devUtils6", "validateWorkerScope", "createStartHandler", "start", "customOptions", "startWorkerInstance", "missingWorkerMessage", "host", "window", "clearInterval", "keepAliveInterval", "setInterval", "startOptions", "workerRegistration", "pendingInstance", "Promise", "resolve", "addEventListener", "devUtils9", "devUtils8", "printStopMessage", "createStop", "stop", "mergeRight", "DEFAULT_START_OPTIONS", "waitUntilReady", "onUnhandledRequest", "mockServiceWorkerUrl", "createDeferredExecutor", "executor", "reject", "result", "onFulfilled", "reason", "queueMicrotask", "rejectionReason", "DeferredPromise", "deferredExecutor", "originalResolve", "originalReject", "onRejected", "decorate", "finally", "onfinally", "#decorate", "defineProperties", "InterceptorError", "setPrototypeOf", "kRequestHandled", "kResponsePromise", "RequestController", "respondWith", "errorWith", "emitAsync", "listners", "isObject", "loose", "call", "isPropertyAccessible", "obj", "key", "e", "createServerErrorResponse", "isResponseError", "isResponseLike", "isNodeLikeError", "handleRequest2", "handleResponse", "onError", "onRequestError", "onResponse", "handleResponseError", "pendingRequestId", "requestAbortPromise", "signal", "aborted", "requestListenersPromise", "race", "unhandledExceptionController", "nextResult", "hasConfigurableGlobal", "propertyName", "descriptor", "getOwnPropertyDescriptor", "createNetworkError", "cause", "assign", "TypeError", "REQUEST_BODY_HEADERS", "kRedirectCount", "followFetchRedirect", "requestUrl", "locationUrl", "protocol", "mode", "username", "password", "<PERSON><PERSON><PERSON><PERSON>", "requestInit", "headerName", "fetch", "left", "right", "origin", "hostname", "BrotliDecompressionStream", "TransformStream", "transform", "chunk", "enqueue", "PipelineStream", "transformStreams", "strategies", "readable", "reduce", "readable2", "pipeThrough", "parseContentEncoding", "contentEncoding", "toLowerCase", "coding", "trim", "createDecompressionStream", "codings", "transformers", "reduceRight", "transformers2", "DecompressionStream", "decompressResponse", "decompressionStream", "pipeTo", "_FetchInterceptor", "pureFetch", "input", "resolvedInput", "responsePromise", "isRequestHandled", "rawResponse", "decompressedStream", "redirect", "response2", "requestCloneForResponseEvent", "FetchInterceptor", "concat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "EventPolyfill", "NONE", "CAPTURING_PHASE", "AT_TARGET", "BUBBLING_PHASE", "srcElement", "currentTarget", "eventPhase", "isTrusted", "composed", "cancelable", "defaultPrevented", "bubbles", "lengthComputable", "loaded", "total", "cancelBubble", "returnValue", "timeStamp", "<PERSON><PERSON><PERSON>", "initEvent", "preventDefault", "stopPropagation", "stopImmediatePropagation", "ProgressEventPolyfill", "SUPPORTS_PROGRESS_EVENT", "ProgressEvent", "createEvent", "progressEvents", "ProgressEventClass", "findPropertySource", "hasProperty", "hasOwnProperty", "getPrototypeOf", "createProxy", "proxy", "Proxy", "optionsToProxyHandler", "constructorCall", "methodCall", "getProperty", "setProperty", "newTarget", "next", "bind", "nextValue", "propertySource", "ownDescriptors", "receiver", "next2", "isDomParserSupportedType", "supportedTypes", "some", "supportedType", "parseJson", "parse", "createResponse", "responseBodyOrNull", "responseURL", "createHeadersFromXMLHttpReqestHeaders", "getAllResponseHeaders", "headersString", "lines", "parts", "getBodyByteLength", "explicit<PERSON><PERSON>nt<PERSON><PERSON><PERSON>", "kIsRequestHandled", "IS_NODE2", "kFetchRequest", "XMLHttpRequestController", "initialRequest", "uploadEvents", "requestHeaders", "responseBuffer", "invoke", "methodName", "toAbsoluteUrl", "registerEvent", "fetchResponse", "fetchRequest", "requestBody", "toFetchApiRequest", "onceRequestSettled", "onRequest", "setRequestHeader", "define", "upload", "registerUploadEvent", "prevEvents", "nextEvents", "totalRequestBodyLength", "trigger", "getResponseHeader", "__", "HEADERS_RECEIVED", "headerValue", "headersList", "entries", "allHeaders", "responseText", "responseXML", "totalResponseBodyLength", "setReadyState", "LOADING", "finalizeResponse", "DONE", "reader", "<PERSON><PERSON><PERSON><PERSON>", "readNextResponseBodyChunk", "done", "read", "responseBufferToText", "responseType", "mimeType", "responseBlob", "Blob", "contentType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "nextReadyState", "UNSENT", "XMLHttpRequestUpload", "registeredEventName", "resolvedBody", "Document", "documentElement", "innerText", "credentials", "withCredentials", "toUpperCase", "proxyHeaders", "property", "createXMLHttpRequestProxy", "XMLHttpRequestProxy", "XMLHttpRequest", "originalRequest", "prototypeDescriptors", "getOwnPropertyDescriptors", "xhrRequestController", "_XMLHttpRequestInterceptor", "interceptorSymbol", "PureXMLHttpRequest", "XMLHttpRequestInterceptor", "handleRequest3", "isHandlerKind2", "createFallbackRequestListener", "createFallbackStart", "fallbackInterceptor", "createFallbackStop", "devUtils10", "SetupApi", "mergeRight2", "supportsReadableStreamTransfer", "stream", "ReadableStream", "close", "MessageChannel", "port1", "webSocketInterceptor", "handleWebSocketEvent", "attachWebSocketLogger", "SetupWorkerApi", "startHandler", "<PERSON><PERSON><PERSON><PERSON>", "handlers", "createWorkerContext", "handlersController", "currentHandlers", "eventType", "removeEventListener", "bindings", "handleIncomingMessage", "unbind", "serviceWorkerApi", "getUnhandledRequestStrategy", "getHandlers", "onMockedConnection", "connection", "onPassthroughConnection", "setupWorker"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/outvariant@1.4.3/node_modules/outvariant/src/format.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/outvariant@1.4.3/node_modules/outvariant/src/invariant.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/is-node-process@1.2.0/node_modules/is-node-process/src/index.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/createStartHandler.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@open-draft+until@2.1.0/node_modules/@open-draft/until/src/until.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/utils/getWorkerInstance.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/utils/getAbsoluteWorkerUrl.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/utils/getWorkerByRegistration.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/utils/enableMocking.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/utils/printStartMessage.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/utils/createMessageChannel.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/utils/pruneGetRequestBody.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/utils/deserializeRequest.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/createRequestListener.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/utils/checkWorkerIntegrity.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/bufferUtils.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/glossary.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/canParseUrl.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/getValueBySymbol.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/fetchUtils.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/getRawRequest.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@open-draft+logger@0.3.0/node_modules/@open-draft/logger/lib/index.mjs", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/strict-event-emitter@0.5.1/node_modules/strict-event-emitter/src/MemoryLeakError.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/strict-event-emitter@0.5.1/node_modules/strict-event-emitter/src/Emitter.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/Interceptor.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/createRequestId.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/BatchInterceptor.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/getCleanUrl.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/createResponseListener.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/utils/validateWorkerScope.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/stop/createStop.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/stop/utils/printStopMessage.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/utils/prepareStartHandler.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@open-draft+deferred-promise@2.2.0/node_modules/@open-draft/deferred-promise/src/createDeferredExecutor.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@open-draft+deferred-promise@2.2.0/node_modules/@open-draft/deferred-promise/src/DeferredPromise.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/RequestController.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/InterceptorError.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/emitAsync.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/handleRequest.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/isObject.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/isPropertyAccessible.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/responseUtils.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/isNodeLikeError.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/hasConfigurableGlobal.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/index.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/utils/createNetworkError.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/utils/followRedirect.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/utils/brotli-decompress.browser.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/fetch/utils/decompression.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/index.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestController.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/concatArrayBuffer.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/polyfills/EventPolyfill.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/polyfills/ProgressEventPolyfill.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/createEvent.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/findPropertySource.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/createProxy.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/isDomParserSupportedType.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/utils/parseJson.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/createResponse.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/utils/getBodyByteLength.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/node_modules/.pnpm/@mswjs+interceptors@0.39.1/node_modules/@mswjs/interceptors/src/interceptors/XMLHttpRequest/XMLHttpRequestProxy.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/createFallbackRequestListener.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/start/createFallbackStart.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/stop/createFallbackStop.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/setupWorker/setupWorker.ts", "/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/browser/utils/supportsReadableStreamTransfer.ts"], "sourcesContent": ["const POSITIONALS_EXP = /(%?)(%([sdijo]))/g\n\nfunction serializePositional(positional: any, flag: string): any {\n  switch (flag) {\n    // Strings.\n    case 's':\n      return positional\n\n    // Digits.\n    case 'd':\n    case 'i':\n      return Number(positional)\n\n    // JSON.\n    case 'j':\n      return JSON.stringify(positional)\n\n    // Objects.\n    case 'o': {\n      // Preserve stings to prevent extra quotes around them.\n      if (typeof positional === 'string') {\n        return positional\n      }\n\n      const json = JSON.stringify(positional)\n\n      // If the positional isn't serializable, return it as-is.\n      if (json === '{}' || json === '[]' || /^\\[object .+?\\]$/.test(json)) {\n        return positional\n      }\n\n      return json\n    }\n  }\n}\n\nexport function format(message: string, ...positionals: any[]): string {\n  if (positionals.length === 0) {\n    return message\n  }\n\n  let positionalIndex = 0\n  let formattedMessage = message.replace(\n    POSITIONALS_EXP,\n    (match, isEscaped, _, flag) => {\n      const positional = positionals[positionalIndex]\n      const value = serializePositional(positional, flag)\n\n      if (!isEscaped) {\n        positionalIndex++\n        return value\n      }\n\n      return match\n    }\n  )\n\n  // Append unresolved positionals to string as-is.\n  if (positionalIndex < positionals.length) {\n    formattedMessage += ` ${positionals.slice(positionalIndex).join(' ')}`\n  }\n\n  formattedMessage = formattedMessage.replace(/%{2,2}/g, '%')\n\n  return formattedMessage\n}\n", "import { format } from './format'\n\nconst STACK_FRAMES_TO_IGNORE = 2\n\n/**\n * Remove the \"outvariant\" package trace from the given error.\n * This scopes down the error stack to the relevant parts\n * when used in other applications.\n */\nfunction cleanErrorStack(error: Error): void {\n  if (!error.stack) {\n    return\n  }\n\n  const nextStack = error.stack.split('\\n')\n  nextStack.splice(1, STACK_FRAMES_TO_IGNORE)\n  error.stack = nextStack.join('\\n')\n}\n\nexport class InvariantError extends Error {\n  name = 'Invariant Violation'\n\n  constructor(public readonly message: string, ...positionals: any[]) {\n    super(message)\n    this.message = format(message, ...positionals)\n    cleanErrorStack(this)\n  }\n}\n\nexport interface CustomErrorConstructor {\n  new (message: string): Error\n}\n\nexport interface CustomErrorFactory {\n  (message: string): Error\n}\n\nexport type CustomError = CustomErrorConstructor | CustomErrorFactory\n\ntype Invariant = {\n  (\n    predicate: unknown,\n    message: string,\n    ...positionals: any[]\n  ): asserts predicate\n\n  as(\n    ErrorConstructor: CustomError,\n    predicate: unknown,\n    message: string,\n    ...positionals: unknown[]\n  ): asserts predicate\n}\n\nexport const invariant: Invariant = (\n  predicate,\n  message,\n  ...positionals\n): asserts predicate => {\n  if (!predicate) {\n    throw new InvariantError(message, ...positionals)\n  }\n}\n\ninvariant.as = (ErrorConstructor, predicate, message, ...positionals) => {\n  if (!predicate) {\n    const formatMessage =\n      positionals.length === 0 ? message : format(message, ...positionals)\n    let error: Error\n\n    try {\n      error = Reflect.construct(ErrorConstructor as CustomErrorConstructor, [\n        formatMessage,\n      ])\n    } catch (err) {\n      error = (ErrorConstructor as CustomErrorFactory)(formatMessage)\n    }\n\n    throw error\n  }\n}\n", "/**\n * Determines if the current process is a Node.js process.\n */\nexport function isNodeProcess(): boolean {\n  if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    return true\n  }\n\n  if (typeof process !== 'undefined') {\n    // Electron (https://www.electronjs.org/docs/latest/api/process#processtype-readonly)\n    const type = (process as any).type\n    if (type === 'renderer' || type === 'worker') {\n      return false\n    }\n\n\n    return !!(\n      process.versions &&\n      process.versions.node\n    )\n  }\n\n  return false\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport { getWorkerInstance } from './utils/getWorkerInstance'\nimport { enableMocking } from './utils/enableMocking'\nimport { SetupWorkerInternalContext, StartHandler } from '../glossary'\nimport { createRequestListener } from './createRequestListener'\nimport { checkWorkerIntegrity } from '../../utils/checkWorkerIntegrity'\nimport { createResponseListener } from './createResponseListener'\nimport { validateWorkerScope } from './utils/validateWorkerScope'\n\nexport const createStartHandler = (\n  context: SetupWorkerInternalContext,\n): StartHandler => {\n  return function start(options, customOptions) {\n    const startWorkerInstance = async () => {\n      // Remove all previously existing event listeners.\n      // This way none of the listeners persists between Fast refresh\n      // of the application's code.\n      context.events.removeAllListeners()\n\n      // Handle requests signaled by the worker.\n      context.workerChannel.on(\n        'REQUEST',\n        createRequestListener(context, options),\n      )\n\n      // Handle responses signaled by the worker.\n      context.workerChannel.on('RESPONSE', createResponseListener(context))\n\n      const instance = await getWorkerInstance(\n        options.serviceWorker.url,\n        options.serviceWorker.options,\n        options.findWorker,\n      )\n\n      const [worker, registration] = instance\n\n      if (!worker) {\n        const missingWorkerMessage = customOptions?.findWorker\n          ? devUtils.formatMessage(\n              `Failed to locate the Service Worker registration using a custom \"findWorker\" predicate.\n\nPlease ensure that the custom predicate properly locates the Service Worker registration at \"%s\".\nMore details: https://mswjs.io/docs/api/setup-worker/start#findworker\n`,\n              options.serviceWorker.url,\n            )\n          : devUtils.formatMessage(\n              `Failed to locate the Service Worker registration.\n\nThis most likely means that the worker script URL \"%s\" cannot resolve against the actual public hostname (%s). This may happen if your application runs behind a proxy, or has a dynamic hostname.\n\nPlease consider using a custom \"serviceWorker.url\" option to point to the actual worker script location, or a custom \"findWorker\" option to resolve the Service Worker registration manually. More details: https://mswjs.io/docs/api/setup-worker/start`,\n              options.serviceWorker.url,\n              location.host,\n            )\n\n        throw new Error(missingWorkerMessage)\n      }\n\n      context.worker = worker\n      context.registration = registration\n\n      context.events.addListener(window, 'beforeunload', () => {\n        if (worker.state !== 'redundant') {\n          // Notify the Service Worker that this client has closed.\n          // Internally, it's similar to disabling the mocking, only\n          // client close event has a handler that self-terminates\n          // the Service Worker when there are no open clients.\n          context.workerChannel.send('CLIENT_CLOSED')\n        }\n        // Make sure we're always clearing the interval - there are reports that not doing this can\n        // cause memory leaks in headless browser environments.\n        window.clearInterval(context.keepAliveInterval)\n\n        // Notify others about this client disconnecting.\n        // E.g. this will purge the in-memory WebSocket clients since\n        // starting the worker again will assign them new IDs.\n        window.postMessage({ type: 'msw/worker:stop' })\n      })\n\n      // Check if the active Service Worker has been generated\n      // by the currently installed version of MSW.\n      await checkWorkerIntegrity(context).catch((error) => {\n        devUtils.error(\n          'Error while checking the worker script integrity. Please report this on GitHub (https://github.com/mswjs/msw/issues), including the original error below.',\n        )\n        // eslint-disable-next-line no-console\n        console.error(error)\n      })\n\n      context.keepAliveInterval = window.setInterval(\n        () => context.workerChannel.send('KEEPALIVE_REQUEST'),\n        5000,\n      )\n\n      // Warn the user when loading the page that lies outside\n      // of the worker's scope.\n      validateWorkerScope(registration, context.startOptions)\n\n      return registration\n    }\n\n    const workerRegistration = startWorkerInstance().then(\n      async (registration) => {\n        const pendingInstance = registration.installing || registration.waiting\n\n        // Wait until the worker is activated.\n        // Assume the worker is already activated if there's no pending registration\n        // (i.e. when reloading the page after a successful activation).\n        if (pendingInstance) {\n          await new Promise<void>((resolve) => {\n            pendingInstance.addEventListener('statechange', () => {\n              if (pendingInstance.state === 'activated') {\n                return resolve()\n              }\n            })\n          })\n        }\n\n        // Print the activation message only after the worker has been activated.\n        await enableMocking(context, options).catch((error) => {\n          throw new Error(`Failed to enable mocking: ${error?.message}`)\n        })\n\n        return registration\n      },\n    )\n\n    return workerRegistration\n  }\n}\n", "export type AsyncTuple<\n  ErrorType extends any = Error,\n  DataType extends any = unknown,\n> =\n  | {\n      error: ErrorType\n      data: null\n    }\n  | { error: null; data: DataType }\n\n/**\n * Gracefully handles a given Promise factory.\n * @example\n * const { error, data } = await until(() => asyncAction())\n */\nexport const until = async <\n  ErrorType extends any = Error,\n  DataType extends any = unknown,\n>(\n  promise: () => Promise<DataType>,\n): Promise<AsyncTuple<ErrorType, DataType>> => {\n  try {\n    const data = await promise().catch((error) => {\n      throw error\n    })\n    return { error: null, data }\n  } catch (error) {\n    return { error, data: null }\n  }\n}\n", "import { until } from '@open-draft/until'\nimport { devUtils } from '~/core/utils/internal/devUtils'\nimport { getAbsoluteWorkerUrl } from '../../../utils/getAbsoluteWorkerUrl'\nimport { getWorkerByRegistration } from './getWorkerByRegistration'\nimport { ServiceWorkerInstanceTuple, FindWorker } from '../../glossary'\n\n/**\n * Returns an active Service Worker instance.\n * When not found, registers a new Service Worker.\n */\nexport const getWorkerInstance = async (\n  url: string,\n  options: RegistrationOptions = {},\n  findWorker: FindWorker,\n): Promise<ServiceWorkerInstanceTuple> => {\n  // Resolve the absolute Service Worker URL.\n  const absoluteWorkerUrl = getAbsoluteWorkerUrl(url)\n\n  const mockRegistrations = await navigator.serviceWorker\n    .getRegistrations()\n    .then((registrations) =>\n      registrations.filter((registration) =>\n        getWorkerByRegistration(registration, absoluteWorkerUrl, findWorker),\n      ),\n    )\n  if (!navigator.serviceWorker.controller && mockRegistrations.length > 0) {\n    // Reload the page when it has associated workers, but no active controller.\n    // The absence of a controller can mean either:\n    // - page has no Service Worker associated with it\n    // - page has been hard-reloaded and its workers won't be used until the next reload.\n    // Since we've checked that there are registrations associated with this page,\n    // at this point we are sure it's hard reload that falls into this clause.\n    location.reload()\n  }\n\n  const [existingRegistration] = mockRegistrations\n\n  if (existingRegistration) {\n    // Schedule the worker update in the background.\n    // Update ensures the existing worker is up-to-date.\n    existingRegistration.update()\n\n    // Return the worker reference immediately.\n    return [\n      getWorkerByRegistration(\n        existingRegistration,\n        absoluteWorkerUrl,\n        findWorker,\n      ),\n      existingRegistration,\n    ]\n  }\n\n  // When the Service Worker wasn't found, register it anew and return the reference.\n  const registrationResult = await until<Error, ServiceWorkerInstanceTuple>(\n    async () => {\n      const registration = await navigator.serviceWorker.register(url, options)\n      return [\n        // Compare existing worker registration by its worker URL,\n        // to prevent irrelevant workers to resolve here (such as Codesandbox worker).\n        getWorkerByRegistration(registration, absoluteWorkerUrl, findWorker),\n        registration,\n      ]\n    },\n  )\n\n  // Handle Service Worker registration errors.\n  if (registrationResult.error) {\n    const isWorkerMissing = registrationResult.error.message.includes('(404)')\n\n    // Produce a custom error message when given a non-existing Service Worker url.\n    // Suggest developers to check their setup.\n    if (isWorkerMissing) {\n      const scopeUrl = new URL(options?.scope || '/', location.href)\n\n      throw new Error(\n        devUtils.formatMessage(`\\\nFailed to register a Service Worker for scope ('${scopeUrl.href}') with script ('${absoluteWorkerUrl}'): Service Worker script does not exist at the given path.\n\nDid you forget to run \"npx msw init <PUBLIC_DIR>\"?\n\nLearn more about creating the Service Worker script: https://mswjs.io/docs/cli/init`),\n      )\n    }\n\n    // Fallback error message for any other registration errors.\n    throw new Error(\n      devUtils.formatMessage(\n        'Failed to register the Service Worker:\\n\\n%s',\n        registrationResult.error.message,\n      ),\n    )\n  }\n\n  return registrationResult.data\n}\n", "/**\n * Returns an absolute Service Worker URL based on the given\n * relative URL (known during the registration).\n */\nexport function getAbsoluteWorkerUrl(workerUrl: string): string {\n  return new URL(workerUrl, location.href).href\n}\n", "import { FindWorker } from '../../glossary'\n\n/**\n * Attempts to resolve a Service Worker instance from a given registration,\n * regardless of its state (active, installing, waiting).\n */\nexport function getWorkerByRegistration(\n  registration: ServiceWorkerRegistration,\n  absoluteWorkerUrl: string,\n  findWorker: FindWorker,\n): ServiceWorker | null {\n  const allStates = [\n    registration.active,\n    registration.installing,\n    registration.waiting,\n  ]\n  const relevantStates = allStates.filter((state): state is ServiceWorker => {\n    return state != null\n  })\n  const worker = relevantStates.find((worker) => {\n    return findWorker(worker.scriptURL, absoluteWorkerUrl)\n  })\n\n  return worker || null\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport { StartOptions, SetupWorkerInternalContext } from '../../glossary'\nimport { printStartMessage } from './printStartMessage'\n\n/**\n * Signals the worker to enable the interception of requests.\n */\nexport async function enableMocking(\n  context: SetupWorkerInternalContext,\n  options: StartOptions,\n) {\n  context.workerChannel.send('MOCK_ACTIVATE')\n  const { payload } = await context.events.once('MOCKING_ENABLED')\n\n  // Warn the developer on multiple \"worker.start()\" calls.\n  // While this will not affect the worker in any way,\n  // it likely indicates an issue with the developer's code.\n  if (context.isMockingEnabled) {\n    devUtils.warn(\n      `Found a redundant \"worker.start()\" call. Note that starting the worker while mocking is already enabled will have no effect. Consider removing this \"worker.start()\" call.`,\n    )\n    return\n  }\n\n  context.isMockingEnabled = true\n\n  printStartMessage({\n    quiet: options.quiet,\n    workerScope: context.registration?.scope,\n    workerUrl: context.worker?.scriptURL,\n    client: payload.client,\n  })\n}\n", "import type { ServiceWorkerIncomingEventsMap } from '../../glossary'\nimport { devUtils } from '~/core/utils/internal/devUtils'\n\ninterface PrintStartMessageArgs {\n  quiet?: boolean\n  message?: string\n  workerUrl?: string\n  workerScope?: string\n  client?: ServiceWorkerIncomingEventsMap['MOCKING_ENABLED']['client']\n}\n\n/**\n * Prints a worker activation message in the browser's console.\n */\nexport function printStartMessage(args: PrintStartMessageArgs = {}) {\n  if (args.quiet) {\n    return\n  }\n\n  const message = args.message || 'Mocking enabled.'\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    `%c${devUtils.formatMessage(message)}`,\n    'color:orangered;font-weight:bold;',\n  )\n  // eslint-disable-next-line no-console\n  console.log(\n    '%cDocumentation: %chttps://mswjs.io/docs',\n    'font-weight:bold',\n    'font-weight:normal',\n  )\n  // eslint-disable-next-line no-console\n  console.log('Found an issue? https://github.com/mswjs/msw/issues')\n\n  if (args.workerUrl) {\n    // eslint-disable-next-line no-console\n    console.log('Worker script URL:', args.workerUrl)\n  }\n\n  if (args.workerScope) {\n    // eslint-disable-next-line no-console\n    console.log('Worker scope:', args.workerScope)\n  }\n\n  if (args.client) {\n    // eslint-disable-next-line no-console\n    console.log('Client ID: %s (%s)', args.client.id, args.client.frameType)\n  }\n\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n", "import {\n  StringifiedResponse,\n  ServiceWorkerIncomingEventsMap,\n} from '../../glossary'\n\nexport interface ServiceWorkerMessage<\n  EventType extends keyof ServiceWorkerIncomingEventsMap,\n  EventPayload,\n> {\n  type: EventType\n  payload: EventPayload\n}\n\ninterface WorkerChannelEventsMap {\n  MOCK_RESPONSE: [\n    data: StringifiedResponse,\n    transfer?: [ReadableStream<Uint8Array>],\n  ]\n  PASSTHROUGH: []\n}\n\nexport class WorkerChannel {\n  constructor(private readonly port: MessagePort) {}\n\n  public postMessage<Event extends keyof WorkerChannelEventsMap>(\n    event: Event,\n    ...rest: WorkerChannelEventsMap[Event]\n  ): void {\n    const [data, transfer] = rest\n    this.port.postMessage({ type: event, data }, { transfer })\n  }\n}\n", "import type { ServiceWorkerIncomingRequest } from '../setupWorker/glossary'\n\ntype Input = Pick<ServiceWorkerIncomingRequest, 'method' | 'body'>\n\n/**\n * Ensures that an empty GET request body is always represented as `undefined`.\n */\nexport function pruneGetRequestBody(\n  request: Input,\n): ServiceWorkerIncomingRequest['body'] {\n  // Force HEAD/GET request body to always be empty.\n  // The worker reads any request's body as <PERSON><PERSON><PERSON><PERSON>uff<PERSON>,\n  // and you cannot re-construct a GET/HEAD Request\n  // with an ArrayBuffer, even if empty. Also note that\n  // \"request.body\" is always undefined in the worker.\n  if (['HEAD', 'GET'].includes(request.method)) {\n    return undefined\n  }\n\n  return request.body\n}\n", "import { pruneGetRequestBody } from './pruneGetRequestBody'\nimport type { ServiceWorkerIncomingRequest } from '../setupWorker/glossary'\n\n/**\n * Converts a given request received from the Service Worker\n * into a Fetch `Request` instance.\n */\nexport function deserializeRequest(\n  serializedRequest: ServiceWorkerIncomingRequest,\n): Request {\n  return new Request(serializedRequest.url, {\n    ...serializedRequest,\n    body: pruneGetRequestBody(serializedRequest),\n  })\n}\n", "import {\n  StartOptions,\n  SetupWorkerInternalContext,\n  ServiceWorkerIncomingEventsMap,\n} from '../glossary'\nimport {\n  ServiceWorkerMessage,\n  WorkerChannel,\n} from './utils/createMessageChannel'\nimport { deserializeRequest } from '../../utils/deserializeRequest'\nimport { RequestHandler } from '~/core/handlers/RequestHandler'\nimport { handleRequest } from '~/core/utils/handleRequest'\nimport { RequiredDeep } from '~/core/typeUtils'\nimport { devUtils } from '~/core/utils/internal/devUtils'\nimport { toResponseInit } from '~/core/utils/toResponseInit'\nimport { isHandlerKind } from '~/core/utils/internal/isHandlerKind'\n\nexport const createRequestListener = (\n  context: SetupWorkerInternalContext,\n  options: RequiredDeep<StartOptions>,\n) => {\n  return async (\n    event: MessageEvent,\n    message: ServiceWorkerMessage<\n      'REQUEST',\n      ServiceWorkerIncomingEventsMap['REQUEST']\n    >,\n  ) => {\n    const messageChannel = new WorkerChannel(event.ports[0])\n\n    const requestId = message.payload.id\n    const request = deserializeRequest(message.payload)\n    const requestCloneForLogs = request.clone()\n\n    // Make this the first request clone before the\n    // request resolution pipeline even starts.\n    // Store the clone in cache so the first matching\n    // request handler would skip the cloning phase.\n    const requestClone = request.clone()\n    RequestHandler.cache.set(request, requestClone)\n\n    try {\n      await handleRequest(\n        request,\n        requestId,\n        context.getRequestHandlers().filter(isHandlerKind('RequestHandler')),\n        options,\n        context.emitter,\n        {\n          onPassthroughResponse() {\n            messageChannel.postMessage('PASSTHROUGH')\n          },\n          async onMockedResponse(response, { handler, parsedResult }) {\n            // Clone the mocked response so its body could be read\n            // to buffer to be sent to the worker and also in the\n            // \".log()\" method of the request handler.\n            const responseClone = response.clone()\n            const responseCloneForLogs = response.clone()\n            const responseInit = toResponseInit(response)\n\n            /**\n             * @note Safari doesn't support transferring a \"ReadableStream\".\n             * Check that the browser supports that before sending it to the worker.\n             */\n            if (context.supports.readableStreamTransfer) {\n              const responseStreamOrNull = response.body\n\n              messageChannel.postMessage(\n                'MOCK_RESPONSE',\n                {\n                  ...responseInit,\n                  body: responseStreamOrNull,\n                },\n                responseStreamOrNull ? [responseStreamOrNull] : undefined,\n              )\n            } else {\n              /**\n               * @note If we are here, this means the current environment doesn't\n               * support \"ReadableStream\" as transferable. In that case,\n               * attempt to read the non-empty response body as ArrayBuffer, if it's not empty.\n               * @see https://github.com/mswjs/msw/issues/1827\n               */\n              const responseBufferOrNull =\n                response.body === null\n                  ? null\n                  : await responseClone.arrayBuffer()\n\n              messageChannel.postMessage('MOCK_RESPONSE', {\n                ...responseInit,\n                body: responseBufferOrNull,\n              })\n            }\n\n            if (!options.quiet) {\n              context.emitter.once('response:mocked', () => {\n                handler.log({\n                  request: requestCloneForLogs,\n                  response: responseCloneForLogs,\n                  parsedResult,\n                })\n              })\n            }\n          },\n        },\n      )\n    } catch (error) {\n      if (error instanceof Error) {\n        devUtils.error(\n          `Uncaught exception in the request handler for \"%s %s\":\n\n%s\n\nThis exception has been gracefully handled as a 500 response, however, it's strongly recommended to resolve this error, as it indicates a mistake in your code. If you wish to mock an error response, please see this guide: https://mswjs.io/docs/http/mocking-responses/error-responses`,\n          request.method,\n          request.url,\n          error.stack ?? error,\n        )\n\n        // Treat all other exceptions in a request handler as unintended,\n        // alerting that there is a problem that needs fixing.\n        messageChannel.postMessage('MOCK_RESPONSE', {\n          status: 500,\n          statusText: 'Request Handler Error',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            name: error.name,\n            message: error.message,\n            stack: error.stack,\n          }),\n        })\n      }\n    }\n  }\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport type { SetupWorkerInternalContext } from '../setupWorker/glossary'\n\n/**\n * Check whether the registered Service Worker has been\n * generated by the installed version of the library.\n * Prints a warning message if the worker scripts mismatch.\n */\nexport async function checkWorkerIntegrity(\n  context: SetupWorkerInternalContext,\n): Promise<void> {\n  // Request the integrity checksum from the registered worker.\n  context.workerChannel.send('INTEGRITY_CHECK_REQUEST')\n\n  const { payload } = await context.events.once('INTEGRITY_CHECK_RESPONSE')\n\n  // Compare the response from the Service Worker and the\n  // global variable set during the build.\n\n  // The integrity is validated based on the worker script's checksum\n  // that's derived from its minified content during the build.\n  // The \"SERVICE_WORKER_CHECKSUM\" global variable is injected by the build.\n  if (payload.checksum !== SERVICE_WORKER_CHECKSUM) {\n    devUtils.warn(\n      `The currently registered Service Worker has been generated by a different version of MSW (${payload.packageVersion}) and may not be fully compatible with the installed version.\n\nIt's recommended you update your worker script by running this command:\n\n  \\u2022 npx msw init <PUBLIC_DIR>\n\nYou can also automate this process and make the worker script update automatically upon the library installations. Read more: https://mswjs.io/docs/cli/init.`,\n    )\n  }\n}\n", "const encoder = new TextEncoder()\n\nexport function encodeBuffer(text: string): Uint8Array {\n  return encoder.encode(text)\n}\n\nexport function decodeBuffer(buffer: <PERSON>rrayBuffer, encoding?: string): string {\n  const decoder = new TextDecoder(encoding)\n  return decoder.decode(buffer)\n}\n\n/**\n * Create an `ArrayBuffer` from the given `Uint8Array`.\n * Takes the byte offset into account to produce the right buffer\n * in the case when the buffer is bigger than the data view.\n */\nexport function toArrayBuffer(array: Uint8Array): ArrayBuffer {\n  return array.buffer.slice(\n    array.byteOffset,\n    array.byteOffset + array.byteLength\n  )\n}\n", "import type { RequestController } from './RequestController'\n\nexport const IS_PATCHED_MODULE: unique symbol = Symbol('isPatchedModule')\n\n/**\n * @note Export `RequestController` as a type only.\n * It's never meant to be created in the userland.\n */\nexport type { RequestController }\n\nexport type RequestCredentials = 'omit' | 'include' | 'same-origin'\n\nexport type HttpRequestEventMap = {\n  request: [\n    args: {\n      request: Request\n      requestId: string\n      controller: RequestController\n    }\n  ]\n  response: [\n    args: {\n      response: Response\n      isMockedResponse: boolean\n      request: Request\n      requestId: string\n    }\n  ]\n  unhandledException: [\n    args: {\n      error: unknown\n      request: Request\n      requestId: string\n      controller: RequestController\n    }\n  ]\n}\n", "/**\n * Returns a boolean indicating whether the given URL string\n * can be parsed into a `URL` instance.\n * A substitute for `URL.canParse()` for Node.js 18.\n */\nexport function canParseUrl(url: string): boolean {\n  try {\n    new URL(url)\n    return true\n  } catch (_error) {\n    return false\n  }\n}\n", "/**\n * Returns the value behind the symbol with the given name.\n */\nexport function getValueBySymbol<T>(\n  symbolName: string,\n  source: object\n): T | undefined {\n  const ownSymbols = Object.getOwnPropertySymbols(source)\n\n  const symbol = ownSymbols.find((symbol) => {\n    return symbol.description === symbolName\n  })\n\n  if (symbol) {\n    return Reflect.get(source, symbol)\n  }\n\n  return\n}\n", "import { canParseUrl } from './canParseUrl'\nimport { getValueBySymbol } from './getValueBySymbol'\n\nexport interface FetchResponseInit extends ResponseInit {\n  url?: string\n}\n\ninterface UndiciFetchInternalState {\n  aborted: boolean\n  rangeRequested: boolean\n  timingAllowPassed: boolean\n  requestIncludesCredentials: boolean\n  type: ResponseType\n  status: number\n  statusText: string\n  timingInfo: unknown\n  cacheState: unknown\n  headersList: Record<symbol, Map<string, unknown>>\n  urlList: Array<URL>\n  body?: {\n    stream: ReadableStream\n    source: unknown\n    length: number\n  }\n}\n\nexport class FetchResponse extends Response {\n  /**\n   * Response status codes for responses that cannot have body.\n   * @see https://fetch.spec.whatwg.org/#statuses\n   */\n  static readonly STATUS_CODES_WITHOUT_BODY = [101, 103, 204, 205, 304]\n\n  static readonly STATUS_CODES_WITH_REDIRECT = [301, 302, 303, 307, 308]\n\n  static isConfigurableStatusCode(status: number): boolean {\n    return status >= 200 && status <= 599\n  }\n\n  static isRedirectResponse(status: number): boolean {\n    return FetchResponse.STATUS_CODES_WITH_REDIRECT.includes(status)\n  }\n\n  /**\n   * Returns a boolean indicating whether the given response status\n   * code represents a response that can have a body.\n   */\n  static isResponseWithBody(status: number): boolean {\n    return !FetchResponse.STATUS_CODES_WITHOUT_BODY.includes(status)\n  }\n\n  static setUrl(url: string | undefined, response: Response): void {\n    if (!url || url === 'about:' || !canParseUrl(url)) {\n      return\n    }\n\n    const state = getValueBySymbol<UndiciFetchInternalState>('state', response)\n\n    if (state) {\n      // In Undici, push the URL to the internal list of URLs.\n      // This will respect the `response.url` getter logic correctly.\n      state.urlList.push(new URL(url))\n    } else {\n      // In other libraries, redefine the `url` property directly.\n      Object.defineProperty(response, 'url', {\n        value: url,\n        enumerable: true,\n        configurable: true,\n        writable: false,\n      })\n    }\n  }\n\n  /**\n   * Parses the given raw HTTP headers into a Fetch API `Headers` instance.\n   */\n  static parseRawHeaders(rawHeaders: Array<string>): Headers {\n    const headers = new Headers()\n    for (let line = 0; line < rawHeaders.length; line += 2) {\n      headers.append(rawHeaders[line], rawHeaders[line + 1])\n    }\n    return headers\n  }\n\n  constructor(body?: BodyInit | null, init: FetchResponseInit = {}) {\n    const status = init.status ?? 200\n    const safeStatus = FetchResponse.isConfigurableStatusCode(status)\n      ? status\n      : 200\n    const finalBody = FetchResponse.isResponseWithBody(status) ? body : null\n\n    super(finalBody, {\n      status: safeStatus,\n      statusText: init.statusText,\n      headers: init.headers,\n    })\n\n    if (status !== safeStatus) {\n      /**\n       * @note Undici keeps an internal \"Symbol(state)\" that holds\n       * the actual value of response status. Update that in Node.js.\n       */\n      const state = getValueBySymbol<UndiciFetchInternalState>('state', this)\n\n      if (state) {\n        state.status = status\n      } else {\n        Object.defineProperty(this, 'status', {\n          value: status,\n          enumerable: true,\n          configurable: true,\n          writable: false,\n        })\n      }\n    }\n\n    FetchResponse.setUrl(init.url, this)\n  }\n}\n", "const kRawRequest = Symbol('kRawRequest')\n\n/**\n * Returns a raw request instance associated with this request.\n *\n * @example\n * interceptor.on('request', ({ request }) => {\n *   const rawRequest = getRawRequest(request)\n *\n *   if (rawRequest instanceof http.ClientRequest) {\n *     console.log(rawRequest.rawHeaders)\n *   }\n * })\n */\nexport function getRawRequest(request: Request): unknown | undefined {\n  return Reflect.get(request, kRawRequest)\n}\n\nexport function setRawRequest(request: Request, rawRequest: unknown): void {\n  Reflect.set(request, kRawRequest, rawRequest)\n}\n", "var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/index.ts\nimport { isNodeProcess } from \"is-node-process\";\nimport { format } from \"outvariant\";\n\n// src/colors.ts\nvar colors_exports = {};\n__export(colors_exports, {\n  blue: () => blue,\n  gray: () => gray,\n  green: () => green,\n  red: () => red,\n  yellow: () => yellow\n});\nfunction yellow(text) {\n  return `\\x1B[33m${text}\\x1B[0m`;\n}\nfunction blue(text) {\n  return `\\x1B[34m${text}\\x1B[0m`;\n}\nfunction gray(text) {\n  return `\\x1B[90m${text}\\x1B[0m`;\n}\nfunction red(text) {\n  return `\\x1B[31m${text}\\x1B[0m`;\n}\nfunction green(text) {\n  return `\\x1B[32m${text}\\x1B[0m`;\n}\n\n// src/index.ts\nvar IS_NODE = isNodeProcess();\nvar Logger = class {\n  constructor(name) {\n    this.name = name;\n    this.prefix = `[${this.name}]`;\n    const LOGGER_NAME = getVariable(\"DEBUG\");\n    const LOGGER_LEVEL = getVariable(\"LOG_LEVEL\");\n    const isLoggingEnabled = LOGGER_NAME === \"1\" || LOGGER_NAME === \"true\" || typeof LOGGER_NAME !== \"undefined\" && this.name.startsWith(LOGGER_NAME);\n    if (isLoggingEnabled) {\n      this.debug = isDefinedAndNotEquals(LOGGER_LEVEL, \"debug\") ? noop : this.debug;\n      this.info = isDefinedAndNotEquals(LOGGER_LEVEL, \"info\") ? noop : this.info;\n      this.success = isDefinedAndNotEquals(LOGGER_LEVEL, \"success\") ? noop : this.success;\n      this.warning = isDefinedAndNotEquals(LOGGER_LEVEL, \"warning\") ? noop : this.warning;\n      this.error = isDefinedAndNotEquals(LOGGER_LEVEL, \"error\") ? noop : this.error;\n    } else {\n      this.info = noop;\n      this.success = noop;\n      this.warning = noop;\n      this.error = noop;\n      this.only = noop;\n    }\n  }\n  prefix;\n  extend(domain) {\n    return new Logger(`${this.name}:${domain}`);\n  }\n  /**\n   * Print a debug message.\n   * @example\n   * logger.debug('no duplicates found, creating a document...')\n   */\n  debug(message, ...positionals) {\n    this.logEntry({\n      level: \"debug\",\n      message: gray(message),\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"gray\"\n      }\n    });\n  }\n  /**\n   * Print an info message.\n   * @example\n   * logger.info('start parsing...')\n   */\n  info(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: this.prefix,\n      colors: {\n        prefix: \"blue\"\n      }\n    });\n    const performance2 = new PerformanceEntry();\n    return (message2, ...positionals2) => {\n      performance2.measure();\n      this.logEntry({\n        level: \"info\",\n        message: `${message2} ${gray(`${performance2.deltaTime}ms`)}`,\n        positionals: positionals2,\n        prefix: this.prefix,\n        colors: {\n          prefix: \"blue\"\n        }\n      });\n    };\n  }\n  /**\n   * Print a success message.\n   * @example\n   * logger.success('successfully created document')\n   */\n  success(message, ...positionals) {\n    this.logEntry({\n      level: \"info\",\n      message,\n      positionals,\n      prefix: `\\u2714 ${this.prefix}`,\n      colors: {\n        timestamp: \"green\",\n        prefix: \"green\"\n      }\n    });\n  }\n  /**\n   * Print a warning.\n   * @example\n   * logger.warning('found legacy document format')\n   */\n  warning(message, ...positionals) {\n    this.logEntry({\n      level: \"warning\",\n      message,\n      positionals,\n      prefix: `\\u26A0 ${this.prefix}`,\n      colors: {\n        timestamp: \"yellow\",\n        prefix: \"yellow\"\n      }\n    });\n  }\n  /**\n   * Print an error message.\n   * @example\n   * logger.error('something went wrong')\n   */\n  error(message, ...positionals) {\n    this.logEntry({\n      level: \"error\",\n      message,\n      positionals,\n      prefix: `\\u2716 ${this.prefix}`,\n      colors: {\n        timestamp: \"red\",\n        prefix: \"red\"\n      }\n    });\n  }\n  /**\n   * Execute the given callback only when the logging is enabled.\n   * This is skipped in its entirety and has no runtime cost otherwise.\n   * This executes regardless of the log level.\n   * @example\n   * logger.only(() => {\n   *   logger.info('additional info')\n   * })\n   */\n  only(callback) {\n    callback();\n  }\n  createEntry(level, message) {\n    return {\n      timestamp: /* @__PURE__ */ new Date(),\n      level,\n      message\n    };\n  }\n  logEntry(args) {\n    const {\n      level,\n      message,\n      prefix,\n      colors: customColors,\n      positionals = []\n    } = args;\n    const entry = this.createEntry(level, message);\n    const timestampColor = customColors?.timestamp || \"gray\";\n    const prefixColor = customColors?.prefix || \"gray\";\n    const colorize = {\n      timestamp: colors_exports[timestampColor],\n      prefix: colors_exports[prefixColor]\n    };\n    const write = this.getWriter(level);\n    write(\n      [colorize.timestamp(this.formatTimestamp(entry.timestamp))].concat(prefix != null ? colorize.prefix(prefix) : []).concat(serializeInput(message)).join(\" \"),\n      ...positionals.map(serializeInput)\n    );\n  }\n  formatTimestamp(timestamp) {\n    return `${timestamp.toLocaleTimeString(\n      \"en-GB\"\n    )}:${timestamp.getMilliseconds()}`;\n  }\n  getWriter(level) {\n    switch (level) {\n      case \"debug\":\n      case \"success\":\n      case \"info\": {\n        return log;\n      }\n      case \"warning\": {\n        return warn;\n      }\n      case \"error\": {\n        return error;\n      }\n    }\n  }\n};\nvar PerformanceEntry = class {\n  startTime;\n  endTime;\n  deltaTime;\n  constructor() {\n    this.startTime = performance.now();\n  }\n  measure() {\n    this.endTime = performance.now();\n    const deltaTime = this.endTime - this.startTime;\n    this.deltaTime = deltaTime.toFixed(2);\n  }\n};\nvar noop = () => void 0;\nfunction log(message, ...positionals) {\n  if (IS_NODE) {\n    process.stdout.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.log(message, ...positionals);\n}\nfunction warn(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.warn(message, ...positionals);\n}\nfunction error(message, ...positionals) {\n  if (IS_NODE) {\n    process.stderr.write(format(message, ...positionals) + \"\\n\");\n    return;\n  }\n  console.error(message, ...positionals);\n}\nfunction getVariable(variableName) {\n  if (IS_NODE) {\n    return process.env[variableName];\n  }\n  return globalThis[variableName]?.toString();\n}\nfunction isDefinedAndNotEquals(value, expected) {\n  return value !== void 0 && value !== expected;\n}\nfunction serializeInput(message) {\n  if (typeof message === \"undefined\") {\n    return \"undefined\";\n  }\n  if (message === null) {\n    return \"null\";\n  }\n  if (typeof message === \"string\") {\n    return message;\n  }\n  if (typeof message === \"object\") {\n    return JSON.stringify(message);\n  }\n  return message.toString();\n}\nexport {\n  Logger\n};\n", "import type { Emitter } from './Emitter'\n\nexport class MemoryLeakError extends Error {\n  constructor(\n    public readonly emitter: Emitter<any>,\n    public readonly type: string | number | symbol,\n    public readonly count: number\n  ) {\n    super(\n      `Possible EventEmitter memory leak detected. ${count} ${type.toString()} listeners added. Use emitter.setMaxListeners() to increase limit`\n    )\n    this.name = 'MaxListenersExceededWarning'\n  }\n}\n", "import { MemoryLeakError } from './MemoryLeakError'\n\nexport type EventMap = {\n  [eventName: string]: Array<unknown>\n}\n\nexport type InternalEventNames = 'newListener' | 'removeListener'\n\nexport type InternalListener<Events extends EventMap> = Listener<\n  [eventName: keyof Events, listener: Listener<Array<unknown>>]\n>\n\nexport type Listener<Data extends Array<unknown>> = (...data: Data) => void\n\n/**\n * Node.js-compatible implementation of `EventEmitter`.\n *\n * @example\n * const emitter = new Emitter<{ hello: [string] }>()\n * emitter.on('hello', (name) => console.log(name))\n * emitter.emit('hello', 'John')\n */\nexport class Emitter<Events extends EventMap> {\n  private events: Map<keyof Events, Array<Listener<any>>>\n  private maxListeners: number\n  private hasWarnedAboutPotentialMemoryLeak: boolean\n\n  static defaultMaxListeners = 10\n\n  static listenerCount<Events extends EventMap>(\n    emitter: Emitter<EventMap>,\n    eventName: keyof Events\n  ): number {\n    return emitter.listenerCount<any>(eventName)\n  }\n\n  constructor() {\n    this.events = new Map()\n    this.maxListeners = Emitter.defaultMaxListeners\n    this.hasWarnedAboutPotentialMemoryLeak = false\n  }\n\n  private _emitInternalEvent(\n    internalEventName: InternalEventNames,\n    eventName: keyof Events,\n    listener: Listener<Array<unknown>>\n  ): void {\n    this.emit(\n      internalEventName,\n      // Anything to make TypeScript happy.\n      ...([eventName, listener] as Events['newListener'] &\n        Events['removeListener'])\n    )\n  }\n\n  private _getListeners<EventName extends keyof Events>(\n    eventName: EventName\n  ): Array<Listener<Array<unknown>>> {\n    // Always return a copy of the listeners array\n    // so they are fixed at the time of the \"_getListeners\" call.\n    return Array.prototype.concat.apply([], this.events.get(eventName)) || []\n  }\n\n  private _removeListener<EventName extends keyof Events>(\n    listeners: Array<Listener<Events[EventName]>>,\n    listener: Listener<Events[EventName]>\n  ): Array<Listener<Events[EventName]>> {\n    const index = listeners.indexOf(listener)\n\n    if (index > -1) {\n      listeners.splice(index, 1)\n    }\n\n    return []\n  }\n\n  private _wrapOnceListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): Listener<Events[EventName]> {\n    const onceListener = (...data: Events[keyof Events]) => {\n      this.removeListener(eventName, onceListener)\n\n      /**\n       * @note Return the result of the original listener.\n       * This way this wrapped preserves listeners that are async.\n       */\n      return listener.apply(this, data)\n    }\n\n    // Inherit the name of the original listener.\n    Object.defineProperty(onceListener, 'name', { value: listener.name })\n\n    return onceListener\n  }\n\n  public setMaxListeners(maxListeners: number): this {\n    this.maxListeners = maxListeners\n    return this\n  }\n\n  /**\n   * Returns the current max listener value for the `Emitter` which is\n   * either set by `emitter.setMaxListeners(n)` or defaults to\n   * `Emitter.defaultMaxListeners`.\n   */\n  public getMaxListeners(): number {\n    return this.maxListeners\n  }\n\n  /**\n   * Returns an array listing the events for which the emitter has registered listeners.\n   * The values in the array will be strings or Symbols.\n   */\n  public eventNames(): Array<keyof Events> {\n    return Array.from(this.events.keys())\n  }\n\n  /**\n   * Synchronously calls each of the listeners registered for the event named `eventName`,\n   * in the order they were registered, passing the supplied arguments to each.\n   * Returns `true` if the event has listeners, `false` otherwise.\n   *\n   * @example\n   * const emitter = new Emitter<{ hello: [string] }>()\n   * emitter.emit('hello', 'John')\n   */\n  public emit<EventName extends keyof Events>(\n    eventName: EventName,\n    ...data: Events[EventName]\n  ): boolean {\n    const listeners = this._getListeners(eventName)\n    listeners.forEach((listener) => {\n      listener.apply(this, data)\n    })\n\n    return listeners.length > 0\n  }\n\n  public addListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public addListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public addListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: InternalListener<Events> | Listener<Events[any]>\n  ): this {\n    // Emit the `newListener` event before adding the listener.\n    this._emitInternalEvent('newListener', eventName, listener)\n\n    const nextListeners = this._getListeners(eventName).concat(listener)\n    this.events.set(eventName, nextListeners)\n\n    if (\n      this.maxListeners > 0 &&\n      this.listenerCount(eventName) > this.maxListeners &&\n      !this.hasWarnedAboutPotentialMemoryLeak\n    ) {\n      this.hasWarnedAboutPotentialMemoryLeak = true\n\n      const memoryLeakWarning = new MemoryLeakError(\n        this,\n        eventName,\n        this.listenerCount(eventName)\n      )\n      console.warn(memoryLeakWarning)\n    }\n\n    return this\n  }\n\n  public on(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public on<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public on<EventName extends keyof Events>(\n    eventName: 'removeListener' | EventName,\n    listener: Listener<any>\n  ): this {\n    return this.addListener(eventName, listener)\n  }\n\n  public once(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public once<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public once<EventName extends keyof Events>(\n    eventName: InternalEventNames | EventName,\n    listener: Listener<any>\n  ): this {\n    return this.addListener(\n      eventName,\n      this._wrapOnceListener(eventName, listener)\n    )\n  }\n\n  public prependListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public prependListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public prependListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    const listeners = this._getListeners(eventName)\n\n    if (listeners.length > 0) {\n      const nextListeners = [listener].concat(listeners)\n      this.events.set(eventName, nextListeners)\n    } else {\n      this.events.set(eventName, listeners.concat(listener))\n    }\n\n    return this\n  }\n\n  public prependOnceListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public prependOnceListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public prependOnceListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    return this.prependListener(\n      eventName,\n      this._wrapOnceListener(eventName, listener)\n    )\n  }\n\n  public removeListener(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public removeListener<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  public removeListener(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    const listeners = this._getListeners(eventName)\n\n    if (listeners.length > 0) {\n      this._removeListener(listeners, listener)\n      this.events.set(eventName, listeners)\n\n      // Emit the `removeListener` event after removing the listener.\n      this._emitInternalEvent('removeListener', eventName, listener)\n    }\n\n    return this\n  }\n\n  public off(\n    eventName: InternalEventNames,\n    listener: InternalListener<Events>\n  ): this\n  public off<EventName extends keyof Events>(\n    eventName: EventName,\n    listener: Listener<Events[EventName]>\n  ): this\n  /**\n   * Alias for `emitter.removeListener()`.\n   *\n   * @example\n   * emitter.off('hello', listener)\n   */\n  public off(\n    eventName: InternalEventNames | keyof Events,\n    listener: Listener<any>\n  ): this {\n    return this.removeListener(eventName, listener)\n  }\n\n  public removeAllListeners(eventName?: InternalEventNames): this\n  public removeAllListeners<EventName extends keyof Events>(\n    eventName?: EventName\n  ): this\n  public removeAllListeners(\n    eventName?: InternalEventNames | keyof Events\n  ): this {\n    if (eventName) {\n      this.events.delete(eventName)\n    } else {\n      this.events.clear()\n    }\n\n    return this\n  }\n\n  public listeners(eventName: InternalEventNames): Array<Listener<any>>\n  public listeners<EventName extends keyof Events>(\n    eventName: EventName\n  ): Array<Listener<Events[EventName]>>\n  /**\n   * Returns a copy of the array of listeners for the event named `eventName`.\n   */\n  public listeners(eventName: InternalEventNames | keyof Events) {\n    return Array.from(this._getListeners(eventName))\n  }\n\n  public listenerCount(eventName: InternalEventNames): number\n  public listenerCount<EventName extends keyof Events>(\n    eventName: EventName\n  ): number\n  /**\n   * Returns the number of listeners listening to the event named `eventName`.\n   */\n  public listenerCount(eventName: InternalEventNames | keyof Events): number {\n    return this._getListeners(eventName).length\n  }\n\n  public rawListeners<EventName extends keyof Events>(\n    eventName: EventName\n  ): Array<Listener<Events[EventName]>> {\n    return this.listeners(eventName)\n  }\n}\n", "import { Logger } from '@open-draft/logger'\nimport { Emitter, Listener } from 'strict-event-emitter'\n\nexport type InterceptorEventMap = Record<string, any>\nexport type InterceptorSubscription = () => void\n\n/**\n * Request header name to detect when a single request\n * is being handled by nested interceptors (XHR -> ClientRequest).\n * Obscure by design to prevent collisions with user-defined headers.\n * Ideally, come up with the Interceptor-level mechanism for this.\n * @see https://github.com/mswjs/interceptors/issues/378\n */\nexport const INTERNAL_REQUEST_ID_HEADER_NAME =\n  'x-interceptors-internal-request-id'\n\nexport function getGlobalSymbol<V>(symbol: Symbol): V | undefined {\n  return (\n    // @ts-ignore https://github.com/Microsoft/TypeScript/issues/24587\n    globalThis[symbol] || undefined\n  )\n}\n\nfunction setGlobalSymbol(symbol: Symbol, value: any): void {\n  // @ts-ignore\n  globalThis[symbol] = value\n}\n\nexport function deleteGlobalSymbol(symbol: Symbol): void {\n  // @ts-ignore\n  delete globalThis[symbol]\n}\n\nexport enum InterceptorReadyState {\n  INACTIVE = 'INACTIVE',\n  APPLYING = 'APPLYING',\n  APPLIED = 'APPLIED',\n  DISPOSING = 'DISPOSING',\n  DISPOSED = 'DISPOSED',\n}\n\nexport type ExtractEventNames<Events extends Record<string, any>> =\n  Events extends Record<infer EventName, any> ? EventName : never\n\nexport class Interceptor<Events extends InterceptorEventMap> {\n  protected emitter: Emitter<Events>\n  protected subscriptions: Array<InterceptorSubscription>\n  protected logger: Logger\n\n  public readyState: InterceptorReadyState\n\n  constructor(private readonly symbol: symbol) {\n    this.readyState = InterceptorReadyState.INACTIVE\n\n    this.emitter = new Emitter()\n    this.subscriptions = []\n    this.logger = new Logger(symbol.description!)\n\n    // Do not limit the maximum number of listeners\n    // so not to limit the maximum amount of parallel events emitted.\n    this.emitter.setMaxListeners(0)\n\n    this.logger.info('constructing the interceptor...')\n  }\n\n  /**\n   * Determine if this interceptor can be applied\n   * in the current environment.\n   */\n  protected checkEnvironment(): boolean {\n    return true\n  }\n\n  /**\n   * Apply this interceptor to the current process.\n   * Returns an already running interceptor instance if it's present.\n   */\n  public apply(): void {\n    const logger = this.logger.extend('apply')\n    logger.info('applying the interceptor...')\n\n    if (this.readyState === InterceptorReadyState.APPLIED) {\n      logger.info('intercepted already applied!')\n      return\n    }\n\n    const shouldApply = this.checkEnvironment()\n\n    if (!shouldApply) {\n      logger.info('the interceptor cannot be applied in this environment!')\n      return\n    }\n\n    this.readyState = InterceptorReadyState.APPLYING\n\n    // Whenever applying a new interceptor, check if it hasn't been applied already.\n    // This enables to apply the same interceptor multiple times, for example from a different\n    // interceptor, only proxying events but keeping the stubs in a single place.\n    const runningInstance = this.getInstance()\n\n    if (runningInstance) {\n      logger.info('found a running instance, reusing...')\n\n      // Proxy any listeners you set on this instance to the running instance.\n      this.on = (event, listener) => {\n        logger.info('proxying the \"%s\" listener', event)\n\n        // Add listeners to the running instance so they appear\n        // at the top of the event listeners list and are executed first.\n        runningInstance.emitter.addListener(event, listener)\n\n        // Ensure that once this interceptor instance is disposed,\n        // it removes all listeners it has appended to the running interceptor instance.\n        this.subscriptions.push(() => {\n          runningInstance.emitter.removeListener(event, listener)\n          logger.info('removed proxied \"%s\" listener!', event)\n        })\n\n        return this\n      }\n\n      this.readyState = InterceptorReadyState.APPLIED\n\n      return\n    }\n\n    logger.info('no running instance found, setting up a new instance...')\n\n    // Setup the interceptor.\n    this.setup()\n\n    // Store the newly applied interceptor instance globally.\n    this.setInstance()\n\n    this.readyState = InterceptorReadyState.APPLIED\n  }\n\n  /**\n   * Setup the module augments and stubs necessary for this interceptor.\n   * This method is not run if there's a running interceptor instance\n   * to prevent instantiating an interceptor multiple times.\n   */\n  protected setup(): void {}\n\n  /**\n   * Listen to the interceptor's public events.\n   */\n  public on<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    const logger = this.logger.extend('on')\n\n    if (\n      this.readyState === InterceptorReadyState.DISPOSING ||\n      this.readyState === InterceptorReadyState.DISPOSED\n    ) {\n      logger.info('cannot listen to events, already disposed!')\n      return this\n    }\n\n    logger.info('adding \"%s\" event listener:', event, listener)\n\n    this.emitter.on(event, listener)\n    return this\n  }\n\n  public once<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    this.emitter.once(event, listener)\n    return this\n  }\n\n  public off<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    this.emitter.off(event, listener)\n    return this\n  }\n\n  public removeAllListeners<EventName extends ExtractEventNames<Events>>(\n    event?: EventName\n  ): this {\n    this.emitter.removeAllListeners(event)\n    return this\n  }\n\n  /**\n   * Disposes of any side-effects this interceptor has introduced.\n   */\n  public dispose(): void {\n    const logger = this.logger.extend('dispose')\n\n    if (this.readyState === InterceptorReadyState.DISPOSED) {\n      logger.info('cannot dispose, already disposed!')\n      return\n    }\n\n    logger.info('disposing the interceptor...')\n    this.readyState = InterceptorReadyState.DISPOSING\n\n    if (!this.getInstance()) {\n      logger.info('no interceptors running, skipping dispose...')\n      return\n    }\n\n    // Delete the global symbol as soon as possible,\n    // indicating that the interceptor is no longer running.\n    this.clearInstance()\n\n    logger.info('global symbol deleted:', getGlobalSymbol(this.symbol))\n\n    if (this.subscriptions.length > 0) {\n      logger.info('disposing of %d subscriptions...', this.subscriptions.length)\n\n      for (const dispose of this.subscriptions) {\n        dispose()\n      }\n\n      this.subscriptions = []\n\n      logger.info('disposed of all subscriptions!', this.subscriptions.length)\n    }\n\n    this.emitter.removeAllListeners()\n    logger.info('destroyed the listener!')\n\n    this.readyState = InterceptorReadyState.DISPOSED\n  }\n\n  private getInstance(): this | undefined {\n    const instance = getGlobalSymbol<this>(this.symbol)\n    this.logger.info('retrieved global instance:', instance?.constructor?.name)\n    return instance\n  }\n\n  private setInstance(): void {\n    setGlobalSymbol(this.symbol, this)\n    this.logger.info('set global instance!', this.symbol.description)\n  }\n\n  private clearInstance(): void {\n    deleteGlobalSymbol(this.symbol)\n    this.logger.info('cleared global instance!', this.symbol.description)\n  }\n}\n", "/**\n * Generate a random ID string to represent a request.\n * @example\n * createRequestId()\n * // \"f774b6c9c600f\"\n */\nexport function createRequestId(): string {\n  return Math.random().toString(16).slice(2)\n}\n", "import { EventMap, Listener } from 'strict-event-emitter'\nimport { Interceptor, ExtractEventNames } from './Interceptor'\n\nexport interface BatchInterceptorOptions<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> {\n  name: string\n  interceptors: InterceptorList\n}\n\nexport type ExtractEventMapType<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> = InterceptorList extends ReadonlyArray<infer InterceptorType>\n  ? InterceptorType extends Interceptor<infer EventMap>\n    ? EventMap\n    : never\n  : never\n\n/**\n * A batch interceptor that exposes a single interface\n * to apply and operate with multiple interceptors at once.\n */\nexport class BatchInterceptor<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>,\n  Events extends EventMap = ExtractEventMapType<InterceptorList>\n> extends Interceptor<Events> {\n  static symbol: symbol\n\n  private interceptors: InterceptorList\n\n  constructor(options: BatchInterceptorOptions<InterceptorList>) {\n    BatchInterceptor.symbol = Symbol(options.name)\n    super(BatchInterceptor.symbol)\n    this.interceptors = options.interceptors\n  }\n\n  protected setup() {\n    const logger = this.logger.extend('setup')\n\n    logger.info('applying all %d interceptors...', this.interceptors.length)\n\n    for (const interceptor of this.interceptors) {\n      logger.info('applying \"%s\" interceptor...', interceptor.constructor.name)\n      interceptor.apply()\n\n      logger.info('adding interceptor dispose subscription')\n      this.subscriptions.push(() => interceptor.dispose())\n    }\n  }\n\n  public on<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    // Instead of adding a listener to the batch interceptor,\n    // propagate the listener to each of the individual interceptors.\n    for (const interceptor of this.interceptors) {\n      interceptor.on(event, listener)\n    }\n\n    return this\n  }\n\n  public once<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.once(event, listener)\n    }\n\n    return this\n  }\n\n  public off<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.off(event, listener)\n    }\n\n    return this\n  }\n\n  public removeAllListeners<EventName extends ExtractEventNames<Events>>(\n    event?: EventName | undefined\n  ): this {\n    for (const interceptors of this.interceptors) {\n      interceptors.removeAllListeners(event)\n    }\n\n    return this\n  }\n}\n", "/**\n * Removes query parameters and hashes from a given URL.\n */\nexport function getCleanUrl(url: URL, isAbsolute: boolean = true): string {\n  return [isAbsolute && url.origin, url.pathname].filter(Boolean).join('')\n}\n", "import { FetchResponse } from '@mswjs/interceptors'\nimport type {\n  ServiceWorkerIncomingEventsMap,\n  SetupWorkerInternalContext,\n} from '../glossary'\nimport type { ServiceWorkerMessage } from './utils/createMessageChannel'\nimport { deserializeRequest } from '../../utils/deserializeRequest'\n\nexport function createResponseListener(context: SetupWorkerInternalContext) {\n  return (\n    _: MessageEvent,\n    message: ServiceWorkerMessage<\n      'RESPONSE',\n      ServiceWorkerIncomingEventsMap['RESPONSE']\n    >,\n  ) => {\n    const { payload: responseJson } = message\n    const request = deserializeRequest(responseJson.request)\n\n    /**\n     * CORS requests with `mode: \"no-cors\"` result in \"opaque\" responses.\n     * That kind of responses cannot be manipulated in JavaScript due\n     * to the security considerations.\n     * @see https://fetch.spec.whatwg.org/#concept-filtered-response-opaque\n     * @see https://github.com/mswjs/msw/issues/529\n     */\n    if (responseJson.response.type?.includes('opaque')) {\n      return\n    }\n\n    const response =\n      responseJson.response.status === 0\n        ? Response.error()\n        : new FetchResponse(\n            /**\n             * Responses may be streams here, but when we create a response object\n             * with null-body status codes, like 204, 205, 304 Response will\n             * throw when passed a non-null body, so ensure it's null here\n             * for those codes\n             */\n            FetchResponse.isResponseWithBody(responseJson.response.status)\n              ? responseJson.response.body\n              : null,\n            {\n              ...responseJson,\n              /**\n               * Set response URL if it's not set already.\n               * @see https://github.com/mswjs/msw/issues/2030\n               * @see https://developer.mozilla.org/en-US/docs/Web/API/Response/url\n               */\n              url: request.url,\n            },\n          )\n\n    context.emitter.emit(\n      responseJson.isMockedResponse ? 'response:mocked' : 'response:bypass',\n      {\n        requestId: responseJson.request.id,\n        request,\n        response,\n      },\n    )\n  }\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport { StartOptions } from '../../glossary'\n\nexport function validateWorkerScope(\n  registration: ServiceWorkerRegistration,\n  options?: StartOptions,\n): void {\n  if (!options?.quiet && !location.href.startsWith(registration.scope)) {\n    devUtils.warn(\n      `\\\nCannot intercept requests on this page because it's outside of the worker's scope (\"${registration.scope}\"). If you wish to mock API requests on this page, you must resolve this scope issue.\n\n- (Recommended) Register the worker at the root level (\"/\") of your application.\n- Set the \"Service-Worker-Allowed\" response header to allow out-of-scope workers.\\\n`,\n    )\n  }\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\nimport { SetupWorkerInternalContext, StopHandler } from '../glossary'\nimport { printStopMessage } from './utils/printStopMessage'\n\nexport const createStop = (\n  context: SetupWorkerInternalContext,\n): StopHandler => {\n  return function stop() {\n    // Warn developers calling \"worker.stop()\" more times than necessary.\n    // This likely indicates a mistake in their code.\n    if (!context.isMockingEnabled) {\n      devUtils.warn(\n        'Found a redundant \"worker.stop()\" call. Note that stopping the worker while mocking already stopped has no effect. Consider removing this \"worker.stop()\" call.',\n      )\n      return\n    }\n\n    /**\n     * Signal the Service Worker to disable mocking for this client.\n     * Use this an an explicit way to stop the mocking, while preserving\n     * the worker-client relation. Does not affect the worker's lifecycle.\n     */\n    context.workerChannel.send('MOCK_DEACTIVATE')\n    context.isMockingEnabled = false\n    window.clearInterval(context.keepAliveInterval)\n\n    // Post the internal stop message on the window\n    // to let any logic know when the worker has stopped.\n    // E.g. the WebSocket client manager needs this to know\n    // when to clear its in-memory clients list.\n    window.postMessage({ type: 'msw/worker:stop' })\n\n    printStopMessage({ quiet: context.startOptions?.quiet })\n  }\n}\n", "import { devUtils } from '~/core/utils/internal/devUtils'\n\nexport function printStopMessage(args: { quiet?: boolean } = {}): void {\n  if (args.quiet) {\n    return\n  }\n\n  // eslint-disable-next-line no-console\n  console.log(\n    `%c${devUtils.formatMessage('Mocking disabled.')}`,\n    'color:orangered;font-weight:bold;',\n  )\n}\n", "import { RequiredDeep } from '~/core/typeUtils'\nimport { mergeRight } from '~/core/utils/internal/mergeRight'\nimport {\n  SetupWorker,\n  SetupWorkerInternalContext,\n  StartHandler,\n  StartOptions,\n} from '../../glossary'\n\nexport const DEFAULT_START_OPTIONS: RequiredDeep<StartOptions> = {\n  serviceWorker: {\n    url: '/mockServiceWorker.js',\n    options: null as any,\n  },\n  quiet: false,\n  waitUntilReady: true,\n  onUnhandledRequest: 'warn',\n  findWorker(scriptURL, mockServiceWorkerUrl) {\n    return scriptURL === mockServiceWorkerUrl\n  },\n}\n\n/**\n * Returns resolved worker start options, merging the default options\n * with the given custom options.\n */\nexport function resolveStartOptions(\n  initialOptions?: StartOptions,\n): RequiredDeep<StartOptions> {\n  return mergeRight(\n    DEFAULT_START_OPTIONS,\n    initialOptions || {},\n  ) as RequiredDeep<StartOptions>\n}\n\nexport function prepareStartHandler(\n  handler: <PERSON><PERSON><PERSON><PERSON>,\n  context: SetupWorkerInternalContext,\n): SetupWorker['start'] {\n  return (initialOptions) => {\n    context.startOptions = resolveStartOptions(initialOptions)\n    return handler(context.startOptions, initialOptions || {})\n  }\n}\n", "export type PromiseState = 'pending' | 'fulfilled' | 'rejected'\n\nexport type Executor<Value> = ConstructorParameters<typeof Promise<Value>>[0]\nexport type ResolveFunction<Value> = Parameters<Executor<Value>>[0]\nexport type RejectFunction<Reason> = Parameters<Executor<Reason>>[1]\n\nexport type DeferredPromiseExecutor<Input = never, Output = Input> = {\n  (resolve?: ResolveFunction<Input>, reject?: RejectFunction<any>): void\n\n  resolve: ResolveFunction<Input>\n  reject: RejectFunction<any>\n  result?: Output\n  state: PromiseState\n  rejectionReason?: unknown\n}\nexport function createDeferredExecutor<\n  Input = never,\n  Output = Input\n>(): DeferredPromiseExecutor<Input, Output> {\n  const executor = <DeferredPromiseExecutor<Input, Output>>((\n    resolve,\n    reject\n  ) => {\n    executor.state = 'pending'\n\n    executor.resolve = (data) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      executor.result = data as Output\n\n      const onFulfilled = <Value>(value: Value) => {\n        executor.state = 'fulfilled'\n        return value\n      }\n\n      return resolve(\n        data instanceof Promise ? data : Promise.resolve(data).then(onFulfilled)\n      )\n    }\n\n    executor.reject = (reason) => {\n      if (executor.state !== 'pending') {\n        return\n      }\n\n      queueMicrotask(() => {\n        executor.state = 'rejected'\n      })\n\n      return reject((executor.rejectionReason = reason))\n    }\n  })\n\n  return executor\n}\n", "import {\n  type Executor,\n  type RejectFunction,\n  type ResolveFunction,\n  type DeferredPromiseExecutor,\n  createDeferredExecutor,\n} from './createDeferredExecutor'\n\nexport class DeferredPromise<Input, Output = Input> extends Promise<Input> {\n  #executor: DeferredPromiseExecutor\n\n  public resolve: ResolveFunction<Output>\n  public reject: RejectFunction<Output>\n\n  constructor(executor: Executor<Input> | null = null) {\n    const deferredExecutor = createDeferredExecutor()\n    super((originalResolve, originalReject) => {\n      deferredExecutor(originalResolve, originalReject)\n      executor?.(deferredExecutor.resolve, deferredExecutor.reject)\n    })\n\n    this.#executor = deferredExecutor\n    this.resolve = this.#executor.resolve\n    this.reject = this.#executor.reject\n  }\n\n  public get state() {\n    return this.#executor.state\n  }\n\n  public get rejectionReason() {\n    return this.#executor.rejectionReason\n  }\n\n  public then<ThenResult = Input, CatchResult = never>(\n    onFulfilled?: (value: Input) => ThenResult | PromiseLike<ThenResult>,\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.then(onFulfilled, onRejected))\n  }\n\n  public catch<CatchResult = never>(\n    onRejected?: (reason: any) => CatchResult | PromiseLike<CatchResult>\n  ) {\n    return this.#decorate(super.catch(onRejected))\n  }\n\n  public finally(onfinally?: () => void | Promise<any>) {\n    return this.#decorate(super.finally(onfinally))\n  }\n\n  #decorate<ChildInput>(\n    promise: Promise<ChildInput>\n  ): DeferredPromise<ChildInput, Output> {\n    return Object.defineProperties(promise, {\n      resolve: { configurable: true, value: this.resolve },\n      reject: { configurable: true, value: this.reject },\n    }) as DeferredPromise<ChildInput, Output>\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\nimport { InterceptorError } from './InterceptorError'\n\nconst kRequestHandled = Symbol('kRequestHandled')\nexport const kResponsePromise = Symbol('kResponsePromise')\n\nexport class RequestController {\n  /**\n   * Internal response promise.\n   * Available only for the library internals to grab the\n   * response instance provided by the developer.\n   * @note This promise cannot be rejected. It's either infinitely\n   * pending or resolved with whichever Response was passed to `respondWith()`.\n   */\n  [kResponsePromise]: DeferredPromise<\n    Response | Record<string, any> | undefined\n  >;\n\n  /**\n   * Internal flag indicating if this request has been handled.\n   * @note The response promise becomes \"fulfilled\" on the next tick.\n   */\n  [kRequestHandled]: boolean\n\n  constructor(private request: Request) {\n    this[kRequestHandled] = false\n    this[kResponsePromise] = new DeferredPromise()\n  }\n\n  /**\n   * Respond to this request with the given `Response` instance.\n   * @example\n   * controller.respondWith(new Response())\n   * controller.respondWith(Response.json({ id }))\n   * controller.respondWith(Response.error())\n   */\n  public respondWith(response: Response): void {\n    invariant.as(\n      InterceptorError,\n      !this[kRequestHandled],\n      'Failed to respond to the \"%s %s\" request: the \"request\" event has already been handled.',\n      this.request.method,\n      this.request.url\n    )\n\n    this[kRequestHandled] = true\n    this[kResponsePromise].resolve(response)\n\n    /**\n     * @note The request controller doesn't do anything\n     * apart from letting the interceptor await the response\n     * provided by the developer through the response promise.\n     * Each interceptor implements the actual respondWith/errorWith\n     * logic based on that interceptor's needs.\n     */\n  }\n\n  /**\n   * Error this request with the given reason.\n   *\n   * @example\n   * controller.errorWith()\n   * controller.errorWith(new Error('Oops!'))\n   * controller.errorWith({ message: 'Oops!'})\n   */\n  public errorWith(reason?: Error | Record<string, any>): void {\n    invariant.as(\n      InterceptorError,\n      !this[kRequestHandled],\n      'Failed to error the \"%s %s\" request: the \"request\" event has already been handled.',\n      this.request.method,\n      this.request.url\n    )\n\n    this[kRequestHandled] = true\n\n    /**\n     * @note Resolve the response promise, not reject.\n     * This helps us differentiate between unhandled exceptions\n     * and intended errors (\"errorWith\") while waiting for the response.\n     */\n    this[kResponsePromise].resolve(reason)\n  }\n}\n", "export class InterceptorError extends Error {\n  constructor(message?: string) {\n    super(message)\n    this.name = 'InterceptorError'\n    Object.setPrototypeOf(this, InterceptorError.prototype)\n  }\n}\n", "import { Emitter, EventMap } from 'strict-event-emitter'\n\n/**\n * Emits an event on the given emitter but executes\n * the listeners sequentially. This accounts for asynchronous\n * listeners (e.g. those having \"sleep\" and handling the request).\n */\nexport async function emitAsync<\n  Events extends EventMap,\n  EventName extends keyof Events\n>(\n  emitter: Emitter<Events>,\n  eventName: EventName,\n  ...data: Events[EventName]\n): Promise<void> {\n  const listners = emitter.listeners(eventName)\n\n  if (listners.length === 0) {\n    return\n  }\n\n  for (const listener of listners) {\n    await listener.apply(emitter, data)\n  }\n}\n", "import type { Emitter } from 'strict-event-emitter'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\nimport { until } from '@open-draft/until'\nimport type { HttpRequestEventMap } from '../glossary'\nimport { emitAsync } from './emitAsync'\nimport { k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request<PERSON>ontroller } from '../RequestController'\nimport {\n  createServerErrorResponse,\n  isResponseError,\n  isResponseLike,\n  ResponseError,\n} from './responseUtils'\nimport { InterceptorError } from '../InterceptorError'\nimport { isNodeLikeError } from './isNodeLikeError'\nimport { isObject } from './isObject'\n\ninterface HandleRequestOptions {\n  requestId: string\n  request: Request\n  emitter: Emitter<HttpRequestEventMap>\n  controller: RequestController\n\n  /**\n   * Called when the request has been handled\n   * with the given `Response` instance.\n   */\n  onResponse: (response: Response) => void | Promise<void>\n\n  /**\n   * Called when the request has been handled\n   * with the given `Response.error()` instance.\n   */\n  onRequestError: (response: ResponseError) => void\n\n  /**\n   * Called when an unhandled error happens during the\n   * request handling. This is never a thrown error/response.\n   */\n  onError: (error: unknown) => void\n}\n\n/**\n * @returns {Promise<boolean>} Indicates whether the request has been handled.\n */\nexport async function handleRequest(\n  options: HandleRequestOptions\n): Promise<boolean> {\n  const handleResponse = async (\n    response: Response | Error | Record<string, any>\n  ) => {\n    if (response instanceof Error) {\n      options.onError(response)\n      return true\n    }\n\n    // Handle \"Response.error()\" instances.\n    if (isResponseError(response)) {\n      options.onRequestError(response)\n      return true\n    }\n\n    /**\n     * Handle normal responses or response-like objects.\n     * @note This must come before the arbitrary object check\n     * since Response instances are, in fact, objects.\n     */\n    if (isResponseLike(response)) {\n      await options.onResponse(response)\n      return true\n    }\n\n    // Handle arbitrary objects provided to `.errorWith(reason)`.\n    if (isObject(response)) {\n      options.onError(response)\n      return true\n    }\n\n    return false\n  }\n\n  const handleResponseError = async (error: unknown): Promise<boolean> => {\n    // Forward the special interceptor error instances\n    // to the developer. These must not be handled in any way.\n    if (error instanceof InterceptorError) {\n      throw result.error\n    }\n\n    // Support mocking Node.js-like errors.\n    if (isNodeLikeError(error)) {\n      options.onError(error)\n      return true\n    }\n\n    // Handle thrown responses.\n    if (error instanceof Response) {\n      return await handleResponse(error)\n    }\n\n    return false\n  }\n\n  // Add the last \"request\" listener to check if the request\n  // has been handled in any way. If it hasn't, resolve the\n  // response promise with undefined.\n  options.emitter.once('request', ({ requestId: pendingRequestId }) => {\n    if (pendingRequestId !== options.requestId) {\n      return\n    }\n\n    if (options.controller[kResponsePromise].state === 'pending') {\n      options.controller[kResponsePromise].resolve(undefined)\n    }\n  })\n\n  const requestAbortPromise = new DeferredPromise<void, unknown>()\n\n  /**\n   * @note `signal` is not always defined in React Native.\n   */\n  if (options.request.signal) {\n    if (options.request.signal.aborted) {\n      requestAbortPromise.reject(options.request.signal.reason)\n    } else {\n      options.request.signal.addEventListener(\n        'abort',\n        () => {\n          requestAbortPromise.reject(options.request.signal.reason)\n        },\n        { once: true }\n      )\n    }\n  }\n\n  const result = await until(async () => {\n    // Emit the \"request\" event and wait until all the listeners\n    // for that event are finished (e.g. async listeners awaited).\n    // By the end of this promise, the developer cannot affect the\n    // request anymore.\n    const requestListenersPromise = emitAsync(options.emitter, 'request', {\n      requestId: options.requestId,\n      request: options.request,\n      controller: options.controller,\n    })\n\n    await Promise.race([\n      // Short-circuit the request handling promise if the request gets aborted.\n      requestAbortPromise,\n      requestListenersPromise,\n      options.controller[kResponsePromise],\n    ])\n\n    // The response promise will settle immediately once\n    // the developer calls either \"respondWith\" or \"errorWith\".\n    return await options.controller[kResponsePromise]\n  })\n\n  // Handle the request being aborted while waiting for the request listeners.\n  if (requestAbortPromise.state === 'rejected') {\n    options.onError(requestAbortPromise.rejectionReason)\n    return true\n  }\n\n  if (result.error) {\n    // Handle the error during the request listener execution.\n    // These can be thrown responses or request errors.\n    if (await handleResponseError(result.error)) {\n      return true\n    }\n\n    // If the developer has added \"unhandledException\" listeners,\n    // allow them to handle the error. They can translate it to a\n    // mocked response, network error, or forward it as-is.\n    if (options.emitter.listenerCount('unhandledException') > 0) {\n      // Create a new request controller just for the unhandled exception case.\n      // This is needed because the original controller might have been already\n      // interacted with (e.g. \"respondWith\" or \"errorWith\" called on it).\n      const unhandledExceptionController = new RequestController(\n        options.request\n      )\n\n      await emitAsync(options.emitter, 'unhandledException', {\n        error: result.error,\n        request: options.request,\n        requestId: options.requestId,\n        controller: unhandledExceptionController,\n      }).then(() => {\n        // If all the \"unhandledException\" listeners have finished\n        // but have not handled the response in any way, preemptively\n        // resolve the pending response promise from the new controller.\n        // This prevents it from hanging forever.\n        if (\n          unhandledExceptionController[kResponsePromise].state === 'pending'\n        ) {\n          unhandledExceptionController[kResponsePromise].resolve(undefined)\n        }\n      })\n\n      const nextResult = await until(\n        () => unhandledExceptionController[kResponsePromise]\n      )\n\n      /**\n       * @note Handle the result of the unhandled controller\n       * in the same way as the original request controller.\n       * The exception here is that thrown errors within the\n       * \"unhandledException\" event do NOT result in another\n       * emit of the same event. They are forwarded as-is.\n       */\n      if (nextResult.error) {\n        return handleResponseError(nextResult.error)\n      }\n\n      if (nextResult.data) {\n        return handleResponse(nextResult.data)\n      }\n    }\n\n    // Otherwise, coerce unhandled exceptions to a 500 Internal Server Error response.\n    options.onResponse(createServerErrorResponse(result.error))\n    return true\n  }\n\n  /**\n   * Handle a mocked Response instance.\n   * @note That this can also be an Error in case\n   * the developer called \"errorWith\". This differentiates\n   * unhandled exceptions from intended errors.\n   */\n  if (result.data) {\n    return handleResponse(result.data)\n  }\n\n  // In all other cases, consider the request unhandled.\n  return false\n}\n", "/**\n * Determines if a given value is an instance of object.\n */\nexport function isObject<T>(value: any, loose = false): value is T {\n  return loose\n    ? Object.prototype.toString.call(value).startsWith('[object ')\n    : Object.prototype.toString.call(value) === '[object Object]'\n}\n", "/**\n * A function that validates if property access is possible on an object\n * without throwing. It returns `true` if the property access is possible\n * and `false` otherwise.\n *\n * Environments like miniflare will throw on property access on certain objects\n * like Request and Response, for unimplemented properties.\n */\nexport function isPropertyAccessible<Obj extends Record<string, any>>(\n  obj: Obj,\n  key: keyof Obj\n) {\n  try {\n    obj[key]\n    return true\n  } catch {\n    return false\n  }\n}\n", "import { isObject } from './isObject'\nimport { isPropertyAccessible } from './isPropertyAccessible'\n\n/**\n * Creates a generic 500 Unhandled Exception response.\n */\nexport function createServerErrorResponse(body: unknown): Response {\n  return new Response(\n    JSON.stringify(\n      body instanceof Error\n        ? {\n            name: body.name,\n            message: body.message,\n            stack: body.stack,\n          }\n        : body\n    ),\n    {\n      status: 500,\n      statusText: 'Unhandled Exception',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    }\n  )\n}\n\nexport type ResponseError = Response & { type: 'error' }\n\n/**\n * Check if the given response is a `Response.error()`.\n *\n * @note Some environments, like Miniflare (Cloudflare) do not\n * implement the \"Response.type\" property and throw on its access.\n * Safely check if we can access \"type\" on \"Response\" before continuing.\n * @see https://github.com/mswjs/msw/issues/1834\n */\nexport function isResponseError(response: unknown): response is ResponseError {\n  return (\n    response != null &&\n    response instanceof Response &&\n    isPropertyAccessible(response, 'type') &&\n    response.type === 'error'\n  )\n}\n\n/**\n * Check if the given value is a `Response` or a Response-like object.\n * This is different from `value instanceof Response` because it supports\n * custom `Response` constructors, like the one when using Undici directly.\n */\nexport function isResponseLike(value: unknown): value is Response {\n  return (\n    isObject<Record<string, any>>(value, true) &&\n    isPropertyAccessible(value, 'status') &&\n    isPropertyAccessible(value, 'statusText') &&\n    isPropertyAccessible(value, 'bodyUsed')\n  )\n}\n", "export function isNodeLikeError(\n  error: unknown\n): error is NodeJS.ErrnoException {\n  if (error == null) {\n    return false\n  }\n\n  if (!(error instanceof Error)) {\n    return false\n  }\n\n  return 'code' in error && 'errno' in error\n}\n", "/**\n * Returns a boolean indicating whether the given global property\n * is defined and is configurable.\n */\nexport function hasConfigurableGlobal(propertyName: string): boolean {\n  const descriptor = Object.getOwnPropertyDescriptor(globalThis, propertyName)\n\n  // The property is not set at all.\n  if (typeof descriptor === 'undefined') {\n    return false\n  }\n\n  // The property is set to a getter that returns undefined.\n  if (\n    typeof descriptor.get === 'function' &&\n    typeof descriptor.get() === 'undefined'\n  ) {\n    return false\n  }\n\n  // The property is set to a value equal to undefined.\n  if (typeof descriptor.get === 'undefined' && descriptor.value == null) {\n    return false\n  }\n\n  if (typeof descriptor.set === 'undefined' && !descriptor.configurable) {\n    console.error(\n      `[MSW] Failed to apply interceptor: the global \\`${propertyName}\\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`\n    )\n    return false\n  }\n\n  return true\n}\n", "import { invariant } from 'outvariant'\nimport { DeferredPromise } from '@open-draft/deferred-promise'\nimport { HttpRequestEventMap, IS_PATCHED_MODULE } from '../../glossary'\nimport { Interceptor } from '../../Interceptor'\nimport { RequestController } from '../../RequestController'\nimport { emitAsync } from '../../utils/emitAsync'\nimport { handleRequest } from '../../utils/handleRequest'\nimport { canParseUrl } from '../../utils/canParseUrl'\nimport { createRequestId } from '../../createRequestId'\nimport { createNetworkError } from './utils/createNetworkError'\nimport { followFetchRedirect } from './utils/followRedirect'\nimport { decompressResponse } from './utils/decompression'\nimport { hasConfigurableGlobal } from '../../utils/hasConfigurableGlobal'\nimport { FetchResponse } from '../../utils/fetchUtils'\nimport { setRawRequest } from '../../getRawRequest'\n\nexport class FetchInterceptor extends Interceptor<HttpRequestEventMap> {\n  static symbol = Symbol('fetch')\n\n  constructor() {\n    super(FetchInterceptor.symbol)\n  }\n\n  protected checkEnvironment() {\n    return hasConfigurableGlobal('fetch')\n  }\n\n  protected async setup() {\n    const pureFetch = globalThis.fetch\n\n    invariant(\n      !(pureFetch as any)[IS_PATCHED_MODULE],\n      'Failed to patch the \"fetch\" module: already patched.'\n    )\n\n    globalThis.fetch = async (input, init) => {\n      const requestId = createRequestId()\n\n      /**\n       * @note Resolve potentially relative request URL\n       * against the present `location`. This is mainly\n       * for native `fetch` in JSDOM.\n       * @see https://github.com/mswjs/msw/issues/1625\n       */\n      const resolvedInput =\n        typeof input === 'string' &&\n        typeof location !== 'undefined' &&\n        !canParseUrl(input)\n          ? new URL(input, location.href)\n          : input\n\n      const request = new Request(resolvedInput, init)\n\n      /**\n       * @note Set the raw request only if a Request instance was provided to fetch.\n       */\n      if (input instanceof Request) {\n        setRawRequest(request, input)\n      }\n\n      const responsePromise = new DeferredPromise<Response>()\n      const controller = new RequestController(request)\n\n      this.logger.info('[%s] %s', request.method, request.url)\n      this.logger.info('awaiting for the mocked response...')\n\n      this.logger.info(\n        'emitting the \"request\" event for %s listener(s)...',\n        this.emitter.listenerCount('request')\n      )\n\n      const isRequestHandled = await handleRequest({\n        request,\n        requestId,\n        emitter: this.emitter,\n        controller,\n        onResponse: async (rawResponse) => {\n          this.logger.info('received mocked response!', {\n            rawResponse,\n          })\n\n          // Decompress the mocked response body, if applicable.\n          const decompressedStream = decompressResponse(rawResponse)\n          const response =\n            decompressedStream === null\n              ? rawResponse\n              : new FetchResponse(decompressedStream, rawResponse)\n\n          FetchResponse.setUrl(request.url, response)\n\n          /**\n           * Undici's handling of following redirect responses.\n           * Treat the \"manual\" redirect mode as a regular mocked response.\n           * This way, the client can manually follow the redirect it receives.\n           * @see https://github.com/nodejs/undici/blob/a6dac3149c505b58d2e6d068b97f4dc993da55f0/lib/web/fetch/index.js#L1173\n           */\n          if (FetchResponse.isRedirectResponse(response.status)) {\n            // Reject the request promise if its `redirect` is set to `error`\n            // and it receives a mocked redirect response.\n            if (request.redirect === 'error') {\n              responsePromise.reject(createNetworkError('unexpected redirect'))\n              return\n            }\n\n            if (request.redirect === 'follow') {\n              followFetchRedirect(request, response).then(\n                (response) => {\n                  responsePromise.resolve(response)\n                },\n                (reason) => {\n                  responsePromise.reject(reason)\n                }\n              )\n              return\n            }\n          }\n\n          if (this.emitter.listenerCount('response') > 0) {\n            this.logger.info('emitting the \"response\" event...')\n\n            // Await the response listeners to finish before resolving\n            // the response promise. This ensures all your logic finishes\n            // before the interceptor resolves the pending response.\n            await emitAsync(this.emitter, 'response', {\n              // Clone the mocked response for the \"response\" event listener.\n              // This way, the listener can read the response and not lock its body\n              // for the actual fetch consumer.\n              response: response.clone(),\n              isMockedResponse: true,\n              request,\n              requestId,\n            })\n          }\n\n          responsePromise.resolve(response)\n        },\n        onRequestError: (response) => {\n          this.logger.info('request has errored!', { response })\n          responsePromise.reject(createNetworkError(response))\n        },\n        onError: (error) => {\n          this.logger.info('request has been aborted!', { error })\n          responsePromise.reject(error)\n        },\n      })\n\n      if (isRequestHandled) {\n        this.logger.info('request has been handled, returning mock promise...')\n        return responsePromise\n      }\n\n      this.logger.info(\n        'no mocked response received, performing request as-is...'\n      )\n\n      /**\n       * @note Clone the request instance right before performing it.\n       * This preserves any modifications made to the intercepted request\n       * in the \"request\" listener. This also allows the user to read the\n       * request body in the \"response\" listener (otherwise \"unusable\").\n       */\n      const requestCloneForResponseEvent = request.clone()\n\n      return pureFetch(request).then(async (response) => {\n        this.logger.info('original fetch performed', response)\n\n        if (this.emitter.listenerCount('response') > 0) {\n          this.logger.info('emitting the \"response\" event...')\n\n          const responseClone = response.clone()\n\n          await emitAsync(this.emitter, 'response', {\n            response: responseClone,\n            isMockedResponse: false,\n            request: requestCloneForResponseEvent,\n            requestId,\n          })\n        }\n\n        return response\n      })\n    }\n\n    Object.defineProperty(globalThis.fetch, IS_PATCHED_MODULE, {\n      enumerable: true,\n      configurable: true,\n      value: true,\n    })\n\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis.fetch, IS_PATCHED_MODULE, {\n        value: undefined,\n      })\n\n      globalThis.fetch = pureFetch\n\n      this.logger.info(\n        'restored native \"globalThis.fetch\"!',\n        globalThis.fetch.name\n      )\n    })\n  }\n}\n", "export function createNetworkError(cause?: unknown) {\n  return Object.assign(new TypeError('Failed to fetch'), {\n    cause,\n  })\n}\n", "import { createNetworkError } from './createNetworkError'\n\nconst REQUEST_BODY_HEADERS = [\n  'content-encoding',\n  'content-language',\n  'content-location',\n  'content-type',\n  'content-length',\n]\n\nconst kRedirectCount = Symbol('kRedirectCount')\n\n/**\n * @see https://github.com/nodejs/undici/blob/a6dac3149c505b58d2e6d068b97f4dc993da55f0/lib/web/fetch/index.js#L1210\n */\nexport async function followFetchRedirect(\n  request: Request,\n  response: Response\n): Promise<Response> {\n  if (response.status !== 303 && request.body != null) {\n    return Promise.reject(createNetworkError())\n  }\n\n  const requestUrl = new URL(request.url)\n\n  let locationUrl: URL\n  try {\n    // If the location is a relative URL, use the request URL as the base URL.\n    locationUrl = new URL(response.headers.get('location')!, request.url) \n  } catch (error) {\n    return Promise.reject(createNetworkError(error))\n  }\n\n  if (\n    !(locationUrl.protocol === 'http:' || locationUrl.protocol === 'https:')\n  ) {\n    return Promise.reject(\n      createNetworkError('URL scheme must be a HTTP(S) scheme')\n    )\n  }\n\n  if (Reflect.get(request, kRedirectCount) > 20) {\n    return Promise.reject(createNetworkError('redirect count exceeded'))\n  }\n\n  Object.defineProperty(request, kRedirectCount, {\n    value: (Reflect.get(request, kRedirectCount) || 0) + 1,\n  })\n\n  if (\n    request.mode === 'cors' &&\n    (locationUrl.username || locationUrl.password) &&\n    !sameOrigin(requestUrl, locationUrl)\n  ) {\n    return Promise.reject(\n      createNetworkError('cross origin not allowed for request mode \"cors\"')\n    )\n  }\n\n  const requestInit: RequestInit = {}\n\n  if (\n    ([301, 302].includes(response.status) && request.method === 'POST') ||\n    (response.status === 303 && !['HEAD', 'GET'].includes(request.method))\n  ) {\n    requestInit.method = 'GET'\n    requestInit.body = null\n\n    REQUEST_BODY_HEADERS.forEach((headerName) => {\n      request.headers.delete(headerName)\n    })\n  }\n\n  if (!sameOrigin(requestUrl, locationUrl)) {\n    request.headers.delete('authorization')\n    request.headers.delete('proxy-authorization')\n    request.headers.delete('cookie')\n    request.headers.delete('host')\n  }\n\n  /**\n   * @note Undici \"safely\" extracts the request body.\n   * I suspect we cannot dispatch this request again\n   * since its body has been read and the stream is locked.\n   */\n\n  requestInit.headers = request.headers\n  return fetch(new Request(locationUrl, requestInit))\n}\n\n/**\n * @see https://github.com/nodejs/undici/blob/a6dac3149c505b58d2e6d068b97f4dc993da55f0/lib/web/fetch/util.js#L761\n */\nfunction sameOrigin(left: URL, right: URL): boolean {\n  if (left.origin === right.origin && left.origin === 'null') {\n    return true\n  }\n\n  if (\n    left.protocol === right.protocol &&\n    left.hostname === right.hostname &&\n    left.port === right.port\n  ) {\n    return true\n  }\n\n  return false\n}\n", "export class BrotliDecompressionStream extends TransformStream {\n  constructor() {\n    console.warn(\n      '[Interceptors]: Brotli decompression of response streams is not supported in the browser'\n    )\n\n    super({\n      transform(chunk, controller) {\n        // Keep the stream as passthrough, it does nothing.\n        controller.enqueue(chunk)\n      },\n    })\n  }\n}\n", "// Import from an internal alias that resolves to different modules\n// depending on the environment. This way, we can keep the fetch interceptor\n// intact while using different strategies for Brotli decompression.\nimport { BrotliDecompressionStream } from 'internal:brotli-decompress'\n\nclass PipelineStream extends TransformStream {\n  constructor(\n    transformStreams: Array<TransformStream>,\n    ...strategies: Array<QueuingStrategy>\n  ) {\n    super({}, ...strategies)\n\n    const readable = [super.readable as any, ...transformStreams].reduce(\n      (readable, transform) => readable.pipeThrough(transform)\n    )\n\n    Object.defineProperty(this, 'readable', {\n      get() {\n        return readable\n      },\n    })\n  }\n}\n\nexport function parseContentEncoding(contentEncoding: string): Array<string> {\n  return contentEncoding\n    .toLowerCase()\n    .split(',')\n    .map((coding) => coding.trim())\n}\n\nfunction createDecompressionStream(\n  contentEncoding: string\n): TransformStream | null {\n  if (contentEncoding === '') {\n    return null\n  }\n\n  const codings = parseContentEncoding(contentEncoding)\n\n  if (codings.length === 0) {\n    return null\n  }\n\n  const transformers = codings.reduceRight<Array<TransformStream>>(\n    (transformers, coding) => {\n      if (coding === 'gzip' || coding === 'x-gzip') {\n        return transformers.concat(new DecompressionStream('gzip'))\n      } else if (coding === 'deflate') {\n        return transformers.concat(new DecompressionStream('deflate'))\n      } else if (coding === 'br') {\n        return transformers.concat(new BrotliDecompressionStream())\n      } else {\n        transformers.length = 0\n      }\n\n      return transformers\n    },\n    []\n  )\n\n  return new PipelineStream(transformers)\n}\n\nexport function decompressResponse(\n  response: Response\n): ReadableStream<any> | null {\n  if (response.body === null) {\n    return null\n  }\n\n  const decompressionStream = createDecompressionStream(\n    response.headers.get('content-encoding') || ''\n  )\n\n  if (!decompressionStream) {\n    return null\n  }\n\n  // Use `pipeTo` and return the decompression stream's readable\n  // instead of `pipeThrough` because that will lock the original\n  // response stream, making it unusable as the input to Response.\n  response.body.pipeTo(decompressionStream.writable)\n  return decompressionStream.readable\n}\n", "import { invariant } from 'outvariant'\nimport { Emitter } from 'strict-event-emitter'\nimport { HttpRequestEventMap, IS_PATCHED_MODULE } from '../../glossary'\nimport { Interceptor } from '../../Interceptor'\nimport { createXMLHttpRequestProxy } from './XMLHttpRequestProxy'\nimport { hasConfigurableGlobal } from '../../utils/hasConfigurableGlobal'\n\nexport type XMLHttpRequestEmitter = Emitter<HttpRequestEventMap>\n\nexport class XMLHttpRequestInterceptor extends Interceptor<HttpRequestEventMap> {\n  static interceptorSymbol = Symbol('xhr')\n\n  constructor() {\n    super(XMLHttpRequestInterceptor.interceptorSymbol)\n  }\n\n  protected checkEnvironment() {\n    return hasConfigurableGlobal('XMLHttpRequest')\n  }\n\n  protected setup() {\n    const logger = this.logger.extend('setup')\n\n    logger.info('patching \"XMLHttpRequest\" module...')\n\n    const PureXMLHttpRequest = globalThis.XMLHttpRequest\n\n    invariant(\n      !(PureXMLHttpRequest as any)[IS_PATCHED_MODULE],\n      'Failed to patch the \"XMLHttpRequest\" module: already patched.'\n    )\n\n    globalThis.XMLHttpRequest = createXMLHttpRequestProxy({\n      emitter: this.emitter,\n      logger: this.logger,\n    })\n\n    logger.info(\n      'native \"XMLHttpRequest\" module patched!',\n      globalThis.XMLHttpRequest.name\n    )\n\n    Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n      enumerable: true,\n      configurable: true,\n      value: true,\n    })\n\n    this.subscriptions.push(() => {\n      Object.defineProperty(globalThis.XMLHttpRequest, IS_PATCHED_MODULE, {\n        value: undefined,\n      })\n\n      globalThis.XMLHttpRequest = PureXMLHttpRequest\n      logger.info(\n        'native \"XMLHttpRequest\" module restored!',\n        globalThis.XMLHttpRequest.name\n      )\n    })\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { isNodeProcess } from 'is-node-process'\nimport type { Logger } from '@open-draft/logger'\nimport { concatArrayBuffer } from './utils/concatArrayBuffer'\nimport { createEvent } from './utils/createEvent'\nimport {\n  decodeBuffer,\n  encodeBuffer,\n  toArrayBuffer,\n} from '../../utils/bufferUtils'\nimport { createProxy } from '../../utils/createProxy'\nimport { isDomParserSupportedType } from './utils/isDomParserSupportedType'\nimport { parseJson } from '../../utils/parseJson'\nimport { createResponse } from './utils/createResponse'\nimport { INTERNAL_REQUEST_ID_HEADER_NAME } from '../../Interceptor'\nimport { createRequestId } from '../../createRequestId'\nimport { getBodyByteLength } from './utils/getBodyByteLength'\nimport { setRawRequest } from '../../getRawRequest'\n\nconst kIsRequestHandled = Symbol('kIsRequestHandled')\nconst IS_NODE = isNodeProcess()\nconst kFetchRequest = Symbol('kFetchRequest')\n\n/**\n * An `XMLHttpRequest` instance controller that allows us\n * to handle any given request instance (e.g. responding to it).\n */\nexport class XMLHttpRequestController {\n  public request: XMLHttpRequest\n  public requestId: string\n  public onRequest?: (\n    this: XMLHttpRequestController,\n    args: {\n      request: Request\n      requestId: string\n    }\n  ) => Promise<void>\n  public onResponse?: (\n    this: XMLHttpRequestController,\n    args: {\n      response: Response\n      isMockedResponse: boolean\n      request: Request\n      requestId: string\n    }\n  ) => void;\n\n  [kIsRequestHandled]: boolean;\n  [kFetchRequest]?: Request\n  private method: string = 'GET'\n  private url: URL = null as any\n  private requestHeaders: Headers\n  private responseBuffer: Uint8Array\n  private events: Map<keyof XMLHttpRequestEventTargetEventMap, Array<Function>>\n  private uploadEvents: Map<\n    keyof XMLHttpRequestEventTargetEventMap,\n    Array<Function>\n  >\n\n  constructor(readonly initialRequest: XMLHttpRequest, public logger: Logger) {\n    this[kIsRequestHandled] = false\n\n    this.events = new Map()\n    this.uploadEvents = new Map()\n    this.requestId = createRequestId()\n    this.requestHeaders = new Headers()\n    this.responseBuffer = new Uint8Array()\n\n    this.request = createProxy(initialRequest, {\n      setProperty: ([propertyName, nextValue], invoke) => {\n        switch (propertyName) {\n          case 'ontimeout': {\n            const eventName = propertyName.slice(\n              2\n            ) as keyof XMLHttpRequestEventTargetEventMap\n\n            /**\n             * @note Proxy callbacks to event listeners because JSDOM has trouble\n             * translating these properties to callbacks. It seemed to be operating\n             * on events exclusively.\n             */\n            this.request.addEventListener(eventName, nextValue as any)\n\n            return invoke()\n          }\n\n          default: {\n            return invoke()\n          }\n        }\n      },\n      methodCall: ([methodName, args], invoke) => {\n        switch (methodName) {\n          case 'open': {\n            const [method, url] = args as [string, string | undefined]\n\n            if (typeof url === 'undefined') {\n              this.method = 'GET'\n              this.url = toAbsoluteUrl(method)\n            } else {\n              this.method = method\n              this.url = toAbsoluteUrl(url)\n            }\n\n            this.logger = this.logger.extend(`${this.method} ${this.url.href}`)\n            this.logger.info('open', this.method, this.url.href)\n\n            return invoke()\n          }\n\n          case 'addEventListener': {\n            const [eventName, listener] = args as [\n              keyof XMLHttpRequestEventTargetEventMap,\n              Function\n            ]\n\n            this.registerEvent(eventName, listener)\n            this.logger.info('addEventListener', eventName, listener)\n\n            return invoke()\n          }\n\n          case 'setRequestHeader': {\n            const [name, value] = args as [string, string]\n            this.requestHeaders.set(name, value)\n\n            this.logger.info('setRequestHeader', name, value)\n\n            return invoke()\n          }\n\n          case 'send': {\n            const [body] = args as [\n              body?: XMLHttpRequestBodyInit | Document | null\n            ]\n\n            this.request.addEventListener('load', () => {\n              if (typeof this.onResponse !== 'undefined') {\n                // Create a Fetch API Response representation of whichever\n                // response this XMLHttpRequest received. Note those may\n                // be either a mocked and the original response.\n                const fetchResponse = createResponse(\n                  this.request,\n                  /**\n                   * The `response` property is the right way to read\n                   * the ambiguous response body, as the request's \"responseType\" may differ.\n                   * @see https://xhr.spec.whatwg.org/#the-response-attribute\n                   */\n                  this.request.response\n                )\n\n                // Notify the consumer about the response.\n                this.onResponse.call(this, {\n                  response: fetchResponse,\n                  isMockedResponse: this[kIsRequestHandled],\n                  request: fetchRequest,\n                  requestId: this.requestId!,\n                })\n              }\n            })\n\n            const requestBody =\n              typeof body === 'string' ? encodeBuffer(body) : body\n\n            // Delegate request handling to the consumer.\n            const fetchRequest = this.toFetchApiRequest(requestBody)\n            this[kFetchRequest] = fetchRequest.clone()\n\n            const onceRequestSettled =\n              this.onRequest?.call(this, {\n                request: fetchRequest,\n                requestId: this.requestId!,\n              }) || Promise.resolve()\n\n            onceRequestSettled.finally(() => {\n              // If the consumer didn't handle the request (called `.respondWith()`) perform it as-is.\n              if (!this[kIsRequestHandled]) {\n                this.logger.info(\n                  'request callback settled but request has not been handled (readystate %d), performing as-is...',\n                  this.request.readyState\n                )\n\n                /**\n                 * @note Set the intercepted request ID on the original request in Node.js\n                 * so that if it triggers any other interceptors, they don't attempt\n                 * to process it once again.\n                 *\n                 * For instance, XMLHttpRequest is often implemented via \"http.ClientRequest\"\n                 * and we don't want for both XHR and ClientRequest interceptors to\n                 * handle the same request at the same time (e.g. emit the \"response\" event twice).\n                 */\n                if (IS_NODE) {\n                  this.request.setRequestHeader(\n                    INTERNAL_REQUEST_ID_HEADER_NAME,\n                    this.requestId!\n                  )\n                }\n\n                return invoke()\n              }\n            })\n\n            break\n          }\n\n          default: {\n            return invoke()\n          }\n        }\n      },\n    })\n\n    /**\n     * Proxy the `.upload` property to gather the event listeners/callbacks.\n     */\n    define(\n      this.request,\n      'upload',\n      createProxy(this.request.upload, {\n        setProperty: ([propertyName, nextValue], invoke) => {\n          switch (propertyName) {\n            case 'onloadstart':\n            case 'onprogress':\n            case 'onaboart':\n            case 'onerror':\n            case 'onload':\n            case 'ontimeout':\n            case 'onloadend': {\n              const eventName = propertyName.slice(\n                2\n              ) as keyof XMLHttpRequestEventTargetEventMap\n\n              this.registerUploadEvent(eventName, nextValue as Function)\n            }\n          }\n\n          return invoke()\n        },\n        methodCall: ([methodName, args], invoke) => {\n          switch (methodName) {\n            case 'addEventListener': {\n              const [eventName, listener] = args as [\n                keyof XMLHttpRequestEventTargetEventMap,\n                Function\n              ]\n              this.registerUploadEvent(eventName, listener)\n              this.logger.info('upload.addEventListener', eventName, listener)\n\n              return invoke()\n            }\n          }\n        },\n      })\n    )\n  }\n\n  private registerEvent(\n    eventName: keyof XMLHttpRequestEventTargetEventMap,\n    listener: Function\n  ): void {\n    const prevEvents = this.events.get(eventName) || []\n    const nextEvents = prevEvents.concat(listener)\n    this.events.set(eventName, nextEvents)\n\n    this.logger.info('registered event \"%s\"', eventName, listener)\n  }\n\n  private registerUploadEvent(\n    eventName: keyof XMLHttpRequestEventTargetEventMap,\n    listener: Function\n  ): void {\n    const prevEvents = this.uploadEvents.get(eventName) || []\n    const nextEvents = prevEvents.concat(listener)\n    this.uploadEvents.set(eventName, nextEvents)\n\n    this.logger.info('registered upload event \"%s\"', eventName, listener)\n  }\n\n  /**\n   * Responds to the current request with the given\n   * Fetch API `Response` instance.\n   */\n  public async respondWith(response: Response): Promise<void> {\n    /**\n     * @note Since `XMLHttpRequestController` delegates the handling of the responses\n     * to the \"load\" event listener that doesn't distinguish between the mocked and original\n     * responses, mark the request that had a mocked response with a corresponding symbol.\n     *\n     * Mark this request as having a mocked response immediately since\n     * calculating request/response total body length is asynchronous.\n     */\n    this[kIsRequestHandled] = true\n\n    /**\n     * Dispatch request upload events for requests with a body.\n     * @see https://github.com/mswjs/interceptors/issues/573\n     */\n    if (this[kFetchRequest]) {\n      const totalRequestBodyLength = await getBodyByteLength(\n        this[kFetchRequest]\n      )\n\n      this.trigger('loadstart', this.request.upload, {\n        loaded: 0,\n        total: totalRequestBodyLength,\n      })\n      this.trigger('progress', this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength,\n      })\n      this.trigger('load', this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength,\n      })\n      this.trigger('loadend', this.request.upload, {\n        loaded: totalRequestBodyLength,\n        total: totalRequestBodyLength,\n      })\n    }\n\n    this.logger.info(\n      'responding with a mocked response: %d %s',\n      response.status,\n      response.statusText\n    )\n\n    define(this.request, 'status', response.status)\n    define(this.request, 'statusText', response.statusText)\n    define(this.request, 'responseURL', this.url.href)\n\n    this.request.getResponseHeader = new Proxy(this.request.getResponseHeader, {\n      apply: (_, __, args: [name: string]) => {\n        this.logger.info('getResponseHeader', args[0])\n\n        if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n          this.logger.info('headers not received yet, returning null')\n\n          // Headers not received yet, nothing to return.\n          return null\n        }\n\n        const headerValue = response.headers.get(args[0])\n        this.logger.info(\n          'resolved response header \"%s\" to',\n          args[0],\n          headerValue\n        )\n\n        return headerValue\n      },\n    })\n\n    this.request.getAllResponseHeaders = new Proxy(\n      this.request.getAllResponseHeaders,\n      {\n        apply: () => {\n          this.logger.info('getAllResponseHeaders')\n\n          if (this.request.readyState < this.request.HEADERS_RECEIVED) {\n            this.logger.info('headers not received yet, returning empty string')\n\n            // Headers not received yet, nothing to return.\n            return ''\n          }\n\n          const headersList = Array.from(response.headers.entries())\n          const allHeaders = headersList\n            .map(([headerName, headerValue]) => {\n              return `${headerName}: ${headerValue}`\n            })\n            .join('\\r\\n')\n\n          this.logger.info('resolved all response headers to', allHeaders)\n\n          return allHeaders\n        },\n      }\n    )\n\n    // Update the response getters to resolve against the mocked response.\n    Object.defineProperties(this.request, {\n      response: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.response,\n      },\n      responseText: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseText,\n      },\n      responseXML: {\n        enumerable: true,\n        configurable: false,\n        get: () => this.responseXML,\n      },\n    })\n\n    const totalResponseBodyLength = await getBodyByteLength(response.clone())\n\n    this.logger.info('calculated response body length', totalResponseBodyLength)\n\n    this.trigger('loadstart', this.request, {\n      loaded: 0,\n      total: totalResponseBodyLength,\n    })\n\n    this.setReadyState(this.request.HEADERS_RECEIVED)\n    this.setReadyState(this.request.LOADING)\n\n    const finalizeResponse = () => {\n      this.logger.info('finalizing the mocked response...')\n\n      this.setReadyState(this.request.DONE)\n\n      this.trigger('load', this.request, {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength,\n      })\n\n      this.trigger('loadend', this.request, {\n        loaded: this.responseBuffer.byteLength,\n        total: totalResponseBodyLength,\n      })\n    }\n\n    if (response.body) {\n      this.logger.info('mocked response has body, streaming...')\n\n      const reader = response.body.getReader()\n\n      const readNextResponseBodyChunk = async () => {\n        const { value, done } = await reader.read()\n\n        if (done) {\n          this.logger.info('response body stream done!')\n          finalizeResponse()\n          return\n        }\n\n        if (value) {\n          this.logger.info('read response body chunk:', value)\n          this.responseBuffer = concatArrayBuffer(this.responseBuffer, value)\n\n          this.trigger('progress', this.request, {\n            loaded: this.responseBuffer.byteLength,\n            total: totalResponseBodyLength,\n          })\n        }\n\n        readNextResponseBodyChunk()\n      }\n\n      readNextResponseBodyChunk()\n    } else {\n      finalizeResponse()\n    }\n  }\n\n  private responseBufferToText(): string {\n    return decodeBuffer(this.responseBuffer)\n  }\n\n  get response(): unknown {\n    this.logger.info(\n      'getResponse (responseType: %s)',\n      this.request.responseType\n    )\n\n    if (this.request.readyState !== this.request.DONE) {\n      return null\n    }\n\n    switch (this.request.responseType) {\n      case 'json': {\n        const responseJson = parseJson(this.responseBufferToText())\n        this.logger.info('resolved response JSON', responseJson)\n\n        return responseJson\n      }\n\n      case 'arraybuffer': {\n        const arrayBuffer = toArrayBuffer(this.responseBuffer)\n        this.logger.info('resolved response ArrayBuffer', arrayBuffer)\n\n        return arrayBuffer\n      }\n\n      case 'blob': {\n        const mimeType =\n          this.request.getResponseHeader('Content-Type') || 'text/plain'\n        const responseBlob = new Blob([this.responseBufferToText()], {\n          type: mimeType,\n        })\n\n        this.logger.info(\n          'resolved response Blob (mime type: %s)',\n          responseBlob,\n          mimeType\n        )\n\n        return responseBlob\n      }\n\n      default: {\n        const responseText = this.responseBufferToText()\n        this.logger.info(\n          'resolving \"%s\" response type as text',\n          this.request.responseType,\n          responseText\n        )\n\n        return responseText\n      }\n    }\n  }\n\n  get responseText(): string {\n    /**\n     * Throw when trying to read the response body as text when the\n     * \"responseType\" doesn't expect text. This just respects the spec better.\n     * @see https://xhr.spec.whatwg.org/#the-responsetext-attribute\n     */\n    invariant(\n      this.request.responseType === '' || this.request.responseType === 'text',\n      'InvalidStateError: The object is in invalid state.'\n    )\n\n    if (\n      this.request.readyState !== this.request.LOADING &&\n      this.request.readyState !== this.request.DONE\n    ) {\n      return ''\n    }\n\n    const responseText = this.responseBufferToText()\n    this.logger.info('getResponseText: \"%s\"', responseText)\n\n    return responseText\n  }\n\n  get responseXML(): Document | null {\n    invariant(\n      this.request.responseType === '' ||\n        this.request.responseType === 'document',\n      'InvalidStateError: The object is in invalid state.'\n    )\n\n    if (this.request.readyState !== this.request.DONE) {\n      return null\n    }\n\n    const contentType = this.request.getResponseHeader('Content-Type') || ''\n\n    if (typeof DOMParser === 'undefined') {\n      console.warn(\n        'Cannot retrieve XMLHttpRequest response body as XML: DOMParser is not defined. You are likely using an environment that is not browser or does not polyfill browser globals correctly.'\n      )\n      return null\n    }\n\n    if (isDomParserSupportedType(contentType)) {\n      return new DOMParser().parseFromString(\n        this.responseBufferToText(),\n        contentType\n      )\n    }\n\n    return null\n  }\n\n  public errorWith(error?: Error): void {\n    /**\n     * @note Mark this request as handled even if it received a mock error.\n     * This prevents the controller from trying to perform this request as-is.\n     */\n    this[kIsRequestHandled] = true\n    this.logger.info('responding with an error')\n\n    this.setReadyState(this.request.DONE)\n    this.trigger('error', this.request)\n    this.trigger('loadend', this.request)\n  }\n\n  /**\n   * Transitions this request's `readyState` to the given one.\n   */\n  private setReadyState(nextReadyState: number): void {\n    this.logger.info(\n      'setReadyState: %d -> %d',\n      this.request.readyState,\n      nextReadyState\n    )\n\n    if (this.request.readyState === nextReadyState) {\n      this.logger.info('ready state identical, skipping transition...')\n      return\n    }\n\n    define(this.request, 'readyState', nextReadyState)\n\n    this.logger.info('set readyState to: %d', nextReadyState)\n\n    if (nextReadyState !== this.request.UNSENT) {\n      this.logger.info('triggerring \"readystatechange\" event...')\n\n      this.trigger('readystatechange', this.request)\n    }\n  }\n\n  /**\n   * Triggers given event on the `XMLHttpRequest` instance.\n   */\n  private trigger<\n    EventName extends keyof (XMLHttpRequestEventTargetEventMap & {\n      readystatechange: ProgressEvent<XMLHttpRequestEventTarget>\n    })\n  >(\n    eventName: EventName,\n    target: XMLHttpRequest | XMLHttpRequestUpload,\n    options?: ProgressEventInit\n  ): void {\n    const callback = (target as XMLHttpRequest)[`on${eventName}`]\n    const event = createEvent(target, eventName, options)\n\n    this.logger.info('trigger \"%s\"', eventName, options || '')\n\n    // Invoke direct callbacks.\n    if (typeof callback === 'function') {\n      this.logger.info('found a direct \"%s\" callback, calling...', eventName)\n      callback.call(target as XMLHttpRequest, event)\n    }\n\n    // Invoke event listeners.\n    const events =\n      target instanceof XMLHttpRequestUpload ? this.uploadEvents : this.events\n\n    for (const [registeredEventName, listeners] of events) {\n      if (registeredEventName === eventName) {\n        this.logger.info(\n          'found %d listener(s) for \"%s\" event, calling...',\n          listeners.length,\n          eventName\n        )\n\n        listeners.forEach((listener) => listener.call(target, event))\n      }\n    }\n  }\n\n  /**\n   * Converts this `XMLHttpRequest` instance into a Fetch API `Request` instance.\n   */\n  private toFetchApiRequest(\n    body: XMLHttpRequestBodyInit | Document | null | undefined\n  ): Request {\n    this.logger.info('converting request to a Fetch API Request...')\n\n    // If the `Document` is used as the body of this XMLHttpRequest,\n    // set its inner text as the Fetch API Request body.\n    const resolvedBody =\n      body instanceof Document ? body.documentElement.innerText : body\n\n    const fetchRequest = new Request(this.url.href, {\n      method: this.method,\n      headers: this.requestHeaders,\n      /**\n       * @see https://xhr.spec.whatwg.org/#cross-origin-credentials\n       */\n      credentials: this.request.withCredentials ? 'include' : 'same-origin',\n      body: ['GET', 'HEAD'].includes(this.method.toUpperCase())\n        ? null\n        : resolvedBody,\n    })\n\n    const proxyHeaders = createProxy(fetchRequest.headers, {\n      methodCall: ([methodName, args], invoke) => {\n        // Forward the latest state of the internal request headers\n        // because the interceptor might have modified them\n        // without responding to the request.\n        switch (methodName) {\n          case 'append':\n          case 'set': {\n            const [headerName, headerValue] = args as [string, string]\n            this.request.setRequestHeader(headerName, headerValue)\n            break\n          }\n\n          case 'delete': {\n            const [headerName] = args as [string]\n            console.warn(\n              `XMLHttpRequest: Cannot remove a \"${headerName}\" header from the Fetch API representation of the \"${fetchRequest.method} ${fetchRequest.url}\" request. XMLHttpRequest headers cannot be removed.`\n            )\n            break\n          }\n        }\n\n        return invoke()\n      },\n    })\n    define(fetchRequest, 'headers', proxyHeaders)\n    setRawRequest(fetchRequest, this.request)\n\n    this.logger.info('converted request to a Fetch API Request!', fetchRequest)\n\n    return fetchRequest\n  }\n}\n\nfunction toAbsoluteUrl(url: string | URL): URL {\n  /**\n   * @note XMLHttpRequest interceptor may run in environments\n   * that implement XMLHttpRequest but don't implement \"location\"\n   * (for example, React Native). If that's the case, return the\n   * input URL as-is (nothing to be relative to).\n   * @see https://github.com/mswjs/msw/issues/1777\n   */\n  if (typeof location === 'undefined') {\n    return new URL(url)\n  }\n\n  return new URL(url.toString(), location.href)\n}\n\nfunction define(\n  target: object,\n  property: string | symbol,\n  value: unknown\n): void {\n  Reflect.defineProperty(target, property, {\n    // Ensure writable properties to allow redefining readonly properties.\n    writable: true,\n    enumerable: true,\n    value,\n  })\n}\n", "/**\n * Concatenate two `Uint8Array` buffers.\n */\nexport function concatArrayBuffer(\n  left: Uint8Array,\n  right: Uint8Array\n): Uint8Array {\n  const result = new Uint8Array(left.byteLength + right.byteLength)\n  result.set(left, 0)\n  result.set(right, left.byteLength)\n  return result\n}\n", "export class EventPolyfill implements Event {\n  readonly NONE = 0\n  readonly CAPTURING_PHASE = 1\n  readonly AT_TARGET = 2\n  readonly BUBBLING_PHASE = 3\n\n  public type: string = ''\n  public srcElement: EventTarget | null = null\n  public target: EventTarget | null\n  public currentTarget: EventTarget | null = null\n  public eventPhase: number = 0\n  public timeStamp: number\n  public isTrusted: boolean = true\n  public composed: boolean = false\n  public cancelable: boolean = true\n  public defaultPrevented: boolean = false\n  public bubbles: boolean = true\n  public lengthComputable: boolean = true\n  public loaded: number = 0\n  public total: number = 0\n\n  cancelBubble: boolean = false\n  returnValue: boolean = true\n\n  constructor(\n    type: string,\n    options?: { target: EventTarget; currentTarget: EventTarget }\n  ) {\n    this.type = type\n    this.target = options?.target || null\n    this.currentTarget = options?.currentTarget || null\n    this.timeStamp = Date.now()\n  }\n\n  public composedPath(): EventTarget[] {\n    return []\n  }\n\n  public initEvent(type: string, bubbles?: boolean, cancelable?: boolean) {\n    this.type = type\n    this.bubbles = !!bubbles\n    this.cancelable = !!cancelable\n  }\n\n  public preventDefault() {\n    this.defaultPrevented = true\n  }\n\n  public stopPropagation() {}\n  public stopImmediatePropagation() {}\n}\n", "import { EventPolyfill } from './EventPolyfill'\n\nexport class ProgressEventPolyfill extends EventPolyfill {\n  readonly lengthComputable: boolean\n  readonly composed: boolean\n  readonly loaded: number\n  readonly total: number\n\n  constructor(type: string, init?: ProgressEventInit) {\n    super(type)\n\n    this.lengthComputable = init?.lengthComputable || false\n    this.composed = init?.composed || false\n    this.loaded = init?.loaded || 0\n    this.total = init?.total || 0\n  }\n}\n", "import { EventPolyfill } from '../polyfills/EventPolyfill'\nimport { ProgressEventPolyfill } from '../polyfills/ProgressEventPolyfill'\n\nconst SUPPORTS_PROGRESS_EVENT = typeof ProgressEvent !== 'undefined'\n\nexport function createEvent(\n  target: XMLHttpRequest | XMLHttpRequestUpload,\n  type: string,\n  init?: ProgressEventInit\n): EventPolyfill | ProgressEvent {\n  const progressEvents = [\n    'error',\n    'progress',\n    'loadstart',\n    'loadend',\n    'load',\n    'timeout',\n    'abort',\n  ]\n\n  /**\n   * `ProgressEvent` is not supported in React Native.\n   * @see https://github.com/mswjs/interceptors/issues/40\n   */\n  const ProgressEventClass = SUPPORTS_PROGRESS_EVENT\n    ? ProgressEvent\n    : ProgressEventPolyfill\n\n  const event = progressEvents.includes(type)\n    ? new ProgressEventClass(type, {\n        lengthComputable: true,\n        loaded: init?.loaded || 0,\n        total: init?.total || 0,\n      })\n    : new EventPolyfill(type, {\n        target,\n        currentTarget: target,\n      })\n\n  return event\n}\n", "/**\n * Returns the source object of the given property on the target object\n * (the target itself, any parent in its prototype, or null).\n */\nexport function findPropertySource(\n  target: object,\n  propertyName: string | symbol\n): object | null {\n  if (!(propertyName in target)) {\n    return null\n  }\n\n  const hasProperty = Object.prototype.hasOwnProperty.call(target, propertyName)\n  if (hasProperty) {\n    return target\n  }\n\n  const prototype = Reflect.getPrototypeOf(target)\n  return prototype ? findPropertySource(prototype, propertyName) : null\n}\n", "import { findPropertySource } from './findPropertySource'\n\nexport interface ProxyOptions<Target extends Record<string, any>> {\n  constructorCall?(args: Array<unknown>, next: NextFunction<Target>): Target\n\n  methodCall?<F extends keyof Target>(\n    this: Target,\n    data: [methodName: F, args: Array<unknown>],\n    next: NextFunction<void>\n  ): void\n\n  setProperty?(\n    data: [propertyName: string | symbol, nextValue: unknown],\n    next: NextFunction<boolean>\n  ): boolean\n\n  getProperty?(\n    data: [propertyName: string | symbol, receiver: Target],\n    next: NextFunction<void>\n  ): void\n}\n\nexport type NextFunction<ReturnType> = () => ReturnType\n\nexport function createProxy<Target extends object>(\n  target: Target,\n  options: ProxyOptions<Target>\n): Target {\n  const proxy = new Proxy(target, optionsToProxyHandler(options))\n\n  return proxy\n}\n\nfunction optionsToProxyHandler<T extends Record<string, any>>(\n  options: ProxyOptions<T>\n): ProxyHandler<T> {\n  const { constructorCall, methodCall, getProperty, setProperty } = options\n  const handler: Proxy<PERSON>andler<T> = {}\n\n  if (typeof constructorCall !== 'undefined') {\n    handler.construct = function (target, args, newTarget) {\n      const next = Reflect.construct.bind(null, target as any, args, newTarget)\n      return constructorCall.call(newTarget, args, next)\n    }\n  }\n\n  handler.set = function (target, propertyName, nextValue) {\n    const next = () => {\n      const propertySource = findPropertySource(target, propertyName) || target\n      const ownDescriptors = Reflect.getOwnPropertyDescriptor(\n        propertySource,\n        propertyName\n      )\n\n      // Respect any custom setters present for this property.\n      if (typeof ownDescriptors?.set !== 'undefined') {\n        ownDescriptors.set.apply(target, [nextValue])\n        return true\n      }\n\n      // Otherwise, set the property on the source.\n      return Reflect.defineProperty(propertySource, propertyName, {\n        writable: true,\n        enumerable: true,\n        configurable: true,\n        value: nextValue,\n      })\n    }\n\n    if (typeof setProperty !== 'undefined') {\n      return setProperty.call(target, [propertyName, nextValue], next)\n    }\n\n    return next()\n  }\n\n  handler.get = function (target, propertyName, receiver) {\n    /**\n     * @note Using `Reflect.get()` here causes \"TypeError: Illegal invocation\".\n     */\n    const next = () => target[propertyName as any]\n\n    const value =\n      typeof getProperty !== 'undefined'\n        ? getProperty.call(target, [propertyName, receiver], next)\n        : next()\n\n    if (typeof value === 'function') {\n      return (...args: Array<any>) => {\n        const next = value.bind(target, ...args)\n\n        if (typeof methodCall !== 'undefined') {\n          return methodCall.call(target, [propertyName as any, args], next)\n        }\n\n        return next()\n      }\n    }\n\n    return value\n  }\n\n  return handler\n}\n", "export function isDomParserSupportedType(\n  type: string\n): type is DOMParserSupportedType {\n  const supportedTypes: Array<DOMParserSupportedType> = [\n    'application/xhtml+xml',\n    'application/xml',\n    'image/svg+xml',\n    'text/html',\n    'text/xml',\n  ]\n  return supportedTypes.some((supportedType) => {\n    return type.startsWith(supportedType)\n  })\n}\n", "/**\n * Parses a given string into JSON.\n * Gracefully handles invalid JSON by returning `null`.\n */\nexport function parseJson(data: string): Record<string, unknown> | null {\n  try {\n    const json = JSON.parse(data)\n    return json\n  } catch (_) {\n    return null\n  }\n}\n", "import { FetchResponse } from '../../../utils/fetchUtils'\n\n/**\n * Creates a Fetch API `Response` instance from the given\n * `XMLHttpRequest` instance and a response body.\n */\nexport function createResponse(\n  request: XMLHttpRequest,\n  body: BodyInit | null\n): Response {\n  /**\n   * Handle XMLHttpRequest responses that must have null as the\n   * response body when represented using Fetch API Response.\n   * XMLHttpRequest response will always have an empty string\n   * as the \"request.response\" in those cases, resulting in an error\n   * when constructing a Response instance.\n   * @see https://github.com/mswjs/interceptors/issues/379\n   */\n  const responseBodyOrNull = FetchResponse.isResponseWithBody(request.status)\n    ? body\n    : null\n\n  return new FetchResponse(responseBodyOrNull, {\n    url: request.responseURL,\n    status: request.status,\n    statusText: request.statusText,\n    headers: createHeadersFromXMLHttpReqestHeaders(\n      request.getAllResponseHeaders()\n    ),\n  })\n}\n\nfunction createHeadersFromXMLHttpReqestHeaders(headersString: string): Headers {\n  const headers = new Headers()\n\n  const lines = headersString.split(/[\\r\\n]+/)\n  for (const line of lines) {\n    if (line.trim() === '') {\n      continue\n    }\n\n    const [name, ...parts] = line.split(': ')\n    const value = parts.join(': ')\n\n    headers.append(name, value)\n  }\n\n  return headers\n}\n", "/**\n * Return a total byte length of the given request/response body.\n * If the `Content-Length` header is present, it will be used as the byte length.\n */\nexport async function getBodyByteLength(\n  input: Request | Response\n): Promise<number> {\n  const explicitContentLength = input.headers.get('content-length')\n\n  if (explicitContentLength != null && explicitContentLength !== '') {\n    return Number(explicitContentLength)\n  }\n\n  const buffer = await input.arrayBuffer()\n  return buffer.byteLength\n}\n", "import type { Logger } from '@open-draft/logger'\nimport { XMLHttpRequestEmitter } from '.'\nimport { RequestController } from '../../RequestController'\nimport { XMLHttpRequestController } from './XMLHttpRequestController'\nimport { handleRequest } from '../../utils/handleRequest'\n\nexport interface XMLHttpRequestProxyOptions {\n  emitter: XMLHttpRequestEmitter\n  logger: Logger\n}\n\n/**\n * Create a proxied `XMLHttpRequest` class.\n * The proxied class establishes spies on certain methods,\n * allowing us to intercept requests and respond to them.\n */\nexport function createXMLHttpRequestProxy({\n  emitter,\n  logger,\n}: XMLHttpRequestProxyOptions) {\n  const XMLHttpRequestProxy = new Proxy(globalThis.XMLHttpRequest, {\n    construct(target, args, newTarget) {\n      logger.info('constructed new XMLHttpRequest')\n\n      const originalRequest = Reflect.construct(\n        target,\n        args,\n        newTarget\n      ) as XMLHttpRequest\n\n      /**\n       * @note Forward prototype descriptors onto the proxied object.\n       * XMLHttpRequest is implemented in JSDOM in a way that assigns\n       * a bunch of descriptors, like \"set responseType()\" on the prototype.\n       * With this propagation, we make sure that those descriptors trigger\n       * when the user operates with the proxied request instance.\n       */\n      const prototypeDescriptors = Object.getOwnPropertyDescriptors(\n        target.prototype\n      )\n      for (const propertyName in prototypeDescriptors) {\n        Reflect.defineProperty(\n          originalRequest,\n          propertyName,\n          prototypeDescriptors[propertyName]\n        )\n      }\n\n      const xhrRequestController = new XMLHttpRequestController(\n        originalRequest,\n        logger\n      )\n\n      xhrRequestController.onRequest = async function ({ request, requestId }) {\n        const controller = new RequestController(request)\n\n        this.logger.info('awaiting mocked response...')\n\n        this.logger.info(\n          'emitting the \"request\" event for %s listener(s)...',\n          emitter.listenerCount('request')\n        )\n\n        const isRequestHandled = await handleRequest({\n          request,\n          requestId,\n          controller,\n          emitter,\n          onResponse: async (response) => {\n            await this.respondWith(response)\n          },\n          onRequestError: () => {\n            this.errorWith(new TypeError('Network error'))\n          },\n          onError: (error) => {\n            this.logger.info('request errored!', { error })\n\n            if (error instanceof Error) {\n              this.errorWith(error)\n            }\n          },\n        })\n\n        if (!isRequestHandled) {\n          this.logger.info(\n            'no mocked response received, performing request as-is...'\n          )\n        }\n      }\n\n      xhrRequestController.onResponse = async function ({\n        response,\n        isMockedResponse,\n        request,\n        requestId,\n      }) {\n        this.logger.info(\n          'emitting the \"response\" event for %s listener(s)...',\n          emitter.listenerCount('response')\n        )\n\n        emitter.emit('response', {\n          response,\n          isMockedResponse,\n          request,\n          requestId,\n        })\n      }\n\n      // Return the proxied request from the controller\n      // so that the controller can react to the consumer's interactions\n      // with this request (opening/sending/etc).\n      return xhrRequestController.request\n    },\n  })\n\n  return XMLHttpRequestProxy\n}\n", "import {\n  Interceptor,\n  BatchInterceptor,\n  HttpRequestEventMap,\n} from '@mswjs/interceptors'\nimport { FetchInterceptor } from '@mswjs/interceptors/fetch'\nimport { XMLHttpRequestInterceptor } from '@mswjs/interceptors/XMLHttpRequest'\nimport { SetupWorkerInternalContext, StartOptions } from '../glossary'\nimport type { RequiredDeep } from '~/core/typeUtils'\nimport { handleRequest } from '~/core/utils/handleRequest'\nimport { isHandlerKind } from '~/core/utils/internal/isHandlerKind'\n\nexport function createFallbackRequestListener(\n  context: SetupWorkerInternalContext,\n  options: RequiredDeep<StartOptions>,\n): Interceptor<HttpRequestEventMap> {\n  const interceptor = new BatchInterceptor({\n    name: 'fallback',\n    interceptors: [new FetchInterceptor(), new XMLHttpRequestInterceptor()],\n  })\n\n  interceptor.on('request', async ({ request, requestId, controller }) => {\n    const requestCloneForLogs = request.clone()\n\n    const response = await handleRequest(\n      request,\n      requestId,\n      context.getRequestHandlers().filter(isHandlerKind('RequestHandler')),\n      options,\n      context.emitter,\n      {\n        onMockedResponse(_, { handler, parsedResult }) {\n          if (!options.quiet) {\n            context.emitter.once('response:mocked', ({ response }) => {\n              handler.log({\n                request: requestCloneForLogs,\n                response,\n                parsedResult,\n              })\n            })\n          }\n        },\n      },\n    )\n\n    if (response) {\n      controller.respondWith(response)\n    }\n  })\n\n  interceptor.on(\n    'response',\n    ({ response, isMockedResponse, request, requestId }) => {\n      context.emitter.emit(\n        isMockedResponse ? 'response:mocked' : 'response:bypass',\n        {\n          response,\n          request,\n          requestId,\n        },\n      )\n    },\n  )\n\n  interceptor.apply()\n\n  return interceptor\n}\n", "import { createFallbackRequestListener } from './createFallbackRequestListener'\nimport { SetupWorkerInternalContext, StartHandler } from '../glossary'\nimport { printStartMessage } from './utils/printStartMessage'\n\nexport function createFallbackStart(\n  context: SetupWorkerInternalContext,\n): StartHandler {\n  return async function start(options) {\n    context.fallbackInterceptor = createFallbackRequestListener(\n      context,\n      options,\n    )\n\n    printStartMessage({\n      message: 'Mocking enabled (fallback mode).',\n      quiet: options.quiet,\n    })\n\n    return undefined\n  }\n}\n", "import { SetupWorkerInternalContext, StopHandler } from '../glossary'\nimport { printStopMessage } from './utils/printStopMessage'\n\nexport function createFallbackStop(\n  context: SetupWorkerInternalContext,\n): StopHandler {\n  return function stop() {\n    context.fallbackInterceptor?.dispose()\n    printStopMessage({ quiet: context.startOptions?.quiet })\n  }\n}\n", "import { invariant } from 'outvariant'\nimport { isNodeProcess } from 'is-node-process'\nimport {\n  SetupWorkerInternalContext,\n  ServiceWorkerIncomingEventsMap,\n  StartReturnType,\n  StopHandler,\n  StartHandler,\n  StartOptions,\n} from './glossary'\nimport { createStartHandler } from './start/createStartHandler'\nimport { createStop } from './stop/createStop'\nimport { ServiceWorkerMessage } from './start/utils/createMessageChannel'\nimport { RequestHandler } from '~/core/handlers/RequestHandler'\nimport { DEFAULT_START_OPTIONS } from './start/utils/prepareStartHandler'\nimport { createFallbackStart } from './start/createFallbackStart'\nimport { createFallbackStop } from './stop/createFallbackStop'\nimport { devUtils } from '~/core/utils/internal/devUtils'\nimport { SetupApi } from '~/core/SetupApi'\nimport { mergeRight } from '~/core/utils/internal/mergeRight'\nimport type { LifeCycleEventsMap } from '~/core/sharedOptions'\nimport type { WebSocketHandler } from '~/core/handlers/WebSocketHandler'\nimport { SetupWorker } from './glossary'\nimport { supportsReadableStreamTransfer } from '../utils/supportsReadableStreamTransfer'\nimport { webSocketInterceptor } from '~/core/ws/webSocketInterceptor'\nimport { handleWebSocketEvent } from '~/core/ws/handleWebSocketEvent'\nimport { attachWebSocketLogger } from '~/core/ws/utils/attachWebSocketLogger'\n\ninterface Listener {\n  target: EventTarget\n  eventType: string\n  callback: EventListenerOrEventListenerObject\n}\n\nexport class SetupWorkerApi\n  extends SetupApi<LifeCycleEventsMap>\n  implements SetupWorker\n{\n  private context: SetupWorkerInternalContext\n  private startHandler: StartHandler = null as any\n  private stopHandler: StopHandler = null as any\n  private listeners: Array<Listener>\n\n  constructor(...handlers: Array<RequestHandler | WebSocketHandler>) {\n    super(...handlers)\n\n    invariant(\n      !isNodeProcess(),\n      devUtils.formatMessage(\n        'Failed to execute `setupWorker` in a non-browser environment. Consider using `setupServer` for Node.js environment instead.',\n      ),\n    )\n\n    this.listeners = []\n    this.context = this.createWorkerContext()\n  }\n\n  private createWorkerContext(): SetupWorkerInternalContext {\n    const context: SetupWorkerInternalContext = {\n      // Mocking is not considered enabled until the worker\n      // signals back the successful activation event.\n      isMockingEnabled: false,\n      startOptions: null as any,\n      worker: null,\n      getRequestHandlers: () => {\n        return this.handlersController.currentHandlers()\n      },\n      registration: null,\n      emitter: this.emitter,\n      workerChannel: {\n        on: (eventType, callback) => {\n          this.context.events.addListener<\n            MessageEvent<ServiceWorkerMessage<typeof eventType, any>>\n          >(navigator.serviceWorker, 'message', (event) => {\n            // Avoid messages broadcasted from unrelated workers.\n            if (event.source !== this.context.worker) {\n              return\n            }\n\n            const message = event.data\n\n            if (!message) {\n              return\n            }\n\n            if (message.type === eventType) {\n              callback(event, message)\n            }\n          })\n        },\n        send: (type) => {\n          this.context.worker?.postMessage(type)\n        },\n      },\n      events: {\n        addListener: (target, eventType, callback) => {\n          target.addEventListener(eventType, callback as EventListener)\n          this.listeners.push({\n            eventType,\n            target,\n            callback: callback as EventListener,\n          })\n\n          return () => {\n            target.removeEventListener(eventType, callback as EventListener)\n          }\n        },\n        removeAllListeners: () => {\n          for (const { target, eventType, callback } of this.listeners) {\n            target.removeEventListener(eventType, callback)\n          }\n          this.listeners = []\n        },\n        once: (eventType) => {\n          const bindings: Array<() => void> = []\n\n          return new Promise<\n            ServiceWorkerMessage<\n              typeof eventType,\n              ServiceWorkerIncomingEventsMap[typeof eventType]\n            >\n          >((resolve, reject) => {\n            const handleIncomingMessage = (event: MessageEvent) => {\n              try {\n                const message = event.data\n\n                if (message.type === eventType) {\n                  resolve(message)\n                }\n              } catch (error) {\n                reject(error)\n              }\n            }\n\n            bindings.push(\n              this.context.events.addListener(\n                navigator.serviceWorker,\n                'message',\n                handleIncomingMessage,\n              ),\n              this.context.events.addListener(\n                navigator.serviceWorker,\n                'messageerror',\n                reject,\n              ),\n            )\n          }).finally(() => {\n            bindings.forEach((unbind) => unbind())\n          })\n        },\n      },\n      supports: {\n        serviceWorkerApi:\n          !('serviceWorker' in navigator) || location.protocol === 'file:',\n        readableStreamTransfer: supportsReadableStreamTransfer(),\n      },\n    }\n\n    this.startHandler = context.supports.serviceWorkerApi\n      ? createFallbackStart(context)\n      : createStartHandler(context)\n\n    this.stopHandler = context.supports.serviceWorkerApi\n      ? createFallbackStop(context)\n      : createStop(context)\n\n    return context\n  }\n\n  public async start(options: StartOptions = {}): StartReturnType {\n    if (options.waitUntilReady === true) {\n      devUtils.warn(\n        'The \"waitUntilReady\" option has been deprecated. Please remove it from this \"worker.start()\" call. Follow the recommended Browser integration (https://mswjs.io/docs/integrations/browser) to eliminate any race conditions between the Service Worker registration and any requests made by your application on initial render.',\n      )\n    }\n\n    this.context.startOptions = mergeRight(\n      DEFAULT_START_OPTIONS,\n      options,\n    ) as SetupWorkerInternalContext['startOptions']\n\n    // Enable the WebSocket interception.\n    handleWebSocketEvent({\n      getUnhandledRequestStrategy: () => {\n        return this.context.startOptions.onUnhandledRequest\n      },\n      getHandlers: () => {\n        return this.handlersController.currentHandlers()\n      },\n      onMockedConnection: (connection) => {\n        if (!this.context.startOptions.quiet) {\n          // Attach the logger for mocked connections since\n          // those won't be visible in the browser's devtools.\n          attachWebSocketLogger(connection)\n        }\n      },\n      onPassthroughConnection() {},\n    })\n    webSocketInterceptor.apply()\n\n    this.subscriptions.push(() => {\n      webSocketInterceptor.dispose()\n    })\n\n    return await this.startHandler(this.context.startOptions, options)\n  }\n\n  public stop(): void {\n    super.dispose()\n    this.context.events.removeAllListeners()\n    this.context.emitter.removeAllListeners()\n    this.stopHandler()\n  }\n}\n\n/**\n * Sets up a requests interception in the browser with the given request handlers.\n * @param {RequestHandler[]} handlers List of request handlers.\n *\n * @see {@link https://mswjs.io/docs/api/setup-worker `setupWorker()` API reference}\n */\nexport function setupWorker(\n  ...handlers: Array<RequestHandler | WebSocketHandler>\n): SetupWorker {\n  return new SetupWorkerApi(...handlers)\n}\n", "/**\n * Returns a boolean indicating whether the current browser\n * supports `ReadableStream` as a `Transferable` when posting\n * messages.\n */\nexport function supportsReadableStreamTransfer() {\n  try {\n    const stream = new ReadableStream({\n      start: (controller) => controller.close(),\n    })\n    const message = new MessageChannel()\n    message.port1.postMessage(stream, [stream])\n    return true\n  } catch {\n    return false\n  }\n}\n"], "mappings": ";AAAA,IAAMA,eAAA,GAAkB;AAExB,SAASC,oBAAoBC,UAAA,EAAiBC,IAAA,EAAmB;EAC/D,QAAQA,IAAA;IAEN,KAAK;MACH,OAAOD,UAAA;IAGT,KAAK;IACL,KAAK;MACH,OAAOE,MAAA,CAAOF,UAAU;IAG1B,KAAK;MACH,OAAOG,IAAA,CAAKC,SAAA,CAAUJ,UAAU;IAGlC,KAAK;MAAK;QAER,IAAI,OAAOA,UAAA,KAAe,UAAU;UAClC,OAAOA,UAAA;QACT;QAEA,MAAMK,IAAA,GAAOF,IAAA,CAAKC,SAAA,CAAUJ,UAAU;QAGtC,IAAIK,IAAA,KAAS,QAAQA,IAAA,KAAS,QAAQ,mBAAmBC,IAAA,CAAKD,IAAI,GAAG;UACnE,OAAOL,UAAA;QACT;QAEA,OAAOK,IAAA;MACT;EACF;AACF;AAEO,SAASE,OAAOC,OAAA,KAAoBC,WAAA,EAA4B;EACrE,IAAIA,WAAA,CAAYC,MAAA,KAAW,GAAG;IAC5B,OAAOF,OAAA;EACT;EAEA,IAAIG,eAAA,GAAkB;EACtB,IAAIC,gBAAA,GAAmBJ,OAAA,CAAQK,OAAA,CAC7Bf,eAAA,EACA,CAACgB,KAAA,EAAOC,SAAA,EAAWC,CAAA,EAAGf,IAAA,KAAS;IAC7B,MAAMD,UAAA,GAAaS,WAAA,CAAYE,eAAA;IAC/B,MAAMM,KAAA,GAAQlB,mBAAA,CAAoBC,UAAA,EAAYC,IAAI;IAElD,IAAI,CAACc,SAAA,EAAW;MACdJ,eAAA;MACA,OAAOM,KAAA;IACT;IAEA,OAAOH,KAAA;EACT,CACF;EAGA,IAAIH,eAAA,GAAkBF,WAAA,CAAYC,MAAA,EAAQ;IACxCE,gBAAA,IAAoB,IAAIH,WAAA,CAAYS,KAAA,CAAMP,eAAe,EAAEQ,IAAA,CAAK,GAAG;EACrE;EAEAP,gBAAA,GAAmBA,gBAAA,CAAiBC,OAAA,CAAQ,WAAW,GAAG;EAE1D,OAAOD,gBAAA;AACT;AC/DA,IAAMQ,sBAAA,GAAyB;AAO/B,SAASC,gBAAgBC,MAAA,EAAoB;EAC3C,IAAI,CAACA,MAAA,CAAMC,KAAA,EAAO;IAChB;EACF;EAEA,MAAMC,SAAA,GAAYF,MAAA,CAAMC,KAAA,CAAME,KAAA,CAAM,IAAI;EACxCD,SAAA,CAAUE,MAAA,CAAO,GAAGN,sBAAsB;EAC1CE,MAAA,CAAMC,KAAA,GAAQC,SAAA,CAAUL,IAAA,CAAK,IAAI;AACnC;AAEO,IAAMQ,cAAA,GAAN,cAA6BC,KAAA,CAAM;EAGxCC,YAA4BrB,OAAA,KAAoBC,WAAA,EAAoB;IAClE,MAAMD,OAAO;IADa,KAAAA,OAAA,GAAAA,OAAA;IAF5B,KAAAsB,IAAA,GAAO;IAIL,KAAKtB,OAAA,GAAUD,MAAA,CAAOC,OAAA,EAAS,GAAGC,WAAW;IAC7CY,eAAA,CAAgB,IAAI;EACtB;AACF;AA2BO,IAAMU,SAAA,GAAuBA,CAClCC,SAAA,EACAxB,OAAA,KACGC,WAAA,KACmB;EACtB,IAAI,CAACuB,SAAA,EAAW;IACd,MAAM,IAAIL,cAAA,CAAenB,OAAA,EAAS,GAAGC,WAAW;EAClD;AACF;AAEAsB,SAAA,CAAUE,EAAA,GAAK,CAACC,gBAAA,EAAkBF,SAAA,EAAWxB,OAAA,KAAYC,WAAA,KAAgB;EACvE,IAAI,CAACuB,SAAA,EAAW;IACd,MAAMG,aAAA,GACJ1B,WAAA,CAAYC,MAAA,KAAW,IAAIF,OAAA,GAAUD,MAAA,CAAOC,OAAA,EAAS,GAAGC,WAAW;IACrE,IAAIa,MAAA;IAEJ,IAAI;MACFA,MAAA,GAAQc,OAAA,CAAQC,SAAA,CAAUH,gBAAA,EAA4C,CACpEC,aAAA,CACD;IACH,SAASG,GAAA,EAAP;MACAhB,MAAA,GAASY,gBAAA,CAAwCC,aAAa;IAChE;IAEA,MAAMb,MAAA;EACR;AACF;;;AC7EO,SAASiB,cAAA,EAAyB;EACvC,IAAI,OAAOC,SAAA,KAAc,eAAeA,SAAA,CAAUC,OAAA,KAAY,eAAe;IAC3E,OAAO;EACT;EAEA,IAAI,OAAOC,OAAA,KAAY,aAAa;IAElC,MAAMC,IAAA,GAAQD,OAAA,CAAgBC,IAAA;IAC9B,IAAIA,IAAA,KAAS,cAAcA,IAAA,KAAS,UAAU;MAC5C,OAAO;IACT;IAGA,OAAO,CAAC,EACND,OAAA,CAAQE,QAAA,IACRF,OAAA,CAAQE,QAAA,CAASC,IAAA;EAErB;EAEA,OAAO;AACT;;;ACvBA,SAASC,QAAA,IAAAC,SAAA,QAAgB;;;ACelB,IAAMC,KAAA,GAAQ,MAInBC,OAAA,IAC6C;EAC7C,IAAI;IACF,MAAMC,IAAA,GAAO,MAAMD,OAAA,CAAQ,EAAEE,KAAA,CAAO7B,MAAA,IAAU;MAC5C,MAAMA,MAAA;IACR,CAAC;IACD,OAAO;MAAE8B,KAAA,EAAO;MAAMF;IAAK;EAC7B,SAAS5B,MAAA,EAAP;IACA,OAAO;MAAE8B,KAAA,EAAA9B,MAAA;MAAO4B,IAAA,EAAM;IAAK;EAC7B;AACF;;;AC5BA,SAASJ,QAAA,QAAgB;;;ACGlB,SAASO,qBAAqBC,SAAA,EAA2B;EAC9D,OAAO,IAAIC,GAAA,CAAID,SAAA,EAAWE,QAAA,CAASC,IAAI,EAAEA,IAAA;AAC3C;;;ACAO,SAASC,wBACdC,YAAA,EACAC,iBAAA,EACAC,UAAA,EACsB;EACtB,MAAMC,SAAA,GAAY,CAChBH,YAAA,CAAaI,MAAA,EACbJ,YAAA,CAAaK,UAAA,EACbL,YAAA,CAAaM,OAAA,CACf;EACA,MAAMC,cAAA,GAAiBJ,SAAA,CAAUK,MAAA,CAAQC,KAAA,IAAkC;IACzE,OAAOA,KAAA,IAAS;EAClB,CAAC;EACD,MAAMC,MAAA,GAASH,cAAA,CAAeI,IAAA,CAAMC,OAAA,IAAW;IAC7C,OAAOV,UAAA,CAAWU,OAAA,CAAOC,SAAA,EAAWZ,iBAAiB;EACvD,CAAC;EAED,OAAOS,MAAA,IAAU;AACnB;;;AFdO,IAAMI,iBAAA,GAAoB,MAAAA,CAC/BC,GAAA,EACAC,OAAA,GAA+B,CAAC,GAChCd,UAAA,KACwC;EAExC,MAAMD,iBAAA,GAAoBP,oBAAA,CAAqBqB,GAAG;EAElD,MAAME,iBAAA,GAAoB,MAAMpC,SAAA,CAAUqC,aAAA,CACvCC,gBAAA,CAAiB,EACjBC,IAAA,CAAMC,aAAA,IACLA,aAAA,CAAcb,MAAA,CAAQR,YAAA,IACpBD,uBAAA,CAAwBC,YAAA,EAAcC,iBAAA,EAAmBC,UAAU,CACrE,CACF;EACF,IAAI,CAACrB,SAAA,CAAUqC,aAAA,CAAcI,UAAA,IAAcL,iBAAA,CAAkBlE,MAAA,GAAS,GAAG;IAOvE8C,QAAA,CAAS0B,MAAA,CAAO;EAClB;EAEA,MAAM,CAACC,oBAAoB,IAAIP,iBAAA;EAE/B,IAAIO,oBAAA,EAAsB;IAGxBA,oBAAA,CAAqBC,MAAA,CAAO;IAG5B,OAAO,CACL1B,uBAAA,CACEyB,oBAAA,EACAvB,iBAAA,EACAC,UACF,GACAsB,oBAAA,CACF;EACF;EAGA,MAAME,kBAAA,GAAqB,MAAMrC,KAAA,CAC/B,YAAY;IACV,MAAMW,YAAA,GAAe,MAAMnB,SAAA,CAAUqC,aAAA,CAAcS,QAAA,CAASZ,GAAA,EAAKC,OAAO;IACxE,OAAO;IAAA;IAAA;IAGLjB,uBAAA,CAAwBC,YAAA,EAAcC,iBAAA,EAAmBC,UAAU,GACnEF,YAAA,CACF;EACF,CACF;EAGA,IAAI0B,kBAAA,CAAmBjC,KAAA,EAAO;IAC5B,MAAMmC,eAAA,GAAkBF,kBAAA,CAAmBjC,KAAA,CAAM5C,OAAA,CAAQgF,QAAA,CAAS,OAAO;IAIzE,IAAID,eAAA,EAAiB;MACnB,MAAME,QAAA,GAAW,IAAIlC,GAAA,CAAIoB,OAAA,EAASe,KAAA,IAAS,KAAKlC,QAAA,CAASC,IAAI;MAE7D,MAAM,IAAI7B,KAAA,CACRkB,QAAA,CAASX,aAAA,CAAc,mDACmBsD,QAAA,CAAShC,IAAI,oBAAoBG,iBAAiB;AAAA;AAAA;AAAA;AAAA,oFAIhB,CAC9E;IACF;IAGA,MAAM,IAAIhC,KAAA,CACRkB,QAAA,CAASX,aAAA,CACP,gDACAkD,kBAAA,CAAmBjC,KAAA,CAAM5C,OAC3B,CACF;EACF;EAEA,OAAO6E,kBAAA,CAAmBnC,IAAA;AAC5B;;;AG/FA,SAASJ,QAAA,IAAA6C,SAAA,QAAgB;;;ACCzB,SAAS7C,QAAA,IAAA8C,SAAA,QAAgB;AAalB,SAASC,kBAAkBC,IAAA,GAA8B,CAAC,GAAG;EAClE,IAAIA,IAAA,CAAKC,KAAA,EAAO;IACd;EACF;EAEA,MAAMvF,OAAA,GAAUsF,IAAA,CAAKtF,OAAA,IAAW;EAGhCwF,OAAA,CAAQC,cAAA,CACN,KAAKL,SAAA,CAASzD,aAAA,CAAc3B,OAAO,CAAC,IACpC,mCACF;EAEAwF,OAAA,CAAQE,GAAA,CACN,4CACA,oBACA,oBACF;EAEAF,OAAA,CAAQE,GAAA,CAAI,qDAAqD;EAEjE,IAAIJ,IAAA,CAAKxC,SAAA,EAAW;IAElB0C,OAAA,CAAQE,GAAA,CAAI,sBAAsBJ,IAAA,CAAKxC,SAAS;EAClD;EAEA,IAAIwC,IAAA,CAAKK,WAAA,EAAa;IAEpBH,OAAA,CAAQE,GAAA,CAAI,iBAAiBJ,IAAA,CAAKK,WAAW;EAC/C;EAEA,IAAIL,IAAA,CAAKM,MAAA,EAAQ;IAEfJ,OAAA,CAAQE,GAAA,CAAI,sBAAsBJ,IAAA,CAAKM,MAAA,CAAOC,EAAA,EAAIP,IAAA,CAAKM,MAAA,CAAOE,SAAS;EACzE;EAGAN,OAAA,CAAQO,QAAA,CAAS;AACnB;;;AD7CA,eAAsBC,cACpBC,OAAA,EACA9B,OAAA,EACA;EACA8B,OAAA,CAAQC,aAAA,CAAcC,IAAA,CAAK,eAAe;EAC1C,MAAM;IAAEC;EAAQ,IAAI,MAAMH,OAAA,CAAQI,MAAA,CAAOC,IAAA,CAAK,iBAAiB;EAK/D,IAAIL,OAAA,CAAQM,gBAAA,EAAkB;IAC5BpB,SAAA,CAASqB,IAAA,CACP,4KACF;IACA;EACF;EAEAP,OAAA,CAAQM,gBAAA,GAAmB;EAE3BlB,iBAAA,CAAkB;IAChBE,KAAA,EAAOpB,OAAA,CAAQoB,KAAA;IACfI,WAAA,EAAaM,OAAA,CAAQ9C,YAAA,EAAc+B,KAAA;IACnCpC,SAAA,EAAWmD,OAAA,CAAQpC,MAAA,EAAQG,SAAA;IAC3B4B,MAAA,EAAQQ,OAAA,CAAQR;EAClB,CAAC;AACH;;;AEXO,IAAMa,aAAA,GAAN,MAAoB;EACzBpF,YAA6BqF,IAAA,EAAmB;IAAnB,KAAAA,IAAA,GAAAA,IAAA;EAAoB;EAE1CC,YACLC,KAAA,KACGC,IAAA,EACG;IACN,MAAM,CAACnE,IAAA,EAAMoE,QAAQ,IAAID,IAAA;IACzB,KAAKH,IAAA,CAAKC,WAAA,CAAY;MAAExE,IAAA,EAAMyE,KAAA;MAAOlE;IAAK,GAAG;MAAEoE;IAAS,CAAC;EAC3D;AACF;;;ACxBO,SAASC,oBACdC,OAAA,EACsC;EAMtC,IAAI,CAAC,QAAQ,KAAK,EAAEhC,QAAA,CAASgC,OAAA,CAAQC,MAAM,GAAG;IAC5C,OAAO;EACT;EAEA,OAAOD,OAAA,CAAQE,IAAA;AACjB;;;ACbO,SAASC,mBACdC,iBAAA,EACS;EACT,OAAO,IAAIC,OAAA,CAAQD,iBAAA,CAAkBlD,GAAA,EAAK;IACxC,GAAGkD,iBAAA;IACHF,IAAA,EAAMH,mBAAA,CAAoBK,iBAAiB;EAC7C,CAAC;AACH;;;ACJA,SAASE,cAAA,QAAsB;AAC/B,SAASC,aAAA,QAAqB;AAE9B,SAASjF,QAAA,IAAAkF,SAAA,QAAgB;AACzB,SAASC,cAAA,QAAsB;AAC/B,SAASC,aAAA,QAAqB;AAEvB,IAAMC,qBAAA,GAAwBA,CACnC1B,OAAA,EACA9B,OAAA,KACG;EACH,OAAO,OACLyC,KAAA,EACA5G,OAAA,KAIG;IACH,MAAM4H,cAAA,GAAiB,IAAInB,aAAA,CAAcG,KAAA,CAAMiB,KAAA,CAAM,CAAC,CAAC;IAEvD,MAAMC,SAAA,GAAY9H,OAAA,CAAQoG,OAAA,CAAQP,EAAA;IAClC,MAAMmB,OAAA,GAAUG,kBAAA,CAAmBnH,OAAA,CAAQoG,OAAO;IAClD,MAAM2B,mBAAA,GAAsBf,OAAA,CAAQgB,KAAA,CAAM;IAM1C,MAAMC,YAAA,GAAejB,OAAA,CAAQgB,KAAA,CAAM;IACnCV,cAAA,CAAeY,KAAA,CAAMC,GAAA,CAAInB,OAAA,EAASiB,YAAY;IAE9C,IAAI;MACF,MAAMV,aAAA,CACJP,OAAA,EACAc,SAAA,EACA7B,OAAA,CAAQmC,kBAAA,CAAmB,EAAEzE,MAAA,CAAO+D,aAAA,CAAc,gBAAgB,CAAC,GACnEvD,OAAA,EACA8B,OAAA,CAAQoC,OAAA,EACR;QACEC,sBAAA,EAAwB;UACtBV,cAAA,CAAejB,WAAA,CAAY,aAAa;QAC1C;QACA,MAAM4B,iBAAiBC,QAAA,EAAU;UAAEC,OAAA;UAASC;QAAa,GAAG;UAI1D,MAAMC,aAAA,GAAgBH,QAAA,CAASR,KAAA,CAAM;UACrC,MAAMY,oBAAA,GAAuBJ,QAAA,CAASR,KAAA,CAAM;UAC5C,MAAMa,YAAA,GAAepB,cAAA,CAAee,QAAQ;UAM5C,IAAIvC,OAAA,CAAQ6C,QAAA,CAASC,sBAAA,EAAwB;YAC3C,MAAMC,oBAAA,GAAuBR,QAAA,CAAStB,IAAA;YAEtCU,cAAA,CAAejB,WAAA,CACb,iBACA;cACE,GAAGkC,YAAA;cACH3B,IAAA,EAAM8B;YACR,GACAA,oBAAA,GAAuB,CAACA,oBAAoB,IAAI,MAClD;UACF,OAAO;YAOL,MAAMC,oBAAA,GACJT,QAAA,CAAStB,IAAA,KAAS,OACd,OACA,MAAMyB,aAAA,CAAcO,WAAA,CAAY;YAEtCtB,cAAA,CAAejB,WAAA,CAAY,iBAAiB;cAC1C,GAAGkC,YAAA;cACH3B,IAAA,EAAM+B;YACR,CAAC;UACH;UAEA,IAAI,CAAC9E,OAAA,CAAQoB,KAAA,EAAO;YAClBU,OAAA,CAAQoC,OAAA,CAAQ/B,IAAA,CAAK,mBAAmB,MAAM;cAC5CmC,OAAA,CAAQ/C,GAAA,CAAI;gBACVsB,OAAA,EAASe,mBAAA;gBACTS,QAAA,EAAUI,oBAAA;gBACVF;cACF,CAAC;YACH,CAAC;UACH;QACF;MACF,CACF;IACF,SAAS5H,MAAA,EAAO;MACd,IAAIA,MAAA,YAAiBM,KAAA,EAAO;QAC1BoG,SAAA,CAAS5E,KAAA,CACP;AAAA;AAAA;AAAA;AAAA,6RAKAoE,OAAA,CAAQC,MAAA,EACRD,OAAA,CAAQ9C,GAAA,EACRpD,MAAA,CAAMC,KAAA,IAASD,MACjB;QAIA8G,cAAA,CAAejB,WAAA,CAAY,iBAAiB;UAC1CwC,MAAA,EAAQ;UACRC,UAAA,EAAY;UACZC,OAAA,EAAS;YACP,gBAAgB;UAClB;UACAnC,IAAA,EAAMvH,IAAA,CAAKC,SAAA,CAAU;YACnB0B,IAAA,EAAMR,MAAA,CAAMQ,IAAA;YACZtB,OAAA,EAASc,MAAA,CAAMd,OAAA;YACfe,KAAA,EAAOD,MAAA,CAAMC;UACf,CAAC;QACH,CAAC;MACH;IACF;EACF;AACF;;;ACvIA,SAASuB,QAAA,IAAAgH,SAAA,QAAgB;AAQzB,eAAsBC,qBACpBtD,OAAA,EACe;EAEfA,OAAA,CAAQC,aAAA,CAAcC,IAAA,CAAK,yBAAyB;EAEpD,MAAM;IAAEC;EAAQ,IAAI,MAAMH,OAAA,CAAQI,MAAA,CAAOC,IAAA,CAAK,0BAA0B;EAQxE,IAAIF,OAAA,CAAQoD,QAAA,KAAa,oCAAyB;IAChDF,SAAA,CAAS9C,IAAA,CACP,6FAA6FJ,OAAA,CAAQqD,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8JAOrH;EACF;AACF;;;ACjCA,IAAMC,OAAA,GAAU,IAAIC,WAAA,CAAY;AAEzB,SAASC,aAAaC,IAAA,EAA0B;EACrD,OAAOH,OAAA,CAAQI,MAAA,CAAOD,IAAI;AAC5B;AAEO,SAASE,aAAaC,MAAA,EAAqBC,QAAA,EAA2B;EAC3E,MAAMC,OAAA,GAAU,IAAIC,WAAA,CAAYF,QAAQ;EACxC,OAAOC,OAAA,CAAQE,MAAA,CAAOJ,MAAM;AAC9B;AAOO,SAASK,cAAcC,KAAA,EAAgC;EAC5D,OAAOA,KAAA,CAAMN,MAAA,CAAOtJ,KAAA,CAClB4J,KAAA,CAAMC,UAAA,EACND,KAAA,CAAMC,UAAA,GAAaD,KAAA,CAAME,UAC3B;AACF;;;ACnBO,IAAMC,iBAAA,GAAmCC,MAAA,CAAO,iBAAiB;ACGjE,SAASC,YAAYzG,GAAA,EAAsB;EAChD,IAAI;IACF,IAAInB,GAAA,CAAImB,GAAG;IACX,OAAO;EACT,SAAS0G,MAAA,EAAP;IACA,OAAO;EACT;AACF;ACTO,SAASC,iBACdC,UAAA,EACAC,MAAA,EACe;EACf,MAAMC,UAAA,GAAaC,MAAA,CAAOC,qBAAA,CAAsBH,MAAM;EAEtD,MAAMI,MAAA,GAASH,UAAA,CAAWlH,IAAA,CAAMsH,OAAA,IAAW;IACzC,OAAOA,OAAA,CAAOC,WAAA,KAAgBP,UAAA;EAChC,CAAC;EAED,IAAIK,MAAA,EAAQ;IACV,OAAOvJ,OAAA,CAAQ0J,GAAA,CAAIP,MAAA,EAAQI,MAAM;EACnC;EAEA;AACF;ACQO,IAAMI,cAAA,GAAN,cAA4BC,QAAA,CAAS;EAS1C,OAAOC,yBAAyBtC,MAAA,EAAyB;IACvD,OAAOA,MAAA,IAAU,OAAOA,MAAA,IAAU;EACpC;EAEA,OAAOuC,mBAAmBvC,MAAA,EAAyB;IACjD,OAAOoC,cAAA,CAAcI,0BAAA,CAA2B3G,QAAA,CAASmE,MAAM;EACjE;;;;;EAMA,OAAOyC,mBAAmBzC,MAAA,EAAyB;IACjD,OAAO,CAACoC,cAAA,CAAcM,yBAAA,CAA0B7G,QAAA,CAASmE,MAAM;EACjE;EAEA,OAAO2C,OAAO5H,GAAA,EAAyBsE,QAAA,EAA0B;IAC/D,IAAI,CAACtE,GAAA,IAAOA,GAAA,KAAQ,YAAY,CAACyG,WAAA,CAAYzG,GAAG,GAAG;MACjD;IACF;IAEA,MAAMN,KAAA,GAAQiH,gBAAA,CAA2C,SAASrC,QAAQ;IAE1E,IAAI5E,KAAA,EAAO;MAGTA,KAAA,CAAMmI,OAAA,CAAQC,IAAA,CAAK,IAAIjJ,GAAA,CAAImB,GAAG,CAAC;IACjC,OAAO;MAEL+G,MAAA,CAAOgB,cAAA,CAAezD,QAAA,EAAU,OAAO;QACrC/H,KAAA,EAAOyD,GAAA;QACPgI,UAAA,EAAY;QACZC,YAAA,EAAc;QACdC,QAAA,EAAU;MACZ,CAAC;IACH;EACF;;;;EAKA,OAAOC,gBAAgBC,UAAA,EAAoC;IACzD,MAAMjD,OAAA,GAAU,IAAIkD,OAAA,CAAQ;IAC5B,SAASC,IAAA,GAAO,GAAGA,IAAA,GAAOF,UAAA,CAAWpM,MAAA,EAAQsM,IAAA,IAAQ,GAAG;MACtDnD,OAAA,CAAQoD,MAAA,CAAOH,UAAA,CAAWE,IAAI,GAAGF,UAAA,CAAWE,IAAA,GAAO,CAAC,CAAC;IACvD;IACA,OAAOnD,OAAA;EACT;EAEAhI,YAAY6F,IAAA,EAAwBwF,IAAA,GAA0B,CAAC,GAAG;IApFpE,IAAAC,EAAA;IAqFI,MAAMxD,MAAA,IAASwD,EAAA,GAAAD,IAAA,CAAKvD,MAAA,KAAL,OAAAwD,EAAA,GAAe;IAC9B,MAAMC,UAAA,GAAarB,cAAA,CAAcE,wBAAA,CAAyBtC,MAAM,IAC5DA,MAAA,GACA;IACJ,MAAM0D,SAAA,GAAYtB,cAAA,CAAcK,kBAAA,CAAmBzC,MAAM,IAAIjC,IAAA,GAAO;IAEpE,MAAM2F,SAAA,EAAW;MACf1D,MAAA,EAAQyD,UAAA;MACRxD,UAAA,EAAYsD,IAAA,CAAKtD,UAAA;MACjBC,OAAA,EAASqD,IAAA,CAAKrD;IAChB,CAAC;IAED,IAAIF,MAAA,KAAWyD,UAAA,EAAY;MAKzB,MAAMhJ,KAAA,GAAQiH,gBAAA,CAA2C,SAAS,IAAI;MAEtE,IAAIjH,KAAA,EAAO;QACTA,KAAA,CAAMuF,MAAA,GAASA,MAAA;MACjB,OAAO;QACL8B,MAAA,CAAOgB,cAAA,CAAe,MAAM,UAAU;UACpCxL,KAAA,EAAO0I,MAAA;UACP+C,UAAA,EAAY;UACZC,YAAA,EAAc;UACdC,QAAA,EAAU;QACZ,CAAC;MACH;IACF;IAEAb,cAAA,CAAcO,MAAA,CAAOY,IAAA,CAAKxI,GAAA,EAAK,IAAI;EACrC;AACF;AA5FO,IAAM4I,aAAA,GAANvB,cAAA;AAAMuB,aAAA,CAKKjB,yBAAA,GAA4B,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AALzDiB,aAAA,CAOKnB,0BAAA,GAA6B,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;ACjCvE,IAAMoB,WAAA,GAAcrC,MAAA,CAAO,aAAa;AAkBjC,SAASsC,cAAchG,OAAA,EAAkBiG,UAAA,EAA2B;EACzErL,OAAA,CAAQuG,GAAA,CAAInB,OAAA,EAAS+F,WAAA,EAAaE,UAAU;AAC9C;;;ACpBA,IAAIC,SAAA,GAAYjC,MAAA,CAAOgB,cAAA;AACvB,IAAIkB,QAAA,GAAWA,CAACC,MAAA,EAAQC,GAAA,KAAQ;EAC9B,SAAS/L,IAAA,IAAQ+L,GAAA,EACfH,SAAA,CAAUE,MAAA,EAAQ9L,IAAA,EAAM;IAAEgK,GAAA,EAAK+B,GAAA,CAAI/L,IAAI;IAAG4K,UAAA,EAAY;EAAK,CAAC;AAChE;AAOA,IAAIoB,cAAA,GAAiB,CAAC;AACtBH,QAAA,CAASG,cAAA,EAAgB;EACvBC,IAAA,EAAMA,CAAA,KAAMA,IAAA;EACZC,IAAA,EAAMA,CAAA,KAAMA,IAAA;EACZC,KAAA,EAAOA,CAAA,KAAMA,KAAA;EACbC,GAAA,EAAKA,CAAA,KAAMA,GAAA;EACXC,MAAA,EAAQA,CAAA,KAAMA;AAChB,CAAC;AACD,SAASA,OAAO9D,IAAA,EAAM;EACpB,OAAO,WAAWA,IAAI;AACxB;AACA,SAAS0D,KAAK1D,IAAA,EAAM;EAClB,OAAO,WAAWA,IAAI;AACxB;AACA,SAAS2D,KAAK3D,IAAA,EAAM;EAClB,OAAO,WAAWA,IAAI;AACxB;AACA,SAAS6D,IAAI7D,IAAA,EAAM;EACjB,OAAO,WAAWA,IAAI;AACxB;AACA,SAAS4D,MAAM5D,IAAA,EAAM;EACnB,OAAO,WAAWA,IAAI;AACxB;AAGA,IAAI+D,OAAA,GAAU7L,aAAA,CAAc;AAC5B,IAAI8L,MAAA,GAAS,MAAM;EACjBxM,YAAYC,IAAA,EAAM;IAChB,KAAKA,IAAA,GAAOA,IAAA;IACZ,KAAKwM,MAAA,GAAS,IAAI,KAAKxM,IAAI;IAC3B,MAAMyM,WAAA,GAAcC,WAAA,CAAY,OAAO;IACvC,MAAMC,YAAA,GAAeD,WAAA,CAAY,WAAW;IAC5C,MAAME,gBAAA,GAAmBH,WAAA,KAAgB,OAAOA,WAAA,KAAgB,UAAU,OAAOA,WAAA,KAAgB,eAAe,KAAKzM,IAAA,CAAK6M,UAAA,CAAWJ,WAAW;IAChJ,IAAIG,gBAAA,EAAkB;MACpB,KAAKE,KAAA,GAAQC,qBAAA,CAAsBJ,YAAA,EAAc,OAAO,IAAIK,IAAA,GAAO,KAAKF,KAAA;MACxE,KAAKG,IAAA,GAAOF,qBAAA,CAAsBJ,YAAA,EAAc,MAAM,IAAIK,IAAA,GAAO,KAAKC,IAAA;MACtE,KAAKC,OAAA,GAAUH,qBAAA,CAAsBJ,YAAA,EAAc,SAAS,IAAIK,IAAA,GAAO,KAAKE,OAAA;MAC5E,KAAKC,OAAA,GAAUJ,qBAAA,CAAsBJ,YAAA,EAAc,SAAS,IAAIK,IAAA,GAAO,KAAKG,OAAA;MAC5E,KAAK7L,KAAA,GAAQyL,qBAAA,CAAsBJ,YAAA,EAAc,OAAO,IAAIK,IAAA,GAAO,KAAK1L,KAAA;IAC1E,OAAO;MACL,KAAK2L,IAAA,GAAOD,IAAA;MACZ,KAAKE,OAAA,GAAUF,IAAA;MACf,KAAKG,OAAA,GAAUH,IAAA;MACf,KAAK1L,KAAA,GAAQ0L,IAAA;MACb,KAAKI,IAAA,GAAOJ,IAAA;IACd;EACF;EACAR,MAAA;EACAa,OAAOC,MAAA,EAAQ;IACb,OAAO,IAAIf,MAAA,CAAO,GAAG,KAAKvM,IAAI,IAAIsN,MAAM,EAAE;EAC5C;EAAA;AAAA;AAAA;AAAA;AAAA;EAMAR,MAAMpO,OAAA,KAAYC,WAAA,EAAa;IAC7B,KAAK4O,QAAA,CAAS;MACZC,KAAA,EAAO;MACP9O,OAAA,EAASwN,IAAA,CAAKxN,OAAO;MACrBC,WAAA;MACA6N,MAAA,EAAQ,KAAKA,MAAA;MACbiB,MAAA,EAAQ;QACNjB,MAAA,EAAQ;MACV;IACF,CAAC;EACH;EAAA;AAAA;AAAA;AAAA;AAAA;EAMAS,KAAKvO,OAAA,KAAYC,WAAA,EAAa;IAC5B,KAAK4O,QAAA,CAAS;MACZC,KAAA,EAAO;MACP9O,OAAA;MACAC,WAAA;MACA6N,MAAA,EAAQ,KAAKA,MAAA;MACbiB,MAAA,EAAQ;QACNjB,MAAA,EAAQ;MACV;IACF,CAAC;IACD,MAAMkB,YAAA,GAAe,IAAIC,gBAAA,CAAiB;IAC1C,OAAO,CAACC,QAAA,KAAaC,YAAA,KAAiB;MACpCH,YAAA,CAAaI,OAAA,CAAQ;MACrB,KAAKP,QAAA,CAAS;QACZC,KAAA,EAAO;QACP9O,OAAA,EAAS,GAAGkP,QAAQ,IAAI1B,IAAA,CAAK,GAAGwB,YAAA,CAAaK,SAAS,IAAI,CAAC;QAC3DpP,WAAA,EAAakP,YAAA;QACbrB,MAAA,EAAQ,KAAKA,MAAA;QACbiB,MAAA,EAAQ;UACNjB,MAAA,EAAQ;QACV;MACF,CAAC;IACH;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAMAU,QAAQxO,OAAA,KAAYC,WAAA,EAAa;IAC/B,KAAK4O,QAAA,CAAS;MACZC,KAAA,EAAO;MACP9O,OAAA;MACAC,WAAA;MACA6N,MAAA,EAAQ,UAAU,KAAKA,MAAM;MAC7BiB,MAAA,EAAQ;QACNO,SAAA,EAAW;QACXxB,MAAA,EAAQ;MACV;IACF,CAAC;EACH;EAAA;AAAA;AAAA;AAAA;AAAA;EAMAW,QAAQzO,OAAA,KAAYC,WAAA,EAAa;IAC/B,KAAK4O,QAAA,CAAS;MACZC,KAAA,EAAO;MACP9O,OAAA;MACAC,WAAA;MACA6N,MAAA,EAAQ,UAAU,KAAKA,MAAM;MAC7BiB,MAAA,EAAQ;QACNO,SAAA,EAAW;QACXxB,MAAA,EAAQ;MACV;IACF,CAAC;EACH;EAAA;AAAA;AAAA;AAAA;AAAA;EAMAlL,MAAM5C,OAAA,KAAYC,WAAA,EAAa;IAC7B,KAAK4O,QAAA,CAAS;MACZC,KAAA,EAAO;MACP9O,OAAA;MACAC,WAAA;MACA6N,MAAA,EAAQ,UAAU,KAAKA,MAAM;MAC7BiB,MAAA,EAAQ;QACNO,SAAA,EAAW;QACXxB,MAAA,EAAQ;MACV;IACF,CAAC;EACH;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUAY,KAAKa,QAAA,EAAU;IACbA,QAAA,CAAS;EACX;EACAC,YAAYV,KAAA,EAAO9O,OAAA,EAAS;IAC1B,OAAO;MACLsP,SAAA,EAA2B,mBAAIG,IAAA,CAAK;MACpCX,KAAA;MACA9O;IACF;EACF;EACA6O,SAASvJ,IAAA,EAAM;IACb,MAAM;MACJwJ,KAAA;MACA9O,OAAA;MACA8N,MAAA;MACAiB,MAAA,EAAQW,YAAA;MACRzP,WAAA,GAAc;IAChB,IAAIqF,IAAA;IACJ,MAAMqK,KAAA,GAAQ,KAAKH,WAAA,CAAYV,KAAA,EAAO9O,OAAO;IAC7C,MAAM4P,cAAA,GAAiBF,YAAA,EAAcJ,SAAA,IAAa;IAClD,MAAMO,WAAA,GAAcH,YAAA,EAAc5B,MAAA,IAAU;IAC5C,MAAMgC,QAAA,GAAW;MACfR,SAAA,EAAWhC,cAAA,CAAesC,cAAc;MACxC9B,MAAA,EAAQR,cAAA,CAAeuC,WAAW;IACpC;IACA,MAAME,KAAA,GAAQ,KAAKC,SAAA,CAAUlB,KAAK;IAClCiB,KAAA,CACE,CAACD,QAAA,CAASR,SAAA,CAAU,KAAKW,eAAA,CAAgBN,KAAA,CAAML,SAAS,CAAC,CAAC,EAAEY,MAAA,CAAOpC,MAAA,IAAU,OAAOgC,QAAA,CAAShC,MAAA,CAAOA,MAAM,IAAI,EAAE,EAAEoC,MAAA,CAAOC,cAAA,CAAenQ,OAAO,CAAC,EAAEW,IAAA,CAAK,GAAG,GAC1J,GAAGV,WAAA,CAAYmQ,GAAA,CAAID,cAAc,CACnC;EACF;EACAF,gBAAgBX,SAAA,EAAW;IACzB,OAAO,GAAGA,SAAA,CAAUe,kBAAA,CAClB,OACF,CAAC,IAAIf,SAAA,CAAUgB,eAAA,CAAgB,CAAC;EAClC;EACAN,UAAUlB,KAAA,EAAO;IACf,QAAQA,KAAA;MACN,KAAK;MACL,KAAK;MACL,KAAK;QAAQ;UACX,OAAOpJ,GAAA;QACT;MACA,KAAK;QAAW;UACd,OAAOc,IAAA;QACT;MACA,KAAK;QAAS;UACZ,OAAO5D,KAAA;QACT;IACF;EACF;AACF;AACA,IAAIqM,gBAAA,GAAmB,MAAM;EAC3BsB,SAAA;EACAC,OAAA;EACAnB,SAAA;EACAhO,YAAA,EAAc;IACZ,KAAKkP,SAAA,GAAYE,WAAA,CAAYC,GAAA,CAAI;EACnC;EACAtB,QAAA,EAAU;IACR,KAAKoB,OAAA,GAAUC,WAAA,CAAYC,GAAA,CAAI;IAC/B,MAAMrB,SAAA,GAAY,KAAKmB,OAAA,GAAU,KAAKD,SAAA;IACtC,KAAKlB,SAAA,GAAYA,SAAA,CAAUsB,OAAA,CAAQ,CAAC;EACtC;AACF;AACA,IAAIrC,IAAA,GAAOA,CAAA,KAAM;AACjB,SAAS5I,IAAI1F,OAAA,KAAYC,WAAA,EAAa;EACpC,IAAI2N,OAAA,EAAS;IACX1L,OAAA,CAAQ0O,MAAA,CAAOb,KAAA,CAAMhQ,MAAA,CAAOC,OAAA,EAAS,GAAGC,WAAW,IAAI,IAAI;IAC3D;EACF;EACAuF,OAAA,CAAQE,GAAA,CAAI1F,OAAA,EAAS,GAAGC,WAAW;AACrC;AACA,SAASuG,KAAKxG,OAAA,KAAYC,WAAA,EAAa;EACrC,IAAI2N,OAAA,EAAS;IACX1L,OAAA,CAAQ2O,MAAA,CAAOd,KAAA,CAAMhQ,MAAA,CAAOC,OAAA,EAAS,GAAGC,WAAW,IAAI,IAAI;IAC3D;EACF;EACAuF,OAAA,CAAQgB,IAAA,CAAKxG,OAAA,EAAS,GAAGC,WAAW;AACtC;AACA,SAAS2C,MAAM5C,OAAA,KAAYC,WAAA,EAAa;EACtC,IAAI2N,OAAA,EAAS;IACX1L,OAAA,CAAQ2O,MAAA,CAAOd,KAAA,CAAMhQ,MAAA,CAAOC,OAAA,EAAS,GAAGC,WAAW,IAAI,IAAI;IAC3D;EACF;EACAuF,OAAA,CAAQ5C,KAAA,CAAM5C,OAAA,EAAS,GAAGC,WAAW;AACvC;AACA,SAAS+N,YAAY8C,YAAA,EAAc;EACjC,IAAIlD,OAAA,EAAS;IACX,OAAO1L,OAAA,CAAQ6O,GAAA,CAAID,YAAY;EACjC;EACA,OAAOE,UAAA,CAAWF,YAAY,GAAGG,QAAA,CAAS;AAC5C;AACA,SAAS5C,sBAAsB5N,KAAA,EAAOyQ,QAAA,EAAU;EAC9C,OAAOzQ,KAAA,KAAU,UAAUA,KAAA,KAAUyQ,QAAA;AACvC;AACA,SAASf,eAAenQ,OAAA,EAAS;EAC/B,IAAI,OAAOA,OAAA,KAAY,aAAa;IAClC,OAAO;EACT;EACA,IAAIA,OAAA,KAAY,MAAM;IACpB,OAAO;EACT;EACA,IAAI,OAAOA,OAAA,KAAY,UAAU;IAC/B,OAAOA,OAAA;EACT;EACA,IAAI,OAAOA,OAAA,KAAY,UAAU;IAC/B,OAAOL,IAAA,CAAKC,SAAA,CAAUI,OAAO;EAC/B;EACA,OAAOA,OAAA,CAAQiR,QAAA,CAAS;AAC1B;;;ACnRO,IAAME,eAAA,GAAN,cAA8B/P,KAAA,CAAM;EACzCC,YACkBgH,OAAA,EACAlG,IAAA,EACAiP,KAAA,EAChB;IACA,MACE,+CAA+CA,KAAA,IAASjP,IAAA,CAAK8O,QAAA,CAAS,oEACxE;IANgB,KAAA5I,OAAA,GAAAA,OAAA;IACA,KAAAlG,IAAA,GAAAA,IAAA;IACA,KAAAiP,KAAA,GAAAA,KAAA;IAKhB,KAAK9P,IAAA,GAAO;EACd;AACF;ACSO,IAAM+P,QAAA,GAAN,MAAuC;EAO5C,OAAOC,cACLjJ,OAAA,EACAkJ,SAAA,EACQ;IACR,OAAOlJ,OAAA,CAAQiJ,aAAA,CAAmBC,SAAS;EAC7C;EAEAlQ,YAAA,EAAc;IACZ,KAAKgF,MAAA,GAAS,mBAAImL,GAAA,CAAI;IACtB,KAAKC,YAAA,GAAeJ,QAAA,CAAQK,mBAAA;IAC5B,KAAKC,iCAAA,GAAoC;EAC3C;EAEQC,mBACNC,iBAAA,EACAN,SAAA,EACAO,QAAA,EACM;IACN,KAAKC,IAAA,CACHF,iBAAA,EAEA,GAAI,CAACN,SAAA,EAAWO,QAAQ,CAE1B;EACF;EAEQE,cACNT,SAAA,EACiC;IAGjC,OAAOU,KAAA,CAAMC,SAAA,CAAUhC,MAAA,CAAOiC,KAAA,CAAM,EAAC,EAAG,KAAK9L,MAAA,CAAOiF,GAAA,CAAIiG,SAAS,CAAC,KAAK,EAAC;EAC1E;EAEQa,gBACNC,SAAA,EACAP,QAAA,EACoC;IACpC,MAAMQ,KAAA,GAAQD,SAAA,CAAUE,OAAA,CAAQT,QAAQ;IAExC,IAAIQ,KAAA,GAAQ,IAAI;MACdD,SAAA,CAAUnR,MAAA,CAAOoR,KAAA,EAAO,CAAC;IAC3B;IAEA,OAAO,EAAC;EACV;EAEQE,kBACNjB,SAAA,EACAO,QAAA,EAC6B;IAC7B,MAAMW,YAAA,GAAeA,CAAA,GAAI/P,IAAA,KAA+B;MACtD,KAAKgQ,cAAA,CAAenB,SAAA,EAAWkB,YAAY;MAM3C,OAAOX,QAAA,CAASK,KAAA,CAAM,MAAMzP,IAAI;IAClC;IAGAuI,MAAA,CAAOgB,cAAA,CAAewG,YAAA,EAAc,QAAQ;MAAEhS,KAAA,EAAOqR,QAAA,CAASxQ;IAAK,CAAC;IAEpE,OAAOmR,YAAA;EACT;EAEOE,gBAAgBlB,YAAA,EAA4B;IACjD,KAAKA,YAAA,GAAeA,YAAA;IACpB,OAAO;EACT;;;;;;EAOOmB,gBAAA,EAA0B;IAC/B,OAAO,KAAKnB,YAAA;EACd;;;;;EAMOoB,WAAA,EAAkC;IACvC,OAAOZ,KAAA,CAAMa,IAAA,CAAK,KAAKzM,MAAA,CAAO0M,IAAA,CAAK,CAAC;EACtC;;;;;;;;;;EAWOhB,KACLR,SAAA,KACG7O,IAAA,EACM;IACT,MAAM2P,SAAA,GAAY,KAAKL,aAAA,CAAcT,SAAS;IAC9Cc,SAAA,CAAUW,OAAA,CAASlB,QAAA,IAAa;MAC9BA,QAAA,CAASK,KAAA,CAAM,MAAMzP,IAAI;IAC3B,CAAC;IAED,OAAO2P,SAAA,CAAUnS,MAAA,GAAS;EAC5B;EAUO+S,YACL1B,SAAA,EACAO,QAAA,EACM;IAEN,KAAKF,kBAAA,CAAmB,eAAeL,SAAA,EAAWO,QAAQ;IAE1D,MAAMoB,aAAA,GAAgB,KAAKlB,aAAA,CAAcT,SAAS,EAAErB,MAAA,CAAO4B,QAAQ;IACnE,KAAKzL,MAAA,CAAO8B,GAAA,CAAIoJ,SAAA,EAAW2B,aAAa;IAExC,IACE,KAAKzB,YAAA,GAAe,KACpB,KAAKH,aAAA,CAAcC,SAAS,IAAI,KAAKE,YAAA,IACrC,CAAC,KAAKE,iCAAA,EACN;MACA,KAAKA,iCAAA,GAAoC;MAEzC,MAAMwB,iBAAA,GAAoB,IAAIhC,eAAA,CAC5B,MACAI,SAAA,EACA,KAAKD,aAAA,CAAcC,SAAS,CAC9B;MACA/L,OAAA,CAAQgB,IAAA,CAAK2M,iBAAiB;IAChC;IAEA,OAAO;EACT;EAUOC,GACL7B,SAAA,EACAO,QAAA,EACM;IACN,OAAO,KAAKmB,WAAA,CAAY1B,SAAA,EAAWO,QAAQ;EAC7C;EAUOxL,KACLiL,SAAA,EACAO,QAAA,EACM;IACN,OAAO,KAAKmB,WAAA,CACV1B,SAAA,EACA,KAAKiB,iBAAA,CAAkBjB,SAAA,EAAWO,QAAQ,CAC5C;EACF;EAUOuB,gBACL9B,SAAA,EACAO,QAAA,EACM;IACN,MAAMO,SAAA,GAAY,KAAKL,aAAA,CAAcT,SAAS;IAE9C,IAAIc,SAAA,CAAUnS,MAAA,GAAS,GAAG;MACxB,MAAMgT,aAAA,GAAgB,CAACpB,QAAQ,EAAE5B,MAAA,CAAOmC,SAAS;MACjD,KAAKhM,MAAA,CAAO8B,GAAA,CAAIoJ,SAAA,EAAW2B,aAAa;IAC1C,OAAO;MACL,KAAK7M,MAAA,CAAO8B,GAAA,CAAIoJ,SAAA,EAAWc,SAAA,CAAUnC,MAAA,CAAO4B,QAAQ,CAAC;IACvD;IAEA,OAAO;EACT;EAUOwB,oBACL/B,SAAA,EACAO,QAAA,EACM;IACN,OAAO,KAAKuB,eAAA,CACV9B,SAAA,EACA,KAAKiB,iBAAA,CAAkBjB,SAAA,EAAWO,QAAQ,CAC5C;EACF;EAUOY,eACLnB,SAAA,EACAO,QAAA,EACM;IACN,MAAMO,SAAA,GAAY,KAAKL,aAAA,CAAcT,SAAS;IAE9C,IAAIc,SAAA,CAAUnS,MAAA,GAAS,GAAG;MACxB,KAAKkS,eAAA,CAAgBC,SAAA,EAAWP,QAAQ;MACxC,KAAKzL,MAAA,CAAO8B,GAAA,CAAIoJ,SAAA,EAAWc,SAAS;MAGpC,KAAKT,kBAAA,CAAmB,kBAAkBL,SAAA,EAAWO,QAAQ;IAC/D;IAEA,OAAO;EACT;;;;;;;EAgBOyB,IACLhC,SAAA,EACAO,QAAA,EACM;IACN,OAAO,KAAKY,cAAA,CAAenB,SAAA,EAAWO,QAAQ;EAChD;EAMO0B,mBACLjC,SAAA,EACM;IACN,IAAIA,SAAA,EAAW;MACb,KAAKlL,MAAA,CAAOoN,MAAA,CAAOlC,SAAS;IAC9B,OAAO;MACL,KAAKlL,MAAA,CAAOqN,KAAA,CAAM;IACpB;IAEA,OAAO;EACT;;;;EASOrB,UAAUd,SAAA,EAA8C;IAC7D,OAAOU,KAAA,CAAMa,IAAA,CAAK,KAAKd,aAAA,CAAcT,SAAS,CAAC;EACjD;;;;EASOD,cAAcC,SAAA,EAAsD;IACzE,OAAO,KAAKS,aAAA,CAAcT,SAAS,EAAErR,MAAA;EACvC;EAEOyT,aACLpC,SAAA,EACoC;IACpC,OAAO,KAAKc,SAAA,CAAUd,SAAS;EACjC;AACF;AA7TO,IAAMqC,OAAA,GAANvC,QAAA;AAAMuC,OAAA,CAKJlC,mBAAA,GAAsB;;;ACdxB,IAAMmC,+BAAA,GACX;AAEK,SAASC,gBAAmB3I,MAAA,EAA+B;EAChE;;IAEE6F,UAAA,CAAW7F,MAAM,KAAK;EAAA;AAE1B;AAEA,SAAS4I,gBAAgB5I,MAAA,EAAgB1K,KAAA,EAAkB;EAEzDuQ,UAAA,CAAW7F,MAAM,IAAI1K,KAAA;AACvB;AAEO,SAASuT,mBAAmB7I,MAAA,EAAsB;EAEvD,OAAO6F,UAAA,CAAW7F,MAAM;AAC1B;AAaO,IAAM8I,WAAA,GAAN,MAAsD;EAO3D5S,YAA6B8J,MAAA,EAAgB;IAAhB,KAAAA,MAAA,GAAAA,MAAA;IAC3B,KAAK+I,UAAA,GAAa;IAElB,KAAK7L,OAAA,GAAU,IAAIuL,OAAA,CAAQ;IAC3B,KAAKO,aAAA,GAAgB,EAAC;IACtB,KAAKC,MAAA,GAAS,IAAIvG,MAAA,CAAO1C,MAAA,CAAOE,WAAY;IAI5C,KAAKhD,OAAA,CAAQsK,eAAA,CAAgB,CAAC;IAE9B,KAAKyB,MAAA,CAAO7F,IAAA,CAAK,iCAAiC;EACpD;;;;;EAMU8F,iBAAA,EAA4B;IACpC,OAAO;EACT;;;;;EAMOlC,MAAA,EAAc;IACnB,MAAMiC,MAAA,GAAS,KAAKA,MAAA,CAAOzF,MAAA,CAAO,OAAO;IACzCyF,MAAA,CAAO7F,IAAA,CAAK,6BAA6B;IAEzC,IAAI,KAAK2F,UAAA,KAAe,WAA+B;MACrDE,MAAA,CAAO7F,IAAA,CAAK,8BAA8B;MAC1C;IACF;IAEA,MAAM+F,WAAA,GAAc,KAAKD,gBAAA,CAAiB;IAE1C,IAAI,CAACC,WAAA,EAAa;MAChBF,MAAA,CAAO7F,IAAA,CAAK,wDAAwD;MACpE;IACF;IAEA,KAAK2F,UAAA,GAAa;IAKlB,MAAMK,eAAA,GAAkB,KAAKC,WAAA,CAAY;IAEzC,IAAID,eAAA,EAAiB;MACnBH,MAAA,CAAO7F,IAAA,CAAK,sCAAsC;MAGlD,KAAK6E,EAAA,GAAK,CAACxM,KAAA,EAAOkL,QAAA,KAAa;QAC7BsC,MAAA,CAAO7F,IAAA,CAAK,8BAA8B3H,KAAK;QAI/C2N,eAAA,CAAgBlM,OAAA,CAAQ4K,WAAA,CAAYrM,KAAA,EAAOkL,QAAQ;QAInD,KAAKqC,aAAA,CAAcnI,IAAA,CAAK,MAAM;UAC5BuI,eAAA,CAAgBlM,OAAA,CAAQqK,cAAA,CAAe9L,KAAA,EAAOkL,QAAQ;UACtDsC,MAAA,CAAO7F,IAAA,CAAK,kCAAkC3H,KAAK;QACrD,CAAC;QAED,OAAO;MACT;MAEA,KAAKsN,UAAA,GAAa;MAElB;IACF;IAEAE,MAAA,CAAO7F,IAAA,CAAK,yDAAyD;IAGrE,KAAKkG,KAAA,CAAM;IAGX,KAAKC,WAAA,CAAY;IAEjB,KAAKR,UAAA,GAAa;EACpB;;;;;;EAOUO,MAAA,EAAc,CAAC;;;;EAKlBrB,GACLxM,KAAA,EACAkL,QAAA,EACM;IACN,MAAMsC,MAAA,GAAS,KAAKA,MAAA,CAAOzF,MAAA,CAAO,IAAI;IAEtC,IACE,KAAKuF,UAAA,KAAe,eACpB,KAAKA,UAAA,KAAe,YACpB;MACAE,MAAA,CAAO7F,IAAA,CAAK,4CAA4C;MACxD,OAAO;IACT;IAEA6F,MAAA,CAAO7F,IAAA,CAAK,+BAA+B3H,KAAA,EAAOkL,QAAQ;IAE1D,KAAKzJ,OAAA,CAAQ+K,EAAA,CAAGxM,KAAA,EAAOkL,QAAQ;IAC/B,OAAO;EACT;EAEOxL,KACLM,KAAA,EACAkL,QAAA,EACM;IACN,KAAKzJ,OAAA,CAAQ/B,IAAA,CAAKM,KAAA,EAAOkL,QAAQ;IACjC,OAAO;EACT;EAEOyB,IACL3M,KAAA,EACAkL,QAAA,EACM;IACN,KAAKzJ,OAAA,CAAQkL,GAAA,CAAI3M,KAAA,EAAOkL,QAAQ;IAChC,OAAO;EACT;EAEO0B,mBACL5M,KAAA,EACM;IACN,KAAKyB,OAAA,CAAQmL,kBAAA,CAAmB5M,KAAK;IACrC,OAAO;EACT;;;;EAKO+N,QAAA,EAAgB;IACrB,MAAMP,MAAA,GAAS,KAAKA,MAAA,CAAOzF,MAAA,CAAO,SAAS;IAE3C,IAAI,KAAKuF,UAAA,KAAe,YAAgC;MACtDE,MAAA,CAAO7F,IAAA,CAAK,mCAAmC;MAC/C;IACF;IAEA6F,MAAA,CAAO7F,IAAA,CAAK,8BAA8B;IAC1C,KAAK2F,UAAA,GAAa;IAElB,IAAI,CAAC,KAAKM,WAAA,CAAY,GAAG;MACvBJ,MAAA,CAAO7F,IAAA,CAAK,8CAA8C;MAC1D;IACF;IAIA,KAAKqG,aAAA,CAAc;IAEnBR,MAAA,CAAO7F,IAAA,CAAK,0BAA0BuF,eAAA,CAAgB,KAAK3I,MAAM,CAAC;IAElE,IAAI,KAAKgJ,aAAA,CAAcjU,MAAA,GAAS,GAAG;MACjCkU,MAAA,CAAO7F,IAAA,CAAK,oCAAoC,KAAK4F,aAAA,CAAcjU,MAAM;MAEzE,WAAWyU,OAAA,IAAW,KAAKR,aAAA,EAAe;QACxCQ,OAAA,CAAQ;MACV;MAEA,KAAKR,aAAA,GAAgB,EAAC;MAEtBC,MAAA,CAAO7F,IAAA,CAAK,kCAAkC,KAAK4F,aAAA,CAAcjU,MAAM;IACzE;IAEA,KAAKmI,OAAA,CAAQmL,kBAAA,CAAmB;IAChCY,MAAA,CAAO7F,IAAA,CAAK,yBAAyB;IAErC,KAAK2F,UAAA,GAAa;EACpB;EAEQM,YAAA,EAAgC;IAzO1C,IAAA7H,EAAA;IA0OI,MAAMkI,QAAA,GAAWf,eAAA,CAAsB,KAAK3I,MAAM;IAClD,KAAKiJ,MAAA,CAAO7F,IAAA,CAAK,+BAA8B5B,EAAA,GAAAkI,QAAA,oBAAAA,QAAA,CAAUxT,WAAA,KAAV,gBAAAsL,EAAA,CAAuBrL,IAAI;IAC1E,OAAOuT,QAAA;EACT;EAEQH,YAAA,EAAoB;IAC1BX,eAAA,CAAgB,KAAK5I,MAAA,EAAQ,IAAI;IACjC,KAAKiJ,MAAA,CAAO7F,IAAA,CAAK,wBAAwB,KAAKpD,MAAA,CAAOE,WAAW;EAClE;EAEQuJ,cAAA,EAAsB;IAC5BZ,kBAAA,CAAmB,KAAK7I,MAAM;IAC9B,KAAKiJ,MAAA,CAAO7F,IAAA,CAAK,4BAA4B,KAAKpD,MAAA,CAAOE,WAAW;EACtE;AACF;AClPO,SAASyJ,gBAAA,EAA0B;EACxC,OAAOC,IAAA,CAAKC,MAAA,CAAO,EAAE/D,QAAA,CAAS,EAAE,EAAEvQ,KAAA,CAAM,CAAC;AAC3C;;;ACcO,IAAMuU,gBAAA,GAAN,cAGGhB,WAAA,CAAoB;EAK5B5S,YAAY8C,OAAA,EAAmD;IAC7D8Q,gBAAA,CAAiB9J,MAAA,GAAST,MAAA,CAAOvG,OAAA,CAAQ7C,IAAI;IAC7C,MAAM2T,gBAAA,CAAiB9J,MAAM;IAC7B,KAAK+J,YAAA,GAAe/Q,OAAA,CAAQ+Q,YAAA;EAC9B;EAEUT,MAAA,EAAQ;IAChB,MAAML,MAAA,GAAS,KAAKA,MAAA,CAAOzF,MAAA,CAAO,OAAO;IAEzCyF,MAAA,CAAO7F,IAAA,CAAK,mCAAmC,KAAK2G,YAAA,CAAahV,MAAM;IAEvE,WAAWiV,WAAA,IAAe,KAAKD,YAAA,EAAc;MAC3Cd,MAAA,CAAO7F,IAAA,CAAK,gCAAgC4G,WAAA,CAAY9T,WAAA,CAAYC,IAAI;MACxE6T,WAAA,CAAYhD,KAAA,CAAM;MAElBiC,MAAA,CAAO7F,IAAA,CAAK,yCAAyC;MACrD,KAAK4F,aAAA,CAAcnI,IAAA,CAAK,MAAMmJ,WAAA,CAAYR,OAAA,CAAQ,CAAC;IACrD;EACF;EAEOvB,GACLxM,KAAA,EACAkL,QAAA,EACM;IAGN,WAAWqD,WAAA,IAAe,KAAKD,YAAA,EAAc;MAC3CC,WAAA,CAAY/B,EAAA,CAAGxM,KAAA,EAAOkL,QAAQ;IAChC;IAEA,OAAO;EACT;EAEOxL,KACLM,KAAA,EACAkL,QAAA,EACM;IACN,WAAWqD,WAAA,IAAe,KAAKD,YAAA,EAAc;MAC3CC,WAAA,CAAY7O,IAAA,CAAKM,KAAA,EAAOkL,QAAQ;IAClC;IAEA,OAAO;EACT;EAEOyB,IACL3M,KAAA,EACAkL,QAAA,EACM;IACN,WAAWqD,WAAA,IAAe,KAAKD,YAAA,EAAc;MAC3CC,WAAA,CAAY5B,GAAA,CAAI3M,KAAA,EAAOkL,QAAQ;IACjC;IAEA,OAAO;EACT;EAEO0B,mBACL5M,KAAA,EACM;IACN,WAAWsO,YAAA,IAAgB,KAAKA,YAAA,EAAc;MAC5CA,YAAA,CAAa1B,kBAAA,CAAmB5M,KAAK;IACvC;IAEA,OAAO;EACT;AACF;;;AEtFO,SAASwO,uBAAuBnP,OAAA,EAAqC;EAC1E,OAAO,CACLzF,CAAA,EACAR,OAAA,KAIG;IACH,MAAM;MAAEoG,OAAA,EAASiP;IAAa,IAAIrV,OAAA;IAClC,MAAMgH,OAAA,GAAUG,kBAAA,CAAmBkO,YAAA,CAAarO,OAAO;IASvD,IAAIqO,YAAA,CAAa7M,QAAA,CAASrG,IAAA,EAAM6C,QAAA,CAAS,QAAQ,GAAG;MAClD;IACF;IAEA,MAAMwD,QAAA,GACJ6M,YAAA,CAAa7M,QAAA,CAASW,MAAA,KAAW,IAC7BqC,QAAA,CAAS5I,KAAA,CAAM,IACf,IAAIkK,aAAA;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAOFA,aAAA,CAAclB,kBAAA,CAAmByJ,YAAA,CAAa7M,QAAA,CAASW,MAAM,IACzDkM,YAAA,CAAa7M,QAAA,CAAStB,IAAA,GACtB,MACJ;MACE,GAAGmO,YAAA;MAAA;AAAA;AAAA;AAAA;AAAA;MAMHnR,GAAA,EAAK8C,OAAA,CAAQ9C;IACf,CACF;IAEN+B,OAAA,CAAQoC,OAAA,CAAQ0J,IAAA,CACdsD,YAAA,CAAaC,gBAAA,GAAmB,oBAAoB,mBACpD;MACExN,SAAA,EAAWuN,YAAA,CAAarO,OAAA,CAAQnB,EAAA;MAChCmB,OAAA;MACAwB;IACF,CACF;EACF;AACF;;;AC/DA,SAASlG,QAAA,IAAAiT,SAAA,QAAgB;AAGlB,SAASC,oBACdrS,YAAA,EACAgB,OAAA,EACM;EACN,IAAI,CAACA,OAAA,EAASoB,KAAA,IAAS,CAACvC,QAAA,CAASC,IAAA,CAAKkL,UAAA,CAAWhL,YAAA,CAAa+B,KAAK,GAAG;IACpEqQ,SAAA,CAAS/O,IAAA,CACP,uFACgFrD,YAAA,CAAa+B,KAAK;AAAA;AAAA;AAAA,kFAKpG;EACF;AACF;;;A1BRO,IAAMuQ,kBAAA,GACXxP,OAAA,IACiB;EACjB,OAAO,SAASyP,MAAMvR,OAAA,EAASwR,aAAA,EAAe;IAC5C,MAAMC,mBAAA,GAAsB,MAAAA,CAAA,KAAY;MAItC3P,OAAA,CAAQI,MAAA,CAAOmN,kBAAA,CAAmB;MAGlCvN,OAAA,CAAQC,aAAA,CAAckN,EAAA,CACpB,WACAzL,qBAAA,CAAsB1B,OAAA,EAAS9B,OAAO,CACxC;MAGA8B,OAAA,CAAQC,aAAA,CAAckN,EAAA,CAAG,YAAYgC,sBAAA,CAAuBnP,OAAO,CAAC;MAEpE,MAAM4O,QAAA,GAAW,MAAM5Q,iBAAA,CACrBE,OAAA,CAAQE,aAAA,CAAcH,GAAA,EACtBC,OAAA,CAAQE,aAAA,CAAcF,OAAA,EACtBA,OAAA,CAAQd,UACV;MAEA,MAAM,CAACQ,MAAA,EAAQV,YAAY,IAAI0R,QAAA;MAE/B,IAAI,CAAChR,MAAA,EAAQ;QACX,MAAMgS,oBAAA,GAAuBF,aAAA,EAAetS,UAAA,GACxCd,SAAA,CAASZ,aAAA,CACP;AAAA;AAAA;AAAA;AAAA,GAKAwC,OAAA,CAAQE,aAAA,CAAcH,GACxB,IACA3B,SAAA,CAASZ,aAAA,CACP;AAAA;AAAA;AAAA;AAAA,2PAKAwC,OAAA,CAAQE,aAAA,CAAcH,GAAA,EACtBlB,QAAA,CAAS8S,IACX;QAEJ,MAAM,IAAI1U,KAAA,CAAMyU,oBAAoB;MACtC;MAEA5P,OAAA,CAAQpC,MAAA,GAASA,MAAA;MACjBoC,OAAA,CAAQ9C,YAAA,GAAeA,YAAA;MAEvB8C,OAAA,CAAQI,MAAA,CAAO4M,WAAA,CAAY8C,MAAA,EAAQ,gBAAgB,MAAM;QACvD,IAAIlS,MAAA,CAAOD,KAAA,KAAU,aAAa;UAKhCqC,OAAA,CAAQC,aAAA,CAAcC,IAAA,CAAK,eAAe;QAC5C;QAGA4P,MAAA,CAAOC,aAAA,CAAc/P,OAAA,CAAQgQ,iBAAiB;QAK9CF,MAAA,CAAOpP,WAAA,CAAY;UAAExE,IAAA,EAAM;QAAkB,CAAC;MAChD,CAAC;MAID,MAAMoH,oBAAA,CAAqBtD,OAAO,EAAEtD,KAAA,CAAO7B,MAAA,IAAU;QACnDyB,SAAA,CAASK,KAAA,CACP,2JACF;QAEA4C,OAAA,CAAQ5C,KAAA,CAAM9B,MAAK;MACrB,CAAC;MAEDmF,OAAA,CAAQgQ,iBAAA,GAAoBF,MAAA,CAAOG,WAAA,CACjC,MAAMjQ,OAAA,CAAQC,aAAA,CAAcC,IAAA,CAAK,mBAAmB,GACpD,GACF;MAIAqP,mBAAA,CAAoBrS,YAAA,EAAc8C,OAAA,CAAQkQ,YAAY;MAEtD,OAAOhT,YAAA;IACT;IAEA,MAAMiT,kBAAA,GAAqBR,mBAAA,CAAoB,EAAErR,IAAA,CAC/C,MAAOpB,YAAA,IAAiB;MACtB,MAAMkT,eAAA,GAAkBlT,YAAA,CAAaK,UAAA,IAAcL,YAAA,CAAaM,OAAA;MAKhE,IAAI4S,eAAA,EAAiB;QACnB,MAAM,IAAIC,OAAA,CAAeC,OAAA,IAAY;UACnCF,eAAA,CAAgBG,gBAAA,CAAiB,eAAe,MAAM;YACpD,IAAIH,eAAA,CAAgBzS,KAAA,KAAU,aAAa;cACzC,OAAO2S,OAAA,CAAQ;YACjB;UACF,CAAC;QACH,CAAC;MACH;MAGA,MAAMvQ,aAAA,CAAcC,OAAA,EAAS9B,OAAO,EAAExB,KAAA,CAAO7B,MAAA,IAAU;QACrD,MAAM,IAAIM,KAAA,CAAM,6BAA6BN,MAAA,EAAOd,OAAO,EAAE;MAC/D,CAAC;MAED,OAAOmD,YAAA;IACT,CACF;IAEA,OAAOiT,kBAAA;EACT;AACF;;;A2BlIA,SAAS9T,QAAA,IAAAmU,SAAA,QAAgB;;;ACAzB,SAASnU,QAAA,IAAAoU,SAAA,QAAgB;AAElB,SAASC,iBAAiBrR,IAAA,GAA4B,CAAC,GAAS;EACrE,IAAIA,IAAA,CAAKC,KAAA,EAAO;IACd;EACF;EAGAC,OAAA,CAAQE,GAAA,CACN,KAAKgR,SAAA,CAAS/U,aAAA,CAAc,mBAAmB,CAAC,IAChD,mCACF;AACF;;;ADRO,IAAMiV,UAAA,GACX3Q,OAAA,IACgB;EAChB,OAAO,SAAS4Q,KAAA,EAAO;IAGrB,IAAI,CAAC5Q,OAAA,CAAQM,gBAAA,EAAkB;MAC7BkQ,SAAA,CAASjQ,IAAA,CACP,iKACF;MACA;IACF;IAOAP,OAAA,CAAQC,aAAA,CAAcC,IAAA,CAAK,iBAAiB;IAC5CF,OAAA,CAAQM,gBAAA,GAAmB;IAC3BwP,MAAA,CAAOC,aAAA,CAAc/P,OAAA,CAAQgQ,iBAAiB;IAM9CF,MAAA,CAAOpP,WAAA,CAAY;MAAExE,IAAA,EAAM;IAAkB,CAAC;IAE9CwU,gBAAA,CAAiB;MAAEpR,KAAA,EAAOU,OAAA,CAAQkQ,YAAA,EAAc5Q;IAAM,CAAC;EACzD;AACF;;;AEjCA,SAASuR,UAAA,QAAkB;AAQpB,IAAMC,qBAAA,GAAoD;EAC/D1S,aAAA,EAAe;IACbH,GAAA,EAAK;IACLC,OAAA,EAAS;EACX;EACAoB,KAAA,EAAO;EACPyR,cAAA,EAAgB;EAChBC,kBAAA,EAAoB;EACpB5T,WAAWW,SAAA,EAAWkT,oBAAA,EAAsB;IAC1C,OAAOlT,SAAA,KAAckT,oBAAA;EACvB;AACF;;;ACLO,SAASC,uBAAA,EAG4B;EAC1C,MAAMC,QAAA,GAAoDA,CACxDb,OAAA,EACAc,MAAA,KACG;IACHD,QAAA,CAASxT,KAAA,GAAQ;IAEjBwT,QAAA,CAASb,OAAA,GAAW7T,IAAA,IAAS;MAC3B,IAAI0U,QAAA,CAASxT,KAAA,KAAU,WAAW;QAChC;MACF;MAEAwT,QAAA,CAASE,MAAA,GAAS5U,IAAA;MAElB,MAAM6U,WAAA,GAAsB9W,KAAA,IAAiB;QAC3C2W,QAAA,CAASxT,KAAA,GAAQ;QACjB,OAAOnD,KAAA;MACT;MAEA,OAAO8V,OAAA,CACL7T,IAAA,YAAgB4T,OAAA,GAAU5T,IAAA,GAAO4T,OAAA,CAAQC,OAAA,CAAQ7T,IAAI,EAAE6B,IAAA,CAAKgT,WAAW,CACzE;IACF;IAEAH,QAAA,CAASC,MAAA,GAAUG,MAAA,IAAW;MAC5B,IAAIJ,QAAA,CAASxT,KAAA,KAAU,WAAW;QAChC;MACF;MAEA6T,cAAA,CAAe,MAAM;QACnBL,QAAA,CAASxT,KAAA,GAAQ;MACnB,CAAC;MAED,OAAOyT,MAAA,CAAQD,QAAA,CAASM,eAAA,GAAkBF,MAAO;IACnD;EACF;EAEA,OAAOJ,QAAA;AACT;AChDO,IAAMO,eAAA,GAAN,cAAqDrB,OAAA,CAAe;EACzE,CAAAc,QAAA;EAEOb,OAAA;EACAc,MAAA;EAEPhW,YAAY+V,QAAA,GAAmC,MAAM;IACnD,MAAMQ,gBAAA,GAAmBT,sBAAA,CAAuB;IAChD,MAAM,CAACU,eAAA,EAAiBC,cAAA,KAAmB;MACzCF,gBAAA,CAAiBC,eAAA,EAAiBC,cAAc;MAChDV,QAAA,GAAWQ,gBAAA,CAAiBrB,OAAA,EAASqB,gBAAA,CAAiBP,MAAM;IAC9D,CAAC;IAED,KAAK,CAAAD,QAAA,GAAYQ,gBAAA;IACjB,KAAKrB,OAAA,GAAU,KAAK,CAAAa,QAAA,CAAUb,OAAA;IAC9B,KAAKc,MAAA,GAAS,KAAK,CAAAD,QAAA,CAAUC,MAAA;EAC/B;EAEA,IAAWzT,MAAA,EAAQ;IACjB,OAAO,KAAK,CAAAwT,QAAA,CAAUxT,KAAA;EACxB;EAEA,IAAW8T,gBAAA,EAAkB;IAC3B,OAAO,KAAK,CAAAN,QAAA,CAAUM,eAAA;EACxB;EAEOnT,KACLgT,WAAA,EACAQ,UAAA,EACA;IACA,OAAO,KAAK,CAAAC,QAAA,CAAU,MAAMzT,IAAA,CAAKgT,WAAA,EAAaQ,UAAU,CAAC;EAC3D;EAEOpV,MACLoV,UAAA,EACA;IACA,OAAO,KAAK,CAAAC,QAAA,CAAU,MAAMrV,KAAA,CAAMoV,UAAU,CAAC;EAC/C;EAEOE,QAAQC,SAAA,EAAuC;IACpD,OAAO,KAAK,CAAAF,QAAA,CAAU,MAAMC,OAAA,CAAQC,SAAS,CAAC;EAChD;EAEA,CAAAF,QAAAG,CACE1V,OAAA,EACqC;IACrC,OAAOwI,MAAA,CAAOmN,gBAAA,CAAiB3V,OAAA,EAAS;MACtC8T,OAAA,EAAS;QAAEpK,YAAA,EAAc;QAAM1L,KAAA,EAAO,KAAK8V;MAAQ;MACnDc,MAAA,EAAQ;QAAElL,YAAA,EAAc;QAAM1L,KAAA,EAAO,KAAK4W;MAAO;IACnD,CAAC;EACH;AACF;;;AE3DO,IAAMgB,gBAAA,GAAN,cAA+BjX,KAAA,CAAM;EAC1CC,YAAYrB,OAAA,EAAkB;IAC5B,MAAMA,OAAO;IACb,KAAKsB,IAAA,GAAO;IACZ2J,MAAA,CAAOqN,cAAA,CAAe,MAAMD,gBAAA,CAAiBnG,SAAS;EACxD;AACF;ADFA,IAAMqG,eAAA,GAAkB7N,MAAA,CAAO,iBAAiB;AACzC,IAAM8N,gBAAA,GAAmB9N,MAAA,CAAO,kBAAkB;AAElD,IAAM+N,iBAAA,GAAN,MAAwB;EAkB7BpX,YAAoB2F,OAAA,EAAkB;IAAlB,KAAAA,OAAA,GAAAA,OAAA;IAClB,KAAKuR,eAAe,IAAI;IACxB,KAAKC,gBAAgB,IAAI,IAAIb,eAAA,CAAgB;EAC/C;;;;;;;;EASOe,YAAYlQ,QAAA,EAA0B;IAC3CjH,SAAA,CAAUE,EAAA,CACR4W,gBAAA,EACA,CAAC,KAAKE,eAAe,GACrB,2FACA,KAAKvR,OAAA,CAAQC,MAAA,EACb,KAAKD,OAAA,CAAQ9C,GACf;IAEA,KAAKqU,eAAe,IAAI;IACxB,KAAKC,gBAAgB,EAAEjC,OAAA,CAAQ/N,QAAQ;EASzC;;;;;;;;;EAUOmQ,UAAUnB,MAAA,EAA4C;IAC3DjW,SAAA,CAAUE,EAAA,CACR4W,gBAAA,EACA,CAAC,KAAKE,eAAe,GACrB,sFACA,KAAKvR,OAAA,CAAQC,MAAA,EACb,KAAKD,OAAA,CAAQ9C,GACf;IAEA,KAAKqU,eAAe,IAAI;IAOxB,KAAKC,gBAAgB,EAAEjC,OAAA,CAAQiB,MAAM;EACvC;AACF;AE7EA,eAAsBoB,UAIpBvQ,OAAA,EACAkJ,SAAA,KACG7O,IAAA,EACY;EACf,MAAMmW,QAAA,GAAWxQ,OAAA,CAAQgK,SAAA,CAAUd,SAAS;EAE5C,IAAIsH,QAAA,CAAS3Y,MAAA,KAAW,GAAG;IACzB;EACF;EAEA,WAAW4R,QAAA,IAAY+G,QAAA,EAAU;IAC/B,MAAM/G,QAAA,CAASK,KAAA,CAAM9J,OAAA,EAAS3F,IAAI;EACpC;AACF;AErBO,SAASoW,SAAYrY,KAAA,EAAYsY,KAAA,GAAQ,OAAmB;EACjE,OAAOA,KAAA,GACH9N,MAAA,CAAOiH,SAAA,CAAUjB,QAAA,CAAS+H,IAAA,CAAKvY,KAAK,EAAE0N,UAAA,CAAW,UAAU,IAC3DlD,MAAA,CAAOiH,SAAA,CAAUjB,QAAA,CAAS+H,IAAA,CAAKvY,KAAK,MAAM;AAChD;ACCO,SAASwY,qBACdC,GAAA,EACAC,GAAA,EACA;EACA,IAAI;IACFD,GAAA,CAAIC,GAAG;IACP,OAAO;EACT,SAAQC,CAAA,EAAN;IACA,OAAO;EACT;AACF;ACZO,SAASC,0BAA0BnS,IAAA,EAAyB;EACjE,OAAO,IAAIsE,QAAA,CACT7L,IAAA,CAAKC,SAAA,CACHsH,IAAA,YAAgB9F,KAAA,GACZ;IACEE,IAAA,EAAM4F,IAAA,CAAK5F,IAAA;IACXtB,OAAA,EAASkH,IAAA,CAAKlH,OAAA;IACde,KAAA,EAAOmG,IAAA,CAAKnG;EACd,IACAmG,IACN,GACA;IACEiC,MAAA,EAAQ;IACRC,UAAA,EAAY;IACZC,OAAA,EAAS;MACP,gBAAgB;IAClB;EACF,CACF;AACF;AAYO,SAASiQ,gBAAgB9Q,QAAA,EAA8C;EAC5E,OACEA,QAAA,IAAY,QACZA,QAAA,YAAoBgD,QAAA,IACpByN,oBAAA,CAAqBzQ,QAAA,EAAU,MAAM,KACrCA,QAAA,CAASrG,IAAA,KAAS;AAEtB;AAOO,SAASoX,eAAe9Y,KAAA,EAAmC;EAChE,OACEqY,QAAA,CAA8BrY,KAAA,EAAO,IAAI,KACzCwY,oBAAA,CAAqBxY,KAAA,EAAO,QAAQ,KACpCwY,oBAAA,CAAqBxY,KAAA,EAAO,YAAY,KACxCwY,oBAAA,CAAqBxY,KAAA,EAAO,UAAU;AAE1C;AC1DO,SAAS+Y,gBACd1Y,MAAA,EACgC;EAChC,IAAIA,MAAA,IAAS,MAAM;IACjB,OAAO;EACT;EAEA,IAAI,EAAEA,MAAA,YAAiBM,KAAA,GAAQ;IAC7B,OAAO;EACT;EAEA,OAAO,UAAUN,MAAA,IAAS,WAAWA,MAAA;AACvC;AJgCA,eAAsB2Y,eACpBtV,OAAA,EACkB;EAClB,MAAMuV,cAAA,GAAiB,MACrBlR,QAAA,IACG;IACH,IAAIA,QAAA,YAAoBpH,KAAA,EAAO;MAC7B+C,OAAA,CAAQwV,OAAA,CAAQnR,QAAQ;MACxB,OAAO;IACT;IAGA,IAAI8Q,eAAA,CAAgB9Q,QAAQ,GAAG;MAC7BrE,OAAA,CAAQyV,cAAA,CAAepR,QAAQ;MAC/B,OAAO;IACT;IAOA,IAAI+Q,cAAA,CAAe/Q,QAAQ,GAAG;MAC5B,MAAMrE,OAAA,CAAQ0V,UAAA,CAAWrR,QAAQ;MACjC,OAAO;IACT;IAGA,IAAIsQ,QAAA,CAAStQ,QAAQ,GAAG;MACtBrE,OAAA,CAAQwV,OAAA,CAAQnR,QAAQ;MACxB,OAAO;IACT;IAEA,OAAO;EACT;EAEA,MAAMsR,mBAAA,GAAsB,MAAOhZ,MAAA,IAAqC;IAGtE,IAAIA,MAAA,YAAiBuX,gBAAA,EAAkB;MACrC,MAAMf,MAAA,CAAO1U,KAAA;IACf;IAGA,IAAI4W,eAAA,CAAgB1Y,MAAK,GAAG;MAC1BqD,OAAA,CAAQwV,OAAA,CAAQ7Y,MAAK;MACrB,OAAO;IACT;IAGA,IAAIA,MAAA,YAAiB0K,QAAA,EAAU;MAC7B,OAAO,MAAMkO,cAAA,CAAe5Y,MAAK;IACnC;IAEA,OAAO;EACT;EAKAqD,OAAA,CAAQkE,OAAA,CAAQ/B,IAAA,CAAK,WAAW,CAAC;IAAEwB,SAAA,EAAWiS;EAAiB,MAAM;IACnE,IAAIA,gBAAA,KAAqB5V,OAAA,CAAQ2D,SAAA,EAAW;MAC1C;IACF;IAEA,IAAI3D,OAAA,CAAQM,UAAA,CAAW+T,gBAAgB,EAAE5U,KAAA,KAAU,WAAW;MAC5DO,OAAA,CAAQM,UAAA,CAAW+T,gBAAgB,EAAEjC,OAAA,CAAQ,MAAS;IACxD;EACF,CAAC;EAED,MAAMyD,mBAAA,GAAsB,IAAIrC,eAAA,CAA+B;EAK/D,IAAIxT,OAAA,CAAQ6C,OAAA,CAAQiT,MAAA,EAAQ;IAC1B,IAAI9V,OAAA,CAAQ6C,OAAA,CAAQiT,MAAA,CAAOC,OAAA,EAAS;MAClCF,mBAAA,CAAoB3C,MAAA,CAAOlT,OAAA,CAAQ6C,OAAA,CAAQiT,MAAA,CAAOzC,MAAM;IAC1D,OAAO;MACLrT,OAAA,CAAQ6C,OAAA,CAAQiT,MAAA,CAAOzD,gBAAA,CACrB,SACA,MAAM;QACJwD,mBAAA,CAAoB3C,MAAA,CAAOlT,OAAA,CAAQ6C,OAAA,CAAQiT,MAAA,CAAOzC,MAAM;MAC1D,GACA;QAAElR,IAAA,EAAM;MAAK,CACf;IACF;EACF;EAEA,MAAMgR,MAAA,GAAS,MAAM9U,KAAA,CAAM,YAAY;IAKrC,MAAM2X,uBAAA,GAA0BvB,SAAA,CAAUzU,OAAA,CAAQkE,OAAA,EAAS,WAAW;MACpEP,SAAA,EAAW3D,OAAA,CAAQ2D,SAAA;MACnBd,OAAA,EAAS7C,OAAA,CAAQ6C,OAAA;MACjBvC,UAAA,EAAYN,OAAA,CAAQM;IACtB,CAAC;IAED,MAAM6R,OAAA,CAAQ8D,IAAA,CAAK;;IAEjBJ,mBAAA,EACAG,uBAAA,EACAhW,OAAA,CAAQM,UAAA,CAAW+T,gBAAgB,EACpC;IAID,OAAO,MAAMrU,OAAA,CAAQM,UAAA,CAAW+T,gBAAgB;EAClD,CAAC;EAGD,IAAIwB,mBAAA,CAAoBpW,KAAA,KAAU,YAAY;IAC5CO,OAAA,CAAQwV,OAAA,CAAQK,mBAAA,CAAoBtC,eAAe;IACnD,OAAO;EACT;EAEA,IAAIJ,MAAA,CAAO1U,KAAA,EAAO;IAGhB,IAAI,MAAMkX,mBAAA,CAAoBxC,MAAA,CAAO1U,KAAK,GAAG;MAC3C,OAAO;IACT;IAKA,IAAIuB,OAAA,CAAQkE,OAAA,CAAQiJ,aAAA,CAAc,oBAAoB,IAAI,GAAG;MAI3D,MAAM+I,4BAAA,GAA+B,IAAI5B,iBAAA,CACvCtU,OAAA,CAAQ6C,OACV;MAEA,MAAM4R,SAAA,CAAUzU,OAAA,CAAQkE,OAAA,EAAS,sBAAsB;QACrDzF,KAAA,EAAO0U,MAAA,CAAO1U,KAAA;QACdoE,OAAA,EAAS7C,OAAA,CAAQ6C,OAAA;QACjBc,SAAA,EAAW3D,OAAA,CAAQ2D,SAAA;QACnBrD,UAAA,EAAY4V;MACd,CAAC,EAAE9V,IAAA,CAAK,MAAM;QAKZ,IACE8V,4BAAA,CAA6B7B,gBAAgB,EAAE5U,KAAA,KAAU,WACzD;UACAyW,4BAAA,CAA6B7B,gBAAgB,EAAEjC,OAAA,CAAQ,MAAS;QAClE;MACF,CAAC;MAED,MAAM+D,UAAA,GAAa,MAAM9X,KAAA,CACvB,MAAM6X,4BAAA,CAA6B7B,gBAAgB,CACrD;MASA,IAAI8B,UAAA,CAAW1X,KAAA,EAAO;QACpB,OAAOkX,mBAAA,CAAoBQ,UAAA,CAAW1X,KAAK;MAC7C;MAEA,IAAI0X,UAAA,CAAW5X,IAAA,EAAM;QACnB,OAAOgX,cAAA,CAAeY,UAAA,CAAW5X,IAAI;MACvC;IACF;IAGAyB,OAAA,CAAQ0V,UAAA,CAAWR,yBAAA,CAA0B/B,MAAA,CAAO1U,KAAK,CAAC;IAC1D,OAAO;EACT;EAQA,IAAI0U,MAAA,CAAO5U,IAAA,EAAM;IACf,OAAOgX,cAAA,CAAepC,MAAA,CAAO5U,IAAI;EACnC;EAGA,OAAO;AACT;;;AKtOO,SAAS6X,sBAAsBC,YAAA,EAA+B;EACnE,MAAMC,UAAA,GAAaxP,MAAA,CAAOyP,wBAAA,CAAyB1J,UAAA,EAAYwJ,YAAY;EAG3E,IAAI,OAAOC,UAAA,KAAe,aAAa;IACrC,OAAO;EACT;EAGA,IACE,OAAOA,UAAA,CAAWnP,GAAA,KAAQ,cAC1B,OAAOmP,UAAA,CAAWnP,GAAA,CAAI,MAAM,aAC5B;IACA,OAAO;EACT;EAGA,IAAI,OAAOmP,UAAA,CAAWnP,GAAA,KAAQ,eAAemP,UAAA,CAAWha,KAAA,IAAS,MAAM;IACrE,OAAO;EACT;EAEA,IAAI,OAAOga,UAAA,CAAWtS,GAAA,KAAQ,eAAe,CAACsS,UAAA,CAAWtO,YAAA,EAAc;IACrE3G,OAAA,CAAQ5C,KAAA,CACN,mDAAmD4X,YAAA,oKACrD;IACA,OAAO;EACT;EAEA,OAAO;AACT;;;AEjCO,SAASG,mBAAmBC,KAAA,EAAiB;EAClD,OAAO3P,MAAA,CAAO4P,MAAA,CAAO,IAAIC,SAAA,CAAU,iBAAiB,GAAG;IACrDF;EACF,CAAC;AACH;ACFA,IAAMG,oBAAA,GAAuB,CAC3B,oBACA,oBACA,oBACA,gBACA,iBACF;AAEA,IAAMC,cAAA,GAAiBtQ,MAAA,CAAO,gBAAgB;AAK9C,eAAsBuQ,oBACpBjU,OAAA,EACAwB,QAAA,EACmB;EACnB,IAAIA,QAAA,CAASW,MAAA,KAAW,OAAOnC,OAAA,CAAQE,IAAA,IAAQ,MAAM;IACnD,OAAOoP,OAAA,CAAQe,MAAA,CAAOsD,kBAAA,CAAmB,CAAC;EAC5C;EAEA,MAAMO,UAAA,GAAa,IAAInY,GAAA,CAAIiE,OAAA,CAAQ9C,GAAG;EAEtC,IAAIiX,WAAA;EACJ,IAAI;IAEFA,WAAA,GAAc,IAAIpY,GAAA,CAAIyF,QAAA,CAASa,OAAA,CAAQiC,GAAA,CAAI,UAAU,GAAItE,OAAA,CAAQ9C,GAAG;EACtE,SAASpD,MAAA,EAAP;IACA,OAAOwV,OAAA,CAAQe,MAAA,CAAOsD,kBAAA,CAAmB7Z,MAAK,CAAC;EACjD;EAEA,IACE,EAAEqa,WAAA,CAAYC,QAAA,KAAa,WAAWD,WAAA,CAAYC,QAAA,KAAa,WAC/D;IACA,OAAO9E,OAAA,CAAQe,MAAA,CACbsD,kBAAA,CAAmB,qCAAqC,CAC1D;EACF;EAEA,IAAI/Y,OAAA,CAAQ0J,GAAA,CAAItE,OAAA,EAASgU,cAAc,IAAI,IAAI;IAC7C,OAAO1E,OAAA,CAAQe,MAAA,CAAOsD,kBAAA,CAAmB,yBAAyB,CAAC;EACrE;EAEA1P,MAAA,CAAOgB,cAAA,CAAejF,OAAA,EAASgU,cAAA,EAAgB;IAC7Cva,KAAA,GAAQmB,OAAA,CAAQ0J,GAAA,CAAItE,OAAA,EAASgU,cAAc,KAAK,KAAK;EACvD,CAAC;EAED,IACEhU,OAAA,CAAQqU,IAAA,KAAS,WAChBF,WAAA,CAAYG,QAAA,IAAYH,WAAA,CAAYI,QAAA,KACrC,CAACC,UAAA,CAAWN,UAAA,EAAYC,WAAW,GACnC;IACA,OAAO7E,OAAA,CAAQe,MAAA,CACbsD,kBAAA,CAAmB,kDAAkD,CACvE;EACF;EAEA,MAAMc,WAAA,GAA2B,CAAC;EAElC,IACG,CAAC,KAAK,GAAG,EAAEzW,QAAA,CAASwD,QAAA,CAASW,MAAM,KAAKnC,OAAA,CAAQC,MAAA,KAAW,UAC3DuB,QAAA,CAASW,MAAA,KAAW,OAAO,CAAC,CAAC,QAAQ,KAAK,EAAEnE,QAAA,CAASgC,OAAA,CAAQC,MAAM,GACpE;IACAwU,WAAA,CAAYxU,MAAA,GAAS;IACrBwU,WAAA,CAAYvU,IAAA,GAAO;IAEnB6T,oBAAA,CAAqB/H,OAAA,CAAS0I,UAAA,IAAe;MAC3C1U,OAAA,CAAQqC,OAAA,CAAQoK,MAAA,CAAOiI,UAAU;IACnC,CAAC;EACH;EAEA,IAAI,CAACF,UAAA,CAAWN,UAAA,EAAYC,WAAW,GAAG;IACxCnU,OAAA,CAAQqC,OAAA,CAAQoK,MAAA,CAAO,eAAe;IACtCzM,OAAA,CAAQqC,OAAA,CAAQoK,MAAA,CAAO,qBAAqB;IAC5CzM,OAAA,CAAQqC,OAAA,CAAQoK,MAAA,CAAO,QAAQ;IAC/BzM,OAAA,CAAQqC,OAAA,CAAQoK,MAAA,CAAO,MAAM;EAC/B;EAQAgI,WAAA,CAAYpS,OAAA,GAAUrC,OAAA,CAAQqC,OAAA;EAC9B,OAAOsS,KAAA,CAAM,IAAItU,OAAA,CAAQ8T,WAAA,EAAaM,WAAW,CAAC;AACpD;AAKA,SAASD,WAAWI,IAAA,EAAWC,KAAA,EAAqB;EAClD,IAAID,IAAA,CAAKE,MAAA,KAAWD,KAAA,CAAMC,MAAA,IAAUF,IAAA,CAAKE,MAAA,KAAW,QAAQ;IAC1D,OAAO;EACT;EAEA,IACEF,IAAA,CAAKR,QAAA,KAAaS,KAAA,CAAMT,QAAA,IACxBQ,IAAA,CAAKG,QAAA,KAAaF,KAAA,CAAME,QAAA,IACxBH,IAAA,CAAKlV,IAAA,KAASmV,KAAA,CAAMnV,IAAA,EACpB;IACA,OAAO;EACT;EAEA,OAAO;AACT;AC3GO,IAAMsV,yBAAA,GAAN,cAAwCC,eAAA,CAAgB;EAC7D5a,YAAA,EAAc;IACZmE,OAAA,CAAQgB,IAAA,CACN,0FACF;IAEA,MAAM;MACJ0V,UAAUC,KAAA,EAAO1X,UAAA,EAAY;QAE3BA,UAAA,CAAW2X,OAAA,CAAQD,KAAK;MAC1B;IACF,CAAC;EACH;AACF;ACRA,IAAME,cAAA,GAAN,cAA6BJ,eAAA,CAAgB;EAC3C5a,YACEib,gBAAA,KACGC,UAAA,EACH;IACA,MAAM,CAAC,GAAG,GAAGA,UAAU;IAEvB,MAAMC,QAAA,GAAW,CAAC,MAAMA,QAAA,EAAiB,GAAGF,gBAAgB,EAAEG,MAAA,CAC5D,CAACC,SAAA,EAAUR,SAAA,KAAcQ,SAAA,CAASC,WAAA,CAAYT,SAAS,CACzD;IAEAjR,MAAA,CAAOgB,cAAA,CAAe,MAAM,YAAY;MACtCX,IAAA,EAAM;QACJ,OAAOkR,QAAA;MACT;IACF,CAAC;EACH;AACF;AAEO,SAASI,qBAAqBC,eAAA,EAAwC;EAC3E,OAAOA,eAAA,CACJC,WAAA,CAAY,EACZ7b,KAAA,CAAM,GAAG,EACTmP,GAAA,CAAK2M,MAAA,IAAWA,MAAA,CAAOC,IAAA,CAAK,CAAC;AAClC;AAEA,SAASC,0BACPJ,eAAA,EACwB;EACxB,IAAIA,eAAA,KAAoB,IAAI;IAC1B,OAAO;EACT;EAEA,MAAMK,OAAA,GAAUN,oBAAA,CAAqBC,eAAe;EAEpD,IAAIK,OAAA,CAAQhd,MAAA,KAAW,GAAG;IACxB,OAAO;EACT;EAEA,MAAMid,YAAA,GAAeD,OAAA,CAAQE,WAAA,CAC3B,CAACC,aAAA,EAAcN,MAAA,KAAW;IACxB,IAAIA,MAAA,KAAW,UAAUA,MAAA,KAAW,UAAU;MAC5C,OAAOM,aAAA,CAAanN,MAAA,CAAO,IAAIoN,mBAAA,CAAoB,MAAM,CAAC;IAC5D,WAAWP,MAAA,KAAW,WAAW;MAC/B,OAAOM,aAAA,CAAanN,MAAA,CAAO,IAAIoN,mBAAA,CAAoB,SAAS,CAAC;IAC/D,WAAWP,MAAA,KAAW,MAAM;MAC1B,OAAOM,aAAA,CAAanN,MAAA,CAAO,IAAI8L,yBAAA,CAA0B,CAAC;IAC5D,OAAO;MACLqB,aAAA,CAAand,MAAA,GAAS;IACxB;IAEA,OAAOmd,aAAA;EACT,GACA,EACF;EAEA,OAAO,IAAIhB,cAAA,CAAec,YAAY;AACxC;AAEO,SAASI,mBACd/U,QAAA,EAC4B;EAC5B,IAAIA,QAAA,CAAStB,IAAA,KAAS,MAAM;IAC1B,OAAO;EACT;EAEA,MAAMsW,mBAAA,GAAsBP,yBAAA,CAC1BzU,QAAA,CAASa,OAAA,CAAQiC,GAAA,CAAI,kBAAkB,KAAK,EAC9C;EAEA,IAAI,CAACkS,mBAAA,EAAqB;IACxB,OAAO;EACT;EAKAhV,QAAA,CAAStB,IAAA,CAAKuW,MAAA,CAAOD,mBAAA,CAAoBpR,QAAQ;EACjD,OAAOoR,mBAAA,CAAoBhB,QAAA;AAC7B;AJpEO,IAAMkB,iBAAA,GAAN,cAA+BzJ,WAAA,CAAiC;EAGrE5S,YAAA,EAAc;IACZ,MAAMqc,iBAAA,CAAiBvS,MAAM;EAC/B;EAEUkJ,iBAAA,EAAmB;IAC3B,OAAOkG,qBAAA,CAAsB,OAAO;EACtC;EAEA,MAAgB9F,MAAA,EAAQ;IACtB,MAAMkJ,SAAA,GAAY3M,UAAA,CAAW2K,KAAA;IAE7Bpa,SAAA,CACE,CAAEoc,SAAA,CAAkBlT,iBAAiB,GACrC,sDACF;IAEAuG,UAAA,CAAW2K,KAAA,GAAQ,OAAOiC,KAAA,EAAOlR,IAAA,KAAS;MACxC,MAAM5E,SAAA,GAAYgN,eAAA,CAAgB;MAQlC,MAAM+I,aAAA,GACJ,OAAOD,KAAA,KAAU,YACjB,OAAO5a,QAAA,KAAa,eACpB,CAAC2H,WAAA,CAAYiT,KAAK,IACd,IAAI7a,GAAA,CAAI6a,KAAA,EAAO5a,QAAA,CAASC,IAAI,IAC5B2a,KAAA;MAEN,MAAM5W,OAAA,GAAU,IAAIK,OAAA,CAAQwW,aAAA,EAAenR,IAAI;MAK/C,IAAIkR,KAAA,YAAiBvW,OAAA,EAAS;QAC5B2F,aAAA,CAAchG,OAAA,EAAS4W,KAAK;MAC9B;MAEA,MAAME,eAAA,GAAkB,IAAInG,eAAA,CAA0B;MACtD,MAAMlT,UAAA,GAAa,IAAIgU,iBAAA,CAAkBzR,OAAO;MAEhD,KAAKoN,MAAA,CAAO7F,IAAA,CAAK,WAAWvH,OAAA,CAAQC,MAAA,EAAQD,OAAA,CAAQ9C,GAAG;MACvD,KAAKkQ,MAAA,CAAO7F,IAAA,CAAK,qCAAqC;MAEtD,KAAK6F,MAAA,CAAO7F,IAAA,CACV,sDACA,KAAKlG,OAAA,CAAQiJ,aAAA,CAAc,SAAS,CACtC;MAEA,MAAMyM,gBAAA,GAAmB,MAAMtE,cAAA,CAAc;QAC3CzS,OAAA;QACAc,SAAA;QACAO,OAAA,EAAS,KAAKA,OAAA;QACd5D,UAAA;QACAoV,UAAA,EAAY,MAAOmE,WAAA,IAAgB;UACjC,KAAK5J,MAAA,CAAO7F,IAAA,CAAK,6BAA6B;YAC5CyP;UACF,CAAC;UAGD,MAAMC,kBAAA,GAAqBV,kBAAA,CAAmBS,WAAW;UACzD,MAAMxV,QAAA,GACJyV,kBAAA,KAAuB,OACnBD,WAAA,GACA,IAAIlR,aAAA,CAAcmR,kBAAA,EAAoBD,WAAW;UAEvDlR,aAAA,CAAchB,MAAA,CAAO9E,OAAA,CAAQ9C,GAAA,EAAKsE,QAAQ;UAQ1C,IAAIsE,aAAA,CAAcpB,kBAAA,CAAmBlD,QAAA,CAASW,MAAM,GAAG;YAGrD,IAAInC,OAAA,CAAQkX,QAAA,KAAa,SAAS;cAChCJ,eAAA,CAAgBzG,MAAA,CAAOsD,kBAAA,CAAmB,qBAAqB,CAAC;cAChE;YACF;YAEA,IAAI3T,OAAA,CAAQkX,QAAA,KAAa,UAAU;cACjCjD,mBAAA,CAAoBjU,OAAA,EAASwB,QAAQ,EAAEjE,IAAA,CACpC4Z,SAAA,IAAa;gBACZL,eAAA,CAAgBvH,OAAA,CAAQ4H,SAAQ;cAClC,GACC3G,MAAA,IAAW;gBACVsG,eAAA,CAAgBzG,MAAA,CAAOG,MAAM;cAC/B,CACF;cACA;YACF;UACF;UAEA,IAAI,KAAKnP,OAAA,CAAQiJ,aAAA,CAAc,UAAU,IAAI,GAAG;YAC9C,KAAK8C,MAAA,CAAO7F,IAAA,CAAK,kCAAkC;YAKnD,MAAMqK,SAAA,CAAU,KAAKvQ,OAAA,EAAS,YAAY;;;;cAIxCG,QAAA,EAAUA,QAAA,CAASR,KAAA,CAAM;cACzBsN,gBAAA,EAAkB;cAClBtO,OAAA;cACAc;YACF,CAAC;UACH;UAEAgW,eAAA,CAAgBvH,OAAA,CAAQ/N,QAAQ;QAClC;QACAoR,cAAA,EAAiBpR,QAAA,IAAa;UAC5B,KAAK4L,MAAA,CAAO7F,IAAA,CAAK,wBAAwB;YAAE/F;UAAS,CAAC;UACrDsV,eAAA,CAAgBzG,MAAA,CAAOsD,kBAAA,CAAmBnS,QAAQ,CAAC;QACrD;QACAmR,OAAA,EAAU7Y,MAAA,IAAU;UAClB,KAAKsT,MAAA,CAAO7F,IAAA,CAAK,6BAA6B;YAAE3L,KAAA,EAAA9B;UAAM,CAAC;UACvDgd,eAAA,CAAgBzG,MAAA,CAAOvW,MAAK;QAC9B;MACF,CAAC;MAED,IAAIid,gBAAA,EAAkB;QACpB,KAAK3J,MAAA,CAAO7F,IAAA,CAAK,qDAAqD;QACtE,OAAOuP,eAAA;MACT;MAEA,KAAK1J,MAAA,CAAO7F,IAAA,CACV,0DACF;MAQA,MAAM6P,4BAAA,GAA+BpX,OAAA,CAAQgB,KAAA,CAAM;MAEnD,OAAO2V,SAAA,CAAU3W,OAAO,EAAEzC,IAAA,CAAK,MAAOiE,QAAA,IAAa;QACjD,KAAK4L,MAAA,CAAO7F,IAAA,CAAK,4BAA4B/F,QAAQ;QAErD,IAAI,KAAKH,OAAA,CAAQiJ,aAAA,CAAc,UAAU,IAAI,GAAG;UAC9C,KAAK8C,MAAA,CAAO7F,IAAA,CAAK,kCAAkC;UAEnD,MAAM5F,aAAA,GAAgBH,QAAA,CAASR,KAAA,CAAM;UAErC,MAAM4Q,SAAA,CAAU,KAAKvQ,OAAA,EAAS,YAAY;YACxCG,QAAA,EAAUG,aAAA;YACV2M,gBAAA,EAAkB;YAClBtO,OAAA,EAASoX,4BAAA;YACTtW;UACF,CAAC;QACH;QAEA,OAAOU,QAAA;MACT,CAAC;IACH;IAEAyC,MAAA,CAAOgB,cAAA,CAAe+E,UAAA,CAAW2K,KAAA,EAAOlR,iBAAA,EAAmB;MACzDyB,UAAA,EAAY;MACZC,YAAA,EAAc;MACd1L,KAAA,EAAO;IACT,CAAC;IAED,KAAK0T,aAAA,CAAcnI,IAAA,CAAK,MAAM;MAC5Bf,MAAA,CAAOgB,cAAA,CAAe+E,UAAA,CAAW2K,KAAA,EAAOlR,iBAAA,EAAmB;QACzDhK,KAAA,EAAO;MACT,CAAC;MAEDuQ,UAAA,CAAW2K,KAAA,GAAQgC,SAAA;MAEnB,KAAKvJ,MAAA,CAAO7F,IAAA,CACV,uCACAyC,UAAA,CAAW2K,KAAA,CAAMra,IACnB;IACF,CAAC;EACH;AACF;AA1LO,IAAM+c,gBAAA,GAANX,iBAAA;AAAMW,gBAAA,CACJlT,MAAA,GAAST,MAAA,CAAO,OAAO;;;AOdzB,SAAS4T,kBACd1C,IAAA,EACAC,KAAA,EACY;EACZ,MAAMvE,MAAA,GAAS,IAAIiH,UAAA,CAAW3C,IAAA,CAAKpR,UAAA,GAAaqR,KAAA,CAAMrR,UAAU;EAChE8M,MAAA,CAAOnP,GAAA,CAAIyT,IAAA,EAAM,CAAC;EAClBtE,MAAA,CAAOnP,GAAA,CAAI0T,KAAA,EAAOD,IAAA,CAAKpR,UAAU;EACjC,OAAO8M,MAAA;AACT;ACXO,IAAMkH,aAAA,GAAN,MAAqC;EAwB1Cnd,YACEc,IAAA,EACAgC,OAAA,EACA;IA1BF,KAASsa,IAAA,GAAO;IAChB,KAASC,eAAA,GAAkB;IAC3B,KAASC,SAAA,GAAY;IACrB,KAASC,cAAA,GAAiB;IAE1B,KAAOzc,IAAA,GAAe;IACtB,KAAO0c,UAAA,GAAiC;IAExC,KAAOC,aAAA,GAAoC;IAC3C,KAAOC,UAAA,GAAqB;IAE5B,KAAOC,SAAA,GAAqB;IAC5B,KAAOC,QAAA,GAAoB;IAC3B,KAAOC,UAAA,GAAsB;IAC7B,KAAOC,gBAAA,GAA4B;IACnC,KAAOC,OAAA,GAAmB;IAC1B,KAAOC,gBAAA,GAA4B;IACnC,KAAOC,MAAA,GAAiB;IACxB,KAAOC,KAAA,GAAgB;IAEvB,KAAAC,YAAA,GAAwB;IACxB,KAAAC,WAAA,GAAuB;IAMrB,KAAKtd,IAAA,GAAOA,IAAA;IACZ,KAAKiL,MAAA,IAASjJ,OAAA,oBAAAA,OAAA,CAASiJ,MAAA,KAAU;IACjC,KAAK0R,aAAA,IAAgB3a,OAAA,oBAAAA,OAAA,CAAS2a,aAAA,KAAiB;IAC/C,KAAKY,SAAA,GAAYjQ,IAAA,CAAKiB,GAAA,CAAI;EAC5B;EAEOiP,aAAA,EAA8B;IACnC,OAAO,EAAC;EACV;EAEOC,UAAUzd,IAAA,EAAcid,OAAA,EAAmBF,UAAA,EAAsB;IACtE,KAAK/c,IAAA,GAAOA,IAAA;IACZ,KAAKid,OAAA,GAAU,CAAC,CAACA,OAAA;IACjB,KAAKF,UAAA,GAAa,CAAC,CAACA,UAAA;EACtB;EAEOW,eAAA,EAAiB;IACtB,KAAKV,gBAAA,GAAmB;EAC1B;EAEOW,gBAAA,EAAkB,CAAC;EACnBC,yBAAA,EAA2B,CAAC;AACrC;AChDO,IAAMC,qBAAA,GAAN,cAAoCxB,aAAA,CAAc;EAMvDnd,YAAYc,IAAA,EAAcuK,IAAA,EAA0B;IAClD,MAAMvK,IAAI;IAEV,KAAKkd,gBAAA,IAAmB3S,IAAA,oBAAAA,IAAA,CAAM2S,gBAAA,KAAoB;IAClD,KAAKJ,QAAA,IAAWvS,IAAA,oBAAAA,IAAA,CAAMuS,QAAA,KAAY;IAClC,KAAKK,MAAA,IAAS5S,IAAA,oBAAAA,IAAA,CAAM4S,MAAA,KAAU;IAC9B,KAAKC,KAAA,IAAQ7S,IAAA,oBAAAA,IAAA,CAAM6S,KAAA,KAAS;EAC9B;AACF;ACbA,IAAMU,uBAAA,GAA0B,OAAOC,aAAA,KAAkB;AAElD,SAASC,YACd/S,MAAA,EACAjL,IAAA,EACAuK,IAAA,EAC+B;EAC/B,MAAM0T,cAAA,GAAiB,CACrB,SACA,YACA,aACA,WACA,QACA,WACA,QACF;EAMA,MAAMC,kBAAA,GAAqBJ,uBAAA,GACvBC,aAAA,GACAF,qBAAA;EAEJ,MAAMpZ,KAAA,GAAQwZ,cAAA,CAAepb,QAAA,CAAS7C,IAAI,IACtC,IAAIke,kBAAA,CAAmBle,IAAA,EAAM;IAC3Bkd,gBAAA,EAAkB;IAClBC,MAAA,GAAQ5S,IAAA,oBAAAA,IAAA,CAAM4S,MAAA,KAAU;IACxBC,KAAA,GAAO7S,IAAA,oBAAAA,IAAA,CAAM6S,KAAA,KAAS;EACxB,CAAC,IACD,IAAIf,aAAA,CAAcrc,IAAA,EAAM;IACtBiL,MAAA;IACA0R,aAAA,EAAe1R;EACjB,CAAC;EAEL,OAAOxG,KAAA;AACT;ACpCO,SAAS0Z,mBACdlT,MAAA,EACAoN,YAAA,EACe;EACf,IAAI,EAAEA,YAAA,IAAgBpN,MAAA,GAAS;IAC7B,OAAO;EACT;EAEA,MAAMmT,WAAA,GAActV,MAAA,CAAOiH,SAAA,CAAUsO,cAAA,CAAexH,IAAA,CAAK5L,MAAA,EAAQoN,YAAY;EAC7E,IAAI+F,WAAA,EAAa;IACf,OAAOnT,MAAA;EACT;EAEA,MAAM8E,SAAA,GAAYtQ,OAAA,CAAQ6e,cAAA,CAAerT,MAAM;EAC/C,OAAO8E,SAAA,GAAYoO,kBAAA,CAAmBpO,SAAA,EAAWsI,YAAY,IAAI;AACnE;ACKO,SAASkG,YACdtT,MAAA,EACAjJ,OAAA,EACQ;EACR,MAAMwc,KAAA,GAAQ,IAAIC,KAAA,CAAMxT,MAAA,EAAQyT,qBAAA,CAAsB1c,OAAO,CAAC;EAE9D,OAAOwc,KAAA;AACT;AAEA,SAASE,sBACP1c,OAAA,EACiB;EACjB,MAAM;IAAE2c,eAAA;IAAiBC,UAAA;IAAYC,WAAA;IAAaC;EAAY,IAAI9c,OAAA;EAClE,MAAMsE,OAAA,GAA2B,CAAC;EAElC,IAAI,OAAOqY,eAAA,KAAoB,aAAa;IAC1CrY,OAAA,CAAQ5G,SAAA,GAAY,UAAUuL,MAAA,EAAQ9H,IAAA,EAAM4b,SAAA,EAAW;MACrD,MAAMC,IAAA,GAAOvf,OAAA,CAAQC,SAAA,CAAUuf,IAAA,CAAK,MAAMhU,MAAA,EAAe9H,IAAA,EAAM4b,SAAS;MACxE,OAAOJ,eAAA,CAAgB9H,IAAA,CAAKkI,SAAA,EAAW5b,IAAA,EAAM6b,IAAI;IACnD;EACF;EAEA1Y,OAAA,CAAQN,GAAA,GAAM,UAAUiF,MAAA,EAAQoN,YAAA,EAAc6G,SAAA,EAAW;IACvD,MAAMF,IAAA,GAAOA,CAAA,KAAM;MACjB,MAAMG,cAAA,GAAiBhB,kBAAA,CAAmBlT,MAAA,EAAQoN,YAAY,KAAKpN,MAAA;MACnE,MAAMmU,cAAA,GAAiB3f,OAAA,CAAQ8Y,wBAAA,CAC7B4G,cAAA,EACA9G,YACF;MAGA,IAAI,QAAO+G,cAAA,oBAAAA,cAAA,CAAgBpZ,GAAA,MAAQ,aAAa;QAC9CoZ,cAAA,CAAepZ,GAAA,CAAIgK,KAAA,CAAM/E,MAAA,EAAQ,CAACiU,SAAS,CAAC;QAC5C,OAAO;MACT;MAGA,OAAOzf,OAAA,CAAQqK,cAAA,CAAeqV,cAAA,EAAgB9G,YAAA,EAAc;QAC1DpO,QAAA,EAAU;QACVF,UAAA,EAAY;QACZC,YAAA,EAAc;QACd1L,KAAA,EAAO4gB;MACT,CAAC;IACH;IAEA,IAAI,OAAOJ,WAAA,KAAgB,aAAa;MACtC,OAAOA,WAAA,CAAYjI,IAAA,CAAK5L,MAAA,EAAQ,CAACoN,YAAA,EAAc6G,SAAS,GAAGF,IAAI;IACjE;IAEA,OAAOA,IAAA,CAAK;EACd;EAEA1Y,OAAA,CAAQ6C,GAAA,GAAM,UAAU8B,MAAA,EAAQoN,YAAA,EAAcgH,QAAA,EAAU;IAItD,MAAML,IAAA,GAAOA,CAAA,KAAM/T,MAAA,CAAOoN,YAAmB;IAE7C,MAAM/Z,KAAA,GACJ,OAAOugB,WAAA,KAAgB,cACnBA,WAAA,CAAYhI,IAAA,CAAK5L,MAAA,EAAQ,CAACoN,YAAA,EAAcgH,QAAQ,GAAGL,IAAI,IACvDA,IAAA,CAAK;IAEX,IAAI,OAAO1gB,KAAA,KAAU,YAAY;MAC/B,OAAO,IAAI6E,IAAA,KAAqB;QAC9B,MAAMmc,KAAA,GAAOhhB,KAAA,CAAM2gB,IAAA,CAAKhU,MAAA,EAAQ,GAAG9H,IAAI;QAEvC,IAAI,OAAOyb,UAAA,KAAe,aAAa;UACrC,OAAOA,UAAA,CAAW/H,IAAA,CAAK5L,MAAA,EAAQ,CAACoN,YAAA,EAAqBlV,IAAI,GAAGmc,KAAI;QAClE;QAEA,OAAOA,KAAA,CAAK;MACd;IACF;IAEA,OAAOhhB,KAAA;EACT;EAEA,OAAOgI,OAAA;AACT;ACvGO,SAASiZ,yBACdvf,IAAA,EACgC;EAChC,MAAMwf,cAAA,GAAgD,CACpD,yBACA,mBACA,iBACA,aACA,WACF;EACA,OAAOA,cAAA,CAAeC,IAAA,CAAMC,aAAA,IAAkB;IAC5C,OAAO1f,IAAA,CAAKgM,UAAA,CAAW0T,aAAa;EACtC,CAAC;AACH;ACTO,SAASC,UAAUpf,IAAA,EAA8C;EACtE,IAAI;IACF,MAAM7C,IAAA,GAAOF,IAAA,CAAKoiB,KAAA,CAAMrf,IAAI;IAC5B,OAAO7C,IAAA;EACT,SAASW,CAAA,EAAP;IACA,OAAO;EACT;AACF;ACLO,SAASwhB,eACdhb,OAAA,EACAE,IAAA,EACU;EASV,MAAM+a,kBAAA,GAAqBnV,aAAA,CAAclB,kBAAA,CAAmB5E,OAAA,CAAQmC,MAAM,IACtEjC,IAAA,GACA;EAEJ,OAAO,IAAI4F,aAAA,CAAcmV,kBAAA,EAAoB;IAC3C/d,GAAA,EAAK8C,OAAA,CAAQkb,WAAA;IACb/Y,MAAA,EAAQnC,OAAA,CAAQmC,MAAA;IAChBC,UAAA,EAAYpC,OAAA,CAAQoC,UAAA;IACpBC,OAAA,EAAS8Y,qCAAA,CACPnb,OAAA,CAAQob,qBAAA,CAAsB,CAChC;EACF,CAAC;AACH;AAEA,SAASD,sCAAsCE,aAAA,EAAgC;EAC7E,MAAMhZ,OAAA,GAAU,IAAIkD,OAAA,CAAQ;EAE5B,MAAM+V,KAAA,GAAQD,aAAA,CAAcphB,KAAA,CAAM,SAAS;EAC3C,WAAWuL,IAAA,IAAQ8V,KAAA,EAAO;IACxB,IAAI9V,IAAA,CAAKwQ,IAAA,CAAK,MAAM,IAAI;MACtB;IACF;IAEA,MAAM,CAAC1b,IAAA,EAAM,GAAGihB,KAAK,IAAI/V,IAAA,CAAKvL,KAAA,CAAM,IAAI;IACxC,MAAMR,KAAA,GAAQ8hB,KAAA,CAAM5hB,IAAA,CAAK,IAAI;IAE7B0I,OAAA,CAAQoD,MAAA,CAAOnL,IAAA,EAAMb,KAAK;EAC5B;EAEA,OAAO4I,OAAA;AACT;AC5CA,eAAsBmZ,kBACpB5E,KAAA,EACiB;EACjB,MAAM6E,qBAAA,GAAwB7E,KAAA,CAAMvU,OAAA,CAAQiC,GAAA,CAAI,gBAAgB;EAEhE,IAAImX,qBAAA,IAAyB,QAAQA,qBAAA,KAA0B,IAAI;IACjE,OAAO/iB,MAAA,CAAO+iB,qBAAqB;EACrC;EAEA,MAAMzY,MAAA,GAAS,MAAM4T,KAAA,CAAM1U,WAAA,CAAY;EACvC,OAAOc,MAAA,CAAOQ,UAAA;AAChB;AVIA,IAAMkY,iBAAA,GAAoBhY,MAAA,CAAO,mBAAmB;AACpD,IAAMiY,QAAA,GAAU5gB,aAAA,CAAc;AAC9B,IAAM6gB,aAAA,GAAgBlY,MAAA,CAAO,eAAe;AAMrC,IAAMmY,wBAAA,GAAN,MAA+B;EAgCpCxhB,YAAqByhB,cAAA,EAAuC1O,MAAA,EAAgB;IAAvD,KAAA0O,cAAA,GAAAA,cAAA;IAAuC,KAAA1O,MAAA,GAAAA,MAAA;IAV5D,KAAQnN,MAAA,GAAiB;IACzB,KAAQ/C,GAAA,GAAW;IAUjB,KAAKwe,iBAAiB,IAAI;IAE1B,KAAKrc,MAAA,GAAS,mBAAImL,GAAA,CAAI;IACtB,KAAKuR,YAAA,GAAe,mBAAIvR,GAAA,CAAI;IAC5B,KAAK1J,SAAA,GAAYgN,eAAA,CAAgB;IACjC,KAAKkO,cAAA,GAAiB,IAAIzW,OAAA,CAAQ;IAClC,KAAK0W,cAAA,GAAiB,IAAI1E,UAAA,CAAW;IAErC,KAAKvX,OAAA,GAAU0Z,WAAA,CAAYoC,cAAA,EAAgB;MACzC7B,WAAA,EAAaA,CAAC,CAACzG,YAAA,EAAc6G,SAAS,GAAG6B,MAAA,KAAW;QAClD,QAAQ1I,YAAA;UACN,KAAK;YAAa;cAChB,MAAMjJ,SAAA,GAAYiJ,YAAA,CAAa9Z,KAAA,CAC7B,CACF;cAOA,KAAKsG,OAAA,CAAQwP,gBAAA,CAAiBjF,SAAA,EAAW8P,SAAgB;cAEzD,OAAO6B,MAAA,CAAO;YAChB;UAEA;YAAS;cACP,OAAOA,MAAA,CAAO;YAChB;QACF;MACF;MACAnC,UAAA,EAAYA,CAAC,CAACoC,UAAA,EAAY7d,IAAI,GAAG4d,MAAA,KAAW;QA3FlD,IAAAvW,EAAA;QA4FQ,QAAQwW,UAAA;UACN,KAAK;YAAQ;cACX,MAAM,CAAClc,MAAA,EAAQ/C,GAAG,IAAIoB,IAAA;cAEtB,IAAI,OAAOpB,GAAA,KAAQ,aAAa;gBAC9B,KAAK+C,MAAA,GAAS;gBACd,KAAK/C,GAAA,GAAMkf,aAAA,CAAcnc,MAAM;cACjC,OAAO;gBACL,KAAKA,MAAA,GAASA,MAAA;gBACd,KAAK/C,GAAA,GAAMkf,aAAA,CAAclf,GAAG;cAC9B;cAEA,KAAKkQ,MAAA,GAAS,KAAKA,MAAA,CAAOzF,MAAA,CAAO,GAAG,KAAK1H,MAAA,IAAU,KAAK/C,GAAA,CAAIjB,IAAA,EAAM;cAClE,KAAKmR,MAAA,CAAO7F,IAAA,CAAK,QAAQ,KAAKtH,MAAA,EAAQ,KAAK/C,GAAA,CAAIjB,IAAI;cAEnD,OAAOigB,MAAA,CAAO;YAChB;UAEA,KAAK;YAAoB;cACvB,MAAM,CAAC3R,SAAA,EAAWO,QAAQ,IAAIxM,IAAA;cAK9B,KAAK+d,aAAA,CAAc9R,SAAA,EAAWO,QAAQ;cACtC,KAAKsC,MAAA,CAAO7F,IAAA,CAAK,oBAAoBgD,SAAA,EAAWO,QAAQ;cAExD,OAAOoR,MAAA,CAAO;YAChB;UAEA,KAAK;YAAoB;cACvB,MAAM,CAAC5hB,IAAA,EAAMb,KAAK,IAAI6E,IAAA;cACtB,KAAK0d,cAAA,CAAe7a,GAAA,CAAI7G,IAAA,EAAMb,KAAK;cAEnC,KAAK2T,MAAA,CAAO7F,IAAA,CAAK,oBAAoBjN,IAAA,EAAMb,KAAK;cAEhD,OAAOyiB,MAAA,CAAO;YAChB;UAEA,KAAK;YAAQ;cACX,MAAM,CAAChc,IAAI,IAAI5B,IAAA;cAIf,KAAK0B,OAAA,CAAQwP,gBAAA,CAAiB,QAAQ,MAAM;gBAC1C,IAAI,OAAO,KAAKqD,UAAA,KAAe,aAAa;kBAI1C,MAAMyJ,aAAA,GAAgBtB,cAAA,CACpB,KAAKhb,OAAA;;;;;;kBAML,KAAKA,OAAA,CAAQwB,QACf;kBAGA,KAAKqR,UAAA,CAAWb,IAAA,CAAK,MAAM;oBACzBxQ,QAAA,EAAU8a,aAAA;oBACVhO,gBAAA,EAAkB,KAAKoN,iBAAiB;oBACxC1b,OAAA,EAASuc,YAAA;oBACTzb,SAAA,EAAW,KAAKA;kBAClB,CAAC;gBACH;cACF,CAAC;cAED,MAAM0b,WAAA,GACJ,OAAOtc,IAAA,KAAS,WAAW0C,YAAA,CAAa1C,IAAI,IAAIA,IAAA;cAGlD,MAAMqc,YAAA,GAAe,KAAKE,iBAAA,CAAkBD,WAAW;cACvD,KAAKZ,aAAa,IAAIW,YAAA,CAAavb,KAAA,CAAM;cAEzC,MAAM0b,kBAAA,KACJ/W,EAAA,QAAKgX,SAAA,KAAL,gBAAAhX,EAAA,CAAgBqM,IAAA,CAAK,MAAM;gBACzBhS,OAAA,EAASuc,YAAA;gBACTzb,SAAA,EAAW,KAAKA;cAClB,OAAMwO,OAAA,CAAQC,OAAA,CAAQ;cAExBmN,kBAAA,CAAmBzL,OAAA,CAAQ,MAAM;gBAE/B,IAAI,CAAC,KAAKyK,iBAAiB,GAAG;kBAC5B,KAAKtO,MAAA,CAAO7F,IAAA,CACV,kGACA,KAAKvH,OAAA,CAAQkN,UACf;kBAWA,IAAIyO,QAAA,EAAS;oBACX,KAAK3b,OAAA,CAAQ4c,gBAAA,CACX/P,+BAAA,EACA,KAAK/L,SACP;kBACF;kBAEA,OAAOob,MAAA,CAAO;gBAChB;cACF,CAAC;cAED;YACF;UAEA;YAAS;cACP,OAAOA,MAAA,CAAO;YAChB;QACF;MACF;IACF,CAAC;IAKDW,MAAA,CACE,KAAK7c,OAAA,EACL,UACA0Z,WAAA,CAAY,KAAK1Z,OAAA,CAAQ8c,MAAA,EAAQ;MAC/B7C,WAAA,EAAaA,CAAC,CAACzG,YAAA,EAAc6G,SAAS,GAAG6B,MAAA,KAAW;QAClD,QAAQ1I,YAAA;UACN,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;YAAa;cAChB,MAAMjJ,SAAA,GAAYiJ,YAAA,CAAa9Z,KAAA,CAC7B,CACF;cAEA,KAAKqjB,mBAAA,CAAoBxS,SAAA,EAAW8P,SAAqB;YAC3D;QACF;QAEA,OAAO6B,MAAA,CAAO;MAChB;MACAnC,UAAA,EAAYA,CAAC,CAACoC,UAAA,EAAY7d,IAAI,GAAG4d,MAAA,KAAW;QAC1C,QAAQC,UAAA;UACN,KAAK;YAAoB;cACvB,MAAM,CAAC5R,SAAA,EAAWO,QAAQ,IAAIxM,IAAA;cAI9B,KAAKye,mBAAA,CAAoBxS,SAAA,EAAWO,QAAQ;cAC5C,KAAKsC,MAAA,CAAO7F,IAAA,CAAK,2BAA2BgD,SAAA,EAAWO,QAAQ;cAE/D,OAAOoR,MAAA,CAAO;YAChB;QACF;MACF;IACF,CAAC,CACH;EACF;EAEQG,cACN9R,SAAA,EACAO,QAAA,EACM;IACN,MAAMkS,UAAA,GAAa,KAAK3d,MAAA,CAAOiF,GAAA,CAAIiG,SAAS,KAAK,EAAC;IAClD,MAAM0S,UAAA,GAAaD,UAAA,CAAW9T,MAAA,CAAO4B,QAAQ;IAC7C,KAAKzL,MAAA,CAAO8B,GAAA,CAAIoJ,SAAA,EAAW0S,UAAU;IAErC,KAAK7P,MAAA,CAAO7F,IAAA,CAAK,yBAAyBgD,SAAA,EAAWO,QAAQ;EAC/D;EAEQiS,oBACNxS,SAAA,EACAO,QAAA,EACM;IACN,MAAMkS,UAAA,GAAa,KAAKjB,YAAA,CAAazX,GAAA,CAAIiG,SAAS,KAAK,EAAC;IACxD,MAAM0S,UAAA,GAAaD,UAAA,CAAW9T,MAAA,CAAO4B,QAAQ;IAC7C,KAAKiR,YAAA,CAAa5a,GAAA,CAAIoJ,SAAA,EAAW0S,UAAU;IAE3C,KAAK7P,MAAA,CAAO7F,IAAA,CAAK,gCAAgCgD,SAAA,EAAWO,QAAQ;EACtE;;;;;EAMA,MAAa4G,YAAYlQ,QAAA,EAAmC;IAS1D,KAAKka,iBAAiB,IAAI;IAM1B,IAAI,KAAKE,aAAa,GAAG;MACvB,MAAMsB,sBAAA,GAAyB,MAAM1B,iBAAA,CACnC,KAAKI,aAAa,CACpB;MAEA,KAAKuB,OAAA,CAAQ,aAAa,KAAKnd,OAAA,CAAQ8c,MAAA,EAAQ;QAC7CxE,MAAA,EAAQ;QACRC,KAAA,EAAO2E;MACT,CAAC;MACD,KAAKC,OAAA,CAAQ,YAAY,KAAKnd,OAAA,CAAQ8c,MAAA,EAAQ;QAC5CxE,MAAA,EAAQ4E,sBAAA;QACR3E,KAAA,EAAO2E;MACT,CAAC;MACD,KAAKC,OAAA,CAAQ,QAAQ,KAAKnd,OAAA,CAAQ8c,MAAA,EAAQ;QACxCxE,MAAA,EAAQ4E,sBAAA;QACR3E,KAAA,EAAO2E;MACT,CAAC;MACD,KAAKC,OAAA,CAAQ,WAAW,KAAKnd,OAAA,CAAQ8c,MAAA,EAAQ;QAC3CxE,MAAA,EAAQ4E,sBAAA;QACR3E,KAAA,EAAO2E;MACT,CAAC;IACH;IAEA,KAAK9P,MAAA,CAAO7F,IAAA,CACV,4CACA/F,QAAA,CAASW,MAAA,EACTX,QAAA,CAASY,UACX;IAEAya,MAAA,CAAO,KAAK7c,OAAA,EAAS,UAAUwB,QAAA,CAASW,MAAM;IAC9C0a,MAAA,CAAO,KAAK7c,OAAA,EAAS,cAAcwB,QAAA,CAASY,UAAU;IACtDya,MAAA,CAAO,KAAK7c,OAAA,EAAS,eAAe,KAAK9C,GAAA,CAAIjB,IAAI;IAEjD,KAAK+D,OAAA,CAAQod,iBAAA,GAAoB,IAAIxD,KAAA,CAAM,KAAK5Z,OAAA,CAAQod,iBAAA,EAAmB;MACzEjS,KAAA,EAAOA,CAAC3R,CAAA,EAAG6jB,EAAA,EAAI/e,IAAA,KAAyB;QACtC,KAAK8O,MAAA,CAAO7F,IAAA,CAAK,qBAAqBjJ,IAAA,CAAK,CAAC,CAAC;QAE7C,IAAI,KAAK0B,OAAA,CAAQkN,UAAA,GAAa,KAAKlN,OAAA,CAAQsd,gBAAA,EAAkB;UAC3D,KAAKlQ,MAAA,CAAO7F,IAAA,CAAK,0CAA0C;UAG3D,OAAO;QACT;QAEA,MAAMgW,WAAA,GAAc/b,QAAA,CAASa,OAAA,CAAQiC,GAAA,CAAIhG,IAAA,CAAK,CAAC,CAAC;QAChD,KAAK8O,MAAA,CAAO7F,IAAA,CACV,oCACAjJ,IAAA,CAAK,CAAC,GACNif,WACF;QAEA,OAAOA,WAAA;MACT;IACF,CAAC;IAED,KAAKvd,OAAA,CAAQob,qBAAA,GAAwB,IAAIxB,KAAA,CACvC,KAAK5Z,OAAA,CAAQob,qBAAA,EACb;MACEjQ,KAAA,EAAOA,CAAA,KAAM;QACX,KAAKiC,MAAA,CAAO7F,IAAA,CAAK,uBAAuB;QAExC,IAAI,KAAKvH,OAAA,CAAQkN,UAAA,GAAa,KAAKlN,OAAA,CAAQsd,gBAAA,EAAkB;UAC3D,KAAKlQ,MAAA,CAAO7F,IAAA,CAAK,kDAAkD;UAGnE,OAAO;QACT;QAEA,MAAMiW,WAAA,GAAcvS,KAAA,CAAMa,IAAA,CAAKtK,QAAA,CAASa,OAAA,CAAQob,OAAA,CAAQ,CAAC;QACzD,MAAMC,UAAA,GAAaF,WAAA,CAChBpU,GAAA,CAAI,CAAC,CAACsL,UAAA,EAAY6I,WAAW,MAAM;UAClC,OAAO,GAAG7I,UAAA,KAAe6I,WAAA;QAC3B,CAAC,EACA5jB,IAAA,CAAK,MAAM;QAEd,KAAKyT,MAAA,CAAO7F,IAAA,CAAK,oCAAoCmW,UAAU;QAE/D,OAAOA,UAAA;MACT;IACF,CACF;IAGAzZ,MAAA,CAAOmN,gBAAA,CAAiB,KAAKpR,OAAA,EAAS;MACpCwB,QAAA,EAAU;QACR0D,UAAA,EAAY;QACZC,YAAA,EAAc;QACdb,GAAA,EAAKA,CAAA,KAAM,KAAK9C;MAClB;MACAmc,YAAA,EAAc;QACZzY,UAAA,EAAY;QACZC,YAAA,EAAc;QACdb,GAAA,EAAKA,CAAA,KAAM,KAAKqZ;MAClB;MACAC,WAAA,EAAa;QACX1Y,UAAA,EAAY;QACZC,YAAA,EAAc;QACdb,GAAA,EAAKA,CAAA,KAAM,KAAKsZ;MAClB;IACF,CAAC;IAED,MAAMC,uBAAA,GAA0B,MAAMrC,iBAAA,CAAkBha,QAAA,CAASR,KAAA,CAAM,CAAC;IAExE,KAAKoM,MAAA,CAAO7F,IAAA,CAAK,mCAAmCsW,uBAAuB;IAE3E,KAAKV,OAAA,CAAQ,aAAa,KAAKnd,OAAA,EAAS;MACtCsY,MAAA,EAAQ;MACRC,KAAA,EAAOsF;IACT,CAAC;IAED,KAAKC,aAAA,CAAc,KAAK9d,OAAA,CAAQsd,gBAAgB;IAChD,KAAKQ,aAAA,CAAc,KAAK9d,OAAA,CAAQ+d,OAAO;IAEvC,MAAMC,gBAAA,GAAmBA,CAAA,KAAM;MAC7B,KAAK5Q,MAAA,CAAO7F,IAAA,CAAK,mCAAmC;MAEpD,KAAKuW,aAAA,CAAc,KAAK9d,OAAA,CAAQie,IAAI;MAEpC,KAAKd,OAAA,CAAQ,QAAQ,KAAKnd,OAAA,EAAS;QACjCsY,MAAA,EAAQ,KAAK2D,cAAA,CAAezY,UAAA;QAC5B+U,KAAA,EAAOsF;MACT,CAAC;MAED,KAAKV,OAAA,CAAQ,WAAW,KAAKnd,OAAA,EAAS;QACpCsY,MAAA,EAAQ,KAAK2D,cAAA,CAAezY,UAAA;QAC5B+U,KAAA,EAAOsF;MACT,CAAC;IACH;IAEA,IAAIrc,QAAA,CAAStB,IAAA,EAAM;MACjB,KAAKkN,MAAA,CAAO7F,IAAA,CAAK,wCAAwC;MAEzD,MAAM2W,MAAA,GAAS1c,QAAA,CAAStB,IAAA,CAAKie,SAAA,CAAU;MAEvC,MAAMC,yBAAA,GAA4B,MAAAA,CAAA,KAAY;QAC5C,MAAM;UAAE3kB,KAAA;UAAO4kB;QAAK,IAAI,MAAMH,MAAA,CAAOI,IAAA,CAAK;QAE1C,IAAID,IAAA,EAAM;UACR,KAAKjR,MAAA,CAAO7F,IAAA,CAAK,4BAA4B;UAC7CyW,gBAAA,CAAiB;UACjB;QACF;QAEA,IAAIvkB,KAAA,EAAO;UACT,KAAK2T,MAAA,CAAO7F,IAAA,CAAK,6BAA6B9N,KAAK;UACnD,KAAKwiB,cAAA,GAAiB3E,iBAAA,CAAkB,KAAK2E,cAAA,EAAgBxiB,KAAK;UAElE,KAAK0jB,OAAA,CAAQ,YAAY,KAAKnd,OAAA,EAAS;YACrCsY,MAAA,EAAQ,KAAK2D,cAAA,CAAezY,UAAA;YAC5B+U,KAAA,EAAOsF;UACT,CAAC;QACH;QAEAO,yBAAA,CAA0B;MAC5B;MAEAA,yBAAA,CAA0B;IAC5B,OAAO;MACLJ,gBAAA,CAAiB;IACnB;EACF;EAEQO,qBAAA,EAA+B;IACrC,OAAOxb,YAAA,CAAa,KAAKkZ,cAAc;EACzC;EAEA,IAAIza,SAAA,EAAoB;IACtB,KAAK4L,MAAA,CAAO7F,IAAA,CACV,kCACA,KAAKvH,OAAA,CAAQwe,YACf;IAEA,IAAI,KAAKxe,OAAA,CAAQkN,UAAA,KAAe,KAAKlN,OAAA,CAAQie,IAAA,EAAM;MACjD,OAAO;IACT;IAEA,QAAQ,KAAKje,OAAA,CAAQwe,YAAA;MACnB,KAAK;QAAQ;UACX,MAAMnQ,YAAA,GAAeyM,SAAA,CAAU,KAAKyD,oBAAA,CAAqB,CAAC;UAC1D,KAAKnR,MAAA,CAAO7F,IAAA,CAAK,0BAA0B8G,YAAY;UAEvD,OAAOA,YAAA;QACT;MAEA,KAAK;QAAe;UAClB,MAAMnM,WAAA,GAAcmB,aAAA,CAAc,KAAK4Y,cAAc;UACrD,KAAK7O,MAAA,CAAO7F,IAAA,CAAK,iCAAiCrF,WAAW;UAE7D,OAAOA,WAAA;QACT;MAEA,KAAK;QAAQ;UACX,MAAMuc,QAAA,GACJ,KAAKze,OAAA,CAAQod,iBAAA,CAAkB,cAAc,KAAK;UACpD,MAAMsB,YAAA,GAAe,IAAIC,IAAA,CAAK,CAAC,KAAKJ,oBAAA,CAAqB,CAAC,GAAG;YAC3DpjB,IAAA,EAAMsjB;UACR,CAAC;UAED,KAAKrR,MAAA,CAAO7F,IAAA,CACV,0CACAmX,YAAA,EACAD,QACF;UAEA,OAAOC,YAAA;QACT;MAEA;QAAS;UACP,MAAMf,YAAA,GAAe,KAAKY,oBAAA,CAAqB;UAC/C,KAAKnR,MAAA,CAAO7F,IAAA,CACV,wCACA,KAAKvH,OAAA,CAAQwe,YAAA,EACbb,YACF;UAEA,OAAOA,YAAA;QACT;IACF;EACF;EAEA,IAAIA,aAAA,EAAuB;IAMzBpjB,SAAA,CACE,KAAKyF,OAAA,CAAQwe,YAAA,KAAiB,MAAM,KAAKxe,OAAA,CAAQwe,YAAA,KAAiB,QAClE,oDACF;IAEA,IACE,KAAKxe,OAAA,CAAQkN,UAAA,KAAe,KAAKlN,OAAA,CAAQ+d,OAAA,IACzC,KAAK/d,OAAA,CAAQkN,UAAA,KAAe,KAAKlN,OAAA,CAAQie,IAAA,EACzC;MACA,OAAO;IACT;IAEA,MAAMN,YAAA,GAAe,KAAKY,oBAAA,CAAqB;IAC/C,KAAKnR,MAAA,CAAO7F,IAAA,CAAK,yBAAyBoW,YAAY;IAEtD,OAAOA,YAAA;EACT;EAEA,IAAIC,YAAA,EAA+B;IACjCrjB,SAAA,CACE,KAAKyF,OAAA,CAAQwe,YAAA,KAAiB,MAC5B,KAAKxe,OAAA,CAAQwe,YAAA,KAAiB,YAChC,oDACF;IAEA,IAAI,KAAKxe,OAAA,CAAQkN,UAAA,KAAe,KAAKlN,OAAA,CAAQie,IAAA,EAAM;MACjD,OAAO;IACT;IAEA,MAAMW,WAAA,GAAc,KAAK5e,OAAA,CAAQod,iBAAA,CAAkB,cAAc,KAAK;IAEtE,IAAI,OAAOyB,SAAA,KAAc,aAAa;MACpCrgB,OAAA,CAAQgB,IAAA,CACN,wLACF;MACA,OAAO;IACT;IAEA,IAAIkb,wBAAA,CAAyBkE,WAAW,GAAG;MACzC,OAAO,IAAIC,SAAA,CAAU,EAAEC,eAAA,CACrB,KAAKP,oBAAA,CAAqB,GAC1BK,WACF;IACF;IAEA,OAAO;EACT;EAEOjN,UAAU7X,MAAA,EAAqB;IAKpC,KAAK4hB,iBAAiB,IAAI;IAC1B,KAAKtO,MAAA,CAAO7F,IAAA,CAAK,0BAA0B;IAE3C,KAAKuW,aAAA,CAAc,KAAK9d,OAAA,CAAQie,IAAI;IACpC,KAAKd,OAAA,CAAQ,SAAS,KAAKnd,OAAO;IAClC,KAAKmd,OAAA,CAAQ,WAAW,KAAKnd,OAAO;EACtC;;;;EAKQ8d,cAAciB,cAAA,EAA8B;IAClD,KAAK3R,MAAA,CAAO7F,IAAA,CACV,2BACA,KAAKvH,OAAA,CAAQkN,UAAA,EACb6R,cACF;IAEA,IAAI,KAAK/e,OAAA,CAAQkN,UAAA,KAAe6R,cAAA,EAAgB;MAC9C,KAAK3R,MAAA,CAAO7F,IAAA,CAAK,+CAA+C;MAChE;IACF;IAEAsV,MAAA,CAAO,KAAK7c,OAAA,EAAS,cAAc+e,cAAc;IAEjD,KAAK3R,MAAA,CAAO7F,IAAA,CAAK,yBAAyBwX,cAAc;IAExD,IAAIA,cAAA,KAAmB,KAAK/e,OAAA,CAAQgf,MAAA,EAAQ;MAC1C,KAAK5R,MAAA,CAAO7F,IAAA,CAAK,yCAAyC;MAE1D,KAAK4V,OAAA,CAAQ,oBAAoB,KAAKnd,OAAO;IAC/C;EACF;;;;EAKQmd,QAKN5S,SAAA,EACAnE,MAAA,EACAjJ,OAAA,EACM;IACN,MAAMoL,QAAA,GAAYnC,MAAA,CAA0B,KAAKmE,SAAA,EAAW;IAC5D,MAAM3K,KAAA,GAAQuZ,WAAA,CAAY/S,MAAA,EAAQmE,SAAA,EAAWpN,OAAO;IAEpD,KAAKiQ,MAAA,CAAO7F,IAAA,CAAK,gBAAgBgD,SAAA,EAAWpN,OAAA,IAAW,EAAE;IAGzD,IAAI,OAAOoL,QAAA,KAAa,YAAY;MAClC,KAAK6E,MAAA,CAAO7F,IAAA,CAAK,4CAA4CgD,SAAS;MACtEhC,QAAA,CAASyJ,IAAA,CAAK5L,MAAA,EAA0BxG,KAAK;IAC/C;IAGA,MAAMP,MAAA,GACJ+G,MAAA,YAAkB6Y,oBAAA,GAAuB,KAAKlD,YAAA,GAAe,KAAK1c,MAAA;IAEpE,WAAW,CAAC6f,mBAAA,EAAqB7T,SAAS,KAAKhM,MAAA,EAAQ;MACrD,IAAI6f,mBAAA,KAAwB3U,SAAA,EAAW;QACrC,KAAK6C,MAAA,CAAO7F,IAAA,CACV,mDACA8D,SAAA,CAAUnS,MAAA,EACVqR,SACF;QAEAc,SAAA,CAAUW,OAAA,CAASlB,QAAA,IAAaA,QAAA,CAASkH,IAAA,CAAK5L,MAAA,EAAQxG,KAAK,CAAC;MAC9D;IACF;EACF;;;;EAKQ6c,kBACNvc,IAAA,EACS;IACT,KAAKkN,MAAA,CAAO7F,IAAA,CAAK,8CAA8C;IAI/D,MAAM4X,YAAA,GACJjf,IAAA,YAAgBkf,QAAA,GAAWlf,IAAA,CAAKmf,eAAA,CAAgBC,SAAA,GAAYpf,IAAA;IAE9D,MAAMqc,YAAA,GAAe,IAAIlc,OAAA,CAAQ,KAAKnD,GAAA,CAAIjB,IAAA,EAAM;MAC9CgE,MAAA,EAAQ,KAAKA,MAAA;MACboC,OAAA,EAAS,KAAK2Z,cAAA;;;;MAIduD,WAAA,EAAa,KAAKvf,OAAA,CAAQwf,eAAA,GAAkB,YAAY;MACxDtf,IAAA,EAAM,CAAC,OAAO,MAAM,EAAElC,QAAA,CAAS,KAAKiC,MAAA,CAAOwf,WAAA,CAAY,CAAC,IACpD,OACAN;IACN,CAAC;IAED,MAAMO,YAAA,GAAehG,WAAA,CAAY6C,YAAA,CAAala,OAAA,EAAS;MACrD0X,UAAA,EAAYA,CAAC,CAACoC,UAAA,EAAY7d,IAAI,GAAG4d,MAAA,KAAW;QAI1C,QAAQC,UAAA;UACN,KAAK;UACL,KAAK;YAAO;cACV,MAAM,CAACzH,UAAA,EAAY6I,WAAW,IAAIjf,IAAA;cAClC,KAAK0B,OAAA,CAAQ4c,gBAAA,CAAiBlI,UAAA,EAAY6I,WAAW;cACrD;YACF;UAEA,KAAK;YAAU;cACb,MAAM,CAAC7I,UAAU,IAAIpW,IAAA;cACrBE,OAAA,CAAQgB,IAAA,CACN,oCAAoCkV,UAAA,sDAAgE6H,YAAA,CAAatc,MAAA,IAAUsc,YAAA,CAAarf,GAAA,sDAC1I;cACA;YACF;QACF;QAEA,OAAOgf,MAAA,CAAO;MAChB;IACF,CAAC;IACDW,MAAA,CAAON,YAAA,EAAc,WAAWmD,YAAY;IAC5C1Z,aAAA,CAAcuW,YAAA,EAAc,KAAKvc,OAAO;IAExC,KAAKoN,MAAA,CAAO7F,IAAA,CAAK,6CAA6CgV,YAAY;IAE1E,OAAOA,YAAA;EACT;AACF;AAEA,SAASH,cAAclf,GAAA,EAAwB;EAQ7C,IAAI,OAAOlB,QAAA,KAAa,aAAa;IACnC,OAAO,IAAID,GAAA,CAAImB,GAAG;EACpB;EAEA,OAAO,IAAInB,GAAA,CAAImB,GAAA,CAAI+M,QAAA,CAAS,GAAGjO,QAAA,CAASC,IAAI;AAC9C;AAEA,SAAS4gB,OACPzW,MAAA,EACAuZ,QAAA,EACAlmB,KAAA,EACM;EACNmB,OAAA,CAAQqK,cAAA,CAAemB,MAAA,EAAQuZ,QAAA,EAAU;;IAEvCva,QAAA,EAAU;IACVF,UAAA,EAAY;IACZzL;EACF,CAAC;AACH;AW/sBO,SAASmmB,0BAA0B;EACxCve,OAAA;EACA+L;AACF,GAA+B;EAC7B,MAAMyS,mBAAA,GAAsB,IAAIjG,KAAA,CAAM5P,UAAA,CAAW8V,cAAA,EAAgB;IAC/DjlB,UAAUuL,MAAA,EAAQ9H,IAAA,EAAM4b,SAAA,EAAW;MACjC9M,MAAA,CAAO7F,IAAA,CAAK,gCAAgC;MAE5C,MAAMwY,eAAA,GAAkBnlB,OAAA,CAAQC,SAAA,CAC9BuL,MAAA,EACA9H,IAAA,EACA4b,SACF;MASA,MAAM8F,oBAAA,GAAuB/b,MAAA,CAAOgc,yBAAA,CAClC7Z,MAAA,CAAO8E,SACT;MACA,WAAWsI,YAAA,IAAgBwM,oBAAA,EAAsB;QAC/CplB,OAAA,CAAQqK,cAAA,CACN8a,eAAA,EACAvM,YAAA,EACAwM,oBAAA,CAAqBxM,YAAY,CACnC;MACF;MAEA,MAAM0M,oBAAA,GAAuB,IAAIrE,wBAAA,CAC/BkE,eAAA,EACA3S,MACF;MAEA8S,oBAAA,CAAqBvD,SAAA,GAAY,gBAAgB;QAAE3c,OAAA;QAASc;MAAU,GAAG;QACvE,MAAMrD,UAAA,GAAa,IAAIgU,iBAAA,CAAkBzR,OAAO;QAEhD,KAAKoN,MAAA,CAAO7F,IAAA,CAAK,6BAA6B;QAE9C,KAAK6F,MAAA,CAAO7F,IAAA,CACV,sDACAlG,OAAA,CAAQiJ,aAAA,CAAc,SAAS,CACjC;QAEA,MAAMyM,gBAAA,GAAmB,MAAMtE,cAAA,CAAc;UAC3CzS,OAAA;UACAc,SAAA;UACArD,UAAA;UACA4D,OAAA;UACAwR,UAAA,EAAY,MAAOrR,QAAA,IAAa;YAC9B,MAAM,KAAKkQ,WAAA,CAAYlQ,QAAQ;UACjC;UACAoR,cAAA,EAAgBA,CAAA,KAAM;YACpB,KAAKjB,SAAA,CAAU,IAAImC,SAAA,CAAU,eAAe,CAAC;UAC/C;UACAnB,OAAA,EAAU7Y,MAAA,IAAU;YAClB,KAAKsT,MAAA,CAAO7F,IAAA,CAAK,oBAAoB;cAAE3L,KAAA,EAAA9B;YAAM,CAAC;YAE9C,IAAIA,MAAA,YAAiBM,KAAA,EAAO;cAC1B,KAAKuX,SAAA,CAAU7X,MAAK;YACtB;UACF;QACF,CAAC;QAED,IAAI,CAACid,gBAAA,EAAkB;UACrB,KAAK3J,MAAA,CAAO7F,IAAA,CACV,0DACF;QACF;MACF;MAEA2Y,oBAAA,CAAqBrN,UAAA,GAAa,gBAAgB;QAChDrR,QAAA;QACA8M,gBAAA;QACAtO,OAAA;QACAc;MACF,GAAG;QACD,KAAKsM,MAAA,CAAO7F,IAAA,CACV,uDACAlG,OAAA,CAAQiJ,aAAA,CAAc,UAAU,CAClC;QAEAjJ,OAAA,CAAQ0J,IAAA,CAAK,YAAY;UACvBvJ,QAAA;UACA8M,gBAAA;UACAtO,OAAA;UACAc;QACF,CAAC;MACH;MAKA,OAAOof,oBAAA,CAAqBlgB,OAAA;IAC9B;EACF,CAAC;EAED,OAAO6f,mBAAA;AACT;AZ5GO,IAAMM,0BAAA,GAAN,cAAwClT,WAAA,CAAiC;EAG9E5S,YAAA,EAAc;IACZ,MAAM8lB,0BAAA,CAA0BC,iBAAiB;EACnD;EAEU/S,iBAAA,EAAmB;IAC3B,OAAOkG,qBAAA,CAAsB,gBAAgB;EAC/C;EAEU9F,MAAA,EAAQ;IAChB,MAAML,MAAA,GAAS,KAAKA,MAAA,CAAOzF,MAAA,CAAO,OAAO;IAEzCyF,MAAA,CAAO7F,IAAA,CAAK,qCAAqC;IAEjD,MAAM8Y,kBAAA,GAAqBrW,UAAA,CAAW8V,cAAA;IAEtCvlB,SAAA,CACE,CAAE8lB,kBAAA,CAA2B5c,iBAAiB,GAC9C,+DACF;IAEAuG,UAAA,CAAW8V,cAAA,GAAiBF,yBAAA,CAA0B;MACpDve,OAAA,EAAS,KAAKA,OAAA;MACd+L,MAAA,EAAQ,KAAKA;IACf,CAAC;IAEDA,MAAA,CAAO7F,IAAA,CACL,2CACAyC,UAAA,CAAW8V,cAAA,CAAexlB,IAC5B;IAEA2J,MAAA,CAAOgB,cAAA,CAAe+E,UAAA,CAAW8V,cAAA,EAAgBrc,iBAAA,EAAmB;MAClEyB,UAAA,EAAY;MACZC,YAAA,EAAc;MACd1L,KAAA,EAAO;IACT,CAAC;IAED,KAAK0T,aAAA,CAAcnI,IAAA,CAAK,MAAM;MAC5Bf,MAAA,CAAOgB,cAAA,CAAe+E,UAAA,CAAW8V,cAAA,EAAgBrc,iBAAA,EAAmB;QAClEhK,KAAA,EAAO;MACT,CAAC;MAEDuQ,UAAA,CAAW8V,cAAA,GAAiBO,kBAAA;MAC5BjT,MAAA,CAAO7F,IAAA,CACL,4CACAyC,UAAA,CAAW8V,cAAA,CAAexlB,IAC5B;IACF,CAAC;EACH;AACF;AAnDO,IAAMgmB,yBAAA,GAANH,0BAAA;AAAMG,yBAAA,CACJF,iBAAA,GAAoB1c,MAAA,CAAO,KAAK;;;AaDzC,SAASnD,aAAA,IAAAggB,cAAA,QAAqB;AAC9B,SAAS7f,aAAA,IAAA8f,cAAA,QAAqB;AAEvB,SAASC,8BACdxhB,OAAA,EACA9B,OAAA,EACkC;EAClC,MAAMgR,WAAA,GAAc,IAAIF,gBAAA,CAAiB;IACvC3T,IAAA,EAAM;IACN4T,YAAA,EAAc,CAAC,IAAImJ,gBAAA,CAAiB,GAAG,IAAIiJ,yBAAA,CAA0B,CAAC;EACxE,CAAC;EAEDnS,WAAA,CAAY/B,EAAA,CAAG,WAAW,OAAO;IAAEpM,OAAA;IAASc,SAAA;IAAWrD;EAAW,MAAM;IACtE,MAAMsD,mBAAA,GAAsBf,OAAA,CAAQgB,KAAA,CAAM;IAE1C,MAAMQ,QAAA,GAAW,MAAM+e,cAAA,CACrBvgB,OAAA,EACAc,SAAA,EACA7B,OAAA,CAAQmC,kBAAA,CAAmB,EAAEzE,MAAA,CAAO6jB,cAAA,CAAc,gBAAgB,CAAC,GACnErjB,OAAA,EACA8B,OAAA,CAAQoC,OAAA,EACR;MACEE,iBAAiB/H,CAAA,EAAG;QAAEiI,OAAA;QAASC;MAAa,GAAG;QAC7C,IAAI,CAACvE,OAAA,CAAQoB,KAAA,EAAO;UAClBU,OAAA,CAAQoC,OAAA,CAAQ/B,IAAA,CAAK,mBAAmB,CAAC;YAAEkC,QAAA,EAAA2V;UAAS,MAAM;YACxD1V,OAAA,CAAQ/C,GAAA,CAAI;cACVsB,OAAA,EAASe,mBAAA;cACTS,QAAA,EAAA2V,SAAA;cACAzV;YACF,CAAC;UACH,CAAC;QACH;MACF;IACF,CACF;IAEA,IAAIF,QAAA,EAAU;MACZ/D,UAAA,CAAWiU,WAAA,CAAYlQ,QAAQ;IACjC;EACF,CAAC;EAED2M,WAAA,CAAY/B,EAAA,CACV,YACA,CAAC;IAAE5K,QAAA;IAAU8M,gBAAA;IAAkBtO,OAAA;IAASc;EAAU,MAAM;IACtD7B,OAAA,CAAQoC,OAAA,CAAQ0J,IAAA,CACduD,gBAAA,GAAmB,oBAAoB,mBACvC;MACE9M,QAAA;MACAxB,OAAA;MACAc;IACF,CACF;EACF,CACF;EAEAqN,WAAA,CAAYhD,KAAA,CAAM;EAElB,OAAOgD,WAAA;AACT;;;AC/DO,SAASuS,oBACdzhB,OAAA,EACc;EACd,OAAO,eAAeyP,MAAMvR,OAAA,EAAS;IACnC8B,OAAA,CAAQ0hB,mBAAA,GAAsBF,6BAAA,CAC5BxhB,OAAA,EACA9B,OACF;IAEAkB,iBAAA,CAAkB;MAChBrF,OAAA,EAAS;MACTuF,KAAA,EAAOpB,OAAA,CAAQoB;IACjB,CAAC;IAED,OAAO;EACT;AACF;;;ACjBO,SAASqiB,mBACd3hB,OAAA,EACa;EACb,OAAO,SAAS4Q,KAAA,EAAO;IACrB5Q,OAAA,CAAQ0hB,mBAAA,EAAqBhT,OAAA,CAAQ;IACrCgC,gBAAA,CAAiB;MAAEpR,KAAA,EAAOU,OAAA,CAAQkQ,YAAA,EAAc5Q;IAAM,CAAC;EACzD;AACF;;;ACOA,SAASjD,QAAA,IAAAulB,UAAA,QAAgB;AACzB,SAASC,QAAA,QAAgB;AACzB,SAAShR,UAAA,IAAAiR,WAAA,QAAkB;;;ACdpB,SAASC,+BAAA,EAAiC;EAC/C,IAAI;IACF,MAAMC,MAAA,GAAS,IAAIC,cAAA,CAAe;MAChCxS,KAAA,EAAQjR,UAAA,IAAeA,UAAA,CAAW0jB,KAAA,CAAM;IAC1C,CAAC;IACD,MAAMnoB,OAAA,GAAU,IAAIooB,cAAA,CAAe;IACnCpoB,OAAA,CAAQqoB,KAAA,CAAM1hB,WAAA,CAAYshB,MAAA,EAAQ,CAACA,MAAM,CAAC;IAC1C,OAAO;EACT,QAAQ;IACN,OAAO;EACT;AACF;;;ADQA,SAASK,oBAAA,QAA4B;AACrC,SAASC,oBAAA,QAA4B;AACrC,SAASC,qBAAA,QAA6B;AAQ/B,IAAMC,cAAA,GAAN,cACGX,QAAA,CAEV;EACU7hB,OAAA;EACAyiB,YAAA,GAA6B;EAC7BC,WAAA,GAA2B;EAC3BtW,SAAA;EAERhR,YAAA,GAAeunB,QAAA,EAAoD;IACjE,MAAM,GAAGA,QAAQ;IAEjBrnB,SAAA,CACE,CAACQ,aAAA,CAAc,GACf8lB,UAAA,CAASlmB,aAAA,CACP,6HACF,CACF;IAEA,KAAK0Q,SAAA,GAAY,EAAC;IAClB,KAAKpM,OAAA,GAAU,KAAK4iB,mBAAA,CAAoB;EAC1C;EAEQA,oBAAA,EAAkD;IACxD,MAAM5iB,OAAA,GAAsC;MAAA;MAAA;MAG1CM,gBAAA,EAAkB;MAClB4P,YAAA,EAAc;MACdtS,MAAA,EAAQ;MACRuE,kBAAA,EAAoBA,CAAA,KAAM;QACxB,OAAO,KAAK0gB,kBAAA,CAAmBC,eAAA,CAAgB;MACjD;MACA5lB,YAAA,EAAc;MACdkF,OAAA,EAAS,KAAKA,OAAA;MACdnC,aAAA,EAAe;QACbkN,EAAA,EAAIA,CAAC4V,SAAA,EAAWzZ,QAAA,KAAa;UAC3B,KAAKtJ,OAAA,CAAQI,MAAA,CAAO4M,WAAA,CAElBjR,SAAA,CAAUqC,aAAA,EAAe,WAAYuC,KAAA,IAAU;YAE/C,IAAIA,KAAA,CAAMmE,MAAA,KAAW,KAAK9E,OAAA,CAAQpC,MAAA,EAAQ;cACxC;YACF;YAEA,MAAM7D,OAAA,GAAU4G,KAAA,CAAMlE,IAAA;YAEtB,IAAI,CAAC1C,OAAA,EAAS;cACZ;YACF;YAEA,IAAIA,OAAA,CAAQmC,IAAA,KAAS6mB,SAAA,EAAW;cAC9BzZ,QAAA,CAAS3I,KAAA,EAAO5G,OAAO;YACzB;UACF,CAAC;QACH;QACAmG,IAAA,EAAOhE,IAAA,IAAS;UACd,KAAK8D,OAAA,CAAQpC,MAAA,EAAQ8C,WAAA,CAAYxE,IAAI;QACvC;MACF;MACAkE,MAAA,EAAQ;QACN4M,WAAA,EAAaA,CAAC7F,MAAA,EAAQ4b,SAAA,EAAWzZ,QAAA,KAAa;UAC5CnC,MAAA,CAAOoJ,gBAAA,CAAiBwS,SAAA,EAAWzZ,QAAyB;UAC5D,KAAK8C,SAAA,CAAUrG,IAAA,CAAK;YAClBgd,SAAA;YACA5b,MAAA;YACAmC;UACF,CAAC;UAED,OAAO,MAAM;YACXnC,MAAA,CAAO6b,mBAAA,CAAoBD,SAAA,EAAWzZ,QAAyB;UACjE;QACF;QACAiE,kBAAA,EAAoBA,CAAA,KAAM;UACxB,WAAW;YAAEpG,MAAA;YAAQ4b,SAAA;YAAWzZ;UAAS,KAAK,KAAK8C,SAAA,EAAW;YAC5DjF,MAAA,CAAO6b,mBAAA,CAAoBD,SAAA,EAAWzZ,QAAQ;UAChD;UACA,KAAK8C,SAAA,GAAY,EAAC;QACpB;QACA/L,IAAA,EAAO0iB,SAAA,IAAc;UACnB,MAAME,QAAA,GAA8B,EAAC;UAErC,OAAO,IAAI5S,OAAA,CAKT,CAACC,OAAA,EAASc,MAAA,KAAW;YACrB,MAAM8R,qBAAA,GAAyBviB,KAAA,IAAwB;cACrD,IAAI;gBACF,MAAM5G,OAAA,GAAU4G,KAAA,CAAMlE,IAAA;gBAEtB,IAAI1C,OAAA,CAAQmC,IAAA,KAAS6mB,SAAA,EAAW;kBAC9BzS,OAAA,CAAQvW,OAAO;gBACjB;cACF,SAASc,MAAA,EAAO;gBACduW,MAAA,CAAOvW,MAAK;cACd;YACF;YAEAooB,QAAA,CAASld,IAAA,CACP,KAAK/F,OAAA,CAAQI,MAAA,CAAO4M,WAAA,CAClBjR,SAAA,CAAUqC,aAAA,EACV,WACA8kB,qBACF,GACA,KAAKljB,OAAA,CAAQI,MAAA,CAAO4M,WAAA,CAClBjR,SAAA,CAAUqC,aAAA,EACV,gBACAgT,MACF,CACF;UACF,CAAC,EAAEY,OAAA,CAAQ,MAAM;YACfiR,QAAA,CAASlW,OAAA,CAASoW,MAAA,IAAWA,MAAA,CAAO,CAAC;UACvC,CAAC;QACH;MACF;MACAtgB,QAAA,EAAU;QACRugB,gBAAA,EACE,EAAE,mBAAmBrnB,SAAA,KAAcgB,QAAA,CAASoY,QAAA,KAAa;QAC3DrS,sBAAA,EAAwBif,8BAAA,CAA+B;MACzD;IACF;IAEA,KAAKU,YAAA,GAAeziB,OAAA,CAAQ6C,QAAA,CAASugB,gBAAA,GACjC3B,mBAAA,CAAoBzhB,OAAO,IAC3BwP,kBAAA,CAAmBxP,OAAO;IAE9B,KAAK0iB,WAAA,GAAc1iB,OAAA,CAAQ6C,QAAA,CAASugB,gBAAA,GAChCzB,kBAAA,CAAmB3hB,OAAO,IAC1B2Q,UAAA,CAAW3Q,OAAO;IAEtB,OAAOA,OAAA;EACT;EAEA,MAAayP,MAAMvR,OAAA,GAAwB,CAAC,GAAoB;IAC9D,IAAIA,OAAA,CAAQ6S,cAAA,KAAmB,MAAM;MACnC6Q,UAAA,CAASrhB,IAAA,CACP,kUACF;IACF;IAEA,KAAKP,OAAA,CAAQkQ,YAAA,GAAe4R,WAAA,CAC1BhR,qBAAA,EACA5S,OACF;IAGAokB,oBAAA,CAAqB;MACnBe,2BAAA,EAA6BA,CAAA,KAAM;QACjC,OAAO,KAAKrjB,OAAA,CAAQkQ,YAAA,CAAac,kBAAA;MACnC;MACAsS,WAAA,EAAaA,CAAA,KAAM;QACjB,OAAO,KAAKT,kBAAA,CAAmBC,eAAA,CAAgB;MACjD;MACAS,kBAAA,EAAqBC,UAAA,IAAe;QAClC,IAAI,CAAC,KAAKxjB,OAAA,CAAQkQ,YAAA,CAAa5Q,KAAA,EAAO;UAGpCijB,qBAAA,CAAsBiB,UAAU;QAClC;MACF;MACAC,wBAAA,EAA0B,CAAC;IAC7B,CAAC;IACDpB,oBAAA,CAAqBnW,KAAA,CAAM;IAE3B,KAAKgC,aAAA,CAAcnI,IAAA,CAAK,MAAM;MAC5Bsc,oBAAA,CAAqB3T,OAAA,CAAQ;IAC/B,CAAC;IAED,OAAO,MAAM,KAAK+T,YAAA,CAAa,KAAKziB,OAAA,CAAQkQ,YAAA,EAAchS,OAAO;EACnE;EAEO0S,KAAA,EAAa;IAClB,MAAMlC,OAAA,CAAQ;IACd,KAAK1O,OAAA,CAAQI,MAAA,CAAOmN,kBAAA,CAAmB;IACvC,KAAKvN,OAAA,CAAQoC,OAAA,CAAQmL,kBAAA,CAAmB;IACxC,KAAKmV,WAAA,CAAY;EACnB;AACF;AAQO,SAASgB,YAAA,GACXf,QAAA,EACU;EACb,OAAO,IAAIH,cAAA,CAAe,GAAGG,QAAQ;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}