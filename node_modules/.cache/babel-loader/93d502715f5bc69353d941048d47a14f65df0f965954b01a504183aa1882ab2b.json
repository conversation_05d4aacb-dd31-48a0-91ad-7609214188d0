{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique operation names\n *\n * A GraphQL document is only valid if all defined operations have unique names.\n *\n * See https://spec.graphql.org/draft/#sec-Operation-Name-Uniqueness\n */\nexport function UniqueOperationNamesRule(context) {\n  const knownOperationNames = Object.create(null);\n  return {\n    OperationDefinition(node) {\n      const operationName = node.name;\n      if (operationName) {\n        if (knownOperationNames[operationName.value]) {\n          context.reportError(new GraphQLError(`There can be only one operation named \"${operationName.value}\".`, {\n            nodes: [knownOperationNames[operationName.value], operationName]\n          }));\n        } else {\n          knownOperationNames[operationName.value] = operationName;\n        }\n      }\n      return false;\n    },\n    FragmentDefinition: () => false\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "UniqueOperationNamesRule", "context", "knownOperationNames", "Object", "create", "OperationDefinition", "node", "operationName", "name", "value", "reportError", "nodes", "FragmentDefinition"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/UniqueOperationNamesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique operation names\n *\n * A GraphQL document is only valid if all defined operations have unique names.\n *\n * See https://spec.graphql.org/draft/#sec-Operation-Name-Uniqueness\n */\nexport function UniqueOperationNamesRule(context) {\n  const knownOperationNames = Object.create(null);\n  return {\n    OperationDefinition(node) {\n      const operationName = node.name;\n\n      if (operationName) {\n        if (knownOperationNames[operationName.value]) {\n          context.reportError(\n            new GraphQLError(\n              `There can be only one operation named \"${operationName.value}\".`,\n              {\n                nodes: [\n                  knownOperationNames[operationName.value],\n                  operationName,\n                ],\n              },\n            ),\n          );\n        } else {\n          knownOperationNames[operationName.value] = operationName;\n        }\n      }\n\n      return false;\n    },\n\n    FragmentDefinition: () => false,\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,OAAO,EAAE;EAChD,MAAMC,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC/C,OAAO;IACLC,mBAAmBA,CAACC,IAAI,EAAE;MACxB,MAAMC,aAAa,GAAGD,IAAI,CAACE,IAAI;MAE/B,IAAID,aAAa,EAAE;QACjB,IAAIL,mBAAmB,CAACK,aAAa,CAACE,KAAK,CAAC,EAAE;UAC5CR,OAAO,CAACS,WAAW,CACjB,IAAIX,YAAY,CACd,0CAA0CQ,aAAa,CAACE,KAAK,IAAI,EACjE;YACEE,KAAK,EAAE,CACLT,mBAAmB,CAACK,aAAa,CAACE,KAAK,CAAC,EACxCF,aAAa;UAEjB,CACF,CACF,CAAC;QACH,CAAC,MAAM;UACLL,mBAAmB,CAACK,aAAa,CAACE,KAAK,CAAC,GAAGF,aAAa;QAC1D;MACF;MAEA,OAAO,KAAK;IACd,CAAC;IAEDK,kBAAkB,EAAEA,CAAA,KAAM;EAC5B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}