{"ast": null, "code": "export { default } from './getValidReactChildren';", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/@mui/utils/esm/getValidReactChildren/index.js"], "sourcesContent": ["export { default } from './getValidReactChildren';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}