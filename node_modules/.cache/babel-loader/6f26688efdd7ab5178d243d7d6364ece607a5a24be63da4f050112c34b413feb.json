{"ast": null, "code": "const MAX_LENGTH = 24;\nfunction truncateMessage(message) {\n  if (message.length <= MAX_LENGTH) {\n    return message;\n  }\n  return `${message.slice(0, MAX_LENGTH)}\\u2026`;\n}\nexport { truncateMessage };", "map": {"version": 3, "names": ["MAX_LENGTH", "truncateMessage", "message", "length", "slice"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/ws/utils/truncateMessage.ts"], "sourcesContent": ["const MAX_LENGTH = 24\n\nexport function truncateMessage(message: string): string {\n  if (message.length <= MAX_LENGTH) {\n    return message\n  }\n\n  return `${message.slice(0, MAX_LENGTH)}…`\n}\n"], "mappings": "AAAA,MAAMA,UAAA,GAAa;AAEZ,SAASC,gBAAgBC,OAAA,EAAyB;EACvD,IAAIA,OAAA,CAAQC,MAAA,IAAUH,UAAA,EAAY;IAChC,OAAOE,OAAA;EACT;EAEA,OAAO,GAAGA,OAAA,CAAQE,KAAA,CAAM,GAAGJ,UAAU,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}