{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isInputObjectType, isNonNullType, isNullableType } from '../../type/definition.mjs';\nimport { isTypeSubTypeOf } from '../../utilities/typeComparators.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Variables in allowed position\n *\n * Variable usages must be compatible with the arguments they are passed to.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variable-Usages-are-Allowed\n */\nexport function VariablesInAllowedPositionRule(context) {\n  let varDefMap = Object.create(null);\n  return {\n    OperationDefinition: {\n      enter() {\n        varDefMap = Object.create(null);\n      },\n      leave(operation) {\n        const usages = context.getRecursiveVariableUsages(operation);\n        for (const {\n          node,\n          type,\n          defaultValue,\n          parentType\n        } of usages) {\n          const varName = node.name.value;\n          const varDef = varDefMap[varName];\n          if (varDef && type) {\n            // A var type is allowed if it is the same or more strict (e.g. is\n            // a subtype of) than the expected type. It can be more strict if\n            // the variable type is non-null when the expected type is nullable.\n            // If both are list types, the variable item type can be more strict\n            // than the expected item type (contravariant).\n            const schema = context.getSchema();\n            const varType = typeFromAST(schema, varDef.type);\n            if (varType && !allowedVariableUsage(schema, varType, varDef.defaultValue, type, defaultValue)) {\n              const varTypeStr = inspect(varType);\n              const typeStr = inspect(type);\n              context.reportError(new GraphQLError(`Variable \"$${varName}\" of type \"${varTypeStr}\" used in position expecting type \"${typeStr}\".`, {\n                nodes: [varDef, node]\n              }));\n            }\n            if (isInputObjectType(parentType) && parentType.isOneOf && isNullableType(varType)) {\n              context.reportError(new GraphQLError(`Variable \"$${varName}\" is of type \"${varType}\" but must be non-nullable to be used for OneOf Input Object \"${parentType}\".`, {\n                nodes: [varDef, node]\n              }));\n            }\n          }\n        }\n      }\n    },\n    VariableDefinition(node) {\n      varDefMap[node.variable.name.value] = node;\n    }\n  };\n}\n/**\n * Returns true if the variable is allowed in the location it was found,\n * which includes considering if default values exist for either the variable\n * or the location at which it is located.\n */\n\nfunction allowedVariableUsage(schema, varType, varDefaultValue, locationType, locationDefaultValue) {\n  if (isNonNullType(locationType) && !isNonNullType(varType)) {\n    const hasNonNullVariableDefaultValue = varDefaultValue != null && varDefaultValue.kind !== Kind.NULL;\n    const hasLocationDefaultValue = locationDefaultValue !== undefined;\n    if (!hasNonNullVariableDefaultValue && !hasLocationDefaultValue) {\n      return false;\n    }\n    const nullableLocationType = locationType.ofType;\n    return isTypeSubTypeOf(schema, varType, nullableLocationType);\n  }\n  return isTypeSubTypeOf(schema, varType, locationType);\n}", "map": {"version": 3, "names": ["inspect", "GraphQLError", "Kind", "isInputObjectType", "isNonNullType", "isNullableType", "isTypeSubTypeOf", "typeFromAST", "VariablesInAllowedPositionRule", "context", "varDefMap", "Object", "create", "OperationDefinition", "enter", "leave", "operation", "usages", "getRecursiveVariableUsages", "node", "type", "defaultValue", "parentType", "varName", "name", "value", "varDef", "schema", "getSchema", "varType", "allowedVariableUsage", "varTypeStr", "typeStr", "reportError", "nodes", "isOneOf", "VariableDefinition", "variable", "varDefaultValue", "locationType", "locationDefaultValue", "hasNonNullVariableDefaultValue", "kind", "NULL", "hasLocationDefaultValue", "undefined", "nullableLocationType", "ofType"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/VariablesInAllowedPositionRule.mjs"], "sourcesContent": ["import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport {\n  isInputObjectType,\n  isNonNullType,\n  isNullableType,\n} from '../../type/definition.mjs';\nimport { isTypeSubTypeOf } from '../../utilities/typeComparators.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Variables in allowed position\n *\n * Variable usages must be compatible with the arguments they are passed to.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variable-Usages-are-Allowed\n */\nexport function VariablesInAllowedPositionRule(context) {\n  let varDefMap = Object.create(null);\n  return {\n    OperationDefinition: {\n      enter() {\n        varDefMap = Object.create(null);\n      },\n\n      leave(operation) {\n        const usages = context.getRecursiveVariableUsages(operation);\n\n        for (const { node, type, defaultValue, parentType } of usages) {\n          const varName = node.name.value;\n          const varDef = varDefMap[varName];\n\n          if (varDef && type) {\n            // A var type is allowed if it is the same or more strict (e.g. is\n            // a subtype of) than the expected type. It can be more strict if\n            // the variable type is non-null when the expected type is nullable.\n            // If both are list types, the variable item type can be more strict\n            // than the expected item type (contravariant).\n            const schema = context.getSchema();\n            const varType = typeFromAST(schema, varDef.type);\n\n            if (\n              varType &&\n              !allowedVariableUsage(\n                schema,\n                varType,\n                varDef.defaultValue,\n                type,\n                defaultValue,\n              )\n            ) {\n              const varTypeStr = inspect(varType);\n              const typeStr = inspect(type);\n              context.reportError(\n                new GraphQLError(\n                  `Variable \"$${varName}\" of type \"${varTypeStr}\" used in position expecting type \"${typeStr}\".`,\n                  {\n                    nodes: [varDef, node],\n                  },\n                ),\n              );\n            }\n\n            if (\n              isInputObjectType(parentType) &&\n              parentType.isOneOf &&\n              isNullableType(varType)\n            ) {\n              context.reportError(\n                new GraphQLError(\n                  `Variable \"$${varName}\" is of type \"${varType}\" but must be non-nullable to be used for OneOf Input Object \"${parentType}\".`,\n                  {\n                    nodes: [varDef, node],\n                  },\n                ),\n              );\n            }\n          }\n        }\n      },\n    },\n\n    VariableDefinition(node) {\n      varDefMap[node.variable.name.value] = node;\n    },\n  };\n}\n/**\n * Returns true if the variable is allowed in the location it was found,\n * which includes considering if default values exist for either the variable\n * or the location at which it is located.\n */\n\nfunction allowedVariableUsage(\n  schema,\n  varType,\n  varDefaultValue,\n  locationType,\n  locationDefaultValue,\n) {\n  if (isNonNullType(locationType) && !isNonNullType(varType)) {\n    const hasNonNullVariableDefaultValue =\n      varDefaultValue != null && varDefaultValue.kind !== Kind.NULL;\n    const hasLocationDefaultValue = locationDefaultValue !== undefined;\n\n    if (!hasNonNullVariableDefaultValue && !hasLocationDefaultValue) {\n      return false;\n    }\n\n    const nullableLocationType = locationType.ofType;\n    return isTypeSubTypeOf(schema, varType, nullableLocationType);\n  }\n\n  return isTypeSubTypeOf(schema, varType, locationType);\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SACEC,iBAAiB,EACjBC,aAAa,EACbC,cAAc,QACT,2BAA2B;AAClC,SAASC,eAAe,QAAQ,qCAAqC;AACrE,SAASC,WAAW,QAAQ,iCAAiC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,8BAA8BA,CAACC,OAAO,EAAE;EACtD,IAAIC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACnC,OAAO;IACLC,mBAAmB,EAAE;MACnBC,KAAKA,CAAA,EAAG;QACNJ,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACjC,CAAC;MAEDG,KAAKA,CAACC,SAAS,EAAE;QACf,MAAMC,MAAM,GAAGR,OAAO,CAACS,0BAA0B,CAACF,SAAS,CAAC;QAE5D,KAAK,MAAM;UAAEG,IAAI;UAAEC,IAAI;UAAEC,YAAY;UAAEC;QAAW,CAAC,IAAIL,MAAM,EAAE;UAC7D,MAAMM,OAAO,GAAGJ,IAAI,CAACK,IAAI,CAACC,KAAK;UAC/B,MAAMC,MAAM,GAAGhB,SAAS,CAACa,OAAO,CAAC;UAEjC,IAAIG,MAAM,IAAIN,IAAI,EAAE;YAClB;YACA;YACA;YACA;YACA;YACA,MAAMO,MAAM,GAAGlB,OAAO,CAACmB,SAAS,CAAC,CAAC;YAClC,MAAMC,OAAO,GAAGtB,WAAW,CAACoB,MAAM,EAAED,MAAM,CAACN,IAAI,CAAC;YAEhD,IACES,OAAO,IACP,CAACC,oBAAoB,CACnBH,MAAM,EACNE,OAAO,EACPH,MAAM,CAACL,YAAY,EACnBD,IAAI,EACJC,YACF,CAAC,EACD;cACA,MAAMU,UAAU,GAAG/B,OAAO,CAAC6B,OAAO,CAAC;cACnC,MAAMG,OAAO,GAAGhC,OAAO,CAACoB,IAAI,CAAC;cAC7BX,OAAO,CAACwB,WAAW,CACjB,IAAIhC,YAAY,CACd,cAAcsB,OAAO,cAAcQ,UAAU,sCAAsCC,OAAO,IAAI,EAC9F;gBACEE,KAAK,EAAE,CAACR,MAAM,EAAEP,IAAI;cACtB,CACF,CACF,CAAC;YACH;YAEA,IACEhB,iBAAiB,CAACmB,UAAU,CAAC,IAC7BA,UAAU,CAACa,OAAO,IAClB9B,cAAc,CAACwB,OAAO,CAAC,EACvB;cACApB,OAAO,CAACwB,WAAW,CACjB,IAAIhC,YAAY,CACd,cAAcsB,OAAO,iBAAiBM,OAAO,iEAAiEP,UAAU,IAAI,EAC5H;gBACEY,KAAK,EAAE,CAACR,MAAM,EAAEP,IAAI;cACtB,CACF,CACF,CAAC;YACH;UACF;QACF;MACF;IACF,CAAC;IAEDiB,kBAAkBA,CAACjB,IAAI,EAAE;MACvBT,SAAS,CAACS,IAAI,CAACkB,QAAQ,CAACb,IAAI,CAACC,KAAK,CAAC,GAAGN,IAAI;IAC5C;EACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASW,oBAAoBA,CAC3BH,MAAM,EACNE,OAAO,EACPS,eAAe,EACfC,YAAY,EACZC,oBAAoB,EACpB;EACA,IAAIpC,aAAa,CAACmC,YAAY,CAAC,IAAI,CAACnC,aAAa,CAACyB,OAAO,CAAC,EAAE;IAC1D,MAAMY,8BAA8B,GAClCH,eAAe,IAAI,IAAI,IAAIA,eAAe,CAACI,IAAI,KAAKxC,IAAI,CAACyC,IAAI;IAC/D,MAAMC,uBAAuB,GAAGJ,oBAAoB,KAAKK,SAAS;IAElE,IAAI,CAACJ,8BAA8B,IAAI,CAACG,uBAAuB,EAAE;MAC/D,OAAO,KAAK;IACd;IAEA,MAAME,oBAAoB,GAAGP,YAAY,CAACQ,MAAM;IAChD,OAAOzC,eAAe,CAACqB,MAAM,EAAEE,OAAO,EAAEiB,oBAAoB,CAAC;EAC/D;EAEA,OAAOxC,eAAe,CAACqB,MAAM,EAAEE,OAAO,EAAEU,YAAY,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}