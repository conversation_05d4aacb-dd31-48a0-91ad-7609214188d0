{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { inspect } from '../../jsutils/inspect.mjs';\nimport { keyMap } from '../../jsutils/keyMap.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { getNamedType, getNullableType, isInputObjectType, isLeafType, isListType, isNonNullType, isRequiredInputField } from '../../type/definition.mjs';\n\n/**\n * Value literals of correct type\n *\n * A GraphQL document is only valid if all value literals are of the type\n * expected at their position.\n *\n * See https://spec.graphql.org/draft/#sec-Values-of-Correct-Type\n */\nexport function ValuesOfCorrectTypeRule(context) {\n  let variableDefinitions = {};\n  return {\n    OperationDefinition: {\n      enter() {\n        variableDefinitions = {};\n      }\n    },\n    VariableDefinition(definition) {\n      variableDefinitions[definition.variable.name.value] = definition;\n    },\n    ListValue(node) {\n      // Note: TypeInfo will traverse into a list's item type, so look to the\n      // parent input type to check if it is a list.\n      const type = getNullableType(context.getParentInputType());\n      if (!isListType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      }\n    },\n    ObjectValue(node) {\n      const type = getNamedType(context.getInputType());\n      if (!isInputObjectType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      } // Ensure every required field exists.\n\n      const fieldNodeMap = keyMap(node.fields, field => field.name.value);\n      for (const fieldDef of Object.values(type.getFields())) {\n        const fieldNode = fieldNodeMap[fieldDef.name];\n        if (!fieldNode && isRequiredInputField(fieldDef)) {\n          const typeStr = inspect(fieldDef.type);\n          context.reportError(new GraphQLError(`Field \"${type.name}.${fieldDef.name}\" of required type \"${typeStr}\" was not provided.`, {\n            nodes: node\n          }));\n        }\n      }\n      if (type.isOneOf) {\n        validateOneOfInputObject(context, node, type, fieldNodeMap, variableDefinitions);\n      }\n    },\n    ObjectField(node) {\n      const parentType = getNamedType(context.getParentInputType());\n      const fieldType = context.getInputType();\n      if (!fieldType && isInputObjectType(parentType)) {\n        const suggestions = suggestionList(node.name.value, Object.keys(parentType.getFields()));\n        context.reportError(new GraphQLError(`Field \"${node.name.value}\" is not defined by type \"${parentType.name}\".` + didYouMean(suggestions), {\n          nodes: node\n        }));\n      }\n    },\n    NullValue(node) {\n      const type = context.getInputType();\n      if (isNonNullType(type)) {\n        context.reportError(new GraphQLError(`Expected value of type \"${inspect(type)}\", found ${print(node)}.`, {\n          nodes: node\n        }));\n      }\n    },\n    EnumValue: node => isValidValueNode(context, node),\n    IntValue: node => isValidValueNode(context, node),\n    FloatValue: node => isValidValueNode(context, node),\n    StringValue: node => isValidValueNode(context, node),\n    BooleanValue: node => isValidValueNode(context, node)\n  };\n}\n/**\n * Any value literal may be a valid representation of a Scalar, depending on\n * that scalar type.\n */\n\nfunction isValidValueNode(context, node) {\n  // Report any error at the full type expected by the location.\n  const locationType = context.getInputType();\n  if (!locationType) {\n    return;\n  }\n  const type = getNamedType(locationType);\n  if (!isLeafType(type)) {\n    const typeStr = inspect(locationType);\n    context.reportError(new GraphQLError(`Expected value of type \"${typeStr}\", found ${print(node)}.`, {\n      nodes: node\n    }));\n    return;\n  } // Scalars and Enums determine if a literal value is valid via parseLiteral(),\n  // which may throw or return an invalid value to indicate failure.\n\n  try {\n    const parseResult = type.parseLiteral(node, undefined\n    /* variables */);\n    if (parseResult === undefined) {\n      const typeStr = inspect(locationType);\n      context.reportError(new GraphQLError(`Expected value of type \"${typeStr}\", found ${print(node)}.`, {\n        nodes: node\n      }));\n    }\n  } catch (error) {\n    const typeStr = inspect(locationType);\n    if (error instanceof GraphQLError) {\n      context.reportError(error);\n    } else {\n      context.reportError(new GraphQLError(`Expected value of type \"${typeStr}\", found ${print(node)}; ` + error.message, {\n        nodes: node,\n        originalError: error\n      }));\n    }\n  }\n}\nfunction validateOneOfInputObject(context, node, type, fieldNodeMap, variableDefinitions) {\n  var _fieldNodeMap$keys$;\n  const keys = Object.keys(fieldNodeMap);\n  const isNotExactlyOneField = keys.length !== 1;\n  if (isNotExactlyOneField) {\n    context.reportError(new GraphQLError(`OneOf Input Object \"${type.name}\" must specify exactly one key.`, {\n      nodes: [node]\n    }));\n    return;\n  }\n  const value = (_fieldNodeMap$keys$ = fieldNodeMap[keys[0]]) === null || _fieldNodeMap$keys$ === void 0 ? void 0 : _fieldNodeMap$keys$.value;\n  const isNullLiteral = !value || value.kind === Kind.NULL;\n  const isVariable = (value === null || value === void 0 ? void 0 : value.kind) === Kind.VARIABLE;\n  if (isNullLiteral) {\n    context.reportError(new GraphQLError(`Field \"${type.name}.${keys[0]}\" must be non-null.`, {\n      nodes: [node]\n    }));\n    return;\n  }\n  if (isVariable) {\n    const variableName = value.name.value;\n    const definition = variableDefinitions[variableName];\n    const isNullableVariable = definition.type.kind !== Kind.NON_NULL_TYPE;\n    if (isNullableVariable) {\n      context.reportError(new GraphQLError(`Variable \"${variableName}\" must be non-nullable to be used for OneOf Input Object \"${type.name}\".`, {\n        nodes: [node]\n      }));\n    }\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "inspect", "keyMap", "suggestionList", "GraphQLError", "Kind", "print", "getNamedType", "getNullableType", "isInputObjectType", "isLeafType", "isListType", "isNonNullType", "isRequiredInputField", "ValuesOfCorrectTypeRule", "context", "variableDefinitions", "OperationDefinition", "enter", "VariableDefinition", "definition", "variable", "name", "value", "ListValue", "node", "type", "getParentInputType", "isValidValueNode", "ObjectValue", "getInputType", "fieldNodeMap", "fields", "field", "fieldDef", "Object", "values", "getFields", "fieldNode", "typeStr", "reportError", "nodes", "isOneOf", "validateOneOfInputObject", "ObjectField", "parentType", "fieldType", "suggestions", "keys", "Null<PERSON><PERSON>ue", "EnumValue", "IntValue", "FloatValue", "StringValue", "BooleanValue", "locationType", "parseResult", "parseLiteral", "undefined", "error", "message", "originalError", "_fieldNodeMap$keys$", "isNotExactlyOneField", "length", "is<PERSON>ull<PERSON>iteral", "kind", "NULL", "isVariable", "VARIABLE", "variableName", "isNullableVariable", "NON_NULL_TYPE"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/ValuesOfCorrectTypeRule.mjs"], "sourcesContent": ["import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { inspect } from '../../jsutils/inspect.mjs';\nimport { keyMap } from '../../jsutils/keyMap.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { print } from '../../language/printer.mjs';\nimport {\n  getNamedType,\n  getNullableType,\n  isInputObjectType,\n  isLeafType,\n  isListType,\n  isNonNullType,\n  isRequiredInputField,\n} from '../../type/definition.mjs';\n\n/**\n * Value literals of correct type\n *\n * A GraphQL document is only valid if all value literals are of the type\n * expected at their position.\n *\n * See https://spec.graphql.org/draft/#sec-Values-of-Correct-Type\n */\nexport function ValuesOfCorrectTypeRule(context) {\n  let variableDefinitions = {};\n  return {\n    OperationDefinition: {\n      enter() {\n        variableDefinitions = {};\n      },\n    },\n\n    VariableDefinition(definition) {\n      variableDefinitions[definition.variable.name.value] = definition;\n    },\n\n    ListValue(node) {\n      // Note: TypeInfo will traverse into a list's item type, so look to the\n      // parent input type to check if it is a list.\n      const type = getNullableType(context.getParentInputType());\n\n      if (!isListType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      }\n    },\n\n    ObjectValue(node) {\n      const type = getNamedType(context.getInputType());\n\n      if (!isInputObjectType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      } // Ensure every required field exists.\n\n      const fieldNodeMap = keyMap(node.fields, (field) => field.name.value);\n\n      for (const fieldDef of Object.values(type.getFields())) {\n        const fieldNode = fieldNodeMap[fieldDef.name];\n\n        if (!fieldNode && isRequiredInputField(fieldDef)) {\n          const typeStr = inspect(fieldDef.type);\n          context.reportError(\n            new GraphQLError(\n              `Field \"${type.name}.${fieldDef.name}\" of required type \"${typeStr}\" was not provided.`,\n              {\n                nodes: node,\n              },\n            ),\n          );\n        }\n      }\n\n      if (type.isOneOf) {\n        validateOneOfInputObject(\n          context,\n          node,\n          type,\n          fieldNodeMap,\n          variableDefinitions,\n        );\n      }\n    },\n\n    ObjectField(node) {\n      const parentType = getNamedType(context.getParentInputType());\n      const fieldType = context.getInputType();\n\n      if (!fieldType && isInputObjectType(parentType)) {\n        const suggestions = suggestionList(\n          node.name.value,\n          Object.keys(parentType.getFields()),\n        );\n        context.reportError(\n          new GraphQLError(\n            `Field \"${node.name.value}\" is not defined by type \"${parentType.name}\".` +\n              didYouMean(suggestions),\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n\n    NullValue(node) {\n      const type = context.getInputType();\n\n      if (isNonNullType(type)) {\n        context.reportError(\n          new GraphQLError(\n            `Expected value of type \"${inspect(type)}\", found ${print(node)}.`,\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n\n    EnumValue: (node) => isValidValueNode(context, node),\n    IntValue: (node) => isValidValueNode(context, node),\n    FloatValue: (node) => isValidValueNode(context, node),\n    StringValue: (node) => isValidValueNode(context, node),\n    BooleanValue: (node) => isValidValueNode(context, node),\n  };\n}\n/**\n * Any value literal may be a valid representation of a Scalar, depending on\n * that scalar type.\n */\n\nfunction isValidValueNode(context, node) {\n  // Report any error at the full type expected by the location.\n  const locationType = context.getInputType();\n\n  if (!locationType) {\n    return;\n  }\n\n  const type = getNamedType(locationType);\n\n  if (!isLeafType(type)) {\n    const typeStr = inspect(locationType);\n    context.reportError(\n      new GraphQLError(\n        `Expected value of type \"${typeStr}\", found ${print(node)}.`,\n        {\n          nodes: node,\n        },\n      ),\n    );\n    return;\n  } // Scalars and Enums determine if a literal value is valid via parseLiteral(),\n  // which may throw or return an invalid value to indicate failure.\n\n  try {\n    const parseResult = type.parseLiteral(\n      node,\n      undefined,\n      /* variables */\n    );\n\n    if (parseResult === undefined) {\n      const typeStr = inspect(locationType);\n      context.reportError(\n        new GraphQLError(\n          `Expected value of type \"${typeStr}\", found ${print(node)}.`,\n          {\n            nodes: node,\n          },\n        ),\n      );\n    }\n  } catch (error) {\n    const typeStr = inspect(locationType);\n\n    if (error instanceof GraphQLError) {\n      context.reportError(error);\n    } else {\n      context.reportError(\n        new GraphQLError(\n          `Expected value of type \"${typeStr}\", found ${print(node)}; ` +\n            error.message,\n          {\n            nodes: node,\n            originalError: error,\n          },\n        ),\n      );\n    }\n  }\n}\n\nfunction validateOneOfInputObject(\n  context,\n  node,\n  type,\n  fieldNodeMap,\n  variableDefinitions,\n) {\n  var _fieldNodeMap$keys$;\n\n  const keys = Object.keys(fieldNodeMap);\n  const isNotExactlyOneField = keys.length !== 1;\n\n  if (isNotExactlyOneField) {\n    context.reportError(\n      new GraphQLError(\n        `OneOf Input Object \"${type.name}\" must specify exactly one key.`,\n        {\n          nodes: [node],\n        },\n      ),\n    );\n    return;\n  }\n\n  const value =\n    (_fieldNodeMap$keys$ = fieldNodeMap[keys[0]]) === null ||\n    _fieldNodeMap$keys$ === void 0\n      ? void 0\n      : _fieldNodeMap$keys$.value;\n  const isNullLiteral = !value || value.kind === Kind.NULL;\n  const isVariable =\n    (value === null || value === void 0 ? void 0 : value.kind) ===\n    Kind.VARIABLE;\n\n  if (isNullLiteral) {\n    context.reportError(\n      new GraphQLError(`Field \"${type.name}.${keys[0]}\" must be non-null.`, {\n        nodes: [node],\n      }),\n    );\n    return;\n  }\n\n  if (isVariable) {\n    const variableName = value.name.value;\n    const definition = variableDefinitions[variableName];\n    const isNullableVariable = definition.type.kind !== Kind.NON_NULL_TYPE;\n\n    if (isNullableVariable) {\n      context.reportError(\n        new GraphQLError(\n          `Variable \"${variableName}\" must be non-nullable to be used for OneOf Input Object \"${type.name}\".`,\n          {\n            nodes: [node],\n          },\n        ),\n      );\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,KAAK,QAAQ,4BAA4B;AAClD,SACEC,YAAY,EACZC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,oBAAoB,QACf,2BAA2B;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,OAAO,EAAE;EAC/C,IAAIC,mBAAmB,GAAG,CAAC,CAAC;EAC5B,OAAO;IACLC,mBAAmB,EAAE;MACnBC,KAAKA,CAAA,EAAG;QACNF,mBAAmB,GAAG,CAAC,CAAC;MAC1B;IACF,CAAC;IAEDG,kBAAkBA,CAACC,UAAU,EAAE;MAC7BJ,mBAAmB,CAACI,UAAU,CAACC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,GAAGH,UAAU;IAClE,CAAC;IAEDI,SAASA,CAACC,IAAI,EAAE;MACd;MACA;MACA,MAAMC,IAAI,GAAGlB,eAAe,CAACO,OAAO,CAACY,kBAAkB,CAAC,CAAC,CAAC;MAE1D,IAAI,CAAChB,UAAU,CAACe,IAAI,CAAC,EAAE;QACrBE,gBAAgB,CAACb,OAAO,EAAEU,IAAI,CAAC;QAC/B,OAAO,KAAK,CAAC,CAAC;MAChB;IACF,CAAC;IAEDI,WAAWA,CAACJ,IAAI,EAAE;MAChB,MAAMC,IAAI,GAAGnB,YAAY,CAACQ,OAAO,CAACe,YAAY,CAAC,CAAC,CAAC;MAEjD,IAAI,CAACrB,iBAAiB,CAACiB,IAAI,CAAC,EAAE;QAC5BE,gBAAgB,CAACb,OAAO,EAAEU,IAAI,CAAC;QAC/B,OAAO,KAAK,CAAC,CAAC;MAChB,CAAC,CAAC;;MAEF,MAAMM,YAAY,GAAG7B,MAAM,CAACuB,IAAI,CAACO,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACX,IAAI,CAACC,KAAK,CAAC;MAErE,KAAK,MAAMW,QAAQ,IAAIC,MAAM,CAACC,MAAM,CAACV,IAAI,CAACW,SAAS,CAAC,CAAC,CAAC,EAAE;QACtD,MAAMC,SAAS,GAAGP,YAAY,CAACG,QAAQ,CAACZ,IAAI,CAAC;QAE7C,IAAI,CAACgB,SAAS,IAAIzB,oBAAoB,CAACqB,QAAQ,CAAC,EAAE;UAChD,MAAMK,OAAO,GAAGtC,OAAO,CAACiC,QAAQ,CAACR,IAAI,CAAC;UACtCX,OAAO,CAACyB,WAAW,CACjB,IAAIpC,YAAY,CACd,UAAUsB,IAAI,CAACJ,IAAI,IAAIY,QAAQ,CAACZ,IAAI,uBAAuBiB,OAAO,qBAAqB,EACvF;YACEE,KAAK,EAAEhB;UACT,CACF,CACF,CAAC;QACH;MACF;MAEA,IAAIC,IAAI,CAACgB,OAAO,EAAE;QAChBC,wBAAwB,CACtB5B,OAAO,EACPU,IAAI,EACJC,IAAI,EACJK,YAAY,EACZf,mBACF,CAAC;MACH;IACF,CAAC;IAED4B,WAAWA,CAACnB,IAAI,EAAE;MAChB,MAAMoB,UAAU,GAAGtC,YAAY,CAACQ,OAAO,CAACY,kBAAkB,CAAC,CAAC,CAAC;MAC7D,MAAMmB,SAAS,GAAG/B,OAAO,CAACe,YAAY,CAAC,CAAC;MAExC,IAAI,CAACgB,SAAS,IAAIrC,iBAAiB,CAACoC,UAAU,CAAC,EAAE;QAC/C,MAAME,WAAW,GAAG5C,cAAc,CAChCsB,IAAI,CAACH,IAAI,CAACC,KAAK,EACfY,MAAM,CAACa,IAAI,CAACH,UAAU,CAACR,SAAS,CAAC,CAAC,CACpC,CAAC;QACDtB,OAAO,CAACyB,WAAW,CACjB,IAAIpC,YAAY,CACd,UAAUqB,IAAI,CAACH,IAAI,CAACC,KAAK,6BAA6BsB,UAAU,CAACvB,IAAI,IAAI,GACvEtB,UAAU,CAAC+C,WAAW,CAAC,EACzB;UACEN,KAAK,EAAEhB;QACT,CACF,CACF,CAAC;MACH;IACF,CAAC;IAEDwB,SAASA,CAACxB,IAAI,EAAE;MACd,MAAMC,IAAI,GAAGX,OAAO,CAACe,YAAY,CAAC,CAAC;MAEnC,IAAIlB,aAAa,CAACc,IAAI,CAAC,EAAE;QACvBX,OAAO,CAACyB,WAAW,CACjB,IAAIpC,YAAY,CACd,2BAA2BH,OAAO,CAACyB,IAAI,CAAC,YAAYpB,KAAK,CAACmB,IAAI,CAAC,GAAG,EAClE;UACEgB,KAAK,EAAEhB;QACT,CACF,CACF,CAAC;MACH;IACF,CAAC;IAEDyB,SAAS,EAAGzB,IAAI,IAAKG,gBAAgB,CAACb,OAAO,EAAEU,IAAI,CAAC;IACpD0B,QAAQ,EAAG1B,IAAI,IAAKG,gBAAgB,CAACb,OAAO,EAAEU,IAAI,CAAC;IACnD2B,UAAU,EAAG3B,IAAI,IAAKG,gBAAgB,CAACb,OAAO,EAAEU,IAAI,CAAC;IACrD4B,WAAW,EAAG5B,IAAI,IAAKG,gBAAgB,CAACb,OAAO,EAAEU,IAAI,CAAC;IACtD6B,YAAY,EAAG7B,IAAI,IAAKG,gBAAgB,CAACb,OAAO,EAAEU,IAAI;EACxD,CAAC;AACH;AACA;AACA;AACA;AACA;;AAEA,SAASG,gBAAgBA,CAACb,OAAO,EAAEU,IAAI,EAAE;EACvC;EACA,MAAM8B,YAAY,GAAGxC,OAAO,CAACe,YAAY,CAAC,CAAC;EAE3C,IAAI,CAACyB,YAAY,EAAE;IACjB;EACF;EAEA,MAAM7B,IAAI,GAAGnB,YAAY,CAACgD,YAAY,CAAC;EAEvC,IAAI,CAAC7C,UAAU,CAACgB,IAAI,CAAC,EAAE;IACrB,MAAMa,OAAO,GAAGtC,OAAO,CAACsD,YAAY,CAAC;IACrCxC,OAAO,CAACyB,WAAW,CACjB,IAAIpC,YAAY,CACd,2BAA2BmC,OAAO,YAAYjC,KAAK,CAACmB,IAAI,CAAC,GAAG,EAC5D;MACEgB,KAAK,EAAEhB;IACT,CACF,CACF,CAAC;IACD;EACF,CAAC,CAAC;EACF;;EAEA,IAAI;IACF,MAAM+B,WAAW,GAAG9B,IAAI,CAAC+B,YAAY,CACnChC,IAAI,EACJiC;IACA,eACF,CAAC;IAED,IAAIF,WAAW,KAAKE,SAAS,EAAE;MAC7B,MAAMnB,OAAO,GAAGtC,OAAO,CAACsD,YAAY,CAAC;MACrCxC,OAAO,CAACyB,WAAW,CACjB,IAAIpC,YAAY,CACd,2BAA2BmC,OAAO,YAAYjC,KAAK,CAACmB,IAAI,CAAC,GAAG,EAC5D;QACEgB,KAAK,EAAEhB;MACT,CACF,CACF,CAAC;IACH;EACF,CAAC,CAAC,OAAOkC,KAAK,EAAE;IACd,MAAMpB,OAAO,GAAGtC,OAAO,CAACsD,YAAY,CAAC;IAErC,IAAII,KAAK,YAAYvD,YAAY,EAAE;MACjCW,OAAO,CAACyB,WAAW,CAACmB,KAAK,CAAC;IAC5B,CAAC,MAAM;MACL5C,OAAO,CAACyB,WAAW,CACjB,IAAIpC,YAAY,CACd,2BAA2BmC,OAAO,YAAYjC,KAAK,CAACmB,IAAI,CAAC,IAAI,GAC3DkC,KAAK,CAACC,OAAO,EACf;QACEnB,KAAK,EAAEhB,IAAI;QACXoC,aAAa,EAAEF;MACjB,CACF,CACF,CAAC;IACH;EACF;AACF;AAEA,SAAShB,wBAAwBA,CAC/B5B,OAAO,EACPU,IAAI,EACJC,IAAI,EACJK,YAAY,EACZf,mBAAmB,EACnB;EACA,IAAI8C,mBAAmB;EAEvB,MAAMd,IAAI,GAAGb,MAAM,CAACa,IAAI,CAACjB,YAAY,CAAC;EACtC,MAAMgC,oBAAoB,GAAGf,IAAI,CAACgB,MAAM,KAAK,CAAC;EAE9C,IAAID,oBAAoB,EAAE;IACxBhD,OAAO,CAACyB,WAAW,CACjB,IAAIpC,YAAY,CACd,uBAAuBsB,IAAI,CAACJ,IAAI,iCAAiC,EACjE;MACEmB,KAAK,EAAE,CAAChB,IAAI;IACd,CACF,CACF,CAAC;IACD;EACF;EAEA,MAAMF,KAAK,GACT,CAACuC,mBAAmB,GAAG/B,YAAY,CAACiB,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,IACtDc,mBAAmB,KAAK,KAAK,CAAC,GAC1B,KAAK,CAAC,GACNA,mBAAmB,CAACvC,KAAK;EAC/B,MAAM0C,aAAa,GAAG,CAAC1C,KAAK,IAAIA,KAAK,CAAC2C,IAAI,KAAK7D,IAAI,CAAC8D,IAAI;EACxD,MAAMC,UAAU,GACd,CAAC7C,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC2C,IAAI,MACzD7D,IAAI,CAACgE,QAAQ;EAEf,IAAIJ,aAAa,EAAE;IACjBlD,OAAO,CAACyB,WAAW,CACjB,IAAIpC,YAAY,CAAC,UAAUsB,IAAI,CAACJ,IAAI,IAAI0B,IAAI,CAAC,CAAC,CAAC,qBAAqB,EAAE;MACpEP,KAAK,EAAE,CAAChB,IAAI;IACd,CAAC,CACH,CAAC;IACD;EACF;EAEA,IAAI2C,UAAU,EAAE;IACd,MAAME,YAAY,GAAG/C,KAAK,CAACD,IAAI,CAACC,KAAK;IACrC,MAAMH,UAAU,GAAGJ,mBAAmB,CAACsD,YAAY,CAAC;IACpD,MAAMC,kBAAkB,GAAGnD,UAAU,CAACM,IAAI,CAACwC,IAAI,KAAK7D,IAAI,CAACmE,aAAa;IAEtE,IAAID,kBAAkB,EAAE;MACtBxD,OAAO,CAACyB,WAAW,CACjB,IAAIpC,YAAY,CACd,aAAakE,YAAY,6DAA6D5C,IAAI,CAACJ,IAAI,IAAI,EACnG;QACEmB,KAAK,EAAE,CAAChB,IAAI;MACd,CACF,CACF,CAAC;IACH;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}