{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/ImpactAssessment.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Grid, Typography, Table, TableBody, TableCell, TableHead, TableRow, Select, MenuItem, TextField, FormControl, InputLabel, Alert, Box } from '@mui/material';\nimport { updateProcessData } from '../../../store/biaSlice';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst IMPACT_CATEGORIES = ['Financial', 'Legal/Regulatory', 'Operational', 'Reputational', 'Customer Service'];\nconst TIMEFRAMES = [{\n  id: '0-4h',\n  label: '0-4 Hours'\n}, {\n  id: '4-8h',\n  label: '4-8 Hours'\n}, {\n  id: '8-24h',\n  label: '8-24 Hours'\n}, {\n  id: '24-72h',\n  label: '24-72 Hours'\n}, {\n  id: '72h+',\n  label: '72+ Hours'\n}];\nconst RTO_OPTIONS = ['4 Hours', '8 Hours', '24 Hours', '72 Hours', '1 Week', '2 Weeks'];\nconst RPO_OPTIONS = ['1 Hour', '4 Hours', '8 Hours', '24 Hours', '72 Hours'];\nconst ImpactAssessment = ({\n  processId\n}) => {\n  _s();\n  var _currentBIA$processDa;\n  const dispatch = useDispatch();\n  const {\n    currentBIA\n  } = useSelector(state => state.bia);\n  const processData = (currentBIA === null || currentBIA === void 0 ? void 0 : (_currentBIA$processDa = currentBIA.processData) === null || _currentBIA$processDa === void 0 ? void 0 : _currentBIA$processDa[processId]) || {};\n  const [impactMatrix, setImpactMatrix] = useState(processData.impactMatrix || {});\n  const [suggestedRTO, setSuggestedRTO] = useState('');\n  const [suggestedRPO, setSuggestedRPO] = useState('');\n  const [finalRTO, setFinalRTO] = useState(processData.finalRTO || '');\n  const [finalRPO, setFinalRPO] = useState(processData.finalRPO || '');\n  const [rtoJustification, setRtoJustification] = useState(processData.rtoJustification || '');\n  const [rpoJustification, setRpoJustification] = useState(processData.rpoJustification || '');\n\n  // Calculate suggested RTO/RPO based on impact matrix\n  useEffect(() => {\n    const calculateSuggestions = () => {\n      let highestImpactTimeframe = null;\n\n      // Find the earliest timeframe with any \"High\" impact\n      for (let timeframe of TIMEFRAMES) {\n        const timeframeData = impactMatrix[timeframe.id] || {};\n        const hasHighImpact = Object.values(timeframeData).some(rating => rating === 'High');\n        if (hasHighImpact) {\n          highestImpactTimeframe = timeframe.id;\n          break;\n        }\n      }\n      if (highestImpactTimeframe) {\n        const rtoMapping = {\n          '0-4h': '4 Hours',\n          '4-8h': '8 Hours',\n          '8-24h': '24 Hours',\n          '24-72h': '72 Hours',\n          '72h+': '1 Week'\n        };\n        const rpoMapping = {\n          '0-4h': '1 Hour',\n          '4-8h': '4 Hours',\n          '8-24h': '8 Hours',\n          '24-72h': '24 Hours',\n          '72h+': '72 Hours'\n        };\n        setSuggestedRTO(rtoMapping[highestImpactTimeframe]);\n        setSuggestedRPO(rpoMapping[highestImpactTimeframe]);\n      } else {\n        setSuggestedRTO('1 Week');\n        setSuggestedRPO('72 Hours');\n      }\n    };\n    calculateSuggestions();\n  }, [impactMatrix]);\n\n  // Set final values to suggested when suggestions change\n  useEffect(() => {\n    if (suggestedRTO && !finalRTO) {\n      setFinalRTO(suggestedRTO);\n    }\n    if (suggestedRPO && !finalRPO) {\n      setFinalRPO(suggestedRPO);\n    }\n  }, [suggestedRTO, suggestedRPO, finalRTO, finalRPO]);\n\n  // Save data to Redux when values change\n  useEffect(() => {\n    const dataToSave = {\n      impactMatrix,\n      suggestedRTO,\n      suggestedRPO,\n      finalRTO,\n      finalRPO,\n      rtoJustification,\n      rpoJustification\n    };\n    dispatch(updateProcessData({\n      processId,\n      data: dataToSave\n    }));\n  }, [dispatch, processId, impactMatrix, suggestedRTO, suggestedRPO, finalRTO, finalRPO, rtoJustification, rpoJustification]);\n  const handleImpactChange = (category, timeframe, rating) => {\n    setImpactMatrix(prev => ({\n      ...prev,\n      [timeframe]: {\n        ...prev[timeframe],\n        [category]: rating\n      }\n    }));\n  };\n  const getImpactValue = (category, timeframe) => {\n    var _impactMatrix$timefra;\n    return ((_impactMatrix$timefra = impactMatrix[timeframe]) === null || _impactMatrix$timefra === void 0 ? void 0 : _impactMatrix$timefra[category]) || '';\n  };\n  const isJustificationRequired = type => {\n    if (type === 'rto') {\n      return finalRTO !== suggestedRTO;\n    }\n    return finalRPO !== suggestedRPO;\n  };\n  return /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 7,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Impact Assessment Matrix\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        sx: {\n          border: 1,\n          borderColor: 'divider'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"Impact Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), TIMEFRAMES.map(timeframe => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              sx: {\n                fontWeight: 'bold',\n                minWidth: 100\n              },\n              children: timeframe.label\n            }, timeframe.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: IMPACT_CATEGORIES.map(category => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'medium'\n              },\n              children: category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), TIMEFRAMES.map(timeframe => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                size: \"small\",\n                value: getImpactValue(category, timeframe.id),\n                onChange: e => handleImpactChange(category, timeframe.id, e.target.value),\n                displayEmpty: true,\n                sx: {\n                  minWidth: 80\n                },\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"-\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Low\",\n                  children: \"Low\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Medium\",\n                  children: \"Medium\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"High\",\n                  children: \"High\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 21\n              }, this)\n            }, timeframe.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 19\n            }, this))]\n          }, category, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 5,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Recovery Objectives\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          border: 1,\n          borderColor: 'divider',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Recovery Time Objective (RTO)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Suggested RTO\",\n          value: suggestedRTO,\n          InputProps: {\n            readOnly: true\n          },\n          margin: \"normal\",\n          size: \"small\",\n          helperText: \"Auto-calculated from impact matrix\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Final RTO\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: finalRTO,\n            onChange: e => setFinalRTO(e.target.value),\n            children: RTO_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: option,\n              children: option\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), isJustificationRequired('rto') && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mt: 1,\n              mb: 1\n            },\n            children: \"Justification required - Final RTO differs from suggested\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            multiline: true,\n            rows: 3,\n            label: \"RTO Justification\",\n            value: rtoJustification,\n            onChange: e => setRtoJustification(e.target.value),\n            size: \"small\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          border: 1,\n          borderColor: 'divider',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Recovery Point Objective (RPO)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Suggested RPO\",\n          value: suggestedRPO,\n          InputProps: {\n            readOnly: true\n          },\n          margin: \"normal\",\n          size: \"small\",\n          helperText: \"Auto-calculated from impact matrix\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Final RPO\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: finalRPO,\n            onChange: e => setFinalRPO(e.target.value),\n            children: RPO_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: option,\n              children: option\n            }, option, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), isJustificationRequired('rpo') && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mt: 1,\n              mb: 1\n            },\n            children: \"Justification required - Final RPO differs from suggested\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            multiline: true,\n            rows: 3,\n            label: \"RPO Justification\",\n            value: rpoJustification,\n            onChange: e => setRpoJustification(e.target.value),\n            size: \"small\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s(ImpactAssessment, \"nK+eQ3nKjRtryITuzN5sL7n5Q6A=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = ImpactAssessment;\nexport default ImpactAssessment;\nvar _c;\n$RefreshReg$(_c, \"ImpactAssessment\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "Grid", "Typography", "Table", "TableBody", "TableCell", "TableHead", "TableRow", "Select", "MenuItem", "TextField", "FormControl", "InputLabel", "<PERSON><PERSON>", "Box", "updateProcessData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "IMPACT_CATEGORIES", "TIMEFRAMES", "id", "label", "RTO_OPTIONS", "RPO_OPTIONS", "ImpactAssessment", "processId", "_s", "_currentBIA$processDa", "dispatch", "currentBIA", "state", "bia", "processData", "impactMatrix", "setImpactMatrix", "suggestedRTO", "setSuggestedRTO", "suggestedRPO", "setSuggestedRPO", "finalRTO", "setFinalRTO", "finalRPO", "setFinalRPO", "rtoJustification", "setRtoJustification", "rpoJustification", "setRpoJustification", "calculateSuggestions", "highestImpactTimeframe", "timeframe", "timeframeData", "hasHighImpact", "Object", "values", "some", "rating", "rtoMapping", "rpoMapping", "dataToSave", "data", "handleImpactChange", "category", "prev", "getImpactValue", "_impactMatrix$timefra", "isJustificationRequired", "type", "container", "spacing", "children", "item", "xs", "md", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "sx", "border", "borderColor", "fontWeight", "map", "align", "min<PERSON><PERSON><PERSON>", "value", "onChange", "e", "target", "displayEmpty", "mb", "p", "borderRadius", "fullWidth", "InputProps", "readOnly", "margin", "helperText", "option", "severity", "mt", "multiline", "rows", "required", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/ImpactAssessment.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport {\n  Grid, Typography, Table, TableBody, TableCell, TableHead, TableRow,\n  Select, MenuItem, TextField, FormControl, InputLabel, Alert, Box\n} from '@mui/material';\nimport { updateProcessData } from '../../../store/biaSlice';\n\nconst IMPACT_CATEGORIES = [\n  'Financial',\n  'Legal/Regulatory',\n  'Operational',\n  'Reputational',\n  'Customer Service'\n];\n\nconst TIMEFRAMES = [\n  { id: '0-4h', label: '0-4 Hours' },\n  { id: '4-8h', label: '4-8 Hours' },\n  { id: '8-24h', label: '8-24 Hours' },\n  { id: '24-72h', label: '24-72 Hours' },\n  { id: '72h+', label: '72+ Hours' }\n];\n\nconst RTO_OPTIONS = [\n  '4 Hours',\n  '8 Hours', \n  '24 Hours',\n  '72 Hours',\n  '1 Week',\n  '2 Weeks'\n];\n\nconst RPO_OPTIONS = [\n  '1 Hour',\n  '4 Hours',\n  '8 Hours',\n  '24 Hours',\n  '72 Hours'\n];\n\nconst ImpactAssessment = ({ processId }) => {\n  const dispatch = useDispatch();\n  const { currentBIA } = useSelector(state => state.bia);\n  const processData = currentBIA?.processData?.[processId] || {};\n  \n  const [impactMatrix, setImpactMatrix] = useState(processData.impactMatrix || {});\n  const [suggestedRTO, setSuggestedRTO] = useState('');\n  const [suggestedRPO, setSuggestedRPO] = useState('');\n  const [finalRTO, setFinalRTO] = useState(processData.finalRTO || '');\n  const [finalRPO, setFinalRPO] = useState(processData.finalRPO || '');\n  const [rtoJustification, setRtoJustification] = useState(processData.rtoJustification || '');\n  const [rpoJustification, setRpoJustification] = useState(processData.rpoJustification || '');\n\n  // Calculate suggested RTO/RPO based on impact matrix\n  useEffect(() => {\n    const calculateSuggestions = () => {\n      let highestImpactTimeframe = null;\n      \n      // Find the earliest timeframe with any \"High\" impact\n      for (let timeframe of TIMEFRAMES) {\n        const timeframeData = impactMatrix[timeframe.id] || {};\n        const hasHighImpact = Object.values(timeframeData).some(rating => rating === 'High');\n        \n        if (hasHighImpact) {\n          highestImpactTimeframe = timeframe.id;\n          break;\n        }\n      }\n\n      if (highestImpactTimeframe) {\n        const rtoMapping = {\n          '0-4h': '4 Hours',\n          '4-8h': '8 Hours', \n          '8-24h': '24 Hours',\n          '24-72h': '72 Hours',\n          '72h+': '1 Week'\n        };\n        \n        const rpoMapping = {\n          '0-4h': '1 Hour',\n          '4-8h': '4 Hours',\n          '8-24h': '8 Hours', \n          '24-72h': '24 Hours',\n          '72h+': '72 Hours'\n        };\n\n        setSuggestedRTO(rtoMapping[highestImpactTimeframe]);\n        setSuggestedRPO(rpoMapping[highestImpactTimeframe]);\n      } else {\n        setSuggestedRTO('1 Week');\n        setSuggestedRPO('72 Hours');\n      }\n    };\n\n    calculateSuggestions();\n  }, [impactMatrix]);\n\n  // Set final values to suggested when suggestions change\n  useEffect(() => {\n    if (suggestedRTO && !finalRTO) {\n      setFinalRTO(suggestedRTO);\n    }\n    if (suggestedRPO && !finalRPO) {\n      setFinalRPO(suggestedRPO);\n    }\n  }, [suggestedRTO, suggestedRPO, finalRTO, finalRPO]);\n\n  // Save data to Redux when values change\n  useEffect(() => {\n    const dataToSave = {\n      impactMatrix,\n      suggestedRTO,\n      suggestedRPO,\n      finalRTO,\n      finalRPO,\n      rtoJustification,\n      rpoJustification\n    };\n    \n    dispatch(updateProcessData({ processId, data: dataToSave }));\n  }, [dispatch, processId, impactMatrix, suggestedRTO, suggestedRPO, finalRTO, finalRPO, rtoJustification, rpoJustification]);\n\n  const handleImpactChange = (category, timeframe, rating) => {\n    setImpactMatrix(prev => ({\n      ...prev,\n      [timeframe]: {\n        ...prev[timeframe],\n        [category]: rating\n      }\n    }));\n  };\n\n  const getImpactValue = (category, timeframe) => {\n    return impactMatrix[timeframe]?.[category] || '';\n  };\n\n  const isJustificationRequired = (type) => {\n    if (type === 'rto') {\n      return finalRTO !== suggestedRTO;\n    }\n    return finalRPO !== suggestedRPO;\n  };\n\n  return (\n    <Grid container spacing={3}>\n      {/* Left Column - Impact Matrix */}\n      <Grid item xs={12} md={7}>\n        <Typography variant=\"h6\" gutterBottom>\n          Impact Assessment Matrix\n        </Typography>\n        \n        <Table size=\"small\" sx={{ border: 1, borderColor: 'divider' }}>\n          <TableHead>\n            <TableRow>\n              <TableCell sx={{ fontWeight: 'bold' }}>Impact Category</TableCell>\n              {TIMEFRAMES.map(timeframe => (\n                <TableCell key={timeframe.id} align=\"center\" sx={{ fontWeight: 'bold', minWidth: 100 }}>\n                  {timeframe.label}\n                </TableCell>\n              ))}\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {IMPACT_CATEGORIES.map(category => (\n              <TableRow key={category}>\n                <TableCell sx={{ fontWeight: 'medium' }}>{category}</TableCell>\n                {TIMEFRAMES.map(timeframe => (\n                  <TableCell key={timeframe.id} align=\"center\">\n                    <Select\n                      size=\"small\"\n                      value={getImpactValue(category, timeframe.id)}\n                      onChange={(e) => handleImpactChange(category, timeframe.id, e.target.value)}\n                      displayEmpty\n                      sx={{ minWidth: 80 }}\n                    >\n                      <MenuItem value=\"\">-</MenuItem>\n                      <MenuItem value=\"Low\">Low</MenuItem>\n                      <MenuItem value=\"Medium\">Medium</MenuItem>\n                      <MenuItem value=\"High\">High</MenuItem>\n                    </Select>\n                  </TableCell>\n                ))}\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </Grid>\n\n      {/* Right Column - Recovery Objectives */}\n      <Grid item xs={12} md={5}>\n        <Typography variant=\"h6\" gutterBottom>\n          Recovery Objectives\n        </Typography>\n\n        {/* RTO Section */}\n        <Box sx={{ mb: 3, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Recovery Time Objective (RTO)\n          </Typography>\n          \n          <TextField\n            fullWidth\n            label=\"Suggested RTO\"\n            value={suggestedRTO}\n            InputProps={{ readOnly: true }}\n            margin=\"normal\"\n            size=\"small\"\n            helperText=\"Auto-calculated from impact matrix\"\n          />\n\n          <FormControl fullWidth margin=\"normal\" size=\"small\">\n            <InputLabel>Final RTO</InputLabel>\n            <Select\n              value={finalRTO}\n              onChange={(e) => setFinalRTO(e.target.value)}\n            >\n              {RTO_OPTIONS.map(option => (\n                <MenuItem key={option} value={option}>{option}</MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n\n          {isJustificationRequired('rto') && (\n            <>\n              <Alert severity=\"warning\" sx={{ mt: 1, mb: 1 }}>\n                Justification required - Final RTO differs from suggested\n              </Alert>\n              <TextField\n                fullWidth\n                multiline\n                rows={3}\n                label=\"RTO Justification\"\n                value={rtoJustification}\n                onChange={(e) => setRtoJustification(e.target.value)}\n                size=\"small\"\n                required\n              />\n            </>\n          )}\n        </Box>\n\n        {/* RPO Section */}\n        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Recovery Point Objective (RPO)\n          </Typography>\n          \n          <TextField\n            fullWidth\n            label=\"Suggested RPO\"\n            value={suggestedRPO}\n            InputProps={{ readOnly: true }}\n            margin=\"normal\"\n            size=\"small\"\n            helperText=\"Auto-calculated from impact matrix\"\n          />\n\n          <FormControl fullWidth margin=\"normal\" size=\"small\">\n            <InputLabel>Final RPO</InputLabel>\n            <Select\n              value={finalRPO}\n              onChange={(e) => setFinalRPO(e.target.value)}\n            >\n              {RPO_OPTIONS.map(option => (\n                <MenuItem key={option} value={option}>{option}</MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n\n          {isJustificationRequired('rpo') && (\n            <>\n              <Alert severity=\"warning\" sx={{ mt: 1, mb: 1 }}>\n                Justification required - Final RPO differs from suggested\n              </Alert>\n              <TextField\n                fullWidth\n                multiline\n                rows={3}\n                label=\"RPO Justification\"\n                value={rpoJustification}\n                onChange={(e) => setRpoJustification(e.target.value)}\n                size=\"small\"\n                required\n              />\n            </>\n          )}\n        </Box>\n      </Grid>\n    </Grid>\n  );\n};\n\nexport default ImpactAssessment;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EAAEC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAClEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAEC,GAAG,QAC3D,eAAe;AACtB,SAASC,iBAAiB,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,MAAMC,iBAAiB,GAAG,CACxB,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,cAAc,EACd,kBAAkB,CACnB;AAED,MAAMC,UAAU,GAAG,CACjB;EAAEC,EAAE,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAY,CAAC,EAClC;EAAED,EAAE,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAY,CAAC,EAClC;EAAED,EAAE,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAa,CAAC,EACpC;EAAED,EAAE,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAc,CAAC,EACtC;EAAED,EAAE,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAY,CAAC,CACnC;AAED,MAAMC,WAAW,GAAG,CAClB,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,EACV,QAAQ,EACR,SAAS,CACV;AAED,MAAMC,WAAW,GAAG,CAClB,QAAQ,EACR,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,CACX;AAED,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC1C,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgC;EAAW,CAAC,GAAG/B,WAAW,CAACgC,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EACtD,MAAMC,WAAW,GAAG,CAAAH,UAAU,aAAVA,UAAU,wBAAAF,qBAAA,GAAVE,UAAU,CAAEG,WAAW,cAAAL,qBAAA,uBAAvBA,qBAAA,CAA0BF,SAAS,CAAC,KAAI,CAAC,CAAC;EAE9D,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAACqC,WAAW,CAACC,YAAY,IAAI,CAAC,CAAC,CAAC;EAChF,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAACqC,WAAW,CAACO,QAAQ,IAAI,EAAE,CAAC;EACpE,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAACqC,WAAW,CAACS,QAAQ,IAAI,EAAE,CAAC;EACpE,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAACqC,WAAW,CAACW,gBAAgB,IAAI,EAAE,CAAC;EAC5F,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAACqC,WAAW,CAACa,gBAAgB,IAAI,EAAE,CAAC;;EAE5F;EACAjD,SAAS,CAAC,MAAM;IACd,MAAMmD,oBAAoB,GAAGA,CAAA,KAAM;MACjC,IAAIC,sBAAsB,GAAG,IAAI;;MAEjC;MACA,KAAK,IAAIC,SAAS,IAAI9B,UAAU,EAAE;QAChC,MAAM+B,aAAa,GAAGjB,YAAY,CAACgB,SAAS,CAAC7B,EAAE,CAAC,IAAI,CAAC,CAAC;QACtD,MAAM+B,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACH,aAAa,CAAC,CAACI,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,MAAM,CAAC;QAEpF,IAAIJ,aAAa,EAAE;UACjBH,sBAAsB,GAAGC,SAAS,CAAC7B,EAAE;UACrC;QACF;MACF;MAEA,IAAI4B,sBAAsB,EAAE;QAC1B,MAAMQ,UAAU,GAAG;UACjB,MAAM,EAAE,SAAS;UACjB,MAAM,EAAE,SAAS;UACjB,OAAO,EAAE,UAAU;UACnB,QAAQ,EAAE,UAAU;UACpB,MAAM,EAAE;QACV,CAAC;QAED,MAAMC,UAAU,GAAG;UACjB,MAAM,EAAE,QAAQ;UAChB,MAAM,EAAE,SAAS;UACjB,OAAO,EAAE,SAAS;UAClB,QAAQ,EAAE,UAAU;UACpB,MAAM,EAAE;QACV,CAAC;QAEDrB,eAAe,CAACoB,UAAU,CAACR,sBAAsB,CAAC,CAAC;QACnDV,eAAe,CAACmB,UAAU,CAACT,sBAAsB,CAAC,CAAC;MACrD,CAAC,MAAM;QACLZ,eAAe,CAAC,QAAQ,CAAC;QACzBE,eAAe,CAAC,UAAU,CAAC;MAC7B;IACF,CAAC;IAEDS,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACd,YAAY,CAAC,CAAC;;EAElB;EACArC,SAAS,CAAC,MAAM;IACd,IAAIuC,YAAY,IAAI,CAACI,QAAQ,EAAE;MAC7BC,WAAW,CAACL,YAAY,CAAC;IAC3B;IACA,IAAIE,YAAY,IAAI,CAACI,QAAQ,EAAE;MAC7BC,WAAW,CAACL,YAAY,CAAC;IAC3B;EACF,CAAC,EAAE,CAACF,YAAY,EAAEE,YAAY,EAAEE,QAAQ,EAAEE,QAAQ,CAAC,CAAC;;EAEpD;EACA7C,SAAS,CAAC,MAAM;IACd,MAAM8D,UAAU,GAAG;MACjBzB,YAAY;MACZE,YAAY;MACZE,YAAY;MACZE,QAAQ;MACRE,QAAQ;MACRE,gBAAgB;MAChBE;IACF,CAAC;IAEDjB,QAAQ,CAACf,iBAAiB,CAAC;MAAEY,SAAS;MAAEkC,IAAI,EAAED;IAAW,CAAC,CAAC,CAAC;EAC9D,CAAC,EAAE,CAAC9B,QAAQ,EAAEH,SAAS,EAAEQ,YAAY,EAAEE,YAAY,EAAEE,YAAY,EAAEE,QAAQ,EAAEE,QAAQ,EAAEE,gBAAgB,EAAEE,gBAAgB,CAAC,CAAC;EAE3H,MAAMe,kBAAkB,GAAGA,CAACC,QAAQ,EAAEZ,SAAS,EAAEM,MAAM,KAAK;IAC1DrB,eAAe,CAAC4B,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACb,SAAS,GAAG;QACX,GAAGa,IAAI,CAACb,SAAS,CAAC;QAClB,CAACY,QAAQ,GAAGN;MACd;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMQ,cAAc,GAAGA,CAACF,QAAQ,EAAEZ,SAAS,KAAK;IAAA,IAAAe,qBAAA;IAC9C,OAAO,EAAAA,qBAAA,GAAA/B,YAAY,CAACgB,SAAS,CAAC,cAAAe,qBAAA,uBAAvBA,qBAAA,CAA0BH,QAAQ,CAAC,KAAI,EAAE;EAClD,CAAC;EAED,MAAMI,uBAAuB,GAAIC,IAAI,IAAK;IACxC,IAAIA,IAAI,KAAK,KAAK,EAAE;MAClB,OAAO3B,QAAQ,KAAKJ,YAAY;IAClC;IACA,OAAOM,QAAQ,KAAKJ,YAAY;EAClC,CAAC;EAED,oBACEtB,OAAA,CAAChB,IAAI;IAACoE,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAC,QAAA,gBAEzBtD,OAAA,CAAChB,IAAI;MAACuE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,gBACvBtD,OAAA,CAACf,UAAU;QAACyE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAL,QAAA,EAAC;MAEtC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/D,OAAA,CAACd,KAAK;QAAC8E,IAAI,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAAb,QAAA,gBAC5DtD,OAAA,CAACX,SAAS;UAAAiE,QAAA,eACRtD,OAAA,CAACV,QAAQ;YAAAgE,QAAA,gBACPtD,OAAA,CAACZ,SAAS;cAAC6E,EAAE,EAAE;gBAAEG,UAAU,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAAe;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACjE3D,UAAU,CAACiE,GAAG,CAACnC,SAAS,iBACvBlC,OAAA,CAACZ,SAAS;cAAoBkF,KAAK,EAAC,QAAQ;cAACL,EAAE,EAAE;gBAAEG,UAAU,EAAE,MAAM;gBAAEG,QAAQ,EAAE;cAAI,CAAE;cAAAjB,QAAA,EACpFpB,SAAS,CAAC5B;YAAK,GADF4B,SAAS,CAAC7B,EAAE;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ/D,OAAA,CAACb,SAAS;UAAAmE,QAAA,EACPnD,iBAAiB,CAACkE,GAAG,CAACvB,QAAQ,iBAC7B9C,OAAA,CAACV,QAAQ;YAAAgE,QAAA,gBACPtD,OAAA,CAACZ,SAAS;cAAC6E,EAAE,EAAE;gBAAEG,UAAU,EAAE;cAAS,CAAE;cAAAd,QAAA,EAAER;YAAQ;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC9D3D,UAAU,CAACiE,GAAG,CAACnC,SAAS,iBACvBlC,OAAA,CAACZ,SAAS;cAAoBkF,KAAK,EAAC,QAAQ;cAAAhB,QAAA,eAC1CtD,OAAA,CAACT,MAAM;gBACLyE,IAAI,EAAC,OAAO;gBACZQ,KAAK,EAAExB,cAAc,CAACF,QAAQ,EAAEZ,SAAS,CAAC7B,EAAE,CAAE;gBAC9CoE,QAAQ,EAAGC,CAAC,IAAK7B,kBAAkB,CAACC,QAAQ,EAAEZ,SAAS,CAAC7B,EAAE,EAAEqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC5EI,YAAY;gBACZX,EAAE,EAAE;kBAAEM,QAAQ,EAAE;gBAAG,CAAE;gBAAAjB,QAAA,gBAErBtD,OAAA,CAACR,QAAQ;kBAACgF,KAAK,EAAC,EAAE;kBAAAlB,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC/B/D,OAAA,CAACR,QAAQ;kBAACgF,KAAK,EAAC,KAAK;kBAAAlB,QAAA,EAAC;gBAAG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpC/D,OAAA,CAACR,QAAQ;kBAACgF,KAAK,EAAC,QAAQ;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1C/D,OAAA,CAACR,QAAQ;kBAACgF,KAAK,EAAC,MAAM;kBAAAlB,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC,GAZK7B,SAAS,CAAC7B,EAAE;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAajB,CACZ,CAAC;UAAA,GAjBWjB,QAAQ;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBb,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGP/D,OAAA,CAAChB,IAAI;MAACuE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,gBACvBtD,OAAA,CAACf,UAAU;QAACyE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAL,QAAA,EAAC;MAEtC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb/D,OAAA,CAACH,GAAG;QAACoE,EAAE,EAAE;UAAEY,EAAE,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEZ,MAAM,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEY,YAAY,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBAC3EtD,OAAA,CAACf,UAAU;UAACyE,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAL,QAAA,EAAC;QAE7C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb/D,OAAA,CAACP,SAAS;UACRuF,SAAS;UACT1E,KAAK,EAAC,eAAe;UACrBkE,KAAK,EAAEpD,YAAa;UACpB6D,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAK,CAAE;UAC/BC,MAAM,EAAC,QAAQ;UACfnB,IAAI,EAAC,OAAO;UACZoB,UAAU,EAAC;QAAoC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAEF/D,OAAA,CAACN,WAAW;UAACsF,SAAS;UAACG,MAAM,EAAC,QAAQ;UAACnB,IAAI,EAAC,OAAO;UAAAV,QAAA,gBACjDtD,OAAA,CAACL,UAAU;YAAA2D,QAAA,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClC/D,OAAA,CAACT,MAAM;YACLiF,KAAK,EAAEhD,QAAS;YAChBiD,QAAQ,EAAGC,CAAC,IAAKjD,WAAW,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAlB,QAAA,EAE5C/C,WAAW,CAAC8D,GAAG,CAACgB,MAAM,iBACrBrF,OAAA,CAACR,QAAQ;cAAcgF,KAAK,EAAEa,MAAO;cAAA/B,QAAA,EAAE+B;YAAM,GAA9BA,MAAM;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEbb,uBAAuB,CAAC,KAAK,CAAC,iBAC7BlD,OAAA,CAAAE,SAAA;UAAAoD,QAAA,gBACEtD,OAAA,CAACJ,KAAK;YAAC0F,QAAQ,EAAC,SAAS;YAACrB,EAAE,EAAE;cAAEsB,EAAE,EAAE,CAAC;cAAEV,EAAE,EAAE;YAAE,CAAE;YAAAvB,QAAA,EAAC;UAEhD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/D,OAAA,CAACP,SAAS;YACRuF,SAAS;YACTQ,SAAS;YACTC,IAAI,EAAE,CAAE;YACRnF,KAAK,EAAC,mBAAmB;YACzBkE,KAAK,EAAE5C,gBAAiB;YACxB6C,QAAQ,EAAGC,CAAC,IAAK7C,mBAAmB,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACrDR,IAAI,EAAC,OAAO;YACZ0B,QAAQ;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACF,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN/D,OAAA,CAACH,GAAG;QAACoE,EAAE,EAAE;UAAEa,CAAC,EAAE,CAAC;UAAEZ,MAAM,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEY,YAAY,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBACpEtD,OAAA,CAACf,UAAU;UAACyE,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAL,QAAA,EAAC;QAE7C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb/D,OAAA,CAACP,SAAS;UACRuF,SAAS;UACT1E,KAAK,EAAC,eAAe;UACrBkE,KAAK,EAAElD,YAAa;UACpB2D,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAK,CAAE;UAC/BC,MAAM,EAAC,QAAQ;UACfnB,IAAI,EAAC,OAAO;UACZoB,UAAU,EAAC;QAAoC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAEF/D,OAAA,CAACN,WAAW;UAACsF,SAAS;UAACG,MAAM,EAAC,QAAQ;UAACnB,IAAI,EAAC,OAAO;UAAAV,QAAA,gBACjDtD,OAAA,CAACL,UAAU;YAAA2D,QAAA,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClC/D,OAAA,CAACT,MAAM;YACLiF,KAAK,EAAE9C,QAAS;YAChB+C,QAAQ,EAAGC,CAAC,IAAK/C,WAAW,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAlB,QAAA,EAE5C9C,WAAW,CAAC6D,GAAG,CAACgB,MAAM,iBACrBrF,OAAA,CAACR,QAAQ;cAAcgF,KAAK,EAAEa,MAAO;cAAA/B,QAAA,EAAE+B;YAAM,GAA9BA,MAAM;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEbb,uBAAuB,CAAC,KAAK,CAAC,iBAC7BlD,OAAA,CAAAE,SAAA;UAAAoD,QAAA,gBACEtD,OAAA,CAACJ,KAAK;YAAC0F,QAAQ,EAAC,SAAS;YAACrB,EAAE,EAAE;cAAEsB,EAAE,EAAE,CAAC;cAAEV,EAAE,EAAE;YAAE,CAAE;YAAAvB,QAAA,EAAC;UAEhD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/D,OAAA,CAACP,SAAS;YACRuF,SAAS;YACTQ,SAAS;YACTC,IAAI,EAAE,CAAE;YACRnF,KAAK,EAAC,mBAAmB;YACzBkE,KAAK,EAAE1C,gBAAiB;YACxB2C,QAAQ,EAAGC,CAAC,IAAK3C,mBAAmB,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACrDR,IAAI,EAAC,OAAO;YACZ0B,QAAQ;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACF,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAACpD,EAAA,CA1PIF,gBAAgB;EAAA,QACH3B,WAAW,EACLC,WAAW;AAAA;AAAA4G,EAAA,GAF9BlF,gBAAgB;AA4PtB,eAAeA,gBAAgB;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}