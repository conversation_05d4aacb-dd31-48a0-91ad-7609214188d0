{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { OperationTypeNode } from '../language/ast.mjs';\nimport { isEqualType, isTypeSubTypeOf } from '../utilities/typeComparators.mjs';\nimport { isEnumType, isInputObjectType, isInputType, isInterfaceType, isNamedType, isNonNullType, isObjectType, isOutputType, isRequiredArgument, isRequiredInputField, isUnionType } from './definition.mjs';\nimport { GraphQLDeprecatedDirective, isDirective } from './directives.mjs';\nimport { isIntrospectionType } from './introspection.mjs';\nimport { assertSchema } from './schema.mjs';\n/**\n * Implements the \"Type Validation\" sub-sections of the specification's\n * \"Type System\" section.\n *\n * Validation runs synchronously, returning an array of encountered errors, or\n * an empty array if no errors were encountered and the Schema is valid.\n */\n\nexport function validateSchema(schema) {\n  // First check to ensure the provided value is in fact a GraphQLSchema.\n  assertSchema(schema); // If this Schema has already been validated, return the previous results.\n\n  if (schema.__validationErrors) {\n    return schema.__validationErrors;\n  } // Validate the schema, producing a list of errors.\n\n  const context = new SchemaValidationContext(schema);\n  validateRootTypes(context);\n  validateDirectives(context);\n  validateTypes(context); // Persist the results of validation before returning to ensure validation\n  // does not run multiple times for this schema.\n\n  const errors = context.getErrors();\n  schema.__validationErrors = errors;\n  return errors;\n}\n/**\n * Utility function which asserts a schema is valid by throwing an error if\n * it is invalid.\n */\n\nexport function assertValidSchema(schema) {\n  const errors = validateSchema(schema);\n  if (errors.length !== 0) {\n    throw new Error(errors.map(error => error.message).join('\\n\\n'));\n  }\n}\nclass SchemaValidationContext {\n  constructor(schema) {\n    this._errors = [];\n    this.schema = schema;\n  }\n  reportError(message, nodes) {\n    const _nodes = Array.isArray(nodes) ? nodes.filter(Boolean) : nodes;\n    this._errors.push(new GraphQLError(message, {\n      nodes: _nodes\n    }));\n  }\n  getErrors() {\n    return this._errors;\n  }\n}\nfunction validateRootTypes(context) {\n  const schema = context.schema;\n  const queryType = schema.getQueryType();\n  if (!queryType) {\n    context.reportError('Query root type must be provided.', schema.astNode);\n  } else if (!isObjectType(queryType)) {\n    var _getOperationTypeNode;\n    context.reportError(`Query root type must be Object type, it cannot be ${inspect(queryType)}.`, (_getOperationTypeNode = getOperationTypeNode(schema, OperationTypeNode.QUERY)) !== null && _getOperationTypeNode !== void 0 ? _getOperationTypeNode : queryType.astNode);\n  }\n  const mutationType = schema.getMutationType();\n  if (mutationType && !isObjectType(mutationType)) {\n    var _getOperationTypeNode2;\n    context.reportError('Mutation root type must be Object type if provided, it cannot be ' + `${inspect(mutationType)}.`, (_getOperationTypeNode2 = getOperationTypeNode(schema, OperationTypeNode.MUTATION)) !== null && _getOperationTypeNode2 !== void 0 ? _getOperationTypeNode2 : mutationType.astNode);\n  }\n  const subscriptionType = schema.getSubscriptionType();\n  if (subscriptionType && !isObjectType(subscriptionType)) {\n    var _getOperationTypeNode3;\n    context.reportError('Subscription root type must be Object type if provided, it cannot be ' + `${inspect(subscriptionType)}.`, (_getOperationTypeNode3 = getOperationTypeNode(schema, OperationTypeNode.SUBSCRIPTION)) !== null && _getOperationTypeNode3 !== void 0 ? _getOperationTypeNode3 : subscriptionType.astNode);\n  }\n}\nfunction getOperationTypeNode(schema, operation) {\n  var _flatMap$find;\n  return (_flatMap$find = [schema.astNode, ...schema.extensionASTNodes].flatMap(\n  // FIXME: https://github.com/graphql/graphql-js/issues/2203\n  schemaNode => {\n    var _schemaNode$operation;\n    return /* c8 ignore next */(_schemaNode$operation = schemaNode === null || schemaNode === void 0 ? void 0 : schemaNode.operationTypes) !== null && _schemaNode$operation !== void 0 ? _schemaNode$operation : [];\n  }).find(operationNode => operationNode.operation === operation)) === null || _flatMap$find === void 0 ? void 0 : _flatMap$find.type;\n}\nfunction validateDirectives(context) {\n  for (const directive of context.schema.getDirectives()) {\n    // Ensure all directives are in fact GraphQL directives.\n    if (!isDirective(directive)) {\n      context.reportError(`Expected directive but got: ${inspect(directive)}.`, directive === null || directive === void 0 ? void 0 : directive.astNode);\n      continue;\n    } // Ensure they are named correctly.\n\n    validateName(context, directive);\n    if (directive.locations.length === 0) {\n      context.reportError(`Directive @${directive.name} must include 1 or more locations.`, directive.astNode);\n    } // Ensure the arguments are valid.\n\n    for (const arg of directive.args) {\n      // Ensure they are named correctly.\n      validateName(context, arg); // Ensure the type is an input type.\n\n      if (!isInputType(arg.type)) {\n        context.reportError(`The type of @${directive.name}(${arg.name}:) must be Input Type ` + `but got: ${inspect(arg.type)}.`, arg.astNode);\n      }\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode;\n        context.reportError(`Required argument @${directive.name}(${arg.name}:) cannot be deprecated.`, [getDeprecatedDirectiveNode(arg.astNode), (_arg$astNode = arg.astNode) === null || _arg$astNode === void 0 ? void 0 : _arg$astNode.type]);\n      }\n    }\n  }\n}\nfunction validateName(context, node) {\n  // Ensure names are valid, however introspection types opt out.\n  if (node.name.startsWith('__')) {\n    context.reportError(`Name \"${node.name}\" must not begin with \"__\", which is reserved by GraphQL introspection.`, node.astNode);\n  }\n}\nfunction validateTypes(context) {\n  const validateInputObjectCircularRefs = createInputObjectCircularRefsValidator(context);\n  const typeMap = context.schema.getTypeMap();\n  for (const type of Object.values(typeMap)) {\n    // Ensure all provided types are in fact GraphQL type.\n    if (!isNamedType(type)) {\n      context.reportError(`Expected GraphQL named type but got: ${inspect(type)}.`, type.astNode);\n      continue;\n    } // Ensure it is named correctly (excluding introspection types).\n\n    if (!isIntrospectionType(type)) {\n      validateName(context, type);\n    }\n    if (isObjectType(type)) {\n      // Ensure fields are valid\n      validateFields(context, type); // Ensure objects implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isInterfaceType(type)) {\n      // Ensure fields are valid.\n      validateFields(context, type); // Ensure interfaces implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isUnionType(type)) {\n      // Ensure Unions include valid member types.\n      validateUnionMembers(context, type);\n    } else if (isEnumType(type)) {\n      // Ensure Enums have valid values.\n      validateEnumValues(context, type);\n    } else if (isInputObjectType(type)) {\n      // Ensure Input Object fields are valid.\n      validateInputFields(context, type); // Ensure Input Objects do not contain non-nullable circular references\n\n      validateInputObjectCircularRefs(type);\n    }\n  }\n}\nfunction validateFields(context, type) {\n  const fields = Object.values(type.getFields()); // Objects and Interfaces both must define one or more fields.\n\n  if (fields.length === 0) {\n    context.reportError(`Type ${type.name} must define one or more fields.`, [type.astNode, ...type.extensionASTNodes]);\n  }\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an output type\n\n    if (!isOutputType(field.type)) {\n      var _field$astNode;\n      context.reportError(`The type of ${type.name}.${field.name} must be Output Type ` + `but got: ${inspect(field.type)}.`, (_field$astNode = field.astNode) === null || _field$astNode === void 0 ? void 0 : _field$astNode.type);\n    } // Ensure the arguments are valid\n\n    for (const arg of field.args) {\n      const argName = arg.name; // Ensure they are named correctly.\n\n      validateName(context, arg); // Ensure the type is an input type\n\n      if (!isInputType(arg.type)) {\n        var _arg$astNode2;\n        context.reportError(`The type of ${type.name}.${field.name}(${argName}:) must be Input ` + `Type but got: ${inspect(arg.type)}.`, (_arg$astNode2 = arg.astNode) === null || _arg$astNode2 === void 0 ? void 0 : _arg$astNode2.type);\n      }\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode3;\n        context.reportError(`Required argument ${type.name}.${field.name}(${argName}:) cannot be deprecated.`, [getDeprecatedDirectiveNode(arg.astNode), (_arg$astNode3 = arg.astNode) === null || _arg$astNode3 === void 0 ? void 0 : _arg$astNode3.type]);\n      }\n    }\n  }\n}\nfunction validateInterfaces(context, type) {\n  const ifaceTypeNames = Object.create(null);\n  for (const iface of type.getInterfaces()) {\n    if (!isInterfaceType(iface)) {\n      context.reportError(`Type ${inspect(type)} must only implement Interface types, ` + `it cannot implement ${inspect(iface)}.`, getAllImplementsInterfaceNodes(type, iface));\n      continue;\n    }\n    if (type === iface) {\n      context.reportError(`Type ${type.name} cannot implement itself because it would create a circular reference.`, getAllImplementsInterfaceNodes(type, iface));\n      continue;\n    }\n    if (ifaceTypeNames[iface.name]) {\n      context.reportError(`Type ${type.name} can only implement ${iface.name} once.`, getAllImplementsInterfaceNodes(type, iface));\n      continue;\n    }\n    ifaceTypeNames[iface.name] = true;\n    validateTypeImplementsAncestors(context, type, iface);\n    validateTypeImplementsInterface(context, type, iface);\n  }\n}\nfunction validateTypeImplementsInterface(context, type, iface) {\n  const typeFieldMap = type.getFields(); // Assert each interface field is implemented.\n\n  for (const ifaceField of Object.values(iface.getFields())) {\n    const fieldName = ifaceField.name;\n    const typeField = typeFieldMap[fieldName]; // Assert interface field exists on type.\n\n    if (!typeField) {\n      context.reportError(`Interface field ${iface.name}.${fieldName} expected but ${type.name} does not provide it.`, [ifaceField.astNode, type.astNode, ...type.extensionASTNodes]);\n      continue;\n    } // Assert interface field type is satisfied by type field type, by being\n    // a valid subtype. (covariant)\n\n    if (!isTypeSubTypeOf(context.schema, typeField.type, ifaceField.type)) {\n      var _ifaceField$astNode, _typeField$astNode;\n      context.reportError(`Interface field ${iface.name}.${fieldName} expects type ` + `${inspect(ifaceField.type)} but ${type.name}.${fieldName} ` + `is type ${inspect(typeField.type)}.`, [(_ifaceField$astNode = ifaceField.astNode) === null || _ifaceField$astNode === void 0 ? void 0 : _ifaceField$astNode.type, (_typeField$astNode = typeField.astNode) === null || _typeField$astNode === void 0 ? void 0 : _typeField$astNode.type]);\n    } // Assert each interface field arg is implemented.\n\n    for (const ifaceArg of ifaceField.args) {\n      const argName = ifaceArg.name;\n      const typeArg = typeField.args.find(arg => arg.name === argName); // Assert interface field arg exists on object field.\n\n      if (!typeArg) {\n        context.reportError(`Interface field argument ${iface.name}.${fieldName}(${argName}:) expected but ${type.name}.${fieldName} does not provide it.`, [ifaceArg.astNode, typeField.astNode]);\n        continue;\n      } // Assert interface field arg type matches object field arg type.\n      // (invariant)\n      // TODO: change to contravariant?\n\n      if (!isEqualType(ifaceArg.type, typeArg.type)) {\n        var _ifaceArg$astNode, _typeArg$astNode;\n        context.reportError(`Interface field argument ${iface.name}.${fieldName}(${argName}:) ` + `expects type ${inspect(ifaceArg.type)} but ` + `${type.name}.${fieldName}(${argName}:) is type ` + `${inspect(typeArg.type)}.`, [(_ifaceArg$astNode = ifaceArg.astNode) === null || _ifaceArg$astNode === void 0 ? void 0 : _ifaceArg$astNode.type, (_typeArg$astNode = typeArg.astNode) === null || _typeArg$astNode === void 0 ? void 0 : _typeArg$astNode.type]);\n      } // TODO: validate default values?\n    } // Assert additional arguments must not be required.\n\n    for (const typeArg of typeField.args) {\n      const argName = typeArg.name;\n      const ifaceArg = ifaceField.args.find(arg => arg.name === argName);\n      if (!ifaceArg && isRequiredArgument(typeArg)) {\n        context.reportError(`Object field ${type.name}.${fieldName} includes required argument ${argName} that is missing from the Interface field ${iface.name}.${fieldName}.`, [typeArg.astNode, ifaceField.astNode]);\n      }\n    }\n  }\n}\nfunction validateTypeImplementsAncestors(context, type, iface) {\n  const ifaceInterfaces = type.getInterfaces();\n  for (const transitive of iface.getInterfaces()) {\n    if (!ifaceInterfaces.includes(transitive)) {\n      context.reportError(transitive === type ? `Type ${type.name} cannot implement ${iface.name} because it would create a circular reference.` : `Type ${type.name} must implement ${transitive.name} because it is implemented by ${iface.name}.`, [...getAllImplementsInterfaceNodes(iface, transitive), ...getAllImplementsInterfaceNodes(type, iface)]);\n    }\n  }\n}\nfunction validateUnionMembers(context, union) {\n  const memberTypes = union.getTypes();\n  if (memberTypes.length === 0) {\n    context.reportError(`Union type ${union.name} must define one or more member types.`, [union.astNode, ...union.extensionASTNodes]);\n  }\n  const includedTypeNames = Object.create(null);\n  for (const memberType of memberTypes) {\n    if (includedTypeNames[memberType.name]) {\n      context.reportError(`Union type ${union.name} can only include type ${memberType.name} once.`, getUnionMemberTypeNodes(union, memberType.name));\n      continue;\n    }\n    includedTypeNames[memberType.name] = true;\n    if (!isObjectType(memberType)) {\n      context.reportError(`Union type ${union.name} can only include Object types, ` + `it cannot include ${inspect(memberType)}.`, getUnionMemberTypeNodes(union, String(memberType)));\n    }\n  }\n}\nfunction validateEnumValues(context, enumType) {\n  const enumValues = enumType.getValues();\n  if (enumValues.length === 0) {\n    context.reportError(`Enum type ${enumType.name} must define one or more values.`, [enumType.astNode, ...enumType.extensionASTNodes]);\n  }\n  for (const enumValue of enumValues) {\n    // Ensure valid name.\n    validateName(context, enumValue);\n  }\n}\nfunction validateInputFields(context, inputObj) {\n  const fields = Object.values(inputObj.getFields());\n  if (fields.length === 0) {\n    context.reportError(`Input Object type ${inputObj.name} must define one or more fields.`, [inputObj.astNode, ...inputObj.extensionASTNodes]);\n  } // Ensure the arguments are valid\n\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an input type\n\n    if (!isInputType(field.type)) {\n      var _field$astNode2;\n      context.reportError(`The type of ${inputObj.name}.${field.name} must be Input Type ` + `but got: ${inspect(field.type)}.`, (_field$astNode2 = field.astNode) === null || _field$astNode2 === void 0 ? void 0 : _field$astNode2.type);\n    }\n    if (isRequiredInputField(field) && field.deprecationReason != null) {\n      var _field$astNode3;\n      context.reportError(`Required input field ${inputObj.name}.${field.name} cannot be deprecated.`, [getDeprecatedDirectiveNode(field.astNode), (_field$astNode3 = field.astNode) === null || _field$astNode3 === void 0 ? void 0 : _field$astNode3.type]);\n    }\n    if (inputObj.isOneOf) {\n      validateOneOfInputObjectField(inputObj, field, context);\n    }\n  }\n}\nfunction validateOneOfInputObjectField(type, field, context) {\n  if (isNonNullType(field.type)) {\n    var _field$astNode4;\n    context.reportError(`OneOf input field ${type.name}.${field.name} must be nullable.`, (_field$astNode4 = field.astNode) === null || _field$astNode4 === void 0 ? void 0 : _field$astNode4.type);\n  }\n  if (field.defaultValue !== undefined) {\n    context.reportError(`OneOf input field ${type.name}.${field.name} cannot have a default value.`, field.astNode);\n  }\n}\nfunction createInputObjectCircularRefsValidator(context) {\n  // Modified copy of algorithm from 'src/validation/rules/NoFragmentCycles.js'.\n  // Tracks already visited types to maintain O(N) and to ensure that cycles\n  // are not redundantly reported.\n  const visitedTypes = Object.create(null); // Array of types nodes used to produce meaningful errors\n\n  const fieldPath = []; // Position in the type path\n\n  const fieldPathIndexByTypeName = Object.create(null);\n  return detectCycleRecursive; // This does a straight-forward DFS to find cycles.\n  // It does not terminate when a cycle was found but continues to explore\n  // the graph to find all possible cycles.\n\n  function detectCycleRecursive(inputObj) {\n    if (visitedTypes[inputObj.name]) {\n      return;\n    }\n    visitedTypes[inputObj.name] = true;\n    fieldPathIndexByTypeName[inputObj.name] = fieldPath.length;\n    const fields = Object.values(inputObj.getFields());\n    for (const field of fields) {\n      if (isNonNullType(field.type) && isInputObjectType(field.type.ofType)) {\n        const fieldType = field.type.ofType;\n        const cycleIndex = fieldPathIndexByTypeName[fieldType.name];\n        fieldPath.push(field);\n        if (cycleIndex === undefined) {\n          detectCycleRecursive(fieldType);\n        } else {\n          const cyclePath = fieldPath.slice(cycleIndex);\n          const pathStr = cyclePath.map(fieldObj => fieldObj.name).join('.');\n          context.reportError(`Cannot reference Input Object \"${fieldType.name}\" within itself through a series of non-null fields: \"${pathStr}\".`, cyclePath.map(fieldObj => fieldObj.astNode));\n        }\n        fieldPath.pop();\n      }\n    }\n    fieldPathIndexByTypeName[inputObj.name] = undefined;\n  }\n}\nfunction getAllImplementsInterfaceNodes(type, iface) {\n  const {\n    astNode,\n    extensionASTNodes\n  } = type;\n  const nodes = astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes.flatMap(typeNode => {\n    var _typeNode$interfaces;\n    return /* c8 ignore next */(_typeNode$interfaces = typeNode.interfaces) !== null && _typeNode$interfaces !== void 0 ? _typeNode$interfaces : [];\n  }).filter(ifaceNode => ifaceNode.name.value === iface.name);\n}\nfunction getUnionMemberTypeNodes(union, typeName) {\n  const {\n    astNode,\n    extensionASTNodes\n  } = union;\n  const nodes = astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes.flatMap(unionNode => {\n    var _unionNode$types;\n    return /* c8 ignore next */(_unionNode$types = unionNode.types) !== null && _unionNode$types !== void 0 ? _unionNode$types : [];\n  }).filter(typeNode => typeNode.name.value === typeName);\n}\nfunction getDeprecatedDirectiveNode(definitionNode) {\n  var _definitionNode$direc;\n  return definitionNode === null || definitionNode === void 0 ? void 0 : (_definitionNode$direc = definitionNode.directives) === null || _definitionNode$direc === void 0 ? void 0 : _definitionNode$direc.find(node => node.name.value === GraphQLDeprecatedDirective.name);\n}", "map": {"version": 3, "names": ["inspect", "GraphQLError", "OperationTypeNode", "isEqualType", "isTypeSubTypeOf", "isEnumType", "isInputObjectType", "isInputType", "isInterfaceType", "isNamedType", "isNonNullType", "isObjectType", "isOutputType", "isRequiredArgument", "isRequiredInputField", "isUnionType", "GraphQLDeprecatedDirective", "isDirective", "isIntrospectionType", "assertSchema", "validateSchema", "schema", "__validationErrors", "context", "SchemaValidationContext", "validateRootTypes", "validateDirectives", "validateTypes", "errors", "getErrors", "assertValidSchema", "length", "Error", "map", "error", "message", "join", "constructor", "_errors", "reportError", "nodes", "_nodes", "Array", "isArray", "filter", "Boolean", "push", "queryType", "getQueryType", "astNode", "_getOperationTypeNode", "getOperationTypeNode", "QUERY", "mutationType", "getMutationType", "_getOperationTypeNode2", "MUTATION", "subscriptionType", "getSubscriptionType", "_getOperationTypeNode3", "SUBSCRIPTION", "operation", "_flatMap$find", "extensionASTNodes", "flatMap", "schemaNode", "_schemaNode$operation", "operationTypes", "find", "operationNode", "type", "directive", "getDirectives", "validateName", "locations", "name", "arg", "args", "deprecationReason", "_arg$astNode", "getDeprecatedDirectiveNode", "node", "startsWith", "validateInputObjectCircularRefs", "createInputObjectCircularRefsValidator", "typeMap", "getTypeMap", "Object", "values", "validateFields", "validateInterfaces", "validateUnionMembers", "validateEnumValues", "validateInputFields", "fields", "getFields", "field", "_field$astNode", "argName", "_arg$astNode2", "_arg$astNode3", "ifaceTypeNames", "create", "iface", "getInterfaces", "getAllImplementsInterfaceNodes", "validateTypeImplementsAncestors", "validateTypeImplementsInterface", "typeFieldMap", "ifaceField", "fieldName", "typeField", "_ifaceField$astNode", "_typeField$astNode", "ifaceArg", "typeArg", "_ifaceArg$astNode", "_typeArg$astNode", "ifaceInterfaces", "transitive", "includes", "union", "memberTypes", "getTypes", "includedTypeNames", "memberType", "getUnionMemberTypeNodes", "String", "enumType", "enum<PERSON><PERSON><PERSON>", "getV<PERSON>ues", "enumValue", "inputObj", "_field$astNode2", "_field$astNode3", "isOneOf", "validateOneOfInputObjectField", "_field$astNode4", "defaultValue", "undefined", "visitedTypes", "fieldPath", "fieldPathIndexByTypeName", "detectCycleRecursive", "ofType", "fieldType", "cycleIndex", "cyclePath", "slice", "pathStr", "field<PERSON>bj", "pop", "typeNode", "_typeNode$interfaces", "interfaces", "ifaceNode", "value", "typeName", "unionNode", "_unionNode$types", "types", "definitionNode", "_definitionNode$direc", "directives"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/type/validate.mjs"], "sourcesContent": ["import { inspect } from '../jsutils/inspect.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { OperationTypeNode } from '../language/ast.mjs';\nimport { isEqualType, isTypeSubTypeOf } from '../utilities/typeComparators.mjs';\nimport {\n  isEnumType,\n  isInputObjectType,\n  isInputType,\n  isInterfaceType,\n  isNamedType,\n  isNonNullType,\n  isObjectType,\n  isOutputType,\n  isRequiredArgument,\n  isRequiredInputField,\n  isUnionType,\n} from './definition.mjs';\nimport { GraphQLDeprecatedDirective, isDirective } from './directives.mjs';\nimport { isIntrospectionType } from './introspection.mjs';\nimport { assertSchema } from './schema.mjs';\n/**\n * Implements the \"Type Validation\" sub-sections of the specification's\n * \"Type System\" section.\n *\n * Validation runs synchronously, returning an array of encountered errors, or\n * an empty array if no errors were encountered and the Schema is valid.\n */\n\nexport function validateSchema(schema) {\n  // First check to ensure the provided value is in fact a GraphQLSchema.\n  assertSchema(schema); // If this Schema has already been validated, return the previous results.\n\n  if (schema.__validationErrors) {\n    return schema.__validationErrors;\n  } // Validate the schema, producing a list of errors.\n\n  const context = new SchemaValidationContext(schema);\n  validateRootTypes(context);\n  validateDirectives(context);\n  validateTypes(context); // Persist the results of validation before returning to ensure validation\n  // does not run multiple times for this schema.\n\n  const errors = context.getErrors();\n  schema.__validationErrors = errors;\n  return errors;\n}\n/**\n * Utility function which asserts a schema is valid by throwing an error if\n * it is invalid.\n */\n\nexport function assertValidSchema(schema) {\n  const errors = validateSchema(schema);\n\n  if (errors.length !== 0) {\n    throw new Error(errors.map((error) => error.message).join('\\n\\n'));\n  }\n}\n\nclass SchemaValidationContext {\n  constructor(schema) {\n    this._errors = [];\n    this.schema = schema;\n  }\n\n  reportError(message, nodes) {\n    const _nodes = Array.isArray(nodes) ? nodes.filter(Boolean) : nodes;\n\n    this._errors.push(\n      new GraphQLError(message, {\n        nodes: _nodes,\n      }),\n    );\n  }\n\n  getErrors() {\n    return this._errors;\n  }\n}\n\nfunction validateRootTypes(context) {\n  const schema = context.schema;\n  const queryType = schema.getQueryType();\n\n  if (!queryType) {\n    context.reportError('Query root type must be provided.', schema.astNode);\n  } else if (!isObjectType(queryType)) {\n    var _getOperationTypeNode;\n\n    context.reportError(\n      `Query root type must be Object type, it cannot be ${inspect(\n        queryType,\n      )}.`,\n      (_getOperationTypeNode = getOperationTypeNode(\n        schema,\n        OperationTypeNode.QUERY,\n      )) !== null && _getOperationTypeNode !== void 0\n        ? _getOperationTypeNode\n        : queryType.astNode,\n    );\n  }\n\n  const mutationType = schema.getMutationType();\n\n  if (mutationType && !isObjectType(mutationType)) {\n    var _getOperationTypeNode2;\n\n    context.reportError(\n      'Mutation root type must be Object type if provided, it cannot be ' +\n        `${inspect(mutationType)}.`,\n      (_getOperationTypeNode2 = getOperationTypeNode(\n        schema,\n        OperationTypeNode.MUTATION,\n      )) !== null && _getOperationTypeNode2 !== void 0\n        ? _getOperationTypeNode2\n        : mutationType.astNode,\n    );\n  }\n\n  const subscriptionType = schema.getSubscriptionType();\n\n  if (subscriptionType && !isObjectType(subscriptionType)) {\n    var _getOperationTypeNode3;\n\n    context.reportError(\n      'Subscription root type must be Object type if provided, it cannot be ' +\n        `${inspect(subscriptionType)}.`,\n      (_getOperationTypeNode3 = getOperationTypeNode(\n        schema,\n        OperationTypeNode.SUBSCRIPTION,\n      )) !== null && _getOperationTypeNode3 !== void 0\n        ? _getOperationTypeNode3\n        : subscriptionType.astNode,\n    );\n  }\n}\n\nfunction getOperationTypeNode(schema, operation) {\n  var _flatMap$find;\n\n  return (_flatMap$find = [schema.astNode, ...schema.extensionASTNodes]\n    .flatMap(\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      (schemaNode) => {\n        var _schemaNode$operation;\n\n        return (\n          /* c8 ignore next */\n          (_schemaNode$operation =\n            schemaNode === null || schemaNode === void 0\n              ? void 0\n              : schemaNode.operationTypes) !== null &&\n            _schemaNode$operation !== void 0\n            ? _schemaNode$operation\n            : []\n        );\n      },\n    )\n    .find((operationNode) => operationNode.operation === operation)) === null ||\n    _flatMap$find === void 0\n    ? void 0\n    : _flatMap$find.type;\n}\n\nfunction validateDirectives(context) {\n  for (const directive of context.schema.getDirectives()) {\n    // Ensure all directives are in fact GraphQL directives.\n    if (!isDirective(directive)) {\n      context.reportError(\n        `Expected directive but got: ${inspect(directive)}.`,\n        directive === null || directive === void 0 ? void 0 : directive.astNode,\n      );\n      continue;\n    } // Ensure they are named correctly.\n\n    validateName(context, directive);\n\n    if (directive.locations.length === 0) {\n      context.reportError(\n        `Directive @${directive.name} must include 1 or more locations.`,\n        directive.astNode,\n      );\n    } // Ensure the arguments are valid.\n\n    for (const arg of directive.args) {\n      // Ensure they are named correctly.\n      validateName(context, arg); // Ensure the type is an input type.\n\n      if (!isInputType(arg.type)) {\n        context.reportError(\n          `The type of @${directive.name}(${arg.name}:) must be Input Type ` +\n            `but got: ${inspect(arg.type)}.`,\n          arg.astNode,\n        );\n      }\n\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode;\n\n        context.reportError(\n          `Required argument @${directive.name}(${arg.name}:) cannot be deprecated.`,\n          [\n            getDeprecatedDirectiveNode(arg.astNode),\n            (_arg$astNode = arg.astNode) === null || _arg$astNode === void 0\n              ? void 0\n              : _arg$astNode.type,\n          ],\n        );\n      }\n    }\n  }\n}\n\nfunction validateName(context, node) {\n  // Ensure names are valid, however introspection types opt out.\n  if (node.name.startsWith('__')) {\n    context.reportError(\n      `Name \"${node.name}\" must not begin with \"__\", which is reserved by GraphQL introspection.`,\n      node.astNode,\n    );\n  }\n}\n\nfunction validateTypes(context) {\n  const validateInputObjectCircularRefs =\n    createInputObjectCircularRefsValidator(context);\n  const typeMap = context.schema.getTypeMap();\n\n  for (const type of Object.values(typeMap)) {\n    // Ensure all provided types are in fact GraphQL type.\n    if (!isNamedType(type)) {\n      context.reportError(\n        `Expected GraphQL named type but got: ${inspect(type)}.`,\n        type.astNode,\n      );\n      continue;\n    } // Ensure it is named correctly (excluding introspection types).\n\n    if (!isIntrospectionType(type)) {\n      validateName(context, type);\n    }\n\n    if (isObjectType(type)) {\n      // Ensure fields are valid\n      validateFields(context, type); // Ensure objects implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isInterfaceType(type)) {\n      // Ensure fields are valid.\n      validateFields(context, type); // Ensure interfaces implement the interfaces they claim to.\n\n      validateInterfaces(context, type);\n    } else if (isUnionType(type)) {\n      // Ensure Unions include valid member types.\n      validateUnionMembers(context, type);\n    } else if (isEnumType(type)) {\n      // Ensure Enums have valid values.\n      validateEnumValues(context, type);\n    } else if (isInputObjectType(type)) {\n      // Ensure Input Object fields are valid.\n      validateInputFields(context, type); // Ensure Input Objects do not contain non-nullable circular references\n\n      validateInputObjectCircularRefs(type);\n    }\n  }\n}\n\nfunction validateFields(context, type) {\n  const fields = Object.values(type.getFields()); // Objects and Interfaces both must define one or more fields.\n\n  if (fields.length === 0) {\n    context.reportError(`Type ${type.name} must define one or more fields.`, [\n      type.astNode,\n      ...type.extensionASTNodes,\n    ]);\n  }\n\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an output type\n\n    if (!isOutputType(field.type)) {\n      var _field$astNode;\n\n      context.reportError(\n        `The type of ${type.name}.${field.name} must be Output Type ` +\n          `but got: ${inspect(field.type)}.`,\n        (_field$astNode = field.astNode) === null || _field$astNode === void 0\n          ? void 0\n          : _field$astNode.type,\n      );\n    } // Ensure the arguments are valid\n\n    for (const arg of field.args) {\n      const argName = arg.name; // Ensure they are named correctly.\n\n      validateName(context, arg); // Ensure the type is an input type\n\n      if (!isInputType(arg.type)) {\n        var _arg$astNode2;\n\n        context.reportError(\n          `The type of ${type.name}.${field.name}(${argName}:) must be Input ` +\n            `Type but got: ${inspect(arg.type)}.`,\n          (_arg$astNode2 = arg.astNode) === null || _arg$astNode2 === void 0\n            ? void 0\n            : _arg$astNode2.type,\n        );\n      }\n\n      if (isRequiredArgument(arg) && arg.deprecationReason != null) {\n        var _arg$astNode3;\n\n        context.reportError(\n          `Required argument ${type.name}.${field.name}(${argName}:) cannot be deprecated.`,\n          [\n            getDeprecatedDirectiveNode(arg.astNode),\n            (_arg$astNode3 = arg.astNode) === null || _arg$astNode3 === void 0\n              ? void 0\n              : _arg$astNode3.type,\n          ],\n        );\n      }\n    }\n  }\n}\n\nfunction validateInterfaces(context, type) {\n  const ifaceTypeNames = Object.create(null);\n\n  for (const iface of type.getInterfaces()) {\n    if (!isInterfaceType(iface)) {\n      context.reportError(\n        `Type ${inspect(type)} must only implement Interface types, ` +\n          `it cannot implement ${inspect(iface)}.`,\n        getAllImplementsInterfaceNodes(type, iface),\n      );\n      continue;\n    }\n\n    if (type === iface) {\n      context.reportError(\n        `Type ${type.name} cannot implement itself because it would create a circular reference.`,\n        getAllImplementsInterfaceNodes(type, iface),\n      );\n      continue;\n    }\n\n    if (ifaceTypeNames[iface.name]) {\n      context.reportError(\n        `Type ${type.name} can only implement ${iface.name} once.`,\n        getAllImplementsInterfaceNodes(type, iface),\n      );\n      continue;\n    }\n\n    ifaceTypeNames[iface.name] = true;\n    validateTypeImplementsAncestors(context, type, iface);\n    validateTypeImplementsInterface(context, type, iface);\n  }\n}\n\nfunction validateTypeImplementsInterface(context, type, iface) {\n  const typeFieldMap = type.getFields(); // Assert each interface field is implemented.\n\n  for (const ifaceField of Object.values(iface.getFields())) {\n    const fieldName = ifaceField.name;\n    const typeField = typeFieldMap[fieldName]; // Assert interface field exists on type.\n\n    if (!typeField) {\n      context.reportError(\n        `Interface field ${iface.name}.${fieldName} expected but ${type.name} does not provide it.`,\n        [ifaceField.astNode, type.astNode, ...type.extensionASTNodes],\n      );\n      continue;\n    } // Assert interface field type is satisfied by type field type, by being\n    // a valid subtype. (covariant)\n\n    if (!isTypeSubTypeOf(context.schema, typeField.type, ifaceField.type)) {\n      var _ifaceField$astNode, _typeField$astNode;\n\n      context.reportError(\n        `Interface field ${iface.name}.${fieldName} expects type ` +\n          `${inspect(ifaceField.type)} but ${type.name}.${fieldName} ` +\n          `is type ${inspect(typeField.type)}.`,\n        [\n          (_ifaceField$astNode = ifaceField.astNode) === null ||\n          _ifaceField$astNode === void 0\n            ? void 0\n            : _ifaceField$astNode.type,\n          (_typeField$astNode = typeField.astNode) === null ||\n          _typeField$astNode === void 0\n            ? void 0\n            : _typeField$astNode.type,\n        ],\n      );\n    } // Assert each interface field arg is implemented.\n\n    for (const ifaceArg of ifaceField.args) {\n      const argName = ifaceArg.name;\n      const typeArg = typeField.args.find((arg) => arg.name === argName); // Assert interface field arg exists on object field.\n\n      if (!typeArg) {\n        context.reportError(\n          `Interface field argument ${iface.name}.${fieldName}(${argName}:) expected but ${type.name}.${fieldName} does not provide it.`,\n          [ifaceArg.astNode, typeField.astNode],\n        );\n        continue;\n      } // Assert interface field arg type matches object field arg type.\n      // (invariant)\n      // TODO: change to contravariant?\n\n      if (!isEqualType(ifaceArg.type, typeArg.type)) {\n        var _ifaceArg$astNode, _typeArg$astNode;\n\n        context.reportError(\n          `Interface field argument ${iface.name}.${fieldName}(${argName}:) ` +\n            `expects type ${inspect(ifaceArg.type)} but ` +\n            `${type.name}.${fieldName}(${argName}:) is type ` +\n            `${inspect(typeArg.type)}.`,\n          [\n            (_ifaceArg$astNode = ifaceArg.astNode) === null ||\n            _ifaceArg$astNode === void 0\n              ? void 0\n              : _ifaceArg$astNode.type,\n            (_typeArg$astNode = typeArg.astNode) === null ||\n            _typeArg$astNode === void 0\n              ? void 0\n              : _typeArg$astNode.type,\n          ],\n        );\n      } // TODO: validate default values?\n    } // Assert additional arguments must not be required.\n\n    for (const typeArg of typeField.args) {\n      const argName = typeArg.name;\n      const ifaceArg = ifaceField.args.find((arg) => arg.name === argName);\n\n      if (!ifaceArg && isRequiredArgument(typeArg)) {\n        context.reportError(\n          `Object field ${type.name}.${fieldName} includes required argument ${argName} that is missing from the Interface field ${iface.name}.${fieldName}.`,\n          [typeArg.astNode, ifaceField.astNode],\n        );\n      }\n    }\n  }\n}\n\nfunction validateTypeImplementsAncestors(context, type, iface) {\n  const ifaceInterfaces = type.getInterfaces();\n\n  for (const transitive of iface.getInterfaces()) {\n    if (!ifaceInterfaces.includes(transitive)) {\n      context.reportError(\n        transitive === type\n          ? `Type ${type.name} cannot implement ${iface.name} because it would create a circular reference.`\n          : `Type ${type.name} must implement ${transitive.name} because it is implemented by ${iface.name}.`,\n        [\n          ...getAllImplementsInterfaceNodes(iface, transitive),\n          ...getAllImplementsInterfaceNodes(type, iface),\n        ],\n      );\n    }\n  }\n}\n\nfunction validateUnionMembers(context, union) {\n  const memberTypes = union.getTypes();\n\n  if (memberTypes.length === 0) {\n    context.reportError(\n      `Union type ${union.name} must define one or more member types.`,\n      [union.astNode, ...union.extensionASTNodes],\n    );\n  }\n\n  const includedTypeNames = Object.create(null);\n\n  for (const memberType of memberTypes) {\n    if (includedTypeNames[memberType.name]) {\n      context.reportError(\n        `Union type ${union.name} can only include type ${memberType.name} once.`,\n        getUnionMemberTypeNodes(union, memberType.name),\n      );\n      continue;\n    }\n\n    includedTypeNames[memberType.name] = true;\n\n    if (!isObjectType(memberType)) {\n      context.reportError(\n        `Union type ${union.name} can only include Object types, ` +\n          `it cannot include ${inspect(memberType)}.`,\n        getUnionMemberTypeNodes(union, String(memberType)),\n      );\n    }\n  }\n}\n\nfunction validateEnumValues(context, enumType) {\n  const enumValues = enumType.getValues();\n\n  if (enumValues.length === 0) {\n    context.reportError(\n      `Enum type ${enumType.name} must define one or more values.`,\n      [enumType.astNode, ...enumType.extensionASTNodes],\n    );\n  }\n\n  for (const enumValue of enumValues) {\n    // Ensure valid name.\n    validateName(context, enumValue);\n  }\n}\n\nfunction validateInputFields(context, inputObj) {\n  const fields = Object.values(inputObj.getFields());\n\n  if (fields.length === 0) {\n    context.reportError(\n      `Input Object type ${inputObj.name} must define one or more fields.`,\n      [inputObj.astNode, ...inputObj.extensionASTNodes],\n    );\n  } // Ensure the arguments are valid\n\n  for (const field of fields) {\n    // Ensure they are named correctly.\n    validateName(context, field); // Ensure the type is an input type\n\n    if (!isInputType(field.type)) {\n      var _field$astNode2;\n\n      context.reportError(\n        `The type of ${inputObj.name}.${field.name} must be Input Type ` +\n          `but got: ${inspect(field.type)}.`,\n        (_field$astNode2 = field.astNode) === null || _field$astNode2 === void 0\n          ? void 0\n          : _field$astNode2.type,\n      );\n    }\n\n    if (isRequiredInputField(field) && field.deprecationReason != null) {\n      var _field$astNode3;\n\n      context.reportError(\n        `Required input field ${inputObj.name}.${field.name} cannot be deprecated.`,\n        [\n          getDeprecatedDirectiveNode(field.astNode),\n          (_field$astNode3 = field.astNode) === null ||\n          _field$astNode3 === void 0\n            ? void 0\n            : _field$astNode3.type,\n        ],\n      );\n    }\n\n    if (inputObj.isOneOf) {\n      validateOneOfInputObjectField(inputObj, field, context);\n    }\n  }\n}\n\nfunction validateOneOfInputObjectField(type, field, context) {\n  if (isNonNullType(field.type)) {\n    var _field$astNode4;\n\n    context.reportError(\n      `OneOf input field ${type.name}.${field.name} must be nullable.`,\n      (_field$astNode4 = field.astNode) === null || _field$astNode4 === void 0\n        ? void 0\n        : _field$astNode4.type,\n    );\n  }\n\n  if (field.defaultValue !== undefined) {\n    context.reportError(\n      `OneOf input field ${type.name}.${field.name} cannot have a default value.`,\n      field.astNode,\n    );\n  }\n}\n\nfunction createInputObjectCircularRefsValidator(context) {\n  // Modified copy of algorithm from 'src/validation/rules/NoFragmentCycles.js'.\n  // Tracks already visited types to maintain O(N) and to ensure that cycles\n  // are not redundantly reported.\n  const visitedTypes = Object.create(null); // Array of types nodes used to produce meaningful errors\n\n  const fieldPath = []; // Position in the type path\n\n  const fieldPathIndexByTypeName = Object.create(null);\n  return detectCycleRecursive; // This does a straight-forward DFS to find cycles.\n  // It does not terminate when a cycle was found but continues to explore\n  // the graph to find all possible cycles.\n\n  function detectCycleRecursive(inputObj) {\n    if (visitedTypes[inputObj.name]) {\n      return;\n    }\n\n    visitedTypes[inputObj.name] = true;\n    fieldPathIndexByTypeName[inputObj.name] = fieldPath.length;\n    const fields = Object.values(inputObj.getFields());\n\n    for (const field of fields) {\n      if (isNonNullType(field.type) && isInputObjectType(field.type.ofType)) {\n        const fieldType = field.type.ofType;\n        const cycleIndex = fieldPathIndexByTypeName[fieldType.name];\n        fieldPath.push(field);\n\n        if (cycleIndex === undefined) {\n          detectCycleRecursive(fieldType);\n        } else {\n          const cyclePath = fieldPath.slice(cycleIndex);\n          const pathStr = cyclePath.map((fieldObj) => fieldObj.name).join('.');\n          context.reportError(\n            `Cannot reference Input Object \"${fieldType.name}\" within itself through a series of non-null fields: \"${pathStr}\".`,\n            cyclePath.map((fieldObj) => fieldObj.astNode),\n          );\n        }\n\n        fieldPath.pop();\n      }\n    }\n\n    fieldPathIndexByTypeName[inputObj.name] = undefined;\n  }\n}\n\nfunction getAllImplementsInterfaceNodes(type, iface) {\n  const { astNode, extensionASTNodes } = type;\n  const nodes =\n    astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes\n    .flatMap((typeNode) => {\n      var _typeNode$interfaces;\n\n      return (\n        /* c8 ignore next */\n        (_typeNode$interfaces = typeNode.interfaces) !== null &&\n          _typeNode$interfaces !== void 0\n          ? _typeNode$interfaces\n          : []\n      );\n    })\n    .filter((ifaceNode) => ifaceNode.name.value === iface.name);\n}\n\nfunction getUnionMemberTypeNodes(union, typeName) {\n  const { astNode, extensionASTNodes } = union;\n  const nodes =\n    astNode != null ? [astNode, ...extensionASTNodes] : extensionASTNodes; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  return nodes\n    .flatMap((unionNode) => {\n      var _unionNode$types;\n\n      return (\n        /* c8 ignore next */\n        (_unionNode$types = unionNode.types) !== null &&\n          _unionNode$types !== void 0\n          ? _unionNode$types\n          : []\n      );\n    })\n    .filter((typeNode) => typeNode.name.value === typeName);\n}\n\nfunction getDeprecatedDirectiveNode(definitionNode) {\n  var _definitionNode$direc;\n\n  return definitionNode === null || definitionNode === void 0\n    ? void 0\n    : (_definitionNode$direc = definitionNode.directives) === null ||\n      _definitionNode$direc === void 0\n    ? void 0\n    : _definitionNode$direc.find(\n        (node) => node.name.value === GraphQLDeprecatedDirective.name,\n      );\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,EAAEC,eAAe,QAAQ,kCAAkC;AAC/E,SACEC,UAAU,EACVC,iBAAiB,EACjBC,WAAW,EACXC,eAAe,EACfC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,kBAAkB,EAClBC,oBAAoB,EACpBC,WAAW,QACN,kBAAkB;AACzB,SAASC,0BAA0B,EAAEC,WAAW,QAAQ,kBAAkB;AAC1E,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,YAAY,QAAQ,cAAc;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,cAAcA,CAACC,MAAM,EAAE;EACrC;EACAF,YAAY,CAACE,MAAM,CAAC,CAAC,CAAC;;EAEtB,IAAIA,MAAM,CAACC,kBAAkB,EAAE;IAC7B,OAAOD,MAAM,CAACC,kBAAkB;EAClC,CAAC,CAAC;;EAEF,MAAMC,OAAO,GAAG,IAAIC,uBAAuB,CAACH,MAAM,CAAC;EACnDI,iBAAiB,CAACF,OAAO,CAAC;EAC1BG,kBAAkB,CAACH,OAAO,CAAC;EAC3BI,aAAa,CAACJ,OAAO,CAAC,CAAC,CAAC;EACxB;;EAEA,MAAMK,MAAM,GAAGL,OAAO,CAACM,SAAS,CAAC,CAAC;EAClCR,MAAM,CAACC,kBAAkB,GAAGM,MAAM;EAClC,OAAOA,MAAM;AACf;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,iBAAiBA,CAACT,MAAM,EAAE;EACxC,MAAMO,MAAM,GAAGR,cAAc,CAACC,MAAM,CAAC;EAErC,IAAIO,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACvB,MAAM,IAAIC,KAAK,CAACJ,MAAM,CAACK,GAAG,CAAEC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC,CAAC;EACpE;AACF;AAEA,MAAMZ,uBAAuB,CAAC;EAC5Ba,WAAWA,CAAChB,MAAM,EAAE;IAClB,IAAI,CAACiB,OAAO,GAAG,EAAE;IACjB,IAAI,CAACjB,MAAM,GAAGA,MAAM;EACtB;EAEAkB,WAAWA,CAACJ,OAAO,EAAEK,KAAK,EAAE;IAC1B,MAAMC,MAAM,GAAGC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,CAACI,MAAM,CAACC,OAAO,CAAC,GAAGL,KAAK;IAEnE,IAAI,CAACF,OAAO,CAACQ,IAAI,CACf,IAAI7C,YAAY,CAACkC,OAAO,EAAE;MACxBK,KAAK,EAAEC;IACT,CAAC,CACH,CAAC;EACH;EAEAZ,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACS,OAAO;EACrB;AACF;AAEA,SAASb,iBAAiBA,CAACF,OAAO,EAAE;EAClC,MAAMF,MAAM,GAAGE,OAAO,CAACF,MAAM;EAC7B,MAAM0B,SAAS,GAAG1B,MAAM,CAAC2B,YAAY,CAAC,CAAC;EAEvC,IAAI,CAACD,SAAS,EAAE;IACdxB,OAAO,CAACgB,WAAW,CAAC,mCAAmC,EAAElB,MAAM,CAAC4B,OAAO,CAAC;EAC1E,CAAC,MAAM,IAAI,CAACtC,YAAY,CAACoC,SAAS,CAAC,EAAE;IACnC,IAAIG,qBAAqB;IAEzB3B,OAAO,CAACgB,WAAW,CACjB,qDAAqDvC,OAAO,CAC1D+C,SACF,CAAC,GAAG,EACJ,CAACG,qBAAqB,GAAGC,oBAAoB,CAC3C9B,MAAM,EACNnB,iBAAiB,CAACkD,KACpB,CAAC,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAC3CA,qBAAqB,GACrBH,SAAS,CAACE,OAChB,CAAC;EACH;EAEA,MAAMI,YAAY,GAAGhC,MAAM,CAACiC,eAAe,CAAC,CAAC;EAE7C,IAAID,YAAY,IAAI,CAAC1C,YAAY,CAAC0C,YAAY,CAAC,EAAE;IAC/C,IAAIE,sBAAsB;IAE1BhC,OAAO,CAACgB,WAAW,CACjB,mEAAmE,GACjE,GAAGvC,OAAO,CAACqD,YAAY,CAAC,GAAG,EAC7B,CAACE,sBAAsB,GAAGJ,oBAAoB,CAC5C9B,MAAM,EACNnB,iBAAiB,CAACsD,QACpB,CAAC,MAAM,IAAI,IAAID,sBAAsB,KAAK,KAAK,CAAC,GAC5CA,sBAAsB,GACtBF,YAAY,CAACJ,OACnB,CAAC;EACH;EAEA,MAAMQ,gBAAgB,GAAGpC,MAAM,CAACqC,mBAAmB,CAAC,CAAC;EAErD,IAAID,gBAAgB,IAAI,CAAC9C,YAAY,CAAC8C,gBAAgB,CAAC,EAAE;IACvD,IAAIE,sBAAsB;IAE1BpC,OAAO,CAACgB,WAAW,CACjB,uEAAuE,GACrE,GAAGvC,OAAO,CAACyD,gBAAgB,CAAC,GAAG,EACjC,CAACE,sBAAsB,GAAGR,oBAAoB,CAC5C9B,MAAM,EACNnB,iBAAiB,CAAC0D,YACpB,CAAC,MAAM,IAAI,IAAID,sBAAsB,KAAK,KAAK,CAAC,GAC5CA,sBAAsB,GACtBF,gBAAgB,CAACR,OACvB,CAAC;EACH;AACF;AAEA,SAASE,oBAAoBA,CAAC9B,MAAM,EAAEwC,SAAS,EAAE;EAC/C,IAAIC,aAAa;EAEjB,OAAO,CAACA,aAAa,GAAG,CAACzC,MAAM,CAAC4B,OAAO,EAAE,GAAG5B,MAAM,CAAC0C,iBAAiB,CAAC,CAClEC,OAAO;EACN;EACCC,UAAU,IAAK;IACd,IAAIC,qBAAqB;IAEzB,OACE,oBACA,CAACA,qBAAqB,GACpBD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GACxC,KAAK,CAAC,GACNA,UAAU,CAACE,cAAc,MAAM,IAAI,IACvCD,qBAAqB,KAAK,KAAK,CAAC,GAC9BA,qBAAqB,GACrB,EAAE;EAEV,CACF,CAAC,CACAE,IAAI,CAAEC,aAAa,IAAKA,aAAa,CAACR,SAAS,KAAKA,SAAS,CAAC,MAAM,IAAI,IACzEC,aAAa,KAAK,KAAK,CAAC,GACtB,KAAK,CAAC,GACNA,aAAa,CAACQ,IAAI;AACxB;AAEA,SAAS5C,kBAAkBA,CAACH,OAAO,EAAE;EACnC,KAAK,MAAMgD,SAAS,IAAIhD,OAAO,CAACF,MAAM,CAACmD,aAAa,CAAC,CAAC,EAAE;IACtD;IACA,IAAI,CAACvD,WAAW,CAACsD,SAAS,CAAC,EAAE;MAC3BhD,OAAO,CAACgB,WAAW,CACjB,+BAA+BvC,OAAO,CAACuE,SAAS,CAAC,GAAG,EACpDA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACtB,OAClE,CAAC;MACD;IACF,CAAC,CAAC;;IAEFwB,YAAY,CAAClD,OAAO,EAAEgD,SAAS,CAAC;IAEhC,IAAIA,SAAS,CAACG,SAAS,CAAC3C,MAAM,KAAK,CAAC,EAAE;MACpCR,OAAO,CAACgB,WAAW,CACjB,cAAcgC,SAAS,CAACI,IAAI,oCAAoC,EAChEJ,SAAS,CAACtB,OACZ,CAAC;IACH,CAAC,CAAC;;IAEF,KAAK,MAAM2B,GAAG,IAAIL,SAAS,CAACM,IAAI,EAAE;MAChC;MACAJ,YAAY,CAAClD,OAAO,EAAEqD,GAAG,CAAC,CAAC,CAAC;;MAE5B,IAAI,CAACrE,WAAW,CAACqE,GAAG,CAACN,IAAI,CAAC,EAAE;QAC1B/C,OAAO,CAACgB,WAAW,CACjB,gBAAgBgC,SAAS,CAACI,IAAI,IAAIC,GAAG,CAACD,IAAI,wBAAwB,GAChE,YAAY3E,OAAO,CAAC4E,GAAG,CAACN,IAAI,CAAC,GAAG,EAClCM,GAAG,CAAC3B,OACN,CAAC;MACH;MAEA,IAAIpC,kBAAkB,CAAC+D,GAAG,CAAC,IAAIA,GAAG,CAACE,iBAAiB,IAAI,IAAI,EAAE;QAC5D,IAAIC,YAAY;QAEhBxD,OAAO,CAACgB,WAAW,CACjB,sBAAsBgC,SAAS,CAACI,IAAI,IAAIC,GAAG,CAACD,IAAI,0BAA0B,EAC1E,CACEK,0BAA0B,CAACJ,GAAG,CAAC3B,OAAO,CAAC,EACvC,CAAC8B,YAAY,GAAGH,GAAG,CAAC3B,OAAO,MAAM,IAAI,IAAI8B,YAAY,KAAK,KAAK,CAAC,GAC5D,KAAK,CAAC,GACNA,YAAY,CAACT,IAAI,CAEzB,CAAC;MACH;IACF;EACF;AACF;AAEA,SAASG,YAAYA,CAAClD,OAAO,EAAE0D,IAAI,EAAE;EACnC;EACA,IAAIA,IAAI,CAACN,IAAI,CAACO,UAAU,CAAC,IAAI,CAAC,EAAE;IAC9B3D,OAAO,CAACgB,WAAW,CACjB,SAAS0C,IAAI,CAACN,IAAI,yEAAyE,EAC3FM,IAAI,CAAChC,OACP,CAAC;EACH;AACF;AAEA,SAAStB,aAAaA,CAACJ,OAAO,EAAE;EAC9B,MAAM4D,+BAA+B,GACnCC,sCAAsC,CAAC7D,OAAO,CAAC;EACjD,MAAM8D,OAAO,GAAG9D,OAAO,CAACF,MAAM,CAACiE,UAAU,CAAC,CAAC;EAE3C,KAAK,MAAMhB,IAAI,IAAIiB,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC,EAAE;IACzC;IACA,IAAI,CAAC5E,WAAW,CAAC6D,IAAI,CAAC,EAAE;MACtB/C,OAAO,CAACgB,WAAW,CACjB,wCAAwCvC,OAAO,CAACsE,IAAI,CAAC,GAAG,EACxDA,IAAI,CAACrB,OACP,CAAC;MACD;IACF,CAAC,CAAC;;IAEF,IAAI,CAAC/B,mBAAmB,CAACoD,IAAI,CAAC,EAAE;MAC9BG,YAAY,CAAClD,OAAO,EAAE+C,IAAI,CAAC;IAC7B;IAEA,IAAI3D,YAAY,CAAC2D,IAAI,CAAC,EAAE;MACtB;MACAmB,cAAc,CAAClE,OAAO,EAAE+C,IAAI,CAAC,CAAC,CAAC;;MAE/BoB,kBAAkB,CAACnE,OAAO,EAAE+C,IAAI,CAAC;IACnC,CAAC,MAAM,IAAI9D,eAAe,CAAC8D,IAAI,CAAC,EAAE;MAChC;MACAmB,cAAc,CAAClE,OAAO,EAAE+C,IAAI,CAAC,CAAC,CAAC;;MAE/BoB,kBAAkB,CAACnE,OAAO,EAAE+C,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIvD,WAAW,CAACuD,IAAI,CAAC,EAAE;MAC5B;MACAqB,oBAAoB,CAACpE,OAAO,EAAE+C,IAAI,CAAC;IACrC,CAAC,MAAM,IAAIjE,UAAU,CAACiE,IAAI,CAAC,EAAE;MAC3B;MACAsB,kBAAkB,CAACrE,OAAO,EAAE+C,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIhE,iBAAiB,CAACgE,IAAI,CAAC,EAAE;MAClC;MACAuB,mBAAmB,CAACtE,OAAO,EAAE+C,IAAI,CAAC,CAAC,CAAC;;MAEpCa,+BAA+B,CAACb,IAAI,CAAC;IACvC;EACF;AACF;AAEA,SAASmB,cAAcA,CAAClE,OAAO,EAAE+C,IAAI,EAAE;EACrC,MAAMwB,MAAM,GAAGP,MAAM,CAACC,MAAM,CAAClB,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,IAAID,MAAM,CAAC/D,MAAM,KAAK,CAAC,EAAE;IACvBR,OAAO,CAACgB,WAAW,CAAC,QAAQ+B,IAAI,CAACK,IAAI,kCAAkC,EAAE,CACvEL,IAAI,CAACrB,OAAO,EACZ,GAAGqB,IAAI,CAACP,iBAAiB,CAC1B,CAAC;EACJ;EAEA,KAAK,MAAMiC,KAAK,IAAIF,MAAM,EAAE;IAC1B;IACArB,YAAY,CAAClD,OAAO,EAAEyE,KAAK,CAAC,CAAC,CAAC;;IAE9B,IAAI,CAACpF,YAAY,CAACoF,KAAK,CAAC1B,IAAI,CAAC,EAAE;MAC7B,IAAI2B,cAAc;MAElB1E,OAAO,CAACgB,WAAW,CACjB,eAAe+B,IAAI,CAACK,IAAI,IAAIqB,KAAK,CAACrB,IAAI,uBAAuB,GAC3D,YAAY3E,OAAO,CAACgG,KAAK,CAAC1B,IAAI,CAAC,GAAG,EACpC,CAAC2B,cAAc,GAAGD,KAAK,CAAC/C,OAAO,MAAM,IAAI,IAAIgD,cAAc,KAAK,KAAK,CAAC,GAClE,KAAK,CAAC,GACNA,cAAc,CAAC3B,IACrB,CAAC;IACH,CAAC,CAAC;;IAEF,KAAK,MAAMM,GAAG,IAAIoB,KAAK,CAACnB,IAAI,EAAE;MAC5B,MAAMqB,OAAO,GAAGtB,GAAG,CAACD,IAAI,CAAC,CAAC;;MAE1BF,YAAY,CAAClD,OAAO,EAAEqD,GAAG,CAAC,CAAC,CAAC;;MAE5B,IAAI,CAACrE,WAAW,CAACqE,GAAG,CAACN,IAAI,CAAC,EAAE;QAC1B,IAAI6B,aAAa;QAEjB5E,OAAO,CAACgB,WAAW,CACjB,eAAe+B,IAAI,CAACK,IAAI,IAAIqB,KAAK,CAACrB,IAAI,IAAIuB,OAAO,mBAAmB,GAClE,iBAAiBlG,OAAO,CAAC4E,GAAG,CAACN,IAAI,CAAC,GAAG,EACvC,CAAC6B,aAAa,GAAGvB,GAAG,CAAC3B,OAAO,MAAM,IAAI,IAAIkD,aAAa,KAAK,KAAK,CAAC,GAC9D,KAAK,CAAC,GACNA,aAAa,CAAC7B,IACpB,CAAC;MACH;MAEA,IAAIzD,kBAAkB,CAAC+D,GAAG,CAAC,IAAIA,GAAG,CAACE,iBAAiB,IAAI,IAAI,EAAE;QAC5D,IAAIsB,aAAa;QAEjB7E,OAAO,CAACgB,WAAW,CACjB,qBAAqB+B,IAAI,CAACK,IAAI,IAAIqB,KAAK,CAACrB,IAAI,IAAIuB,OAAO,0BAA0B,EACjF,CACElB,0BAA0B,CAACJ,GAAG,CAAC3B,OAAO,CAAC,EACvC,CAACmD,aAAa,GAAGxB,GAAG,CAAC3B,OAAO,MAAM,IAAI,IAAImD,aAAa,KAAK,KAAK,CAAC,GAC9D,KAAK,CAAC,GACNA,aAAa,CAAC9B,IAAI,CAE1B,CAAC;MACH;IACF;EACF;AACF;AAEA,SAASoB,kBAAkBA,CAACnE,OAAO,EAAE+C,IAAI,EAAE;EACzC,MAAM+B,cAAc,GAAGd,MAAM,CAACe,MAAM,CAAC,IAAI,CAAC;EAE1C,KAAK,MAAMC,KAAK,IAAIjC,IAAI,CAACkC,aAAa,CAAC,CAAC,EAAE;IACxC,IAAI,CAAChG,eAAe,CAAC+F,KAAK,CAAC,EAAE;MAC3BhF,OAAO,CAACgB,WAAW,CACjB,QAAQvC,OAAO,CAACsE,IAAI,CAAC,wCAAwC,GAC3D,uBAAuBtE,OAAO,CAACuG,KAAK,CAAC,GAAG,EAC1CE,8BAA8B,CAACnC,IAAI,EAAEiC,KAAK,CAC5C,CAAC;MACD;IACF;IAEA,IAAIjC,IAAI,KAAKiC,KAAK,EAAE;MAClBhF,OAAO,CAACgB,WAAW,CACjB,QAAQ+B,IAAI,CAACK,IAAI,wEAAwE,EACzF8B,8BAA8B,CAACnC,IAAI,EAAEiC,KAAK,CAC5C,CAAC;MACD;IACF;IAEA,IAAIF,cAAc,CAACE,KAAK,CAAC5B,IAAI,CAAC,EAAE;MAC9BpD,OAAO,CAACgB,WAAW,CACjB,QAAQ+B,IAAI,CAACK,IAAI,uBAAuB4B,KAAK,CAAC5B,IAAI,QAAQ,EAC1D8B,8BAA8B,CAACnC,IAAI,EAAEiC,KAAK,CAC5C,CAAC;MACD;IACF;IAEAF,cAAc,CAACE,KAAK,CAAC5B,IAAI,CAAC,GAAG,IAAI;IACjC+B,+BAA+B,CAACnF,OAAO,EAAE+C,IAAI,EAAEiC,KAAK,CAAC;IACrDI,+BAA+B,CAACpF,OAAO,EAAE+C,IAAI,EAAEiC,KAAK,CAAC;EACvD;AACF;AAEA,SAASI,+BAA+BA,CAACpF,OAAO,EAAE+C,IAAI,EAAEiC,KAAK,EAAE;EAC7D,MAAMK,YAAY,GAAGtC,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEvC,KAAK,MAAMc,UAAU,IAAItB,MAAM,CAACC,MAAM,CAACe,KAAK,CAACR,SAAS,CAAC,CAAC,CAAC,EAAE;IACzD,MAAMe,SAAS,GAAGD,UAAU,CAAClC,IAAI;IACjC,MAAMoC,SAAS,GAAGH,YAAY,CAACE,SAAS,CAAC,CAAC,CAAC;;IAE3C,IAAI,CAACC,SAAS,EAAE;MACdxF,OAAO,CAACgB,WAAW,CACjB,mBAAmBgE,KAAK,CAAC5B,IAAI,IAAImC,SAAS,iBAAiBxC,IAAI,CAACK,IAAI,uBAAuB,EAC3F,CAACkC,UAAU,CAAC5D,OAAO,EAAEqB,IAAI,CAACrB,OAAO,EAAE,GAAGqB,IAAI,CAACP,iBAAiB,CAC9D,CAAC;MACD;IACF,CAAC,CAAC;IACF;;IAEA,IAAI,CAAC3D,eAAe,CAACmB,OAAO,CAACF,MAAM,EAAE0F,SAAS,CAACzC,IAAI,EAAEuC,UAAU,CAACvC,IAAI,CAAC,EAAE;MACrE,IAAI0C,mBAAmB,EAAEC,kBAAkB;MAE3C1F,OAAO,CAACgB,WAAW,CACjB,mBAAmBgE,KAAK,CAAC5B,IAAI,IAAImC,SAAS,gBAAgB,GACxD,GAAG9G,OAAO,CAAC6G,UAAU,CAACvC,IAAI,CAAC,QAAQA,IAAI,CAACK,IAAI,IAAImC,SAAS,GAAG,GAC5D,WAAW9G,OAAO,CAAC+G,SAAS,CAACzC,IAAI,CAAC,GAAG,EACvC,CACE,CAAC0C,mBAAmB,GAAGH,UAAU,CAAC5D,OAAO,MAAM,IAAI,IACnD+D,mBAAmB,KAAK,KAAK,CAAC,GAC1B,KAAK,CAAC,GACNA,mBAAmB,CAAC1C,IAAI,EAC5B,CAAC2C,kBAAkB,GAAGF,SAAS,CAAC9D,OAAO,MAAM,IAAI,IACjDgE,kBAAkB,KAAK,KAAK,CAAC,GACzB,KAAK,CAAC,GACNA,kBAAkB,CAAC3C,IAAI,CAE/B,CAAC;IACH,CAAC,CAAC;;IAEF,KAAK,MAAM4C,QAAQ,IAAIL,UAAU,CAAChC,IAAI,EAAE;MACtC,MAAMqB,OAAO,GAAGgB,QAAQ,CAACvC,IAAI;MAC7B,MAAMwC,OAAO,GAAGJ,SAAS,CAAClC,IAAI,CAACT,IAAI,CAAEQ,GAAG,IAAKA,GAAG,CAACD,IAAI,KAAKuB,OAAO,CAAC,CAAC,CAAC;;MAEpE,IAAI,CAACiB,OAAO,EAAE;QACZ5F,OAAO,CAACgB,WAAW,CACjB,4BAA4BgE,KAAK,CAAC5B,IAAI,IAAImC,SAAS,IAAIZ,OAAO,mBAAmB5B,IAAI,CAACK,IAAI,IAAImC,SAAS,uBAAuB,EAC9H,CAACI,QAAQ,CAACjE,OAAO,EAAE8D,SAAS,CAAC9D,OAAO,CACtC,CAAC;QACD;MACF,CAAC,CAAC;MACF;MACA;;MAEA,IAAI,CAAC9C,WAAW,CAAC+G,QAAQ,CAAC5C,IAAI,EAAE6C,OAAO,CAAC7C,IAAI,CAAC,EAAE;QAC7C,IAAI8C,iBAAiB,EAAEC,gBAAgB;QAEvC9F,OAAO,CAACgB,WAAW,CACjB,4BAA4BgE,KAAK,CAAC5B,IAAI,IAAImC,SAAS,IAAIZ,OAAO,KAAK,GACjE,gBAAgBlG,OAAO,CAACkH,QAAQ,CAAC5C,IAAI,CAAC,OAAO,GAC7C,GAAGA,IAAI,CAACK,IAAI,IAAImC,SAAS,IAAIZ,OAAO,aAAa,GACjD,GAAGlG,OAAO,CAACmH,OAAO,CAAC7C,IAAI,CAAC,GAAG,EAC7B,CACE,CAAC8C,iBAAiB,GAAGF,QAAQ,CAACjE,OAAO,MAAM,IAAI,IAC/CmE,iBAAiB,KAAK,KAAK,CAAC,GACxB,KAAK,CAAC,GACNA,iBAAiB,CAAC9C,IAAI,EAC1B,CAAC+C,gBAAgB,GAAGF,OAAO,CAAClE,OAAO,MAAM,IAAI,IAC7CoE,gBAAgB,KAAK,KAAK,CAAC,GACvB,KAAK,CAAC,GACNA,gBAAgB,CAAC/C,IAAI,CAE7B,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF,KAAK,MAAM6C,OAAO,IAAIJ,SAAS,CAAClC,IAAI,EAAE;MACpC,MAAMqB,OAAO,GAAGiB,OAAO,CAACxC,IAAI;MAC5B,MAAMuC,QAAQ,GAAGL,UAAU,CAAChC,IAAI,CAACT,IAAI,CAAEQ,GAAG,IAAKA,GAAG,CAACD,IAAI,KAAKuB,OAAO,CAAC;MAEpE,IAAI,CAACgB,QAAQ,IAAIrG,kBAAkB,CAACsG,OAAO,CAAC,EAAE;QAC5C5F,OAAO,CAACgB,WAAW,CACjB,gBAAgB+B,IAAI,CAACK,IAAI,IAAImC,SAAS,+BAA+BZ,OAAO,6CAA6CK,KAAK,CAAC5B,IAAI,IAAImC,SAAS,GAAG,EACnJ,CAACK,OAAO,CAAClE,OAAO,EAAE4D,UAAU,CAAC5D,OAAO,CACtC,CAAC;MACH;IACF;EACF;AACF;AAEA,SAASyD,+BAA+BA,CAACnF,OAAO,EAAE+C,IAAI,EAAEiC,KAAK,EAAE;EAC7D,MAAMe,eAAe,GAAGhD,IAAI,CAACkC,aAAa,CAAC,CAAC;EAE5C,KAAK,MAAMe,UAAU,IAAIhB,KAAK,CAACC,aAAa,CAAC,CAAC,EAAE;IAC9C,IAAI,CAACc,eAAe,CAACE,QAAQ,CAACD,UAAU,CAAC,EAAE;MACzChG,OAAO,CAACgB,WAAW,CACjBgF,UAAU,KAAKjD,IAAI,GACf,QAAQA,IAAI,CAACK,IAAI,qBAAqB4B,KAAK,CAAC5B,IAAI,gDAAgD,GAChG,QAAQL,IAAI,CAACK,IAAI,mBAAmB4C,UAAU,CAAC5C,IAAI,iCAAiC4B,KAAK,CAAC5B,IAAI,GAAG,EACrG,CACE,GAAG8B,8BAA8B,CAACF,KAAK,EAAEgB,UAAU,CAAC,EACpD,GAAGd,8BAA8B,CAACnC,IAAI,EAAEiC,KAAK,CAAC,CAElD,CAAC;IACH;EACF;AACF;AAEA,SAASZ,oBAAoBA,CAACpE,OAAO,EAAEkG,KAAK,EAAE;EAC5C,MAAMC,WAAW,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC;EAEpC,IAAID,WAAW,CAAC3F,MAAM,KAAK,CAAC,EAAE;IAC5BR,OAAO,CAACgB,WAAW,CACjB,cAAckF,KAAK,CAAC9C,IAAI,wCAAwC,EAChE,CAAC8C,KAAK,CAACxE,OAAO,EAAE,GAAGwE,KAAK,CAAC1D,iBAAiB,CAC5C,CAAC;EACH;EAEA,MAAM6D,iBAAiB,GAAGrC,MAAM,CAACe,MAAM,CAAC,IAAI,CAAC;EAE7C,KAAK,MAAMuB,UAAU,IAAIH,WAAW,EAAE;IACpC,IAAIE,iBAAiB,CAACC,UAAU,CAAClD,IAAI,CAAC,EAAE;MACtCpD,OAAO,CAACgB,WAAW,CACjB,cAAckF,KAAK,CAAC9C,IAAI,0BAA0BkD,UAAU,CAAClD,IAAI,QAAQ,EACzEmD,uBAAuB,CAACL,KAAK,EAAEI,UAAU,CAAClD,IAAI,CAChD,CAAC;MACD;IACF;IAEAiD,iBAAiB,CAACC,UAAU,CAAClD,IAAI,CAAC,GAAG,IAAI;IAEzC,IAAI,CAAChE,YAAY,CAACkH,UAAU,CAAC,EAAE;MAC7BtG,OAAO,CAACgB,WAAW,CACjB,cAAckF,KAAK,CAAC9C,IAAI,kCAAkC,GACxD,qBAAqB3E,OAAO,CAAC6H,UAAU,CAAC,GAAG,EAC7CC,uBAAuB,CAACL,KAAK,EAAEM,MAAM,CAACF,UAAU,CAAC,CACnD,CAAC;IACH;EACF;AACF;AAEA,SAASjC,kBAAkBA,CAACrE,OAAO,EAAEyG,QAAQ,EAAE;EAC7C,MAAMC,UAAU,GAAGD,QAAQ,CAACE,SAAS,CAAC,CAAC;EAEvC,IAAID,UAAU,CAAClG,MAAM,KAAK,CAAC,EAAE;IAC3BR,OAAO,CAACgB,WAAW,CACjB,aAAayF,QAAQ,CAACrD,IAAI,kCAAkC,EAC5D,CAACqD,QAAQ,CAAC/E,OAAO,EAAE,GAAG+E,QAAQ,CAACjE,iBAAiB,CAClD,CAAC;EACH;EAEA,KAAK,MAAMoE,SAAS,IAAIF,UAAU,EAAE;IAClC;IACAxD,YAAY,CAAClD,OAAO,EAAE4G,SAAS,CAAC;EAClC;AACF;AAEA,SAAStC,mBAAmBA,CAACtE,OAAO,EAAE6G,QAAQ,EAAE;EAC9C,MAAMtC,MAAM,GAAGP,MAAM,CAACC,MAAM,CAAC4C,QAAQ,CAACrC,SAAS,CAAC,CAAC,CAAC;EAElD,IAAID,MAAM,CAAC/D,MAAM,KAAK,CAAC,EAAE;IACvBR,OAAO,CAACgB,WAAW,CACjB,qBAAqB6F,QAAQ,CAACzD,IAAI,kCAAkC,EACpE,CAACyD,QAAQ,CAACnF,OAAO,EAAE,GAAGmF,QAAQ,CAACrE,iBAAiB,CAClD,CAAC;EACH,CAAC,CAAC;;EAEF,KAAK,MAAMiC,KAAK,IAAIF,MAAM,EAAE;IAC1B;IACArB,YAAY,CAAClD,OAAO,EAAEyE,KAAK,CAAC,CAAC,CAAC;;IAE9B,IAAI,CAACzF,WAAW,CAACyF,KAAK,CAAC1B,IAAI,CAAC,EAAE;MAC5B,IAAI+D,eAAe;MAEnB9G,OAAO,CAACgB,WAAW,CACjB,eAAe6F,QAAQ,CAACzD,IAAI,IAAIqB,KAAK,CAACrB,IAAI,sBAAsB,GAC9D,YAAY3E,OAAO,CAACgG,KAAK,CAAC1B,IAAI,CAAC,GAAG,EACpC,CAAC+D,eAAe,GAAGrC,KAAK,CAAC/C,OAAO,MAAM,IAAI,IAAIoF,eAAe,KAAK,KAAK,CAAC,GACpE,KAAK,CAAC,GACNA,eAAe,CAAC/D,IACtB,CAAC;IACH;IAEA,IAAIxD,oBAAoB,CAACkF,KAAK,CAAC,IAAIA,KAAK,CAAClB,iBAAiB,IAAI,IAAI,EAAE;MAClE,IAAIwD,eAAe;MAEnB/G,OAAO,CAACgB,WAAW,CACjB,wBAAwB6F,QAAQ,CAACzD,IAAI,IAAIqB,KAAK,CAACrB,IAAI,wBAAwB,EAC3E,CACEK,0BAA0B,CAACgB,KAAK,CAAC/C,OAAO,CAAC,EACzC,CAACqF,eAAe,GAAGtC,KAAK,CAAC/C,OAAO,MAAM,IAAI,IAC1CqF,eAAe,KAAK,KAAK,CAAC,GACtB,KAAK,CAAC,GACNA,eAAe,CAAChE,IAAI,CAE5B,CAAC;IACH;IAEA,IAAI8D,QAAQ,CAACG,OAAO,EAAE;MACpBC,6BAA6B,CAACJ,QAAQ,EAAEpC,KAAK,EAAEzE,OAAO,CAAC;IACzD;EACF;AACF;AAEA,SAASiH,6BAA6BA,CAAClE,IAAI,EAAE0B,KAAK,EAAEzE,OAAO,EAAE;EAC3D,IAAIb,aAAa,CAACsF,KAAK,CAAC1B,IAAI,CAAC,EAAE;IAC7B,IAAImE,eAAe;IAEnBlH,OAAO,CAACgB,WAAW,CACjB,qBAAqB+B,IAAI,CAACK,IAAI,IAAIqB,KAAK,CAACrB,IAAI,oBAAoB,EAChE,CAAC8D,eAAe,GAAGzC,KAAK,CAAC/C,OAAO,MAAM,IAAI,IAAIwF,eAAe,KAAK,KAAK,CAAC,GACpE,KAAK,CAAC,GACNA,eAAe,CAACnE,IACtB,CAAC;EACH;EAEA,IAAI0B,KAAK,CAAC0C,YAAY,KAAKC,SAAS,EAAE;IACpCpH,OAAO,CAACgB,WAAW,CACjB,qBAAqB+B,IAAI,CAACK,IAAI,IAAIqB,KAAK,CAACrB,IAAI,+BAA+B,EAC3EqB,KAAK,CAAC/C,OACR,CAAC;EACH;AACF;AAEA,SAASmC,sCAAsCA,CAAC7D,OAAO,EAAE;EACvD;EACA;EACA;EACA,MAAMqH,YAAY,GAAGrD,MAAM,CAACe,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE1C,MAAMuC,SAAS,GAAG,EAAE,CAAC,CAAC;;EAEtB,MAAMC,wBAAwB,GAAGvD,MAAM,CAACe,MAAM,CAAC,IAAI,CAAC;EACpD,OAAOyC,oBAAoB,CAAC,CAAC;EAC7B;EACA;;EAEA,SAASA,oBAAoBA,CAACX,QAAQ,EAAE;IACtC,IAAIQ,YAAY,CAACR,QAAQ,CAACzD,IAAI,CAAC,EAAE;MAC/B;IACF;IAEAiE,YAAY,CAACR,QAAQ,CAACzD,IAAI,CAAC,GAAG,IAAI;IAClCmE,wBAAwB,CAACV,QAAQ,CAACzD,IAAI,CAAC,GAAGkE,SAAS,CAAC9G,MAAM;IAC1D,MAAM+D,MAAM,GAAGP,MAAM,CAACC,MAAM,CAAC4C,QAAQ,CAACrC,SAAS,CAAC,CAAC,CAAC;IAElD,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MAC1B,IAAIpF,aAAa,CAACsF,KAAK,CAAC1B,IAAI,CAAC,IAAIhE,iBAAiB,CAAC0F,KAAK,CAAC1B,IAAI,CAAC0E,MAAM,CAAC,EAAE;QACrE,MAAMC,SAAS,GAAGjD,KAAK,CAAC1B,IAAI,CAAC0E,MAAM;QACnC,MAAME,UAAU,GAAGJ,wBAAwB,CAACG,SAAS,CAACtE,IAAI,CAAC;QAC3DkE,SAAS,CAAC/F,IAAI,CAACkD,KAAK,CAAC;QAErB,IAAIkD,UAAU,KAAKP,SAAS,EAAE;UAC5BI,oBAAoB,CAACE,SAAS,CAAC;QACjC,CAAC,MAAM;UACL,MAAME,SAAS,GAAGN,SAAS,CAACO,KAAK,CAACF,UAAU,CAAC;UAC7C,MAAMG,OAAO,GAAGF,SAAS,CAAClH,GAAG,CAAEqH,QAAQ,IAAKA,QAAQ,CAAC3E,IAAI,CAAC,CAACvC,IAAI,CAAC,GAAG,CAAC;UACpEb,OAAO,CAACgB,WAAW,CACjB,kCAAkC0G,SAAS,CAACtE,IAAI,yDAAyD0E,OAAO,IAAI,EACpHF,SAAS,CAAClH,GAAG,CAAEqH,QAAQ,IAAKA,QAAQ,CAACrG,OAAO,CAC9C,CAAC;QACH;QAEA4F,SAAS,CAACU,GAAG,CAAC,CAAC;MACjB;IACF;IAEAT,wBAAwB,CAACV,QAAQ,CAACzD,IAAI,CAAC,GAAGgE,SAAS;EACrD;AACF;AAEA,SAASlC,8BAA8BA,CAACnC,IAAI,EAAEiC,KAAK,EAAE;EACnD,MAAM;IAAEtD,OAAO;IAAEc;EAAkB,CAAC,GAAGO,IAAI;EAC3C,MAAM9B,KAAK,GACTS,OAAO,IAAI,IAAI,GAAG,CAACA,OAAO,EAAE,GAAGc,iBAAiB,CAAC,GAAGA,iBAAiB,CAAC,CAAC;;EAEzE,OAAOvB,KAAK,CACTwB,OAAO,CAAEwF,QAAQ,IAAK;IACrB,IAAIC,oBAAoB;IAExB,OACE,oBACA,CAACA,oBAAoB,GAAGD,QAAQ,CAACE,UAAU,MAAM,IAAI,IACnDD,oBAAoB,KAAK,KAAK,CAAC,GAC7BA,oBAAoB,GACpB,EAAE;EAEV,CAAC,CAAC,CACD7G,MAAM,CAAE+G,SAAS,IAAKA,SAAS,CAAChF,IAAI,CAACiF,KAAK,KAAKrD,KAAK,CAAC5B,IAAI,CAAC;AAC/D;AAEA,SAASmD,uBAAuBA,CAACL,KAAK,EAAEoC,QAAQ,EAAE;EAChD,MAAM;IAAE5G,OAAO;IAAEc;EAAkB,CAAC,GAAG0D,KAAK;EAC5C,MAAMjF,KAAK,GACTS,OAAO,IAAI,IAAI,GAAG,CAACA,OAAO,EAAE,GAAGc,iBAAiB,CAAC,GAAGA,iBAAiB,CAAC,CAAC;;EAEzE,OAAOvB,KAAK,CACTwB,OAAO,CAAE8F,SAAS,IAAK;IACtB,IAAIC,gBAAgB;IAEpB,OACE,oBACA,CAACA,gBAAgB,GAAGD,SAAS,CAACE,KAAK,MAAM,IAAI,IAC3CD,gBAAgB,KAAK,KAAK,CAAC,GACzBA,gBAAgB,GAChB,EAAE;EAEV,CAAC,CAAC,CACDnH,MAAM,CAAE4G,QAAQ,IAAKA,QAAQ,CAAC7E,IAAI,CAACiF,KAAK,KAAKC,QAAQ,CAAC;AAC3D;AAEA,SAAS7E,0BAA0BA,CAACiF,cAAc,EAAE;EAClD,IAAIC,qBAAqB;EAEzB,OAAOD,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GACvD,KAAK,CAAC,GACN,CAACC,qBAAqB,GAAGD,cAAc,CAACE,UAAU,MAAM,IAAI,IAC5DD,qBAAqB,KAAK,KAAK,CAAC,GAChC,KAAK,CAAC,GACNA,qBAAqB,CAAC9F,IAAI,CACvBa,IAAI,IAAKA,IAAI,CAACN,IAAI,CAACiF,KAAK,KAAK5I,0BAA0B,CAAC2D,IAC3D,CAAC;AACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}