{"ast": null, "code": "import { isAbsoluteUrl } from './isAbsoluteUrl.mjs';\nfunction getAbsoluteUrl(path, baseUrl) {\n  if (isAbsoluteUrl(path)) {\n    return path;\n  }\n  if (path.startsWith(\"*\")) {\n    return path;\n  }\n  const origin = baseUrl || typeof location !== \"undefined\" && location.href;\n  return origin ?\n  // Encode and decode the path to preserve escaped characters.\n  decodeURI(new URL(encodeURI(path), origin).href) : path;\n}\nexport { getAbsoluteUrl };", "map": {"version": 3, "names": ["isAbsoluteUrl", "getAbsoluteUrl", "path", "baseUrl", "startsWith", "origin", "location", "href", "decodeURI", "URL", "encodeURI"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/url/getAbsoluteUrl.ts"], "sourcesContent": ["import { isAbsoluteUrl } from './isAbsoluteUrl'\n\n/**\n * Returns an absolute URL based on the given path.\n */\nexport function getAbsoluteUrl(path: string, baseUrl?: string): string {\n  // already absolute URL\n  if (isAbsoluteUrl(path)) {\n    return path\n  }\n\n  // Ignore path with pattern start with *\n  if (path.startsWith('*')) {\n    return path\n  }\n\n  // Resolve a relative request URL against a given custom \"baseUrl\"\n  // or the document baseURI (in the case of browser/browser-like environments).\n  const origin = baseUrl || (typeof location !== 'undefined' && location.href)\n\n  return origin\n    ? // Encode and decode the path to preserve escaped characters.\n      decodeURI(new URL(encodeURI(path), origin).href)\n    : path\n}\n"], "mappings": "AAAA,SAASA,aAAA,QAAqB;AAKvB,SAASC,eAAeC,IAAA,EAAcC,OAAA,EAA0B;EAErE,IAAIH,aAAA,CAAcE,IAAI,GAAG;IACvB,OAAOA,IAAA;EACT;EAGA,IAAIA,IAAA,CAAKE,UAAA,CAAW,GAAG,GAAG;IACxB,OAAOF,IAAA;EACT;EAIA,MAAMG,MAAA,GAASF,OAAA,IAAY,OAAOG,QAAA,KAAa,eAAeA,QAAA,CAASC,IAAA;EAEvE,OAAOF,MAAA;EAAA;EAEHG,SAAA,CAAU,IAAIC,GAAA,CAAIC,SAAA,CAAUR,IAAI,GAAGG,MAAM,EAAEE,IAAI,IAC/CL,IAAA;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}