{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isNode, QueryDocumentKeys } from './ast.mjs';\nimport { Kind } from './kinds.mjs';\n/**\n * A visitor is provided to visit, it contains the collection of\n * relevant functions to be called during the visitor's traversal.\n */\n\nexport const BREAK = Object.freeze({});\n/**\n * visit() will walk through an AST using a depth-first traversal, calling\n * the visitor's enter function at each node in the traversal, and calling the\n * leave function after visiting that node and all of its child nodes.\n *\n * By returning different values from the enter and leave functions, the\n * behavior of the visitor can be altered, including skipping over a sub-tree of\n * the AST (by returning false), editing the AST by returning a value or null\n * to remove the value, or to stop the whole traversal by returning BREAK.\n *\n * When using visit() to edit an AST, the original AST will not be modified, and\n * a new version of the AST with the changes applied will be returned from the\n * visit function.\n *\n * ```ts\n * const editedAST = visit(ast, {\n *   enter(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: skip visiting this node\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   },\n *   leave(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: no action\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   }\n * });\n * ```\n *\n * Alternatively to providing enter() and leave() functions, a visitor can\n * instead provide functions named the same as the kinds of AST nodes, or\n * enter/leave visitors at a named key, leading to three permutations of the\n * visitor API:\n *\n * 1) Named visitors triggered when entering a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind(node) {\n *     // enter the \"Kind\" node\n *   }\n * })\n * ```\n *\n * 2) Named visitors that trigger upon entering and leaving a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind: {\n *     enter(node) {\n *       // enter the \"Kind\" node\n *     }\n *     leave(node) {\n *       // leave the \"Kind\" node\n *     }\n *   }\n * })\n * ```\n *\n * 3) Generic visitors that trigger upon entering and leaving any node.\n *\n * ```ts\n * visit(ast, {\n *   enter(node) {\n *     // enter any node\n *   },\n *   leave(node) {\n *     // leave any node\n *   }\n * })\n * ```\n */\n\nexport function visit(root, visitor, visitorKeys = QueryDocumentKeys) {\n  const enterLeaveMap = new Map();\n  for (const kind of Object.values(Kind)) {\n    enterLeaveMap.set(kind, getEnterLeaveForKind(visitor, kind));\n  }\n  /* eslint-disable no-undef-init */\n\n  let stack = undefined;\n  let inArray = Array.isArray(root);\n  let keys = [root];\n  let index = -1;\n  let edits = [];\n  let node = root;\n  let key = undefined;\n  let parent = undefined;\n  const path = [];\n  const ancestors = [];\n  /* eslint-enable no-undef-init */\n\n  do {\n    index++;\n    const isLeaving = index === keys.length;\n    const isEdited = isLeaving && edits.length !== 0;\n    if (isLeaving) {\n      key = ancestors.length === 0 ? undefined : path[path.length - 1];\n      node = parent;\n      parent = ancestors.pop();\n      if (isEdited) {\n        if (inArray) {\n          node = node.slice();\n          let editOffset = 0;\n          for (const [editKey, editValue] of edits) {\n            const arrayKey = editKey - editOffset;\n            if (editValue === null) {\n              node.splice(arrayKey, 1);\n              editOffset++;\n            } else {\n              node[arrayKey] = editValue;\n            }\n          }\n        } else {\n          node = {\n            ...node\n          };\n          for (const [editKey, editValue] of edits) {\n            node[editKey] = editValue;\n          }\n        }\n      }\n      index = stack.index;\n      keys = stack.keys;\n      edits = stack.edits;\n      inArray = stack.inArray;\n      stack = stack.prev;\n    } else if (parent) {\n      key = inArray ? index : keys[index];\n      node = parent[key];\n      if (node === null || node === undefined) {\n        continue;\n      }\n      path.push(key);\n    }\n    let result;\n    if (!Array.isArray(node)) {\n      var _enterLeaveMap$get, _enterLeaveMap$get2;\n      isNode(node) || devAssert(false, `Invalid AST Node: ${inspect(node)}.`);\n      const visitFn = isLeaving ? (_enterLeaveMap$get = enterLeaveMap.get(node.kind)) === null || _enterLeaveMap$get === void 0 ? void 0 : _enterLeaveMap$get.leave : (_enterLeaveMap$get2 = enterLeaveMap.get(node.kind)) === null || _enterLeaveMap$get2 === void 0 ? void 0 : _enterLeaveMap$get2.enter;\n      result = visitFn === null || visitFn === void 0 ? void 0 : visitFn.call(visitor, node, key, parent, path, ancestors);\n      if (result === BREAK) {\n        break;\n      }\n      if (result === false) {\n        if (!isLeaving) {\n          path.pop();\n          continue;\n        }\n      } else if (result !== undefined) {\n        edits.push([key, result]);\n        if (!isLeaving) {\n          if (isNode(result)) {\n            node = result;\n          } else {\n            path.pop();\n            continue;\n          }\n        }\n      }\n    }\n    if (result === undefined && isEdited) {\n      edits.push([key, node]);\n    }\n    if (isLeaving) {\n      path.pop();\n    } else {\n      var _node$kind;\n      stack = {\n        inArray,\n        index,\n        keys,\n        edits,\n        prev: stack\n      };\n      inArray = Array.isArray(node);\n      keys = inArray ? node : (_node$kind = visitorKeys[node.kind]) !== null && _node$kind !== void 0 ? _node$kind : [];\n      index = -1;\n      edits = [];\n      if (parent) {\n        ancestors.push(parent);\n      }\n      parent = node;\n    }\n  } while (stack !== undefined);\n  if (edits.length !== 0) {\n    // New root\n    return edits[edits.length - 1][1];\n  }\n  return root;\n}\n/**\n * Creates a new visitor instance which delegates to many visitors to run in\n * parallel. Each visitor will be visited for each node before moving on.\n *\n * If a prior visitor edits a node, no following visitors will see that node.\n */\n\nexport function visitInParallel(visitors) {\n  const skipping = new Array(visitors.length).fill(null);\n  const mergedVisitor = Object.create(null);\n  for (const kind of Object.values(Kind)) {\n    let hasVisitor = false;\n    const enterList = new Array(visitors.length).fill(undefined);\n    const leaveList = new Array(visitors.length).fill(undefined);\n    for (let i = 0; i < visitors.length; ++i) {\n      const {\n        enter,\n        leave\n      } = getEnterLeaveForKind(visitors[i], kind);\n      hasVisitor || (hasVisitor = enter != null || leave != null);\n      enterList[i] = enter;\n      leaveList[i] = leave;\n    }\n    if (!hasVisitor) {\n      continue;\n    }\n    const mergedEnterLeave = {\n      enter(...args) {\n        const node = args[0];\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _enterList$i;\n            const result = (_enterList$i = enterList[i]) === null || _enterList$i === void 0 ? void 0 : _enterList$i.apply(visitors[i], args);\n            if (result === false) {\n              skipping[i] = node;\n            } else if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined) {\n              return result;\n            }\n          }\n        }\n      },\n      leave(...args) {\n        const node = args[0];\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _leaveList$i;\n            const result = (_leaveList$i = leaveList[i]) === null || _leaveList$i === void 0 ? void 0 : _leaveList$i.apply(visitors[i], args);\n            if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined && result !== false) {\n              return result;\n            }\n          } else if (skipping[i] === node) {\n            skipping[i] = null;\n          }\n        }\n      }\n    };\n    mergedVisitor[kind] = mergedEnterLeave;\n  }\n  return mergedVisitor;\n}\n/**\n * Given a visitor instance and a node kind, return EnterLeaveVisitor for that kind.\n */\n\nexport function getEnterLeaveForKind(visitor, kind) {\n  const kindVisitor = visitor[kind];\n  if (typeof kindVisitor === 'object') {\n    // { Kind: { enter() {}, leave() {} } }\n    return kindVisitor;\n  } else if (typeof kindVisitor === 'function') {\n    // { Kind() {} }\n    return {\n      enter: kindVisitor,\n      leave: undefined\n    };\n  } // { enter() {}, leave() {} }\n\n  return {\n    enter: visitor.enter,\n    leave: visitor.leave\n  };\n}\n/**\n * Given a visitor instance, if it is leaving or not, and a node kind, return\n * the function the visitor runtime should call.\n *\n * @deprecated Please use `getEnterLeaveForKind` instead. Will be removed in v17\n */\n\n/* c8 ignore next 8 */\n\nexport function getVisitFn(visitor, kind, isLeaving) {\n  const {\n    enter,\n    leave\n  } = getEnterLeaveForKind(visitor, kind);\n  return isLeaving ? leave : enter;\n}", "map": {"version": 3, "names": ["devAssert", "inspect", "isNode", "QueryDocumentKeys", "Kind", "BREAK", "Object", "freeze", "visit", "root", "visitor", "visitorKeys", "enterLeaveMap", "Map", "kind", "values", "set", "getEnterLeaveForKind", "stack", "undefined", "inArray", "Array", "isArray", "keys", "index", "edits", "node", "key", "parent", "path", "ancestors", "isLeaving", "length", "isEdited", "pop", "slice", "editOffset", "<PERSON><PERSON><PERSON>", "editValue", "<PERSON><PERSON><PERSON>", "splice", "prev", "push", "result", "_enterLeaveMap$get", "_enterLeaveMap$get2", "visitFn", "get", "leave", "enter", "call", "_node$kind", "visitInParallel", "visitors", "skipping", "fill", "mergedVisitor", "create", "hasVisitor", "enterList", "leaveList", "i", "mergedEnterLeave", "args", "_enterList$i", "apply", "_leaveList$i", "kindVisitor", "getVisitFn"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/language/visitor.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isNode, QueryDocumentKeys } from './ast.mjs';\nimport { Kind } from './kinds.mjs';\n/**\n * A visitor is provided to visit, it contains the collection of\n * relevant functions to be called during the visitor's traversal.\n */\n\nexport const BREAK = Object.freeze({});\n/**\n * visit() will walk through an AST using a depth-first traversal, calling\n * the visitor's enter function at each node in the traversal, and calling the\n * leave function after visiting that node and all of its child nodes.\n *\n * By returning different values from the enter and leave functions, the\n * behavior of the visitor can be altered, including skipping over a sub-tree of\n * the AST (by returning false), editing the AST by returning a value or null\n * to remove the value, or to stop the whole traversal by returning BREAK.\n *\n * When using visit() to edit an AST, the original AST will not be modified, and\n * a new version of the AST with the changes applied will be returned from the\n * visit function.\n *\n * ```ts\n * const editedAST = visit(ast, {\n *   enter(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: skip visiting this node\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   },\n *   leave(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: no action\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   }\n * });\n * ```\n *\n * Alternatively to providing enter() and leave() functions, a visitor can\n * instead provide functions named the same as the kinds of AST nodes, or\n * enter/leave visitors at a named key, leading to three permutations of the\n * visitor API:\n *\n * 1) Named visitors triggered when entering a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind(node) {\n *     // enter the \"Kind\" node\n *   }\n * })\n * ```\n *\n * 2) Named visitors that trigger upon entering and leaving a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind: {\n *     enter(node) {\n *       // enter the \"Kind\" node\n *     }\n *     leave(node) {\n *       // leave the \"Kind\" node\n *     }\n *   }\n * })\n * ```\n *\n * 3) Generic visitors that trigger upon entering and leaving any node.\n *\n * ```ts\n * visit(ast, {\n *   enter(node) {\n *     // enter any node\n *   },\n *   leave(node) {\n *     // leave any node\n *   }\n * })\n * ```\n */\n\nexport function visit(root, visitor, visitorKeys = QueryDocumentKeys) {\n  const enterLeaveMap = new Map();\n\n  for (const kind of Object.values(Kind)) {\n    enterLeaveMap.set(kind, getEnterLeaveForKind(visitor, kind));\n  }\n  /* eslint-disable no-undef-init */\n\n  let stack = undefined;\n  let inArray = Array.isArray(root);\n  let keys = [root];\n  let index = -1;\n  let edits = [];\n  let node = root;\n  let key = undefined;\n  let parent = undefined;\n  const path = [];\n  const ancestors = [];\n  /* eslint-enable no-undef-init */\n\n  do {\n    index++;\n    const isLeaving = index === keys.length;\n    const isEdited = isLeaving && edits.length !== 0;\n\n    if (isLeaving) {\n      key = ancestors.length === 0 ? undefined : path[path.length - 1];\n      node = parent;\n      parent = ancestors.pop();\n\n      if (isEdited) {\n        if (inArray) {\n          node = node.slice();\n          let editOffset = 0;\n\n          for (const [editKey, editValue] of edits) {\n            const arrayKey = editKey - editOffset;\n\n            if (editValue === null) {\n              node.splice(arrayKey, 1);\n              editOffset++;\n            } else {\n              node[arrayKey] = editValue;\n            }\n          }\n        } else {\n          node = { ...node };\n\n          for (const [editKey, editValue] of edits) {\n            node[editKey] = editValue;\n          }\n        }\n      }\n\n      index = stack.index;\n      keys = stack.keys;\n      edits = stack.edits;\n      inArray = stack.inArray;\n      stack = stack.prev;\n    } else if (parent) {\n      key = inArray ? index : keys[index];\n      node = parent[key];\n\n      if (node === null || node === undefined) {\n        continue;\n      }\n\n      path.push(key);\n    }\n\n    let result;\n\n    if (!Array.isArray(node)) {\n      var _enterLeaveMap$get, _enterLeaveMap$get2;\n\n      isNode(node) || devAssert(false, `Invalid AST Node: ${inspect(node)}.`);\n      const visitFn = isLeaving\n        ? (_enterLeaveMap$get = enterLeaveMap.get(node.kind)) === null ||\n          _enterLeaveMap$get === void 0\n          ? void 0\n          : _enterLeaveMap$get.leave\n        : (_enterLeaveMap$get2 = enterLeaveMap.get(node.kind)) === null ||\n          _enterLeaveMap$get2 === void 0\n        ? void 0\n        : _enterLeaveMap$get2.enter;\n      result =\n        visitFn === null || visitFn === void 0\n          ? void 0\n          : visitFn.call(visitor, node, key, parent, path, ancestors);\n\n      if (result === BREAK) {\n        break;\n      }\n\n      if (result === false) {\n        if (!isLeaving) {\n          path.pop();\n          continue;\n        }\n      } else if (result !== undefined) {\n        edits.push([key, result]);\n\n        if (!isLeaving) {\n          if (isNode(result)) {\n            node = result;\n          } else {\n            path.pop();\n            continue;\n          }\n        }\n      }\n    }\n\n    if (result === undefined && isEdited) {\n      edits.push([key, node]);\n    }\n\n    if (isLeaving) {\n      path.pop();\n    } else {\n      var _node$kind;\n\n      stack = {\n        inArray,\n        index,\n        keys,\n        edits,\n        prev: stack,\n      };\n      inArray = Array.isArray(node);\n      keys = inArray\n        ? node\n        : (_node$kind = visitorKeys[node.kind]) !== null &&\n          _node$kind !== void 0\n        ? _node$kind\n        : [];\n      index = -1;\n      edits = [];\n\n      if (parent) {\n        ancestors.push(parent);\n      }\n\n      parent = node;\n    }\n  } while (stack !== undefined);\n\n  if (edits.length !== 0) {\n    // New root\n    return edits[edits.length - 1][1];\n  }\n\n  return root;\n}\n/**\n * Creates a new visitor instance which delegates to many visitors to run in\n * parallel. Each visitor will be visited for each node before moving on.\n *\n * If a prior visitor edits a node, no following visitors will see that node.\n */\n\nexport function visitInParallel(visitors) {\n  const skipping = new Array(visitors.length).fill(null);\n  const mergedVisitor = Object.create(null);\n\n  for (const kind of Object.values(Kind)) {\n    let hasVisitor = false;\n    const enterList = new Array(visitors.length).fill(undefined);\n    const leaveList = new Array(visitors.length).fill(undefined);\n\n    for (let i = 0; i < visitors.length; ++i) {\n      const { enter, leave } = getEnterLeaveForKind(visitors[i], kind);\n      hasVisitor || (hasVisitor = enter != null || leave != null);\n      enterList[i] = enter;\n      leaveList[i] = leave;\n    }\n\n    if (!hasVisitor) {\n      continue;\n    }\n\n    const mergedEnterLeave = {\n      enter(...args) {\n        const node = args[0];\n\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _enterList$i;\n\n            const result =\n              (_enterList$i = enterList[i]) === null || _enterList$i === void 0\n                ? void 0\n                : _enterList$i.apply(visitors[i], args);\n\n            if (result === false) {\n              skipping[i] = node;\n            } else if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined) {\n              return result;\n            }\n          }\n        }\n      },\n\n      leave(...args) {\n        const node = args[0];\n\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _leaveList$i;\n\n            const result =\n              (_leaveList$i = leaveList[i]) === null || _leaveList$i === void 0\n                ? void 0\n                : _leaveList$i.apply(visitors[i], args);\n\n            if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined && result !== false) {\n              return result;\n            }\n          } else if (skipping[i] === node) {\n            skipping[i] = null;\n          }\n        }\n      },\n    };\n    mergedVisitor[kind] = mergedEnterLeave;\n  }\n\n  return mergedVisitor;\n}\n/**\n * Given a visitor instance and a node kind, return EnterLeaveVisitor for that kind.\n */\n\nexport function getEnterLeaveForKind(visitor, kind) {\n  const kindVisitor = visitor[kind];\n\n  if (typeof kindVisitor === 'object') {\n    // { Kind: { enter() {}, leave() {} } }\n    return kindVisitor;\n  } else if (typeof kindVisitor === 'function') {\n    // { Kind() {} }\n    return {\n      enter: kindVisitor,\n      leave: undefined,\n    };\n  } // { enter() {}, leave() {} }\n\n  return {\n    enter: visitor.enter,\n    leave: visitor.leave,\n  };\n}\n/**\n * Given a visitor instance, if it is leaving or not, and a node kind, return\n * the function the visitor runtime should call.\n *\n * @deprecated Please use `getEnterLeaveForKind` instead. Will be removed in v17\n */\n\n/* c8 ignore next 8 */\n\nexport function getVisitFn(visitor, kind, isLeaving) {\n  const { enter, leave } = getEnterLeaveForKind(visitor, kind);\n  return isLeaving ? leave : enter;\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,MAAM,EAAEC,iBAAiB,QAAQ,WAAW;AACrD,SAASC,IAAI,QAAQ,aAAa;AAClC;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAEC,OAAO,EAAEC,WAAW,GAAGR,iBAAiB,EAAE;EACpE,MAAMS,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;EAE/B,KAAK,MAAMC,IAAI,IAAIR,MAAM,CAACS,MAAM,CAACX,IAAI,CAAC,EAAE;IACtCQ,aAAa,CAACI,GAAG,CAACF,IAAI,EAAEG,oBAAoB,CAACP,OAAO,EAAEI,IAAI,CAAC,CAAC;EAC9D;EACA;;EAEA,IAAII,KAAK,GAAGC,SAAS;EACrB,IAAIC,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACb,IAAI,CAAC;EACjC,IAAIc,IAAI,GAAG,CAACd,IAAI,CAAC;EACjB,IAAIe,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,IAAI,GAAGjB,IAAI;EACf,IAAIkB,GAAG,GAAGR,SAAS;EACnB,IAAIS,MAAM,GAAGT,SAAS;EACtB,MAAMU,IAAI,GAAG,EAAE;EACf,MAAMC,SAAS,GAAG,EAAE;EACpB;;EAEA,GAAG;IACDN,KAAK,EAAE;IACP,MAAMO,SAAS,GAAGP,KAAK,KAAKD,IAAI,CAACS,MAAM;IACvC,MAAMC,QAAQ,GAAGF,SAAS,IAAIN,KAAK,CAACO,MAAM,KAAK,CAAC;IAEhD,IAAID,SAAS,EAAE;MACbJ,GAAG,GAAGG,SAAS,CAACE,MAAM,KAAK,CAAC,GAAGb,SAAS,GAAGU,IAAI,CAACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;MAChEN,IAAI,GAAGE,MAAM;MACbA,MAAM,GAAGE,SAAS,CAACI,GAAG,CAAC,CAAC;MAExB,IAAID,QAAQ,EAAE;QACZ,IAAIb,OAAO,EAAE;UACXM,IAAI,GAAGA,IAAI,CAACS,KAAK,CAAC,CAAC;UACnB,IAAIC,UAAU,GAAG,CAAC;UAElB,KAAK,MAAM,CAACC,OAAO,EAAEC,SAAS,CAAC,IAAIb,KAAK,EAAE;YACxC,MAAMc,QAAQ,GAAGF,OAAO,GAAGD,UAAU;YAErC,IAAIE,SAAS,KAAK,IAAI,EAAE;cACtBZ,IAAI,CAACc,MAAM,CAACD,QAAQ,EAAE,CAAC,CAAC;cACxBH,UAAU,EAAE;YACd,CAAC,MAAM;cACLV,IAAI,CAACa,QAAQ,CAAC,GAAGD,SAAS;YAC5B;UACF;QACF,CAAC,MAAM;UACLZ,IAAI,GAAG;YAAE,GAAGA;UAAK,CAAC;UAElB,KAAK,MAAM,CAACW,OAAO,EAAEC,SAAS,CAAC,IAAIb,KAAK,EAAE;YACxCC,IAAI,CAACW,OAAO,CAAC,GAAGC,SAAS;UAC3B;QACF;MACF;MAEAd,KAAK,GAAGN,KAAK,CAACM,KAAK;MACnBD,IAAI,GAAGL,KAAK,CAACK,IAAI;MACjBE,KAAK,GAAGP,KAAK,CAACO,KAAK;MACnBL,OAAO,GAAGF,KAAK,CAACE,OAAO;MACvBF,KAAK,GAAGA,KAAK,CAACuB,IAAI;IACpB,CAAC,MAAM,IAAIb,MAAM,EAAE;MACjBD,GAAG,GAAGP,OAAO,GAAGI,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC;MACnCE,IAAI,GAAGE,MAAM,CAACD,GAAG,CAAC;MAElB,IAAID,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKP,SAAS,EAAE;QACvC;MACF;MAEAU,IAAI,CAACa,IAAI,CAACf,GAAG,CAAC;IAChB;IAEA,IAAIgB,MAAM;IAEV,IAAI,CAACtB,KAAK,CAACC,OAAO,CAACI,IAAI,CAAC,EAAE;MACxB,IAAIkB,kBAAkB,EAAEC,mBAAmB;MAE3C3C,MAAM,CAACwB,IAAI,CAAC,IAAI1B,SAAS,CAAC,KAAK,EAAE,qBAAqBC,OAAO,CAACyB,IAAI,CAAC,GAAG,CAAC;MACvE,MAAMoB,OAAO,GAAGf,SAAS,GACrB,CAACa,kBAAkB,GAAGhC,aAAa,CAACmC,GAAG,CAACrB,IAAI,CAACZ,IAAI,CAAC,MAAM,IAAI,IAC5D8B,kBAAkB,KAAK,KAAK,CAAC,GAC3B,KAAK,CAAC,GACNA,kBAAkB,CAACI,KAAK,GAC1B,CAACH,mBAAmB,GAAGjC,aAAa,CAACmC,GAAG,CAACrB,IAAI,CAACZ,IAAI,CAAC,MAAM,IAAI,IAC7D+B,mBAAmB,KAAK,KAAK,CAAC,GAC9B,KAAK,CAAC,GACNA,mBAAmB,CAACI,KAAK;MAC7BN,MAAM,GACJG,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAClC,KAAK,CAAC,GACNA,OAAO,CAACI,IAAI,CAACxC,OAAO,EAAEgB,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,CAAC;MAE/D,IAAIa,MAAM,KAAKtC,KAAK,EAAE;QACpB;MACF;MAEA,IAAIsC,MAAM,KAAK,KAAK,EAAE;QACpB,IAAI,CAACZ,SAAS,EAAE;UACdF,IAAI,CAACK,GAAG,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM,IAAIS,MAAM,KAAKxB,SAAS,EAAE;QAC/BM,KAAK,CAACiB,IAAI,CAAC,CAACf,GAAG,EAAEgB,MAAM,CAAC,CAAC;QAEzB,IAAI,CAACZ,SAAS,EAAE;UACd,IAAI7B,MAAM,CAACyC,MAAM,CAAC,EAAE;YAClBjB,IAAI,GAAGiB,MAAM;UACf,CAAC,MAAM;YACLd,IAAI,CAACK,GAAG,CAAC,CAAC;YACV;UACF;QACF;MACF;IACF;IAEA,IAAIS,MAAM,KAAKxB,SAAS,IAAIc,QAAQ,EAAE;MACpCR,KAAK,CAACiB,IAAI,CAAC,CAACf,GAAG,EAAED,IAAI,CAAC,CAAC;IACzB;IAEA,IAAIK,SAAS,EAAE;MACbF,IAAI,CAACK,GAAG,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAIiB,UAAU;MAEdjC,KAAK,GAAG;QACNE,OAAO;QACPI,KAAK;QACLD,IAAI;QACJE,KAAK;QACLgB,IAAI,EAAEvB;MACR,CAAC;MACDE,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACI,IAAI,CAAC;MAC7BH,IAAI,GAAGH,OAAO,GACVM,IAAI,GACJ,CAACyB,UAAU,GAAGxC,WAAW,CAACe,IAAI,CAACZ,IAAI,CAAC,MAAM,IAAI,IAC9CqC,UAAU,KAAK,KAAK,CAAC,GACrBA,UAAU,GACV,EAAE;MACN3B,KAAK,GAAG,CAAC,CAAC;MACVC,KAAK,GAAG,EAAE;MAEV,IAAIG,MAAM,EAAE;QACVE,SAAS,CAACY,IAAI,CAACd,MAAM,CAAC;MACxB;MAEAA,MAAM,GAAGF,IAAI;IACf;EACF,CAAC,QAAQR,KAAK,KAAKC,SAAS;EAE5B,IAAIM,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;IACtB;IACA,OAAOP,KAAK,CAACA,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC;EAEA,OAAOvB,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAAS2C,eAAeA,CAACC,QAAQ,EAAE;EACxC,MAAMC,QAAQ,GAAG,IAAIjC,KAAK,CAACgC,QAAQ,CAACrB,MAAM,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;EACtD,MAAMC,aAAa,GAAGlD,MAAM,CAACmD,MAAM,CAAC,IAAI,CAAC;EAEzC,KAAK,MAAM3C,IAAI,IAAIR,MAAM,CAACS,MAAM,CAACX,IAAI,CAAC,EAAE;IACtC,IAAIsD,UAAU,GAAG,KAAK;IACtB,MAAMC,SAAS,GAAG,IAAItC,KAAK,CAACgC,QAAQ,CAACrB,MAAM,CAAC,CAACuB,IAAI,CAACpC,SAAS,CAAC;IAC5D,MAAMyC,SAAS,GAAG,IAAIvC,KAAK,CAACgC,QAAQ,CAACrB,MAAM,CAAC,CAACuB,IAAI,CAACpC,SAAS,CAAC;IAE5D,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,QAAQ,CAACrB,MAAM,EAAE,EAAE6B,CAAC,EAAE;MACxC,MAAM;QAAEZ,KAAK;QAAED;MAAM,CAAC,GAAG/B,oBAAoB,CAACoC,QAAQ,CAACQ,CAAC,CAAC,EAAE/C,IAAI,CAAC;MAChE4C,UAAU,KAAKA,UAAU,GAAGT,KAAK,IAAI,IAAI,IAAID,KAAK,IAAI,IAAI,CAAC;MAC3DW,SAAS,CAACE,CAAC,CAAC,GAAGZ,KAAK;MACpBW,SAAS,CAACC,CAAC,CAAC,GAAGb,KAAK;IACtB;IAEA,IAAI,CAACU,UAAU,EAAE;MACf;IACF;IAEA,MAAMI,gBAAgB,GAAG;MACvBb,KAAKA,CAAC,GAAGc,IAAI,EAAE;QACb,MAAMrC,IAAI,GAAGqC,IAAI,CAAC,CAAC,CAAC;QAEpB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,QAAQ,CAACrB,MAAM,EAAE6B,CAAC,EAAE,EAAE;UACxC,IAAIP,QAAQ,CAACO,CAAC,CAAC,KAAK,IAAI,EAAE;YACxB,IAAIG,YAAY;YAEhB,MAAMrB,MAAM,GACV,CAACqB,YAAY,GAAGL,SAAS,CAACE,CAAC,CAAC,MAAM,IAAI,IAAIG,YAAY,KAAK,KAAK,CAAC,GAC7D,KAAK,CAAC,GACNA,YAAY,CAACC,KAAK,CAACZ,QAAQ,CAACQ,CAAC,CAAC,EAAEE,IAAI,CAAC;YAE3C,IAAIpB,MAAM,KAAK,KAAK,EAAE;cACpBW,QAAQ,CAACO,CAAC,CAAC,GAAGnC,IAAI;YACpB,CAAC,MAAM,IAAIiB,MAAM,KAAKtC,KAAK,EAAE;cAC3BiD,QAAQ,CAACO,CAAC,CAAC,GAAGxD,KAAK;YACrB,CAAC,MAAM,IAAIsC,MAAM,KAAKxB,SAAS,EAAE;cAC/B,OAAOwB,MAAM;YACf;UACF;QACF;MACF,CAAC;MAEDK,KAAKA,CAAC,GAAGe,IAAI,EAAE;QACb,MAAMrC,IAAI,GAAGqC,IAAI,CAAC,CAAC,CAAC;QAEpB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,QAAQ,CAACrB,MAAM,EAAE6B,CAAC,EAAE,EAAE;UACxC,IAAIP,QAAQ,CAACO,CAAC,CAAC,KAAK,IAAI,EAAE;YACxB,IAAIK,YAAY;YAEhB,MAAMvB,MAAM,GACV,CAACuB,YAAY,GAAGN,SAAS,CAACC,CAAC,CAAC,MAAM,IAAI,IAAIK,YAAY,KAAK,KAAK,CAAC,GAC7D,KAAK,CAAC,GACNA,YAAY,CAACD,KAAK,CAACZ,QAAQ,CAACQ,CAAC,CAAC,EAAEE,IAAI,CAAC;YAE3C,IAAIpB,MAAM,KAAKtC,KAAK,EAAE;cACpBiD,QAAQ,CAACO,CAAC,CAAC,GAAGxD,KAAK;YACrB,CAAC,MAAM,IAAIsC,MAAM,KAAKxB,SAAS,IAAIwB,MAAM,KAAK,KAAK,EAAE;cACnD,OAAOA,MAAM;YACf;UACF,CAAC,MAAM,IAAIW,QAAQ,CAACO,CAAC,CAAC,KAAKnC,IAAI,EAAE;YAC/B4B,QAAQ,CAACO,CAAC,CAAC,GAAG,IAAI;UACpB;QACF;MACF;IACF,CAAC;IACDL,aAAa,CAAC1C,IAAI,CAAC,GAAGgD,gBAAgB;EACxC;EAEA,OAAON,aAAa;AACtB;AACA;AACA;AACA;;AAEA,OAAO,SAASvC,oBAAoBA,CAACP,OAAO,EAAEI,IAAI,EAAE;EAClD,MAAMqD,WAAW,GAAGzD,OAAO,CAACI,IAAI,CAAC;EAEjC,IAAI,OAAOqD,WAAW,KAAK,QAAQ,EAAE;IACnC;IACA,OAAOA,WAAW;EACpB,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IAC5C;IACA,OAAO;MACLlB,KAAK,EAAEkB,WAAW;MAClBnB,KAAK,EAAE7B;IACT,CAAC;EACH,CAAC,CAAC;;EAEF,OAAO;IACL8B,KAAK,EAAEvC,OAAO,CAACuC,KAAK;IACpBD,KAAK,EAAEtC,OAAO,CAACsC;EACjB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,OAAO,SAASoB,UAAUA,CAAC1D,OAAO,EAAEI,IAAI,EAAEiB,SAAS,EAAE;EACnD,MAAM;IAAEkB,KAAK;IAAED;EAAM,CAAC,GAAG/B,oBAAoB,CAACP,OAAO,EAAEI,IAAI,CAAC;EAC5D,OAAOiB,SAAS,GAAGiB,KAAK,GAAGC,KAAK;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}