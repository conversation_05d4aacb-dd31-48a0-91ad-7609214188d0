{"ast": null, "code": "// Produce the GraphQL query recommended for a full schema introspection.\nexport { getIntrospectionQuery } from './getIntrospectionQuery.mjs';\n// Gets the target Operation from a Document.\nexport { getOperationAST } from './getOperationAST.mjs'; // Gets the Type for the target Operation AST.\n\nexport { getOperationRootType } from './getOperationRootType.mjs'; // Convert a GraphQLSchema to an IntrospectionQuery.\n\nexport { introspectionFromSchema } from './introspectionFromSchema.mjs'; // Build a GraphQLSchema from an introspection result.\n\nexport { buildClientSchema } from './buildClientSchema.mjs'; // Build a GraphQLSchema from GraphQL Schema language.\n\nexport { buildASTSchema, buildSchema } from './buildASTSchema.mjs';\n// Extends an existing GraphQLSchema from a parsed GraphQL Schema language AST.\nexport { extendSchema } from './extendSchema.mjs'; // Sort a GraphQLSchema.\n\nexport { lexicographicSortSchema } from './lexicographicSortSchema.mjs'; // Print a GraphQLSchema to GraphQL Schema language.\n\nexport { printSchema, printType, printIntrospectionSchema } from './printSchema.mjs'; // Create a GraphQLType from a GraphQL language AST.\n\nexport { typeFromAST } from './typeFromAST.mjs'; // Create a JavaScript value from a GraphQL language AST with a type.\n\nexport { valueFromAST } from './valueFromAST.mjs'; // Create a JavaScript value from a GraphQL language AST without a type.\n\nexport { valueFromASTUntyped } from './valueFromASTUntyped.mjs'; // Create a GraphQL language AST from a JavaScript value.\n\nexport { astFromValue } from './astFromValue.mjs'; // A helper to use within recursive-descent visitors which need to be aware of the GraphQL type system.\n\nexport { TypeInfo, visitWithTypeInfo } from './TypeInfo.mjs'; // Coerces a JavaScript value to a GraphQL type, or produces errors.\n\nexport { coerceInputValue } from './coerceInputValue.mjs'; // Concatenates multiple AST together.\n\nexport { concatAST } from './concatAST.mjs'; // Separates an AST into an AST per Operation.\n\nexport { separateOperations } from './separateOperations.mjs'; // Strips characters that are not significant to the validity or execution of a GraphQL document.\n\nexport { stripIgnoredCharacters } from './stripIgnoredCharacters.mjs'; // Comparators for types\n\nexport { isEqualType, isTypeSubTypeOf, doTypesOverlap } from './typeComparators.mjs'; // Asserts that a string is a valid GraphQL name\n\nexport { assertValidName, isValidNameError } from './assertValidName.mjs'; // Compares two GraphQLSchemas and detects breaking changes.\n\nexport { BreakingChangeType, DangerousChangeType, findBreakingChanges, findDangerousChanges } from './findBreakingChanges.mjs';", "map": {"version": 3, "names": ["getIntrospectionQuery", "getOperationAST", "getOperationRootType", "introspectionFromSchema", "buildClientSchema", "buildASTSchema", "buildSchema", "extendSchema", "lexicographicSortSchema", "printSchema", "printType", "printIntrospectionSchema", "typeFromAST", "valueFromAST", "valueFromASTUntyped", "astFromValue", "TypeInfo", "visitWithTypeInfo", "coerceInputValue", "concatAST", "separateOperations", "stripIgnoredCharacters", "isEqualType", "isTypeSubTypeOf", "doTypesOverlap", "assertValidName", "isValidNameError", "BreakingChangeType", "DangerousChangeType", "findBreakingChanges", "findDangerousChanges"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/utilities/index.mjs"], "sourcesContent": ["// Produce the GraphQL query recommended for a full schema introspection.\nexport { getIntrospectionQuery } from './getIntrospectionQuery.mjs';\n// Gets the target Operation from a Document.\nexport { getOperationAST } from './getOperationAST.mjs'; // Gets the Type for the target Operation AST.\n\nexport { getOperationRootType } from './getOperationRootType.mjs'; // Convert a GraphQLSchema to an IntrospectionQuery.\n\nexport { introspectionFromSchema } from './introspectionFromSchema.mjs'; // Build a GraphQLSchema from an introspection result.\n\nexport { buildClientSchema } from './buildClientSchema.mjs'; // Build a GraphQLSchema from GraphQL Schema language.\n\nexport { buildASTSchema, buildSchema } from './buildASTSchema.mjs';\n// Extends an existing GraphQLSchema from a parsed GraphQL Schema language AST.\nexport { extendSchema } from './extendSchema.mjs'; // Sort a GraphQLSchema.\n\nexport { lexicographicSortSchema } from './lexicographicSortSchema.mjs'; // Print a GraphQLSchema to GraphQL Schema language.\n\nexport {\n  printSchema,\n  printType,\n  printIntrospectionSchema,\n} from './printSchema.mjs'; // Create a GraphQLType from a GraphQL language AST.\n\nexport { typeFromAST } from './typeFromAST.mjs'; // Create a JavaScript value from a GraphQL language AST with a type.\n\nexport { valueFromAST } from './valueFromAST.mjs'; // Create a JavaScript value from a GraphQL language AST without a type.\n\nexport { valueFromASTUntyped } from './valueFromASTUntyped.mjs'; // Create a GraphQL language AST from a JavaScript value.\n\nexport { astFromValue } from './astFromValue.mjs'; // A helper to use within recursive-descent visitors which need to be aware of the GraphQL type system.\n\nexport { TypeInfo, visitWithTypeInfo } from './TypeInfo.mjs'; // Coerces a JavaScript value to a GraphQL type, or produces errors.\n\nexport { coerceInputValue } from './coerceInputValue.mjs'; // Concatenates multiple AST together.\n\nexport { concatAST } from './concatAST.mjs'; // Separates an AST into an AST per Operation.\n\nexport { separateOperations } from './separateOperations.mjs'; // Strips characters that are not significant to the validity or execution of a GraphQL document.\n\nexport { stripIgnoredCharacters } from './stripIgnoredCharacters.mjs'; // Comparators for types\n\nexport {\n  isEqualType,\n  isTypeSubTypeOf,\n  doTypesOverlap,\n} from './typeComparators.mjs'; // Asserts that a string is a valid GraphQL name\n\nexport { assertValidName, isValidNameError } from './assertValidName.mjs'; // Compares two GraphQLSchemas and detects breaking changes.\n\nexport {\n  BreakingChangeType,\n  DangerousChangeType,\n  findBreakingChanges,\n  findDangerousChanges,\n} from './findBreakingChanges.mjs';\n"], "mappings": "AAAA;AACA,SAASA,qBAAqB,QAAQ,6BAA6B;AACnE;AACA,SAASC,eAAe,QAAQ,uBAAuB,CAAC,CAAC;;AAEzD,SAASC,oBAAoB,QAAQ,4BAA4B,CAAC,CAAC;;AAEnE,SAASC,uBAAuB,QAAQ,+BAA+B,CAAC,CAAC;;AAEzE,SAASC,iBAAiB,QAAQ,yBAAyB,CAAC,CAAC;;AAE7D,SAASC,cAAc,EAAEC,WAAW,QAAQ,sBAAsB;AAClE;AACA,SAASC,YAAY,QAAQ,oBAAoB,CAAC,CAAC;;AAEnD,SAASC,uBAAuB,QAAQ,+BAA+B,CAAC,CAAC;;AAEzE,SACEC,WAAW,EACXC,SAAS,EACTC,wBAAwB,QACnB,mBAAmB,CAAC,CAAC;;AAE5B,SAASC,WAAW,QAAQ,mBAAmB,CAAC,CAAC;;AAEjD,SAASC,YAAY,QAAQ,oBAAoB,CAAC,CAAC;;AAEnD,SAASC,mBAAmB,QAAQ,2BAA2B,CAAC,CAAC;;AAEjE,SAASC,YAAY,QAAQ,oBAAoB,CAAC,CAAC;;AAEnD,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,gBAAgB,CAAC,CAAC;;AAE9D,SAASC,gBAAgB,QAAQ,wBAAwB,CAAC,CAAC;;AAE3D,SAASC,SAAS,QAAQ,iBAAiB,CAAC,CAAC;;AAE7C,SAASC,kBAAkB,QAAQ,0BAA0B,CAAC,CAAC;;AAE/D,SAASC,sBAAsB,QAAQ,8BAA8B,CAAC,CAAC;;AAEvE,SACEC,WAAW,EACXC,eAAe,EACfC,cAAc,QACT,uBAAuB,CAAC,CAAC;;AAEhC,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,uBAAuB,CAAC,CAAC;;AAE3E,SACEC,kBAAkB,EAClBC,mBAAmB,EACnBC,mBAAmB,EACnBC,oBAAoB,QACf,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}