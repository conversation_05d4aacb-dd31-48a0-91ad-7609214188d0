{"ast": null, "code": "import { groupBy } from '../../jsutils/groupBy.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique variable names\n *\n * A GraphQL operation is only valid if all its variables are uniquely named.\n */\nexport function UniqueVariableNamesRule(context) {\n  return {\n    OperationDefinition(operationNode) {\n      var _operationNode$variab;\n\n      // See: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const variableDefinitions = (_operationNode$variab = operationNode.variableDefinitions) !== null && _operationNode$variab !== void 0 ? _operationNode$variab : [];\n      const seenVariableDefinitions = groupBy(variableDefinitions, node => node.variable.name.value);\n      for (const [variableName, variableNodes] of seenVariableDefinitions) {\n        if (variableNodes.length > 1) {\n          context.reportError(new GraphQLError(`There can be only one variable named \"$${variableName}\".`, {\n            nodes: variableNodes.map(node => node.variable.name)\n          }));\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["groupBy", "GraphQLError", "UniqueVariableNamesRule", "context", "OperationDefinition", "operationNode", "_operationNode$variab", "variableDefinitions", "seenVariableDefinitions", "node", "variable", "name", "value", "variableName", "variableNodes", "length", "reportError", "nodes", "map"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/validation/rules/UniqueVariableNamesRule.mjs"], "sourcesContent": ["import { groupBy } from '../../jsutils/groupBy.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique variable names\n *\n * A GraphQL operation is only valid if all its variables are uniquely named.\n */\nexport function UniqueVariableNamesRule(context) {\n  return {\n    OperationDefinition(operationNode) {\n      var _operationNode$variab;\n\n      // See: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const variableDefinitions =\n        (_operationNode$variab = operationNode.variableDefinitions) !== null &&\n        _operationNode$variab !== void 0\n          ? _operationNode$variab\n          : [];\n      const seenVariableDefinitions = groupBy(\n        variableDefinitions,\n        (node) => node.variable.name.value,\n      );\n\n      for (const [variableName, variableNodes] of seenVariableDefinitions) {\n        if (variableNodes.length > 1) {\n          context.reportError(\n            new GraphQLError(\n              `There can be only one variable named \"$${variableName}\".`,\n              {\n                nodes: variableNodes.map((node) => node.variable.name),\n              },\n            ),\n          );\n        }\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,OAAO,EAAE;EAC/C,OAAO;IACLC,mBAAmBA,CAACC,aAAa,EAAE;MACjC,IAAIC,qBAAqB;;MAEzB;;MAEA;MACA,MAAMC,mBAAmB,GACvB,CAACD,qBAAqB,GAAGD,aAAa,CAACE,mBAAmB,MAAM,IAAI,IACpED,qBAAqB,KAAK,KAAK,CAAC,GAC5BA,qBAAqB,GACrB,EAAE;MACR,MAAME,uBAAuB,GAAGR,OAAO,CACrCO,mBAAmB,EAClBE,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,KAC/B,CAAC;MAED,KAAK,MAAM,CAACC,YAAY,EAAEC,aAAa,CAAC,IAAIN,uBAAuB,EAAE;QACnE,IAAIM,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;UAC5BZ,OAAO,CAACa,WAAW,CACjB,IAAIf,YAAY,CACd,0CAA0CY,YAAY,IAAI,EAC1D;YACEI,KAAK,EAAEH,aAAa,CAACI,GAAG,CAAET,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAACC,IAAI;UACvD,CACF,CACF,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}