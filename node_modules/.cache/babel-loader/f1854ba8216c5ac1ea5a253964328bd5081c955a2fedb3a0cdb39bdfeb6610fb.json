{"ast": null, "code": "import { devUtils } from '../../utils/internal/devUtils.mjs';\nimport { getTimestamp } from '../../utils/logging/getTimestamp.mjs';\nimport { toPublicUrl } from '../../utils/request/toPublicUrl.mjs';\nimport { getMessageLength } from './getMessageLength.mjs';\nimport { getPublicData } from './getPublicData.mjs';\nconst colors = {\n  system: \"#3b82f6\",\n  outgoing: \"#22c55e\",\n  incoming: \"#ef4444\",\n  mocked: \"#ff6a33\"\n};\nfunction attachWebSocketLogger(connection) {\n  const {\n    client,\n    server\n  } = connection;\n  logConnectionOpen(client);\n  client.addEventListener(\"message\", event => {\n    logOutgoingClientMessage(event);\n  });\n  client.addEventListener(\"close\", event => {\n    logConnectionClose(event);\n  });\n  client.socket.addEventListener(\"error\", event => {\n    logClientError(event);\n  });\n  client.send = new Proxy(client.send, {\n    apply(target, thisArg, args) {\n      const [data] = args;\n      const messageEvent = new MessageEvent(\"message\", {\n        data\n      });\n      Object.defineProperties(messageEvent, {\n        currentTarget: {\n          enumerable: true,\n          writable: false,\n          value: client.socket\n        },\n        target: {\n          enumerable: true,\n          writable: false,\n          value: client.socket\n        }\n      });\n      queueMicrotask(() => {\n        logIncomingMockedClientMessage(messageEvent);\n      });\n      return Reflect.apply(target, thisArg, args);\n    }\n  });\n  server.addEventListener(\"open\", () => {\n    server.addEventListener(\"message\", event => {\n      logIncomingServerMessage(event);\n    });\n  }, {\n    once: true\n  });\n  server.send = new Proxy(server.send, {\n    apply(target, thisArg, args) {\n      const [data] = args;\n      const messageEvent = new MessageEvent(\"message\", {\n        data\n      });\n      Object.defineProperties(messageEvent, {\n        currentTarget: {\n          enumerable: true,\n          writable: false,\n          value: server.socket\n        },\n        target: {\n          enumerable: true,\n          writable: false,\n          value: server.socket\n        }\n      });\n      logOutgoingMockedClientMessage(messageEvent);\n      return Reflect.apply(target, thisArg, args);\n    }\n  });\n}\nfunction logConnectionOpen(client) {\n  const publicUrl = toPublicUrl(client.url);\n  console.groupCollapsed(devUtils.formatMessage(`${getTimestamp()} %c\\u25B6%c ${publicUrl}`), `color:${colors.system}`, \"color:inherit\");\n  console.log(\"Client:\", client.socket);\n  console.groupEnd();\n}\nfunction logConnectionClose(event) {\n  const target = event.target;\n  const publicUrl = toPublicUrl(target.url);\n  console.groupCollapsed(devUtils.formatMessage(`${getTimestamp({\n    milliseconds: true\n  })} %c\\u25A0%c ${publicUrl}`), `color:${colors.system}`, \"color:inherit\");\n  console.log(event);\n  console.groupEnd();\n}\nfunction logClientError(event) {\n  const socket = event.target;\n  const publicUrl = toPublicUrl(socket.url);\n  console.groupCollapsed(devUtils.formatMessage(`${getTimestamp({\n    milliseconds: true\n  })} %c\\xD7%c ${publicUrl}`), `color:${colors.system}`, \"color:inherit\");\n  console.log(event);\n  console.groupEnd();\n}\nasync function logOutgoingClientMessage(event) {\n  const byteLength = getMessageLength(event.data);\n  const publicData = await getPublicData(event.data);\n  const arrow = event.defaultPrevented ? \"\\u21E1\" : \"\\u2B06\";\n  console.groupCollapsed(devUtils.formatMessage(`${getTimestamp({\n    milliseconds: true\n  })} %c${arrow}%c ${publicData} %c${byteLength}%c`), `color:${colors.outgoing}`, \"color:inherit\", \"color:gray;font-weight:normal\", \"color:inherit;font-weight:inherit\");\n  console.log(event);\n  console.groupEnd();\n}\nasync function logOutgoingMockedClientMessage(event) {\n  const byteLength = getMessageLength(event.data);\n  const publicData = await getPublicData(event.data);\n  console.groupCollapsed(devUtils.formatMessage(`${getTimestamp({\n    milliseconds: true\n  })} %c\\u2B06%c ${publicData} %c${byteLength}%c`), `color:${colors.mocked}`, \"color:inherit\", \"color:gray;font-weight:normal\", \"color:inherit;font-weight:inherit\");\n  console.log(event);\n  console.groupEnd();\n}\nasync function logIncomingMockedClientMessage(event) {\n  const byteLength = getMessageLength(event.data);\n  const publicData = await getPublicData(event.data);\n  console.groupCollapsed(devUtils.formatMessage(`${getTimestamp({\n    milliseconds: true\n  })} %c\\u2B07%c ${publicData} %c${byteLength}%c`), `color:${colors.mocked}`, \"color:inherit\", \"color:gray;font-weight:normal\", \"color:inherit;font-weight:inherit\");\n  console.log(event);\n  console.groupEnd();\n}\nasync function logIncomingServerMessage(event) {\n  const byteLength = getMessageLength(event.data);\n  const publicData = await getPublicData(event.data);\n  const arrow = event.defaultPrevented ? \"\\u21E3\" : \"\\u2B07\";\n  console.groupCollapsed(devUtils.formatMessage(`${getTimestamp({\n    milliseconds: true\n  })} %c${arrow}%c ${publicData} %c${byteLength}%c`), `color:${colors.incoming}`, \"color:inherit\", \"color:gray;font-weight:normal\", \"color:inherit;font-weight:inherit\");\n  console.log(event);\n  console.groupEnd();\n}\nexport { attachWebSocketLogger, logConnectionOpen };", "map": {"version": 3, "names": ["devU<PERSON>s", "getTimestamp", "toPublicUrl", "getMessageLength", "getPublicData", "colors", "system", "outgoing", "incoming", "mocked", "attachWebSocketLogger", "connection", "client", "server", "logConnectionOpen", "addEventListener", "event", "logOutgoingClientMessage", "logConnectionClose", "socket", "logClientError", "send", "Proxy", "apply", "target", "thisArg", "args", "data", "messageEvent", "MessageEvent", "Object", "defineProperties", "currentTarget", "enumerable", "writable", "value", "queueMicrotask", "logIncomingMockedClientMessage", "Reflect", "logIncomingServerMessage", "once", "logOutgoingMockedClientMessage", "publicUrl", "url", "console", "groupCollapsed", "formatMessage", "log", "groupEnd", "milliseconds", "byteLength", "publicData", "arrow", "defaultPrevented"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/ws/utils/attachWebSocketLogger.ts"], "sourcesContent": ["import type {\n  WebSocketClientConnection,\n  WebSocketConnectionData,\n  WebSocketData,\n} from '@mswjs/interceptors/WebSocket'\nimport { devUtils } from '../../utils/internal/devUtils'\nimport { getTimestamp } from '../../utils/logging/getTimestamp'\nimport { toPublicUrl } from '../../utils/request/toPublicUrl'\nimport { getMessageLength } from './getMessageLength'\nimport { getPublicData } from './getPublicData'\n\nconst colors = {\n  system: '#3b82f6',\n  outgoing: '#22c55e',\n  incoming: '#ef4444',\n  mocked: '#ff6a33',\n}\n\nexport function attachWebSocketLogger(\n  connection: WebSocketConnectionData,\n): void {\n  const { client, server } = connection\n\n  logConnectionOpen(client)\n\n  // Log the events sent from the WebSocket client.\n  // WebSocket client connection object is written from the\n  // server's perspective so these message events are outgoing.\n  /**\n   * @todo Provide the reference to the exact event handler\n   * that called this `client.send()`.\n   */\n  client.addEventListener('message', (event) => {\n    logOutgoingClientMessage(event)\n  })\n\n  client.addEventListener('close', (event) => {\n    logConnectionClose(event)\n  })\n\n  // Log client errors (connection closures due to errors).\n  client.socket.addEventListener('error', (event) => {\n    logClientError(event)\n  })\n\n  client.send = new Proxy(client.send, {\n    apply(target, thisArg, args) {\n      const [data] = args\n      const messageEvent = new MessageEvent('message', { data })\n      Object.defineProperties(messageEvent, {\n        currentTarget: {\n          enumerable: true,\n          writable: false,\n          value: client.socket,\n        },\n        target: {\n          enumerable: true,\n          writable: false,\n          value: client.socket,\n        },\n      })\n\n      queueMicrotask(() => {\n        logIncomingMockedClientMessage(messageEvent)\n      })\n\n      return Reflect.apply(target, thisArg, args)\n    },\n  })\n\n  server.addEventListener(\n    'open',\n    () => {\n      server.addEventListener('message', (event) => {\n        logIncomingServerMessage(event)\n      })\n    },\n    { once: true },\n  )\n\n  // Log outgoing client events initiated by the event handler.\n  // The actual client never sent these but the handler did.\n  server.send = new Proxy(server.send, {\n    apply(target, thisArg, args) {\n      const [data] = args\n      const messageEvent = new MessageEvent('message', { data })\n      Object.defineProperties(messageEvent, {\n        currentTarget: {\n          enumerable: true,\n          writable: false,\n          value: server.socket,\n        },\n        target: {\n          enumerable: true,\n          writable: false,\n          value: server.socket,\n        },\n      })\n\n      logOutgoingMockedClientMessage(messageEvent)\n\n      return Reflect.apply(target, thisArg, args)\n    },\n  })\n}\n\n/**\n * Prints the WebSocket connection.\n * This is meant to be logged by every WebSocket handler\n * that intercepted this connection. This helps you see\n * what handlers observe this connection.\n */\nexport function logConnectionOpen(client: WebSocketClientConnection) {\n  const publicUrl = toPublicUrl(client.url)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(`${getTimestamp()} %c▶%c ${publicUrl}`),\n    `color:${colors.system}`,\n    'color:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log('Client:', client.socket)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\nfunction logConnectionClose(event: CloseEvent) {\n  const target = event.target as WebSocket\n  const publicUrl = toPublicUrl(target.url)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c■%c ${publicUrl}`,\n    ),\n    `color:${colors.system}`,\n    'color:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\nfunction logClientError(event: Event) {\n  const socket = event.target as WebSocket\n  const publicUrl = toPublicUrl(socket.url)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c\\u00D7%c ${publicUrl}`,\n    ),\n    `color:${colors.system}`,\n    'color:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\n/**\n * Prints the outgoing client message.\n */\nasync function logOutgoingClientMessage(event: MessageEvent<WebSocketData>) {\n  const byteLength = getMessageLength(event.data)\n  const publicData = await getPublicData(event.data)\n  const arrow = event.defaultPrevented ? '⇡' : '⬆'\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c${arrow}%c ${publicData} %c${byteLength}%c`,\n    ),\n    `color:${colors.outgoing}`,\n    'color:inherit',\n    'color:gray;font-weight:normal',\n    'color:inherit;font-weight:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\n/**\n * Prints the outgoing client message initiated\n * by `server.send()` in the event handler.\n */\nasync function logOutgoingMockedClientMessage(\n  event: MessageEvent<WebSocketData>,\n) {\n  const byteLength = getMessageLength(event.data)\n  const publicData = await getPublicData(event.data)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c⬆%c ${publicData} %c${byteLength}%c`,\n    ),\n    `color:${colors.mocked}`,\n    'color:inherit',\n    'color:gray;font-weight:normal',\n    'color:inherit;font-weight:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\n/**\n * Prints the outgoing client message initiated\n * by `client.send()` in the event handler.\n */\nasync function logIncomingMockedClientMessage(\n  event: MessageEvent<WebSocketData>,\n) {\n  const byteLength = getMessageLength(event.data)\n  const publicData = await getPublicData(event.data)\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c⬇%c ${publicData} %c${byteLength}%c`,\n    ),\n    `color:${colors.mocked}`,\n    'color:inherit',\n    'color:gray;font-weight:normal',\n    'color:inherit;font-weight:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n\nasync function logIncomingServerMessage(event: MessageEvent<WebSocketData>) {\n  const byteLength = getMessageLength(event.data)\n  const publicData = await getPublicData(event.data)\n  const arrow = event.defaultPrevented ? '⇣' : '⬇'\n\n  // eslint-disable-next-line no-console\n  console.groupCollapsed(\n    devUtils.formatMessage(\n      `${getTimestamp({ milliseconds: true })} %c${arrow}%c ${publicData} %c${byteLength}%c`,\n    ),\n    `color:${colors.incoming}`,\n    'color:inherit',\n    'color:gray;font-weight:normal',\n    'color:inherit;font-weight:inherit',\n  )\n  // eslint-disable-next-line no-console\n  console.log(event)\n  // eslint-disable-next-line no-console\n  console.groupEnd()\n}\n"], "mappings": "AAKA,SAASA,QAAA,QAAgB;AACzB,SAASC,YAAA,QAAoB;AAC7B,SAASC,WAAA,QAAmB;AAC5B,SAASC,gBAAA,QAAwB;AACjC,SAASC,aAAA,QAAqB;AAE9B,MAAMC,MAAA,GAAS;EACbC,MAAA,EAAQ;EACRC,QAAA,EAAU;EACVC,QAAA,EAAU;EACVC,MAAA,EAAQ;AACV;AAEO,SAASC,sBACdC,UAAA,EACM;EACN,MAAM;IAAEC,MAAA;IAAQC;EAAO,IAAIF,UAAA;EAE3BG,iBAAA,CAAkBF,MAAM;EASxBA,MAAA,CAAOG,gBAAA,CAAiB,WAAYC,KAAA,IAAU;IAC5CC,wBAAA,CAAyBD,KAAK;EAChC,CAAC;EAEDJ,MAAA,CAAOG,gBAAA,CAAiB,SAAUC,KAAA,IAAU;IAC1CE,kBAAA,CAAmBF,KAAK;EAC1B,CAAC;EAGDJ,MAAA,CAAOO,MAAA,CAAOJ,gBAAA,CAAiB,SAAUC,KAAA,IAAU;IACjDI,cAAA,CAAeJ,KAAK;EACtB,CAAC;EAEDJ,MAAA,CAAOS,IAAA,GAAO,IAAIC,KAAA,CAAMV,MAAA,CAAOS,IAAA,EAAM;IACnCE,MAAMC,MAAA,EAAQC,OAAA,EAASC,IAAA,EAAM;MAC3B,MAAM,CAACC,IAAI,IAAID,IAAA;MACf,MAAME,YAAA,GAAe,IAAIC,YAAA,CAAa,WAAW;QAAEF;MAAK,CAAC;MACzDG,MAAA,CAAOC,gBAAA,CAAiBH,YAAA,EAAc;QACpCI,aAAA,EAAe;UACbC,UAAA,EAAY;UACZC,QAAA,EAAU;UACVC,KAAA,EAAOvB,MAAA,CAAOO;QAChB;QACAK,MAAA,EAAQ;UACNS,UAAA,EAAY;UACZC,QAAA,EAAU;UACVC,KAAA,EAAOvB,MAAA,CAAOO;QAChB;MACF,CAAC;MAEDiB,cAAA,CAAe,MAAM;QACnBC,8BAAA,CAA+BT,YAAY;MAC7C,CAAC;MAED,OAAOU,OAAA,CAAQf,KAAA,CAAMC,MAAA,EAAQC,OAAA,EAASC,IAAI;IAC5C;EACF,CAAC;EAEDb,MAAA,CAAOE,gBAAA,CACL,QACA,MAAM;IACJF,MAAA,CAAOE,gBAAA,CAAiB,WAAYC,KAAA,IAAU;MAC5CuB,wBAAA,CAAyBvB,KAAK;IAChC,CAAC;EACH,GACA;IAAEwB,IAAA,EAAM;EAAK,CACf;EAIA3B,MAAA,CAAOQ,IAAA,GAAO,IAAIC,KAAA,CAAMT,MAAA,CAAOQ,IAAA,EAAM;IACnCE,MAAMC,MAAA,EAAQC,OAAA,EAASC,IAAA,EAAM;MAC3B,MAAM,CAACC,IAAI,IAAID,IAAA;MACf,MAAME,YAAA,GAAe,IAAIC,YAAA,CAAa,WAAW;QAAEF;MAAK,CAAC;MACzDG,MAAA,CAAOC,gBAAA,CAAiBH,YAAA,EAAc;QACpCI,aAAA,EAAe;UACbC,UAAA,EAAY;UACZC,QAAA,EAAU;UACVC,KAAA,EAAOtB,MAAA,CAAOM;QAChB;QACAK,MAAA,EAAQ;UACNS,UAAA,EAAY;UACZC,QAAA,EAAU;UACVC,KAAA,EAAOtB,MAAA,CAAOM;QAChB;MACF,CAAC;MAEDsB,8BAAA,CAA+Bb,YAAY;MAE3C,OAAOU,OAAA,CAAQf,KAAA,CAAMC,MAAA,EAAQC,OAAA,EAASC,IAAI;IAC5C;EACF,CAAC;AACH;AAQO,SAASZ,kBAAkBF,MAAA,EAAmC;EACnE,MAAM8B,SAAA,GAAYxC,WAAA,CAAYU,MAAA,CAAO+B,GAAG;EAGxCC,OAAA,CAAQC,cAAA,CACN7C,QAAA,CAAS8C,aAAA,CAAc,GAAG7C,YAAA,CAAa,CAAC,eAAUyC,SAAS,EAAE,GAC7D,SAASrC,MAAA,CAAOC,MAAM,IACtB,eACF;EAEAsC,OAAA,CAAQG,GAAA,CAAI,WAAWnC,MAAA,CAAOO,MAAM;EAEpCyB,OAAA,CAAQI,QAAA,CAAS;AACnB;AAEA,SAAS9B,mBAAmBF,KAAA,EAAmB;EAC7C,MAAMQ,MAAA,GAASR,KAAA,CAAMQ,MAAA;EACrB,MAAMkB,SAAA,GAAYxC,WAAA,CAAYsB,MAAA,CAAOmB,GAAG;EAGxCC,OAAA,CAAQC,cAAA,CACN7C,QAAA,CAAS8C,aAAA,CACP,GAAG7C,YAAA,CAAa;IAAEgD,YAAA,EAAc;EAAK,CAAC,CAAC,eAAUP,SAAS,EAC5D,GACA,SAASrC,MAAA,CAAOC,MAAM,IACtB,eACF;EAEAsC,OAAA,CAAQG,GAAA,CAAI/B,KAAK;EAEjB4B,OAAA,CAAQI,QAAA,CAAS;AACnB;AAEA,SAAS5B,eAAeJ,KAAA,EAAc;EACpC,MAAMG,MAAA,GAASH,KAAA,CAAMQ,MAAA;EACrB,MAAMkB,SAAA,GAAYxC,WAAA,CAAYiB,MAAA,CAAOwB,GAAG;EAGxCC,OAAA,CAAQC,cAAA,CACN7C,QAAA,CAAS8C,aAAA,CACP,GAAG7C,YAAA,CAAa;IAAEgD,YAAA,EAAc;EAAK,CAAC,CAAC,aAAeP,SAAS,EACjE,GACA,SAASrC,MAAA,CAAOC,MAAM,IACtB,eACF;EAEAsC,OAAA,CAAQG,GAAA,CAAI/B,KAAK;EAEjB4B,OAAA,CAAQI,QAAA,CAAS;AACnB;AAKA,eAAe/B,yBAAyBD,KAAA,EAAoC;EAC1E,MAAMkC,UAAA,GAAa/C,gBAAA,CAAiBa,KAAA,CAAMW,IAAI;EAC9C,MAAMwB,UAAA,GAAa,MAAM/C,aAAA,CAAcY,KAAA,CAAMW,IAAI;EACjD,MAAMyB,KAAA,GAAQpC,KAAA,CAAMqC,gBAAA,GAAmB,WAAM;EAG7CT,OAAA,CAAQC,cAAA,CACN7C,QAAA,CAAS8C,aAAA,CACP,GAAG7C,YAAA,CAAa;IAAEgD,YAAA,EAAc;EAAK,CAAC,CAAC,MAAMG,KAAK,MAAMD,UAAU,MAAMD,UAAU,IACpF,GACA,SAAS7C,MAAA,CAAOE,QAAQ,IACxB,iBACA,iCACA,mCACF;EAEAqC,OAAA,CAAQG,GAAA,CAAI/B,KAAK;EAEjB4B,OAAA,CAAQI,QAAA,CAAS;AACnB;AAMA,eAAeP,+BACbzB,KAAA,EACA;EACA,MAAMkC,UAAA,GAAa/C,gBAAA,CAAiBa,KAAA,CAAMW,IAAI;EAC9C,MAAMwB,UAAA,GAAa,MAAM/C,aAAA,CAAcY,KAAA,CAAMW,IAAI;EAGjDiB,OAAA,CAAQC,cAAA,CACN7C,QAAA,CAAS8C,aAAA,CACP,GAAG7C,YAAA,CAAa;IAAEgD,YAAA,EAAc;EAAK,CAAC,CAAC,eAAUE,UAAU,MAAMD,UAAU,IAC7E,GACA,SAAS7C,MAAA,CAAOI,MAAM,IACtB,iBACA,iCACA,mCACF;EAEAmC,OAAA,CAAQG,GAAA,CAAI/B,KAAK;EAEjB4B,OAAA,CAAQI,QAAA,CAAS;AACnB;AAMA,eAAeX,+BACbrB,KAAA,EACA;EACA,MAAMkC,UAAA,GAAa/C,gBAAA,CAAiBa,KAAA,CAAMW,IAAI;EAC9C,MAAMwB,UAAA,GAAa,MAAM/C,aAAA,CAAcY,KAAA,CAAMW,IAAI;EAGjDiB,OAAA,CAAQC,cAAA,CACN7C,QAAA,CAAS8C,aAAA,CACP,GAAG7C,YAAA,CAAa;IAAEgD,YAAA,EAAc;EAAK,CAAC,CAAC,eAAUE,UAAU,MAAMD,UAAU,IAC7E,GACA,SAAS7C,MAAA,CAAOI,MAAM,IACtB,iBACA,iCACA,mCACF;EAEAmC,OAAA,CAAQG,GAAA,CAAI/B,KAAK;EAEjB4B,OAAA,CAAQI,QAAA,CAAS;AACnB;AAEA,eAAeT,yBAAyBvB,KAAA,EAAoC;EAC1E,MAAMkC,UAAA,GAAa/C,gBAAA,CAAiBa,KAAA,CAAMW,IAAI;EAC9C,MAAMwB,UAAA,GAAa,MAAM/C,aAAA,CAAcY,KAAA,CAAMW,IAAI;EACjD,MAAMyB,KAAA,GAAQpC,KAAA,CAAMqC,gBAAA,GAAmB,WAAM;EAG7CT,OAAA,CAAQC,cAAA,CACN7C,QAAA,CAAS8C,aAAA,CACP,GAAG7C,YAAA,CAAa;IAAEgD,YAAA,EAAc;EAAK,CAAC,CAAC,MAAMG,KAAK,MAAMD,UAAU,MAAMD,UAAU,IACpF,GACA,SAAS7C,MAAA,CAAOG,QAAQ,IACxB,iBACA,iCACA,mCACF;EAEAoC,OAAA,CAAQG,GAAA,CAAI/B,KAAK;EAEjB4B,OAAA,CAAQI,QAAA,CAAS;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}