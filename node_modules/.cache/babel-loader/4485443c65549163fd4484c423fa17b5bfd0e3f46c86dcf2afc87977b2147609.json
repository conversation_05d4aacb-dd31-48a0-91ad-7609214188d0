{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isAsyncIterable } from '../jsutils/isAsyncIterable.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { locatedError } from '../error/locatedError.mjs';\nimport { collectFields } from './collectFields.mjs';\nimport { assertValidExecutionArguments, buildExecutionContext, buildResolveInfo, execute, getFieldDef } from './execute.mjs';\nimport { mapAsyncIterator } from './mapAsyncIterator.mjs';\nimport { getArgumentValues } from './values.mjs';\n/**\n * Implements the \"Subscribe\" algorithm described in the GraphQL specification.\n *\n * Returns a Promise which resolves to either an AsyncIterator (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to an AsyncIterator, which\n * yields a stream of ExecutionResults representing the response stream.\n *\n * Accepts either an object with named arguments, or individual arguments.\n */\n\nexport async function subscribe(args) {\n  // Temporary for v15 to v16 migration. Remove in v17\n  arguments.length < 2 || devAssert(false, 'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.');\n  const resultOrStream = await createSourceEventStream(args);\n  if (!isAsyncIterable(resultOrStream)) {\n    return resultOrStream;\n  } // For each payload yielded from a subscription, map it over the normal\n  // GraphQL `execute` function, with `payload` as the rootValue.\n  // This implements the \"MapSourceToResponseEvent\" algorithm described in\n  // the GraphQL specification. The `execute` function provides the\n  // \"ExecuteSubscriptionEvent\" algorithm, as it is nearly identical to the\n  // \"ExecuteQuery\" algorithm, for which `execute` is also used.\n\n  const mapSourceToResponse = payload => execute({\n    ...args,\n    rootValue: payload\n  }); // Map every source value to a ExecutionResult value as described above.\n\n  return mapAsyncIterator(resultOrStream, mapSourceToResponse);\n}\nfunction toNormalizedArgs(args) {\n  const firstArg = args[0];\n  if (firstArg && 'document' in firstArg) {\n    return firstArg;\n  }\n  return {\n    schema: firstArg,\n    // FIXME: when underlying TS bug fixed, see https://github.com/microsoft/TypeScript/issues/31613\n    document: args[1],\n    rootValue: args[2],\n    contextValue: args[3],\n    variableValues: args[4],\n    operationName: args[5],\n    subscribeFieldResolver: args[6]\n  };\n}\n/**\n * Implements the \"CreateSourceEventStream\" algorithm described in the\n * GraphQL specification, resolving the subscription source event stream.\n *\n * Returns a Promise which resolves to either an AsyncIterable (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to the AsyncIterable for the\n * event stream returned by the resolver.\n *\n * A Source Event Stream represents a sequence of events, each of which triggers\n * a GraphQL execution for that event.\n *\n * This may be useful when hosting the stateful subscription service in a\n * different process or machine than the stateless GraphQL execution engine,\n * or otherwise separating these two steps. For more on this, see the\n * \"Supporting Subscriptions at Scale\" information in the GraphQL specification.\n */\n\nexport async function createSourceEventStream(...rawArgs) {\n  const args = toNormalizedArgs(rawArgs);\n  const {\n    schema,\n    document,\n    variableValues\n  } = args; // If arguments are missing or incorrectly typed, this is an internal\n  // developer mistake which should throw an early error.\n\n  assertValidExecutionArguments(schema, document, variableValues); // If a valid execution context cannot be created due to incorrect arguments,\n  // a \"Response\" with only errors is returned.\n\n  const exeContext = buildExecutionContext(args); // Return early errors if execution context failed.\n\n  if (!('schema' in exeContext)) {\n    return {\n      errors: exeContext\n    };\n  }\n  try {\n    const eventStream = await executeSubscription(exeContext); // Assert field returned an event stream, otherwise yield an error.\n\n    if (!isAsyncIterable(eventStream)) {\n      throw new Error('Subscription field must return Async Iterable. ' + `Received: ${inspect(eventStream)}.`);\n    }\n    return eventStream;\n  } catch (error) {\n    // If it GraphQLError, report it as an ExecutionResult, containing only errors and no data.\n    // Otherwise treat the error as a system-class error and re-throw it.\n    if (error instanceof GraphQLError) {\n      return {\n        errors: [error]\n      };\n    }\n    throw error;\n  }\n}\nasync function executeSubscription(exeContext) {\n  const {\n    schema,\n    fragments,\n    operation,\n    variableValues,\n    rootValue\n  } = exeContext;\n  const rootType = schema.getSubscriptionType();\n  if (rootType == null) {\n    throw new GraphQLError('Schema is not configured to execute subscription operation.', {\n      nodes: operation\n    });\n  }\n  const rootFields = collectFields(schema, fragments, variableValues, rootType, operation.selectionSet);\n  const [responseName, fieldNodes] = [...rootFields.entries()][0];\n  const fieldDef = getFieldDef(schema, rootType, fieldNodes[0]);\n  if (!fieldDef) {\n    const fieldName = fieldNodes[0].name.value;\n    throw new GraphQLError(`The subscription field \"${fieldName}\" is not defined.`, {\n      nodes: fieldNodes\n    });\n  }\n  const path = addPath(undefined, responseName, rootType.name);\n  const info = buildResolveInfo(exeContext, fieldDef, fieldNodes, rootType, path);\n  try {\n    var _fieldDef$subscribe;\n\n    // Implements the \"ResolveFieldEventStream\" algorithm from GraphQL specification.\n    // It differs from \"ResolveFieldValue\" due to providing a different `resolveFn`.\n    // Build a JS object of arguments from the field.arguments AST, using the\n    // variables scope to fulfill any variable references.\n    const args = getArgumentValues(fieldDef, fieldNodes[0], variableValues); // The resolve function's optional third argument is a context value that\n    // is provided to every resolve function within an execution. It is commonly\n    // used to represent an authenticated user, or request-specific caches.\n\n    const contextValue = exeContext.contextValue; // Call the `subscribe()` resolver or the default resolver to produce an\n    // AsyncIterable yielding raw payloads.\n\n    const resolveFn = (_fieldDef$subscribe = fieldDef.subscribe) !== null && _fieldDef$subscribe !== void 0 ? _fieldDef$subscribe : exeContext.subscribeFieldResolver;\n    const eventStream = await resolveFn(rootValue, args, contextValue, info);\n    if (eventStream instanceof Error) {\n      throw eventStream;\n    }\n    return eventStream;\n  } catch (error) {\n    throw locatedError(error, fieldNodes, pathToArray(path));\n  }\n}", "map": {"version": 3, "names": ["devAssert", "inspect", "isAsyncIterable", "addPath", "pathToArray", "GraphQLError", "locatedError", "collectFields", "assertValidExecutionArguments", "buildExecutionContext", "buildResolveInfo", "execute", "getFieldDef", "mapAsyncIterator", "getArgumentValues", "subscribe", "args", "arguments", "length", "resultOrStream", "createSourceEventStream", "mapSourceToResponse", "payload", "rootValue", "toNormalizedArgs", "firstArg", "schema", "document", "contextValue", "variableValues", "operationName", "subscribeFieldResolver", "rawArgs", "exeContext", "errors", "eventStream", "executeSubscription", "Error", "error", "fragments", "operation", "rootType", "getSubscriptionType", "nodes", "rootFields", "selectionSet", "responseName", "fieldNodes", "entries", "fieldDef", "fieldName", "name", "value", "path", "undefined", "info", "_fieldDef$subscribe", "resolveFn"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/graphql/execution/subscribe.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isAsyncIterable } from '../jsutils/isAsyncIterable.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { locatedError } from '../error/locatedError.mjs';\nimport { collectFields } from './collectFields.mjs';\nimport {\n  assertValidExecutionArguments,\n  buildExecutionContext,\n  buildResolveInfo,\n  execute,\n  getFieldDef,\n} from './execute.mjs';\nimport { mapAsyncIterator } from './mapAsyncIterator.mjs';\nimport { getArgumentValues } from './values.mjs';\n/**\n * Implements the \"Subscribe\" algorithm described in the GraphQL specification.\n *\n * Returns a Promise which resolves to either an AsyncIterator (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to an AsyncIterator, which\n * yields a stream of ExecutionResults representing the response stream.\n *\n * Accepts either an object with named arguments, or individual arguments.\n */\n\nexport async function subscribe(args) {\n  // Temporary for v15 to v16 migration. Remove in v17\n  arguments.length < 2 ||\n    devAssert(\n      false,\n      'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.',\n    );\n  const resultOrStream = await createSourceEventStream(args);\n\n  if (!isAsyncIterable(resultOrStream)) {\n    return resultOrStream;\n  } // For each payload yielded from a subscription, map it over the normal\n  // GraphQL `execute` function, with `payload` as the rootValue.\n  // This implements the \"MapSourceToResponseEvent\" algorithm described in\n  // the GraphQL specification. The `execute` function provides the\n  // \"ExecuteSubscriptionEvent\" algorithm, as it is nearly identical to the\n  // \"ExecuteQuery\" algorithm, for which `execute` is also used.\n\n  const mapSourceToResponse = (payload) =>\n    execute({ ...args, rootValue: payload }); // Map every source value to a ExecutionResult value as described above.\n\n  return mapAsyncIterator(resultOrStream, mapSourceToResponse);\n}\n\nfunction toNormalizedArgs(args) {\n  const firstArg = args[0];\n\n  if (firstArg && 'document' in firstArg) {\n    return firstArg;\n  }\n\n  return {\n    schema: firstArg,\n    // FIXME: when underlying TS bug fixed, see https://github.com/microsoft/TypeScript/issues/31613\n    document: args[1],\n    rootValue: args[2],\n    contextValue: args[3],\n    variableValues: args[4],\n    operationName: args[5],\n    subscribeFieldResolver: args[6],\n  };\n}\n/**\n * Implements the \"CreateSourceEventStream\" algorithm described in the\n * GraphQL specification, resolving the subscription source event stream.\n *\n * Returns a Promise which resolves to either an AsyncIterable (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to the AsyncIterable for the\n * event stream returned by the resolver.\n *\n * A Source Event Stream represents a sequence of events, each of which triggers\n * a GraphQL execution for that event.\n *\n * This may be useful when hosting the stateful subscription service in a\n * different process or machine than the stateless GraphQL execution engine,\n * or otherwise separating these two steps. For more on this, see the\n * \"Supporting Subscriptions at Scale\" information in the GraphQL specification.\n */\n\nexport async function createSourceEventStream(...rawArgs) {\n  const args = toNormalizedArgs(rawArgs);\n  const { schema, document, variableValues } = args; // If arguments are missing or incorrectly typed, this is an internal\n  // developer mistake which should throw an early error.\n\n  assertValidExecutionArguments(schema, document, variableValues); // If a valid execution context cannot be created due to incorrect arguments,\n  // a \"Response\" with only errors is returned.\n\n  const exeContext = buildExecutionContext(args); // Return early errors if execution context failed.\n\n  if (!('schema' in exeContext)) {\n    return {\n      errors: exeContext,\n    };\n  }\n\n  try {\n    const eventStream = await executeSubscription(exeContext); // Assert field returned an event stream, otherwise yield an error.\n\n    if (!isAsyncIterable(eventStream)) {\n      throw new Error(\n        'Subscription field must return Async Iterable. ' +\n          `Received: ${inspect(eventStream)}.`,\n      );\n    }\n\n    return eventStream;\n  } catch (error) {\n    // If it GraphQLError, report it as an ExecutionResult, containing only errors and no data.\n    // Otherwise treat the error as a system-class error and re-throw it.\n    if (error instanceof GraphQLError) {\n      return {\n        errors: [error],\n      };\n    }\n\n    throw error;\n  }\n}\n\nasync function executeSubscription(exeContext) {\n  const { schema, fragments, operation, variableValues, rootValue } =\n    exeContext;\n  const rootType = schema.getSubscriptionType();\n\n  if (rootType == null) {\n    throw new GraphQLError(\n      'Schema is not configured to execute subscription operation.',\n      {\n        nodes: operation,\n      },\n    );\n  }\n\n  const rootFields = collectFields(\n    schema,\n    fragments,\n    variableValues,\n    rootType,\n    operation.selectionSet,\n  );\n  const [responseName, fieldNodes] = [...rootFields.entries()][0];\n  const fieldDef = getFieldDef(schema, rootType, fieldNodes[0]);\n\n  if (!fieldDef) {\n    const fieldName = fieldNodes[0].name.value;\n    throw new GraphQLError(\n      `The subscription field \"${fieldName}\" is not defined.`,\n      {\n        nodes: fieldNodes,\n      },\n    );\n  }\n\n  const path = addPath(undefined, responseName, rootType.name);\n  const info = buildResolveInfo(\n    exeContext,\n    fieldDef,\n    fieldNodes,\n    rootType,\n    path,\n  );\n\n  try {\n    var _fieldDef$subscribe;\n\n    // Implements the \"ResolveFieldEventStream\" algorithm from GraphQL specification.\n    // It differs from \"ResolveFieldValue\" due to providing a different `resolveFn`.\n    // Build a JS object of arguments from the field.arguments AST, using the\n    // variables scope to fulfill any variable references.\n    const args = getArgumentValues(fieldDef, fieldNodes[0], variableValues); // The resolve function's optional third argument is a context value that\n    // is provided to every resolve function within an execution. It is commonly\n    // used to represent an authenticated user, or request-specific caches.\n\n    const contextValue = exeContext.contextValue; // Call the `subscribe()` resolver or the default resolver to produce an\n    // AsyncIterable yielding raw payloads.\n\n    const resolveFn =\n      (_fieldDef$subscribe = fieldDef.subscribe) !== null &&\n      _fieldDef$subscribe !== void 0\n        ? _fieldDef$subscribe\n        : exeContext.subscribeFieldResolver;\n    const eventStream = await resolveFn(rootValue, args, contextValue, info);\n\n    if (eventStream instanceof Error) {\n      throw eventStream;\n    }\n\n    return eventStream;\n  } catch (error) {\n    throw locatedError(error, fieldNodes, pathToArray(path));\n  }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,OAAO,EAAEC,WAAW,QAAQ,qBAAqB;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SACEC,6BAA6B,EAC7BC,qBAAqB,EACrBC,gBAAgB,EAChBC,OAAO,EACPC,WAAW,QACN,eAAe;AACtB,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,iBAAiB,QAAQ,cAAc;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,eAAeC,SAASA,CAACC,IAAI,EAAE;EACpC;EACAC,SAAS,CAACC,MAAM,GAAG,CAAC,IAClBlB,SAAS,CACP,KAAK,EACL,qGACF,CAAC;EACH,MAAMmB,cAAc,GAAG,MAAMC,uBAAuB,CAACJ,IAAI,CAAC;EAE1D,IAAI,CAACd,eAAe,CAACiB,cAAc,CAAC,EAAE;IACpC,OAAOA,cAAc;EACvB,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;;EAEA,MAAME,mBAAmB,GAAIC,OAAO,IAClCX,OAAO,CAAC;IAAE,GAAGK,IAAI;IAAEO,SAAS,EAAED;EAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5C,OAAOT,gBAAgB,CAACM,cAAc,EAAEE,mBAAmB,CAAC;AAC9D;AAEA,SAASG,gBAAgBA,CAACR,IAAI,EAAE;EAC9B,MAAMS,QAAQ,GAAGT,IAAI,CAAC,CAAC,CAAC;EAExB,IAAIS,QAAQ,IAAI,UAAU,IAAIA,QAAQ,EAAE;IACtC,OAAOA,QAAQ;EACjB;EAEA,OAAO;IACLC,MAAM,EAAED,QAAQ;IAChB;IACAE,QAAQ,EAAEX,IAAI,CAAC,CAAC,CAAC;IACjBO,SAAS,EAAEP,IAAI,CAAC,CAAC,CAAC;IAClBY,YAAY,EAAEZ,IAAI,CAAC,CAAC,CAAC;IACrBa,cAAc,EAAEb,IAAI,CAAC,CAAC,CAAC;IACvBc,aAAa,EAAEd,IAAI,CAAC,CAAC,CAAC;IACtBe,sBAAsB,EAAEf,IAAI,CAAC,CAAC;EAChC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,eAAeI,uBAAuBA,CAAC,GAAGY,OAAO,EAAE;EACxD,MAAMhB,IAAI,GAAGQ,gBAAgB,CAACQ,OAAO,CAAC;EACtC,MAAM;IAAEN,MAAM;IAAEC,QAAQ;IAAEE;EAAe,CAAC,GAAGb,IAAI,CAAC,CAAC;EACnD;;EAEAR,6BAA6B,CAACkB,MAAM,EAAEC,QAAQ,EAAEE,cAAc,CAAC,CAAC,CAAC;EACjE;;EAEA,MAAMI,UAAU,GAAGxB,qBAAqB,CAACO,IAAI,CAAC,CAAC,CAAC;;EAEhD,IAAI,EAAE,QAAQ,IAAIiB,UAAU,CAAC,EAAE;IAC7B,OAAO;MACLC,MAAM,EAAED;IACV,CAAC;EACH;EAEA,IAAI;IACF,MAAME,WAAW,GAAG,MAAMC,mBAAmB,CAACH,UAAU,CAAC,CAAC,CAAC;;IAE3D,IAAI,CAAC/B,eAAe,CAACiC,WAAW,CAAC,EAAE;MACjC,MAAM,IAAIE,KAAK,CACb,iDAAiD,GAC/C,aAAapC,OAAO,CAACkC,WAAW,CAAC,GACrC,CAAC;IACH;IAEA,OAAOA,WAAW;EACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd;IACA;IACA,IAAIA,KAAK,YAAYjC,YAAY,EAAE;MACjC,OAAO;QACL6B,MAAM,EAAE,CAACI,KAAK;MAChB,CAAC;IACH;IAEA,MAAMA,KAAK;EACb;AACF;AAEA,eAAeF,mBAAmBA,CAACH,UAAU,EAAE;EAC7C,MAAM;IAAEP,MAAM;IAAEa,SAAS;IAAEC,SAAS;IAAEX,cAAc;IAAEN;EAAU,CAAC,GAC/DU,UAAU;EACZ,MAAMQ,QAAQ,GAAGf,MAAM,CAACgB,mBAAmB,CAAC,CAAC;EAE7C,IAAID,QAAQ,IAAI,IAAI,EAAE;IACpB,MAAM,IAAIpC,YAAY,CACpB,6DAA6D,EAC7D;MACEsC,KAAK,EAAEH;IACT,CACF,CAAC;EACH;EAEA,MAAMI,UAAU,GAAGrC,aAAa,CAC9BmB,MAAM,EACNa,SAAS,EACTV,cAAc,EACdY,QAAQ,EACRD,SAAS,CAACK,YACZ,CAAC;EACD,MAAM,CAACC,YAAY,EAAEC,UAAU,CAAC,GAAG,CAAC,GAAGH,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAMC,QAAQ,GAAGrC,WAAW,CAACc,MAAM,EAAEe,QAAQ,EAAEM,UAAU,CAAC,CAAC,CAAC,CAAC;EAE7D,IAAI,CAACE,QAAQ,EAAE;IACb,MAAMC,SAAS,GAAGH,UAAU,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,KAAK;IAC1C,MAAM,IAAI/C,YAAY,CACpB,2BAA2B6C,SAAS,mBAAmB,EACvD;MACEP,KAAK,EAAEI;IACT,CACF,CAAC;EACH;EAEA,MAAMM,IAAI,GAAGlD,OAAO,CAACmD,SAAS,EAAER,YAAY,EAAEL,QAAQ,CAACU,IAAI,CAAC;EAC5D,MAAMI,IAAI,GAAG7C,gBAAgB,CAC3BuB,UAAU,EACVgB,QAAQ,EACRF,UAAU,EACVN,QAAQ,EACRY,IACF,CAAC;EAED,IAAI;IACF,IAAIG,mBAAmB;;IAEvB;IACA;IACA;IACA;IACA,MAAMxC,IAAI,GAAGF,iBAAiB,CAACmC,QAAQ,EAAEF,UAAU,CAAC,CAAC,CAAC,EAAElB,cAAc,CAAC,CAAC,CAAC;IACzE;IACA;;IAEA,MAAMD,YAAY,GAAGK,UAAU,CAACL,YAAY,CAAC,CAAC;IAC9C;;IAEA,MAAM6B,SAAS,GACb,CAACD,mBAAmB,GAAGP,QAAQ,CAAClC,SAAS,MAAM,IAAI,IACnDyC,mBAAmB,KAAK,KAAK,CAAC,GAC1BA,mBAAmB,GACnBvB,UAAU,CAACF,sBAAsB;IACvC,MAAMI,WAAW,GAAG,MAAMsB,SAAS,CAAClC,SAAS,EAAEP,IAAI,EAAEY,YAAY,EAAE2B,IAAI,CAAC;IAExE,IAAIpB,WAAW,YAAYE,KAAK,EAAE;MAChC,MAAMF,WAAW;IACnB;IAEA,OAAOA,WAAW;EACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,MAAMhC,YAAY,CAACgC,KAAK,EAAES,UAAU,EAAE3C,WAAW,CAACiD,IAAI,CAAC,CAAC;EAC1D;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}