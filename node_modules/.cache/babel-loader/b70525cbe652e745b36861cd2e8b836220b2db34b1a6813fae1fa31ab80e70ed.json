{"ast": null, "code": "function isIterable(fn) {\n  if (!fn) {\n    return false;\n  }\n  return Reflect.has(fn, Symbol.iterator) || Reflect.has(fn, Symbol.asyncIterator);\n}\nexport { isIterable };", "map": {"version": 3, "names": ["isIterable", "fn", "Reflect", "has", "Symbol", "iterator", "asyncIterator"], "sources": ["/Users/<USER>/Documents/augment-projects/bia-grc/node_modules/msw/src/core/utils/internal/isIterable.ts"], "sourcesContent": ["/**\n * This is the same as TypeScript's `Iterable`, but with all three type parameters.\n * @todo Remove once TypeScript 5.6 is the minimum.\n */\nexport interface Iterable<T, TReturn, TNext> {\n  [Symbol.iterator](): Iterator<T, TReturn, TNext>\n}\n\n/**\n * This is the same as TypeScript's `AsyncIterable`, but with all three type parameters.\n * @todo Remove once TypeScript 5.6 is the minimum.\n */\nexport interface AsyncIterable<T, TReturn, TNext> {\n  [Symbol.asyncIterator](): AsyncIterator<T, TReturn, TNext>\n}\n\n/**\n * Determines if the given function is an iterator.\n */\nexport function isIterable<IteratorType>(\n  fn: any,\n): fn is\n  | Iterable<IteratorType, IteratorType, IteratorType>\n  | AsyncIterable<IteratorType, IteratorType, IteratorType> {\n  if (!fn) {\n    return false\n  }\n\n  return (\n    Reflect.has(fn, Symbol.iterator) || Reflect.has(fn, Symbol.asyncIterator)\n  )\n}\n"], "mappings": "AAmBO,SAASA,WACdC,EAAA,EAG0D;EAC1D,IAAI,CAACA,EAAA,EAAI;IACP,OAAO;EACT;EAEA,OACEC,OAAA,CAAQC,GAAA,CAAIF,EAAA,EAAIG,MAAA,CAAOC,QAAQ,KAAKH,OAAA,CAAQC,GAAA,CAAIF,EAAA,EAAIG,MAAA,CAAOE,aAAa;AAE5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}