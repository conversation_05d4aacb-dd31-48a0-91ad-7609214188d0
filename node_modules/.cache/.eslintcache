[{"/Users/<USER>/Documents/augment-projects/bia-grc/src/index.js": "1", "/Users/<USER>/Documents/augment-projects/bia-grc/src/App.js": "2", "/Users/<USER>/Documents/augment-projects/bia-grc/src/mocks/browser.js": "3", "/Users/<USER>/Documents/augment-projects/bia-grc/src/mocks/handlers.js": "4", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/Dashboard.js": "5", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/Layout.js": "6", "/Users/<USER>/Documents/augment-projects/bia-grc/src/store/store.js": "7", "/Users/<USER>/Documents/augment-projects/bia-grc/src/mockData.js": "8", "/Users/<USER>/Documents/augment-projects/bia-grc/src/store/biaSlice.js": "9", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/BIAWorkspace.js": "10", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/ProcessAnalysis.js": "11", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/SetupScope.js": "12", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/ReviewSubmit.js": "13", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/Scorecard.js": "14", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/ImpactAssessment.js": "15", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/Dependencies.js": "16", "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/Resources.js": "17"}, {"size": 415, "mtime": 1753355735366, "results": "18", "hashOfConfig": "19"}, {"size": 1146, "mtime": 1753356780372, "results": "20", "hashOfConfig": "19"}, {"size": 130, "mtime": 1753356758955, "results": "21", "hashOfConfig": "19"}, {"size": 1023, "mtime": 1753356733141, "results": "22", "hashOfConfig": "19"}, {"size": 6387, "mtime": 1753355967933, "results": "23", "hashOfConfig": "19"}, {"size": 1685, "mtime": 1753355738572, "results": "24", "hashOfConfig": "19"}, {"size": 169, "mtime": 1753355759781, "results": "25", "hashOfConfig": "19"}, {"size": 1415, "mtime": 1753355595081, "results": "26", "hashOfConfig": "19"}, {"size": 2132, "mtime": 1753356882309, "results": "27", "hashOfConfig": "19"}, {"size": 4142, "mtime": 1753355907695, "results": "28", "hashOfConfig": "19"}, {"size": 2818, "mtime": 1753355929830, "results": "29", "hashOfConfig": "19"}, {"size": 6245, "mtime": 1753355912861, "results": "30", "hashOfConfig": "19"}, {"size": 15476, "mtime": 1753355958597, "results": "31", "hashOfConfig": "19"}, {"size": 7363, "mtime": 1753355919212, "results": "32", "hashOfConfig": "19"}, {"size": 9156, "mtime": 1753355933897, "results": "33", "hashOfConfig": "19"}, {"size": 10304, "mtime": 1753355941448, "results": "34", "hashOfConfig": "19"}, {"size": 5770, "mtime": 1753355948711, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8a0znh", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/bia-grc/src/index.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/App.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/mocks/browser.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/mocks/handlers.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/Dashboard.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/Layout.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/store/store.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/mockData.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/store/biaSlice.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/BIAWorkspace.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/ProcessAnalysis.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/SetupScope.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/ReviewSubmit.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/Scorecard.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/ImpactAssessment.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/Dependencies.js", [], [], "/Users/<USER>/Documents/augment-projects/bia-grc/src/components/workspace/analysis/Resources.js", [], []]